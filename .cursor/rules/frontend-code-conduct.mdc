---
description: 
globs: 
alwaysApply: false
---
# 前端编码规范

## 命名规范

1. **组件命名**
   - 使用 `UpperCamelCase`（大驼峰）命名组件
   - 低码平台组件需要以 `lyy-` 为前缀
   - 页面组件根 div 的 class 使用 `-container` 后缀（如：`home-container`）
   - 非页面组件根 div 的 class 使用 `-component` 后缀（如：`navigation-bar-component`）

2. **代码命名**
   - 方法名、参数名、成员变量、局部变量使用 `lowerCamelCase`（小驼峰）
   - 常量使用 `UPPER_SNAKE_CASE`（全大写下划线）
   - 禁止使用中文或拼音命名
   - 使用有意义的英文命名，确保拼写正确
   - 避免使用不规范、不通用的缩写

3. **文件命名**
   - 非类文件的功能文件夹名称统一使用小写
   - 使用设计模式时，在文件名/类名中体现（如：`RequestProxy`、`HumanFactory`）
   - 枚举/常量文件使用描述性命名（如：`StatusCodeConstants`）

## 代码风格

1. **通用规范**
   - 使用 ES6+ 特性（优先使用 const/let，避免使用 var）
   - 尽可能使用函数式编程
   - 使用 JSDoc + ts-document 规范编写文件、类、方法的文档注释
   - 使用 TODO/FIXME 标记待办事项
   - 使用合适的设计模式进行开发
   - 对必要/重要代码进行详细注释，省略一些非必要注释，使用语义化变量名/函数名作为替代
   - 保持注释与代码同步更新

2. **React/Vue 组件**
   - React 组件必须定义 propTypes
   - 使用有意义的类名（避免使用 `l-g`、`a-b` 等缩写）
   - 使用 Virtual DOM 框架时避免直接操作 DOM
   - 将重复的 DOM 结构抽取为可复用组件

3. **CSS/Less/Sass**
   - 使用全局前缀（如：`global-`）避免样式冲突
   - 优先使用 flex 布局
   - 避免过度使用 `!important`
   - 将公共样式抽取为可复用的 mixins/函数

## 最佳实践

1. **代码组织**
   - 遵循 DRY（Don't Repeat Yourself）原则
   - 按以下顺序组织导入：
     1. Node 模块（直接导入）
     2. Node 模块（方法导入）
     3. 项目对象（相对路径导入）
     4. 项目方法（相对路径导入）
     5. 样式文件

2. **错误处理**
   - 提供用户友好的错误信息
   - 仅对不稳定代码使用 try-catch
   - 必须处理捕获的异常
   - 使用 try-catch-finally 进行资源清理

3. **性能优化**
   - 避免在循环中实例化对象
   - 在执行耗时操作前进行类型检查
   - 优化第三方库的导入
   - 在可能的情况下使用 CDN 加载大型库

4. **安全性**
   - 敏感数据不要提交到版本控制系统
   - 使用环境特定的配置文件
   - 避免暴露内部实现细节

## 项目结构

1. **目录组织**
   - 保持目录命名的一致性（单数/复数）
   - 保持项目结构清晰合理
   - 使用适当的文件扩展名

2. **配置管理**
   - 提供个性化配置文件
   - 在版本控制中忽略本地配置文件


   - 使用环境特定的设置