import { axiosInst } from '../index'
import md5 from 'js-md5'
import { uuidv4 } from '@leyaoyao/libs'

export const getPublicKeyApi = () => {
  return axiosInst({
    baseURL: '/v2/',
    url: '/manage-service/web/login/key',
    method: 'post',
  })
}
export const getAgentTokenApi = () => {
  return axiosInst({
    method: 'post',
    url: '/agent/rest/login',
    data: {
      username: '14500001111',
      password: md5('16881688'),
      source: '',
      verifyCode: 'lyyohmygod',
    },
  })
}
export const getBLogin = (data) => {
  return axiosInst({
    method: 'post',
    url: 'https://db.leyaoyao.com/lyy/rest/group/distributor/login',
    headers: {
      'Authorization-Identifier': uuidv4(),
    },
    data,
  })
}
export const getCarCharingApi = (data) => {
  return axiosInst({
    method: 'post',
    url: 'https://sb.leyaoyao.com/lyy/rest/group/distributor/login',
    headers: {
      'Authorization-Identifier': uuidv4(),
    },
    data,
  })
}
export const getWawawuTokenApi = () => {
  return axiosInst({
    method: 'post',
    url: '/wawawu/rest/login',
    data: {
      username: '15899974725',
      password: md5('123456'),
      verifyCode: '123456',
    },
    headers: {
      'Authorization-Identifier': 'cdsf-21435cds-2134',
    },
  })
}
export const getSmallGroupTokenApi = () => {
  return axiosInst({
    method: 'post',
    url: '/gw/venue/login',
    data: {
      name: '13300001234',
      password: 'qwer1234',
    },
  })
}
export const getYiPuleTokenApi = (data) => {
  return axiosInst({
    baseURL: '/v2/',
    url: '/manage-service/web/login',
    method: 'post',
    data,
  })
}
