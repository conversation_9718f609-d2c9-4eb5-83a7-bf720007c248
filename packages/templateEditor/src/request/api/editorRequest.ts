import request, { DataType } from '@/request/request'

/**
 * 获取页面JSON配置
 * @returns
 */
export function getEditorJson(params, headers?) {
  return request({
    url: `/editor/page/configures/${params.id}`,
    method: 'get',
    params,
    headers,
  })
}

/**
 * 获取模板配置
 * @returns
 */
export function saveCompTemplate(data) {
  return request({
    url: '/editor/page/configure/templates/',
    method: 'post',
    data,
  })
}

/**
 * 查询默认应用配置
 */
export function getDefaultHttp(params, headers?) {
  return request({
    url: '/editor/system/environments/default',
    method: 'get',
    params,
    headers,
  })
}

/**
 * 配置保存
 */
export function saveConfig(data, headers?) {
  return request({
    url: '/editor/page/configures/',
    method: 'put',
    data,
    headers,
  })
}

/**
 * 分页查询模板列表
 */
export function getTemplateList(params, headers?) {
  return request({
    url: '/editor/page/configure/templates/',
    method: 'get',
    params,
    headers,
  })
}

/**
 * 删除模板
 */
export function deleteTemplate(id, headers?) {
  return request({
    url: `/editor/page/configure/templates/${id}`,
    method: 'delete',
    headers,
  })
}

/**
 * 查询菜单列表
 */
export function getMenusTree(params, headers?) {
  return request({
    url: '/editor/menus/tree',
    method: 'get',
    params,
    headers,
  })
}
/**
 * 获取权限码
 * @returns
 */
export function getAuthCodeFn(url, params) {
  return request({
    url: url,
    method: 'get',
    params,
  })
}
/**
 * 查询默认应用配置
 */
export function getHistoryPageConfig(url, params, headers?) {
  return request({
    url: url,
    method: 'get',
    params,
    headers,
  })
}

/**
 * 获取Js对象列表 或数据集列表
 */
export function getJsObject(params) {
  return request({
    url: '/editor/page/component',
    method: 'get',
    params,
  })
}

/**
 * 新增逻辑集
 */
export function addJsObject(data) {
  return request({
    url: '/editor/page/component',
    method: 'post',
    data,
  })
}

/**
 * 更新逻辑集
 */
export function updateJsObject(data) {
  return request({
    url: '/editor/page/component',
    method: 'put',
    data,
  })
}
/**
 * 删除逻辑集
 */
export function deleteJsObject(data) {
  return request({
    url: '/editor/page/component',
    method: 'delete',
    data,
  })
}

/**
 * 获取当前用户信息接口
 * @returns
 */
export function getUserInfo(params) {
  return request({
    url: '/editor/code/codeusers/currentUser',
    method: 'get',
    params,
  })
}

/**
 * 获取当前应用当前用户的分支
 * @returns
 */
export function getCurrentBranch(params) {
  return request({
    url: '/editor/code/operation/visibleBranches',
    method: 'get',
    params,
  })
}

/**
 * 获取当前应用当前用户的本地分支信息
 * @returns
 */
export function getCurrentLocalBranch(params) {
  return request({
    url: '/editor/code/operation/selectedLocalBranch',
    method: 'get',
    params,
  })
}

/**
 * 获取当前分支状态
 * @returns
 */
export function getCurrentBranchStatus(params) {
  return request({
    url: '/editor/code/operation/branchStatus',
    method: 'get',
    params,
  })
}

/**
 * checkout分支
 * @returns
 */
export function checkoutBranch(data) {
  return request({
    url: '/editor/code/operation/checkout',
    method: 'post',
    data,
  })
}

/**
 * pull分支
 * @returns
 */
export function pullBranch(data) {
  return request({
    url: '/editor/code/operation/pull',
    method: 'post',
    data,
  })
}

/**
 * 获取未提交页面
 * @returns
 */
export function getNotSubmit(params) {
  return request({
    url: '/editor/code/operation/stash',
    method: 'get',
    params,
  })
}

/**
 * 获取指派人列表
 * @returns
 */
export function getAssignUserList(params) {
  return request({
    url: '/editor/code/codeusers/assignUserList',
    method: 'get',
    params,
  })
}

/**
 * commit分支
 * @returns
 */
export function commitBranch(data) {
  return request({
    url: '/editor/code/operation/commit',
    method: 'post',
    data,
  })
}

/**
 * push分支
 * @returns
 */
export function pushBranch(data) {
  return request({
    url: '/editor/code/operation/push',
    method: 'post',
    data,
  })
}

/**
 * merge分支
 * @returns
 */
export function mergeBranch(data) {
  return request({
    url: '/editor/code/operation/mergeRequest',
    method: 'post',
    data,
  })
}

/**
 * 推送内容记录pushrecord
 * @returns
 */
export function pushRecordsList(params) {
  return request({
    url: '/editor/code/operation/pushRecords',
    method: 'get',
    params,
  })
}

/**
 * 获取分支列表
 * @returns
 */
export function getBranchList(params) {
  return request({
    url: '/editor/code/management/branches',
    method: 'get',
    params,
  })
}

/**
 * 删除分支前校验
 * @returns
 */
export function beforeDeleteBranch(data) {
  return request({
    url: '/editor/code/management/branches',
    method: 'post',
    data,
  })
}

/**
 * 删除分支
 * @returns
 */
export function deleteBranch(data) {
  return request({
    url: '/editor/code/management/branches',
    method: 'delete',
    data,
  })
}

// 获取失效记录表
export function getFailtemp(params) {
  return request({
    url: `/editor/code/temp`,
    method: 'get',
    params,
  })
}

// 删除失效记录
export function deleteFailtemp(data) {
  return request({
    url: `/editor/code/temp`,
    method: 'DELETE',
    data,
  })
}

// 获取当前失效记录内容
export function getFailtempDetail(codeTempId) {
  return request({
    url: `/editor/code/temp/${codeTempId}`,
    method: 'get',
  })
}

// 获取待处理推送记录表
export function getDropList(params) {
  return request({
    url: `/editor/code/drop`,
    method: 'get',
    params,
  })
}

// 获取待处理推送记录表
export function handleDealDrop(data) {
  return request({
    url: `/editor/code/drop/dealDrop`,
    method: 'POST',
    data,
  })
}

// 获取逻辑集/数据集列表
export function getLogic(params) {
  return request({
    url: `/editor/page/components/histories`,
    method: 'GET',
    params,
  })
}

// 恢复该条历史记录
export function getBackLogin(data) {
  return request({
    url: `/editor/page/components/histories/recoverHistory`,
    method: 'POST',
    data,
  })
}

// 导入数据集/逻辑集
export function importLogic(data) {
  return request({
    url: `/editor/page/component/import`,
    method: 'POST',
    data,
  })
}

// 拉去pull逻辑集/数据集数据
export function pullLogic(data) {
  return request({
    url: `/editor/page/component/pull`,
    method: 'POST',
    data,
  })
}

// 获取冲突对比内容
export function getClashContent(params) {
  return request({
    url: `/editor/code/drop/getPullComparison`,
    method: 'GET',
    params,
  })
}

// 处理待推送记录-Pull冲突
export function dealDropOfPull(data) {
  return request({
    url: `/editor/code/drop/dealDropOfPull`,
    method: 'POST',
    data,
  })
}

// commit取消冲突
export function cancelCommit(data) {
  return request({
    url: `/editor/code/operation/cancelCommit`,
    method: 'POST',
    data,
  })
}

// 复制数据集/逻辑集
export function copyJsObject(data) {
  return request({
    url: `/editor/page/component/copy`,
    method: 'POST',
    data,
  })
}

// 复制数据集/逻辑集
export function pasteJsObject(data) {
  return request({
    url: `/editor/page/component/paste`,
    method: 'POST',
    data,
  })
}
