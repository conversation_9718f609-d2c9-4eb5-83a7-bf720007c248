import axios, { AxiosRequestConfig } from 'axios'
import { notification, Modal } from 'ant-design-vue'
// 处理数据类型 BigInt（大整数）
import jsonBig from 'json-bigint'
import qs from 'qs'
import router from '@/Router'

export interface KeyMapType {
  source: string
  resKey: string
}

export interface DataType<T> {
  data: T
  description?: string
  resultCode: number
}

// 不需要校验的白名单
export const whitelist = ['/sso/getLoginUrl']

export const axiosService = axios.create({
  baseURL: '/gw',
  timeout: 1_000_000, // request timeout
  paramsSerializer: (params) => {
    /** 数组序列化逗号风格 [1,2,3] => 1,2,3 */
    return qs.stringify(params, { arrayFormat: 'comma' })
  },
  // transformResponse 允许自定义原始的响应数据（字符串）
  transformResponse: [
    function (data) {
      try {
        // 如果转换成功则返回转换的数据结果
        const jsonBigData = jsonBig.parse(data)
        return jsonBigData
      } catch {
        // 如果转换失败，则包装为统一数据格式并返回
        return {
          data,
        }
      }
    },
  ],
})

axiosService.interceptors.request.use(
  (config) => {
    //编辑器本身项目的请求头配置
    const token = window.sessionStorage.getItem('ramToken') || ''
    const editorHeaders = {
      'ram-token': token,
      'ram-system': window.editorSystem,
    }
    // 不是白名单的接口 都需要加上token
    if (!whitelist.includes(config.url)) {
      config.headers['token'] = sessionStorage.getItem('token')
      config.headers = editorHeaders
    }
    return config
  },
  (error) => {
    console.log(`request error：${error}`)
    return Promise.reject(error)
  },
)

axiosService.interceptors.response.use(
  (response) => {
    const res = response.data
    const { description, result, code, message } = res
    // 如果是bolb返回值类型的 直接把整个响应数据返回
    if (
      ['blob'].includes(response.request.responseType) &&
      (typeof code === 'undefined' || typeof result === 'undefined')
    ) {
      return response
    }
    if (
      [200, 203, 204, '0000000'].includes(code) ||
      [200, 203, 204, 0].includes(result)
    ) {
      return res
    } else if (code === 403 || result === 403) {
      notification.error({
        message: '权限不足',
        description: '你想干什么坏事哈',
      })
      return res
    } else if (code === 401 || result === 401) {
      Modal.confirm({
        title: '登陆失效',
        content: '登陆已经失效，您可以取消停留在此页面，或重新登录!',
        onOk: () => {
          sessionStorage.setItem('token', '')
          router.replace({
            name: 'login',
            query: {
              ...router.currentRoute.value.query,
              routerName: router.currentRoute.value.name as string,
            },
          })
        },
      })
      return Promise.reject(new Error(message || 'Error'))
    } else {
      notification.error({
        message: '请求失败',
        description: message || code || description,
      })
    }

    return res
  },
  (error) => {
    const { response } = error
    if (error.message.includes('timeout')) {
      return Promise.reject(error)
    }
    if (response.status === 401) {
      router.replace({
        name: 'login',
      })
      return Promise.reject(error)
    }
    notification.error({
      message: '请求失败',
      description: error.message,
    })
    return Promise.reject(error)
  },
)

export default axiosService
