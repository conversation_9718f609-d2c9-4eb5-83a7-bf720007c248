import axios, { AxiosRequestConfig } from 'axios'
import qs from 'qs'

export const axiosInst = axios.create({
  baseURL: '',
  // headers: {
  //   'authorization-system': 50,
  //   value: 'ElectricVehicle',
  // },
  timeout: 10_000,
  paramsSerializer: (params) => {
    /** 数组序列化逗号风格 [1,2,3] => 1,2,3 */
    return qs.stringify(params, { arrayFormat: 'comma' })
  },
})

/**
 * 请求拦截，请求头部添加token信息
 *
 */
axiosInst.interceptors.request.use(
  (config) => {
    const { headers } = config
    const token = sessionStorage.getItem('token')
    // const tokenInfo = tokenInfoStr ? JSON.parse(tokenInfoStr) : null
    const reConfig = { ...config }
    if (token) {
      reConfig.headers = {
        ...headers,
        token,
      }
    }
    return reConfig
  },
  (error) => {
    return Promise.reject(error)
  },
)

/**
 * @description 进一步处理结果的请求封装
 * @param params axios的参数
 * @returns 返回数据体 data
 */
const axiosRequest = async (config: AxiosRequestConfig<unknown>) => {
  return axiosInst(config).then((res) => {
    const { data } = res
    return data.code === 0 ? data : Promise.reject(data)
  })
}

export default axiosRequest
