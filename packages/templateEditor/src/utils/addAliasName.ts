import { Login } from '@/Pages/login/index.vue'
import { uuidv4, recordTarget } from '@leyaoyao/libs'
import { ConsoleSqlOutlined } from '@ant-design/icons-vue'
import { getEntityId } from '@leyaoyao/libs/utils/compHash'
import { treeFind } from '@leyaoyao/libs/utils/tree-find'

export const addAliasNameAnduuid = (comp) => {
  comp.compId = 'c' + uuidv4()
  comp.prop = comp.prop ?? {}
  comp.prop.aliasName = comp.compNameCn + comp.compId.slice(0, 5)
  return comp
}
export const addEntityId = (tree) => {
  treeFind(tree, (comp) => {
    if (comp && !comp.entityId) {
      comp.entityId = getEntityId(comp)
    }
  })
}
const resetAliasNameWalk = (comp) => {
  comp.prop = comp.prop ?? {}
  comp.compId = comp.compId?comp.compId:'c' + uuidv4()
  comp.prop.aliasName = comp.compNameCn + comp.compId.slice(0, 5)
}
// 替换别名
export const resetAliasName = (pageConfig) => {
  const compIdMap = {}
  recordTarget(pageConfig, compIdMap, resetAliasNameWalk)
  return pageConfig[0]
}
