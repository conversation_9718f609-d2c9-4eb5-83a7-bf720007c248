/**
 * 处理组件内子组件 抛到子组件内
 * @param item 组件
 * **/
const mapList = {
  tabs: true,
  searchFormList: true,
  // operationButtons: true,
  extraList: true,
  childList: true,
  cardList: true,
  titleComp: true,
  dropdownList: true,
  buttonList: true,
  children: true,
  pulldownChildren: true,
}
export const setOtherChildToChildrens = (item, filterNodes: []) => {
  filterNodes = filterNodes ?? []
  const childrens =
    item.childrens && item.childrens.length > 0 ? item.childrens : []
  for (const k of Object.keys(mapList)) {
    if (k == 'tabs') {
      if (item?.prop?.tabs) {
        for (const ele of item.prop.tabs) {
          childrens.push(...ele.childrens)
        }
      }
    } else {
      if (item?.prop && item?.prop[k] && !filterNodes.includes(k)) {
        childrens.push(...item.prop[k])
      }
    }
  }
  // // tab容器
  // if (item?.prop.tabs) {
  //   for (const ele of item.prop.tabs) {
  //     childrens.push(...ele.childrens)
  //   }
  // }
  // // 搜索组件
  // if (item.prop?.searchFormList) {
  //   childrens.push(...item.prop.searchFormList)
  // }
  // // 操作按钮组
  // if (
  //   item.prop?.operationButtons &&
  //   !filterNodes.includes('operationButtons')
  // ) {
  //   childrens.push(...item.prop.operationButtons)
  // }
  // // 卡片
  // if (item.prop?.extraList && !filterNodes.includes('extraList')) {
  //   childrens.push(...item.prop.extraList)
  // }
  // if (item.prop?.titleComp && !filterNodes.includes('titleComp')) {
  //   childrens.push(...item.prop.titleComp)
  // }
  // if (item.prop?.dropdownList && !filterNodes.includes('dropdownList')) {
  //   childrens.push(...item.prop.dropdownList)
  // }
  // // 按钮组
  // if (item.prop?.buttonList && !filterNodes.includes('buttonList')) {
  //   childrens.push(...item.prop.buttonList)
  // }
  // // 栅格布局
  // if (item.prop?.children) {
  //   childrens.push(...item.prop.children)
  // }
  // // 目录树
  // if (item.prop?.operationIcons && !filterNodes.includes('operationIcons')) {
  //   childrens.push(...item.prop.operationIcons)
  // }
  return childrens.length > 0 ? childrens : null
}
