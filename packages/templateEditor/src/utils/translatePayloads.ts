import _ from 'lodash'
declare interface IPayload {
  key?: string
  value?: string
  source?: string
  sourceKey?: string
}
export function translatePayloads(textArr: string[]) {
  return textArr.map((text) => {
    return getPayloadsItem(text)
  })
}
export function getPayloadsItem(text: string) {
  if (!text) {
    return undefined
  }
  const payload: any = {}
  let sourceText = text.replaceAll(' ', '').replaceAll('\n', '')
  if (text.includes('=')) {
    payload.key = text.split('=')[0]
    sourceText = sourceText.split('=')[1]
  }
  if (!text.includes('$')) {
    try {
      payload.value = eval('(' + sourceText + ')')
    } catch {
      payload.value = sourceText
    }
  } else {
    sourceText = sourceText.replace('{', '').replace('$', '').replace('}', '')
    if (sourceText.includes('.')) {
      payload.source = sourceText.split('.')[0]
      payload.sourceKey = sourceText.split(payload.source + '.')[1]
      payload.decompose = true
    } else {
      payload.source = sourceText
    }
  }
  return payload
}
export function payloadsToText(payloads: IPayload[]) {
  return payloads.map((e) => {
    return payloadToText(e)
  })
}
export function payloadToText(payload: IPayload) {
  if (!payload) {
    return ''
  }
  let text = ''
  if (payload.key) {
    text = payload.key + '='
  }
  if (payload.value != undefined) {
    text =
      text +
      (_.isArray(payload.value) || _.isObject(payload.value)
        ? JSON.stringify(payload.value)
        : payload.value)
  } else {
    text = text + '${' + payload.source
    if (payload.sourceKey) {
      text = text + '.' + payload.sourceKey
    }
    text = text + '}'
  }
  return text
}
