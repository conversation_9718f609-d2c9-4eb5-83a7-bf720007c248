import { selectOptions } from './../Pages/Edit/Right/AttrConfig/config/SelectOptions'
import _ from 'lodash'
// 需要展示出来的组件
const setShowComp = new Set([
  'lyy-form',
  'lyy-table',
  'lyy-select',
  'lyy-pagination',
  'lyy-cascader',
  'lyy-checkbox',
  'lyy-selector-pro',
  'lyy-transfer',
  'lyy-tree',
  'lyy-alert',
  'lyy-avatar',
  'lyy-directory-tree',
  'lyy-excel-table',
  'lyy-html',
  'lyy-image',
  'lyy-image-previewer',
  'lyy-popconfirm',
  'lyy-modal',
  'lyy-qrcode',
  'lyy-advanced-filter',
  'lyy-tabs',
  'lyy-tree-select',
  'lyy-select-merchant',
  'lyy-erp-sku',
  'lyy-pull-down-selector-input',
])
// 组件属性映射
export const keyMap = {
  prop: '组件属性',
  modelValue: '组件值',
  options: '选项列表',
  form: '表单默认值',
  treeData: '树选项列表',
  fieldOptions: '字段列表',
  selectOptions: '枚举数据',
  selectedRows: '多选行数组',
  datasource: '数据源',
  currentRow: '当前选中行',
  selectedRowKeys: '多选行的key值数组',
  selectKey: '选中key值',
  select: '当前选中元素',
  html: '超文本',
  title: '标题',
  compId: '组件Id',
  total: '总数',
  optionList: '级联选项列表',
  description: '提示内容',
  defaultSrc: '默认图片路径',
  src: '图片路径',
  defaultSrcs: '图片路径数组',
  url: '二维码路径',
  operatorOptions: '操作符列表',
  query: 'query',
  params: 'params',
  selected: '选中的数据',
  selectOption: '当前选中项',
}
const filterKeys = new Set(['prop', 'modelValue'])
export const getCompPropName = (item) => {
  try {
    const { prop } = item
    const { text, label, title } = prop
    const treeTitle = text || label || title
    return treeTitle ? `(${treeTitle})` : ''
  } catch {
    return ''
  }
}
// 过滤不需要展示的数据
export const filterPageData = (data, showComps) => {
  const compFilter = showComps ? new Set(showComps) : setShowComp
  const newData = _.clone(data)
  const newObj = {}
  for (const e of Object.keys(newData)) {
    // 如果是组件
    if (newData[e] && newData[e].compId && newData[e].compName) {
      // 根据过滤数据 筛选需要展示的组件
      if (compFilter.has(newData[e].compName)) {
        // 如果有别名
        if (newData[e].prop.aliasName) {
          // 如果别名重复 生成临时别名 规则是组件名+组件id后4位
          if (newObj[newData[e].prop.aliasName]) {
            if (
              newData[e].compId != newObj[newData[e].prop.aliasName]['组件Id']
            ) {
              const aliasName =
                newData[e].compNameCn + newData[e].compId.slice(0, 5)
              newObj[aliasName] = filterProps(newData[e])
            }
          } else {
            newObj[newData[e].prop.aliasName + getCompPropName(newData[e])] =
              filterProps(newData[e])
          }
        } else {
          // 如果没有别名 给源数据当前组件加上临时别名 规则是组件名+组件id后4位
          const aliasName =
            newData[e].compNameCn + newData[e].compId.slice(0, 5)
          newObj[aliasName] = filterProps(newData[e])
        }
      }
    } else {
      if (e == 'route') {
        newObj['路由'] = filterProps(newData[e])
      } else {
        const filterKeys = ['表单集合', 'undefined']
        if (!filterKeys.includes(e)) {
          newObj[e] = newData[e]
        }
      }
    }
  }
  return newObj
}
// 过滤不需要展示的属性
export const filterProps = (obj) => {
  const item = _.clone(obj)
  const newItem = {}
  item &&
    Object.keys(item).forEach((k) => {
      const newKey = keyMap[k]
      // 处理属性翻译
      if (newKey) {
        //如果是有需要翻译的子属性
        newItem[newKey] = filterKeys.has(k) ? filterProps(item[k]) : item[k]
      }
    })
  return newItem
}
