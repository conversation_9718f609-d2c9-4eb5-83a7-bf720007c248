<template>
  <a-layout style="min-height: 100vh">
    <a-layout-sider style="background: #fff">
      <div class="siderTitle">后台管理</div>
      <a-menu v-model:selectedKeys="selectedKeys" mode="inline">
        <a-menu-item key="1" v-if="isRepositoryOwner">
          <span>成员管理</span>
        </a-menu-item>
        <a-menu-item key="2" v-if="isRepositoryOwner">
          <span>角色管理</span>
        </a-menu-item>
        <a-menu-item key="3">
          <span>合并记录</span>
        </a-menu-item>
        <a-menu-item key="4">
          <span>推送记录</span>
        </a-menu-item>
        <a-menu-item key="5">
          <span>分支管理</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header class="contentHeader">
        <a-button @click="goBackAppDetail">返回</a-button>
      </a-layout-header>
      <a-layout-content>
        <template v-for="item in menuList" :key="item.key">
          <component
            v-if="selectedKeys[0] == item.key"
            :is="item.component"
            @branchDelete="branchDelete"
          ></component>
        </template>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import { LeftOutlined } from '@ant-design/icons-vue'
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Member from './components/member/index.vue'
import Charactar from './components/charactar/index.vue'
import Merge from './components/merge/index.vue'
import Push from './components/push/index.vue'
import Branch from './components/branch/index.vue'

const router = useRouter()
const route = useRoute()
const isLocalBranchDelete = ref(false)
const isRepositoryOwner = ref(
  JSON.parse(localStorage.getItem('userInfo')).isRepositoryOwner,
)
const selectedKeys = ref([''])
const menuList = ref([
  // 菜单列表
  {
    title: '成员管理',
    key: '1',
    component: Member,
  },
  {
    title: '角色管理',
    key: '2',
    component: Charactar,
  },
  {
    title: '合并记录',
    key: '3',
    component: Merge,
  },
  {
    title: '推送记录',
    key: '4',
    component: Push,
  },
  {
    title: '分支管理',
    key: '5',
    component: Branch,
  },
])

onMounted(() => {
  const key = route.query.key
  selectedKeys.value[0] = key
})
// 返回系统预览列表
const goBackAppDetail = () => {
  if (isLocalBranchDelete.value) {
    router.push({
      path: '/1',
    })
  } else {
    router.go(-1)
  }
}
const branchDelete = (data) => {
  isLocalBranchDelete.value = data
}
</script>

<style lang="less" scoped>
.siderTitle {
  height: 64px;
  line-height: 64px;
  font-size: 20px;
  text-align: center;
}
.contentHeader {
  background: #fff !important;
  padding: 0 20px;
}
</style>
