<template>
  <div class="conflict-container">
    <div class="desc">
      <div class="line">
        <span v-if="conflicts.length > 0">
          共有
          <i class="red" style="color: red">{{ conflicts.length }}</i>
          出差异，差异开始行号为:
          <i
            class="conflict-line"
            v-for="item in conflicts"
            :key="item"
            @click="scrollToConflict(item)"
          >
            {{ item }}
          </i>
        </span>
        <span v-else>无差异，左右内容一致</span>
      </div>
      <div class="button-box">
        <a-button type="default" style="margin-right: 10px">
          接受左边内容
        </a-button>
        <a-button type="default">接受右边内容</a-button>
      </div>
    </div>
    <div class="branch-box">
      <div class="target-branch">{{ targetBranchShowName }}</div>
      <div class="source-branch">{{ sourceBranchShowName }}</div>
    </div>
    <!-- 显示文件差异的容器 -->
    <div id="diff-container" ref="CodeMirrorRef" class="diff-container"></div>
  </div>
</template>

<script lang="ts" setup>
import CodeMirror from 'codemirror'
import 'codemirror/lib/codemirror.css'
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/idea.css'
import 'codemirror/theme/darcula.css'
import 'codemirror/mode/javascript/javascript.js'
// import 'codemirror/mode/css/css.js'
import 'codemirror/addon/selection/active-line.js'

import 'codemirror/addon/merge/merge.js'
import 'codemirror/addon/merge/merge.css'
import DiffMatchPatch from 'diff-match-patch'
import { computed, defineExpose, nextTick, onMounted, reactive, ref } from 'vue'
const conflicts = ref([])
window.diff_match_patch = DiffMatchPatch
const CodeMirrorRef = ref(null)
window.DIFF_DELETE = -1
window.DIFF_INSERT = 1
window.DIFF_EQUAL = 0
const props = defineProps<{
  sourceBranchShowName?: string
  targetBranchShowName?: string
  sourceBranchText?: string
  targetBranchText?: string
}>()
const sourceBranchShowName = computed(() => props.sourceBranchShowName)
const targetBranchShowName = computed(() => props.targetBranchShowName)
const sourceBranchText = computed(() => props.sourceBranchText)
const targetBranchText = computed(() => props.targetBranchText)
let editor = null

const scrollToConflict = (line) => {
  editor?.scrollIntoView({ line: line, ch: 0 }, 140)
}
const conflictHandler = () => {
  const diffs = CodeMirror.MergeView(CodeMirrorRef.value, {
    value:
      (sourceBranchText.value &&
        JSON.stringify(sourceBranchText.value, null, 2)) ||
      '',
    origLeft: null,
    LineNumbers: true,
    orig:
      (targetBranchText.value &&
        JSON.stringify(targetBranchText.value, null, 2)) ||
      '',
    // orig: '',
    mode: 'json',
    height: 600,
    reverseButtons: true,
    lineNumbers: true, // 显示行号
    lineWrapping: true, // 自动换行
    styleActiveLine: true, // 选中行高亮
    theme: 'idea', // 主题配置
    matchBrackets: true, // 匹配括号
    // gutters: ['CodeMirror-lint-markers'],
    lint: true,
  })
  editor = diffs.edit
  nextTick(() => {
    editor?.scrollIntoView(
      { line: diffs.right.chunks[0].origFrom + 1, ch: 0 },
      40,
    )
  })
  const diffCount = diffs.right.chunks.length
  for (let i = 0; i < diffCount; i++) {
    conflicts.value.push(diffs.right.chunks[i].origFrom + 1)
  }
}
const CodeMirrorHeight = ref('400px')
onMounted(() => {
  CodeMirrorHeight.value =
    CodeMirrorRef.value.getBoundingClientRect().height - 20 + 'px'
  conflictHandler()
})
defineExpose({
  conflictHandler: () => {
    // conflictHandler()
  },
})
</script>
<style scoped lang="less">
.conflict-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .diff-container {
    flex: 1;
    position: relative;
  }
  ::v-deep(.CodeMirror),
  ::v-deep(.CodeMirror-merge) {
    height: v-bind(CodeMirrorHeight);
  }
}
.branch-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 4px;
  .target-branch,
  .source-branch {
    width: 50%;
    color: #0f9595;
  }
}
.desc {
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  .line {
    flex: 1;
    .conflict-line {
      margin-right: 6px;
      color: #0f9595;
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .button-box {
    flex-shrink: 0;
  }
}
</style>
