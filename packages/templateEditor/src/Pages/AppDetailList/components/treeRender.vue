<script lang="ts" setup>
import {
  defineComponent,
  defineEmits,
  reactive,
  ref,
  onMounted,
  computed,
} from 'vue'
import axios from 'axios'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { useStore } from '@leyaoyao/libs'
import { PAGE_CONFIG } from '@leyaoyao/libs/constants/store-key'
import { IElement } from '@leyaoyao/render-template/src/global'
import { notification, type MenuProps, Modal, message } from 'ant-design-vue'
import debounce from 'lodash/debounce'
import {
  getUserInfo,
  getCurrentBranch,
  getCurrentLocalBranch,
  getCurrentBranchStatus,
  checkoutBranch,
  pullBranch,
  getNotSubmit,
  getAssignUserList,
  commitBranch,
  pushBranch,
  mergeBranch,
  getHistoryPageConfig,
  pushRecordsList,
  getClashContent,
  dealDropOfPull,
  cancelCommit,
} from '@/request/api/editorRequest'
import { getCurrentStashBranches } from '@/request/api/management'
import checkOutPopup from './checkoutPopup.vue'
import commitPopup from './commitPopup.vue'
import pushPopup from './pushPopup.vue'
import mergePopup from './mergePopup.vue'
import failureRecordPopup from './failureRecordPopup.vue'
import pendingPushPopup from './pendingPushPopup.vue'
import codeConflict from './codeConflict.vue'

defineComponent({
  name: 'tree-render',
})
const $route = useRoute()
const $router = useRouter()
const pageCoing = reactive([])
const systemId = ref(null)
const setListPage = (config: IElement[]) => {
  pageCoing.length = 0
  pageCoing.push(...config)
  useStore().setValue(PAGE_CONFIG, elements)
  sessionStorage.removeItem('sessionResponseDataKey')
}
defineExpose({
  setListPage,
})
const elements = reactive([
  {
    compName: 'lyy-form',
    compId: 'ctzlmapxud',
    compNameCn: '表单',
    style: {
      height: '100%',
    },
    prop: {
      aliasName: '表单ctzlm',
      isSignChange: null,
      mainLayout: false,
      labelCol: {
        style: {
          width: '110px',
        },
      },
      class: '',
    },
    modelValue: {},
    actions: [
      {
        event: 'mounted',
        action: 'customerJS',
        option: {
          customJs:
            "(useAction,data,context,globalData) => {\n data['ctzlmapxud'].modelValue.isRemote = JSON.parse(window.localStorage.getItem('branch' + window.localStorage.getItem('authSystemId')))?.isRemote\n\n    \n    \n    return useAction\n  }",
        },
        delay: '',
        thenActions: [],
      },
    ],
    childrens: [
      {
        compId: 'row-312333-222-33',
        compName: 'lyy-row',
        style: {
          height: '100%',
        },
        childrens: [
          {
            compId: 'col-11-222-33',
            compName: 'lyy-col',
            prop: {
              span: 4,
              gap: 10,
            },
            childrens: [
              {
                compId: 'tree-1',
                compName: 'lyy-directory-tree',
                fetch: {},
                modelValue: {
                  selectKey: '',
                  expandedKeys: [],
                  select: {},
                  operation: {},
                  drop: {},
                },
                prop: {
                  title: '页面菜单列表',
                  onextraevent: 'treeclick',
                  fieldNames: {
                    key: 'path',
                    title: 'name',
                    children: 'children',
                    parentId: 'parentId',
                  },
                  operationButtons: [
                    {
                      compName: 'lyy-button',
                      prop: {
                        text: '添加目录',
                        size: 'small',
                        type: 'primary',
                        ghost: true,
                      },
                      actions: [
                        // {
                        //   event: 'click',
                        //   action: 'resetValue',
                        //   option: {
                        //     targetId: 'createForm',
                        //   },
                        //   thenActions: [
                        //     {
                        //       action: 'openModal',
                        //       option: {
                        //         targetId: 'createModal',
                        //       },
                        //     },
                        //   ],
                        // },
                        {
                          event: 'click',
                          action: 'openModal',
                          option: {
                            targetId: 'createModal',
                          },
                        },
                      ],
                      style: {
                        marginLeft: '7px',
                      },
                    },
                  ],
                  // 节点前面的图标
                  prefixIcons: [
                    {
                      iconName: 'folder-open-outlined',
                      iconProps: {
                        style: {
                          color: '#ffa05c',
                        },
                      },
                      customOption: {
                        key: 'type',
                        value: 'group',
                      },
                    },
                    {
                      iconName: 'file-text-outlined',
                      iconProps: {
                        style: {
                          color: 'rgb(84 190 15)',
                        },
                      },
                      customOption: {
                        key: 'type',
                        value: 'form',
                      },
                    },
                  ],
                  // 节点后面的操作图标
                  operationIcons: [
                    // 文件夹新增菜单
                    {
                      compName: 'lyy-icon',
                      prop: {
                        iconName: 'PlusOutlined',
                        customOption: {
                          key: 'type',
                          value: 'group',
                          menuList: [
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '添加目录',
                                type: 'link',
                                icon: {
                                  iconName: 'folder-add-outlined',
                                  iconProps: {
                                    style: {
                                      color: '#5a5a5a',
                                    },
                                  },
                                },
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'openModal',
                                  option: {
                                    targetId: 'createChildModal',
                                  },
                                  thenActions: [
                                    {
                                      action: 'setNewValue',
                                      option: {
                                        to: {
                                          type: 'dynamic',
                                          dynamic: {
                                            nodePath:
                                              'createChildForm.modelValue.parentId',
                                          },
                                        },
                                        from: {
                                          dynamic: {
                                            nodePath:
                                              'tree-1.modelValue.operation.id',
                                          },
                                        },
                                      },
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '删除',
                                type: 'link',
                                danger: true,
                                icon: {
                                  iconName: 'delete-outlined',
                                  iconProps: {
                                    style: {
                                      color: '#5a5a5a',
                                    },
                                  },
                                },
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'openConfirm',
                                  option: {
                                    type: 'confirm',
                                    title: '删除提示',
                                    content: `确定要删除此文件夹吗`,
                                    okText: '确认',
                                    cancelText: '取消',
                                  },
                                  thenActions: [
                                    {
                                      action: 'request',
                                      option: {
                                        url: '/gw/editor/menus/${id}',
                                        baseURL: '',
                                        method: 'DELETE',
                                        customOption: {
                                          jsonPath: 'body',
                                          codeValue: '0000000',
                                          codeKey: 'code',
                                          successfulFeedback: {
                                            type: 'notification',
                                            status: 'success',
                                            message: '删除成功',
                                          },
                                        },
                                        headerPayloads: [
                                          {
                                            key: 'ram-token',
                                            value: 'sessionStorage.ramToken',
                                          },
                                          {
                                            key: 'ram-system',
                                            value: window.editorSystem,
                                          },
                                        ],
                                        payloads: {
                                          type: 'higher',
                                          higher: [
                                            {
                                              key: 'id',
                                              value:
                                                'tree-1.modelValue.operation.id',
                                            },
                                          ],
                                        },
                                      },
                                      thenActions: [
                                        {
                                          action: 'broadcast',
                                          option: {
                                            targetId: 'tree-1',
                                            event: 'mounted',
                                          },
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '菜单更新',
                                type: 'link',
                                icon: {
                                  iconName: 'form-outlined',
                                  iconProps: {
                                    style: {
                                      color: '#5a5a5a',
                                    },
                                  },
                                },
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'openModal',
                                  option: {
                                    targetId: 'updateGroupModal',
                                  },
                                  thenActions: [
                                    {
                                      action: 'broadcast',
                                      option: {
                                        targetId: 'updateGroupForm',
                                        event: 'mount',
                                      },
                                      thenActions: [],
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                      },
                    },
                    // 文件夹操作菜单
                    {
                      compName: 'lyy-icon',
                      prop: {
                        iconName: 'EllipsisOutlined',
                        customOption: {
                          key: 'type',
                          value: 'form',
                          menuList: [
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '编辑',
                                type: 'link',
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'linkto',
                                  option: {
                                    url: '/edit/${id}',
                                    mode: 'params',
                                    payloads: [
                                      {
                                        key: 'id',
                                        value:
                                          'tree-1.modelValue.operation.path',
                                      },
                                      {
                                        key: 'authSystemId',
                                        value: 'sessionStorage.authSystemId',
                                      },
                                    ],
                                  },
                                },
                              ],
                            },
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '菜单更新',
                                type: 'link',
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'openModal',
                                  option: {
                                    targetId: 'updateGroupModal',
                                  },
                                  thenActions: [
                                    {
                                      action: 'broadcast',
                                      option: {
                                        targetId: 'updateGroupForm',
                                        event: 'mount',
                                      },
                                      thenActions: [],
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '展示菜单',
                                type: 'link',
                                customOption: {
                                  key: 'hide',
                                  value: 'Y',
                                },
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'request',
                                  option: {
                                    baseURL: '',
                                    url: '/gw/editor/menus/show',
                                    method: 'post',
                                    customOption: {
                                      jsonPath: 'body',
                                      codeValue: '0000000',
                                      codeKey: 'code',
                                      successfulFeedback: {
                                        type: 'notification',
                                        status: 'success',
                                        message: '操作成功',
                                      },
                                    },
                                    headerPayloads: [
                                      {
                                        key: 'ram-token',
                                        value: 'sessionStorage.ramToken',
                                      },
                                      {
                                        key: 'ram-system',
                                        value: window.editorSystem,
                                      },
                                    ],
                                    payloads: {
                                      type: 'higher',
                                      higher: [
                                        {
                                          key: 'id',
                                          value:
                                            'tree-1.modelValue.operation.id',
                                        },
                                      ],
                                    },
                                  },
                                  thenActions: [
                                    {
                                      action: 'broadcast',
                                      option: {
                                        targetId: 'tree-1',
                                        event: 'mounted',
                                      },
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '隐藏菜单',
                                type: 'link',
                                customOption: {
                                  key: 'hide',
                                  value: 'N',
                                },
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'request',
                                  option: {
                                    baseURL: '',
                                    url: '/gw/editor/menus/hide',
                                    method: 'post',
                                    customOption: {
                                      jsonPath: 'body',
                                      codeValue: '0000000',
                                      codeKey: 'code',
                                      successfulFeedback: {
                                        type: 'notification',
                                        status: 'success',
                                        message: '操作成功',
                                      },
                                    },
                                    headerPayloads: [
                                      {
                                        key: 'ram-token',
                                        value: 'sessionStorage.ramToken',
                                      },
                                      {
                                        key: 'ram-system',
                                        value: window.editorSystem,
                                      },
                                    ],
                                    payloads: {
                                      type: 'higher',
                                      higher: [
                                        {
                                          key: 'id',
                                          value:
                                            'tree-1.modelValue.operation.id',
                                        },
                                      ],
                                    },
                                  },
                                  thenActions: [
                                    {
                                      action: 'broadcast',
                                      option: {
                                        targetId: 'tree-1',
                                        event: 'mounted',
                                      },
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              compName: 'lyy-button',
                              prop: {
                                text: '删除',
                                type: 'link',
                                danger: true,
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'openConfirm',
                                  option: {
                                    type: 'confirm',
                                    title: '删除提示',
                                    content: `确定要删除此模块吗`,
                                    okText: '确认',
                                    cancelText: '取消',
                                  },
                                  thenActions: [
                                    {
                                      action: 'request',
                                      option: {
                                        url: '/gw/editor/menus/${id}',
                                        method: 'DELETE',
                                        baseURL: '',
                                        customOption: {
                                          jsonPath: 'body',
                                          codeValue: '0000000',
                                          codeKey: 'code',
                                          successfulFeedback: {
                                            type: 'notification',
                                            status: 'success',
                                            message: '删除成功',
                                          },
                                        },
                                        headerPayloads: [
                                          {
                                            key: 'ram-token',
                                            value: 'sessionStorage.ramToken',
                                          },
                                          {
                                            key: 'ram-system',
                                            value: window.editorSystem,
                                          },
                                        ],
                                        payloads: {
                                          type: 'higher',
                                          higher: [
                                            {
                                              key: 'id',
                                              value:
                                                'tree-1.modelValue.operation.id',
                                            },
                                          ],
                                        },
                                      },
                                      thenActions: [
                                        {
                                          action: 'broadcast',
                                          option: {
                                            targetId: 'tree-1',
                                            event: 'mounted',
                                          },
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                      },
                    },
                  ],
                  placeholder: '搜索',
                  draggable: true,
                  //拖拽规则未定
                  dragRules: [
                    {
                      key: 'type',
                      value: 'form',
                      rejectedChildren: [
                        {
                          key: 'type',
                          value: 'group',
                        },
                        {
                          key: 'type',
                          value: 'form',
                        },
                      ],
                    },
                  ],
                },
                actions: [
                  {
                    event: 'mounted',
                    action: 'request',
                    option: {
                      url: '/gw/editor/menus/tree',
                      method: 'get',
                      baseURL: '',
                      headerPayloads: [
                        {
                          key: 'ram-token',
                          value: 'sessionStorage.ramToken',
                        },
                        {
                          key: 'ram-system',
                          value: window.editorSystem,
                        },
                      ],
                      customOption: {
                        jsonPath: 'body',
                        codeValue: '0000000',
                        codeKey: 'code',
                      },
                      // payloads: [
                      //   {
                      //     source: 'routeQuery',
                      //     sourceKey: 'authSystemId',
                      //     key: 'authSystemId',
                      //   },
                      // ],
                      payloads: {
                        type: 'higher',
                        higher: [
                          {
                            key: 'authSystemId',
                            value: 'sessionStorage.authSystemId',
                          },
                        ],
                      },
                      targets: [
                        {
                          targetId: 'tree-1',
                          targetPath: 'prop.treeData',
                        },
                      ],
                      interceptors: {
                        responseInterceptor(res) {
                          function setNodePath(treeList) {
                            for (const item of treeList) {
                              if (item.type === 'group') {
                                item.path = item.id
                              }
                              if (item?.children?.length) {
                                setNodePath(item.children)
                              }
                            }
                          }
                          setNodePath(res.data.body)
                          return res
                        },
                      },
                    },
                    thenActions: [
                      // 根据 pageId 回显树节点
                      {
                        action: 'customerJS',
                        option: {
                          customJs: `(useAction,data,context) => {\n    /* 自定义JS使用说明：\n* 1.动作执行函数useAction，可以执行所有类型的动作\n* 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n* 3.通过数据树对象data可以获取当前数据树的所有数据\n* 4.事件配置具体看 事件文档\n*/\nconst parts = document.location.href.split('/')\nconst pageId = parts[parts.length - 1].includes('?') ? parts[parts.length - 1].split('?')[0] : parts[parts.length - 1]\nconst treeData = data['tree-1']?.prop?.treeData\nconst selectKey = getIdByPageId(treeData, pageId)\nconst expandedKeys = getParentIds(treeData, selectKey)\nfunction getIdByPageId(treeList, pageId) {\n  for (const node of treeList) {\n    if (node.path === pageId) {\n        return node.path\n    }\n    if (node?.children?.length) {\n        const id = getIdByPageId(node.children, pageId)\nif (id) {\n  return id\n}\n    }\n  }\n}\n\nfunction getParentIds(treeList, id) {\n  if (treeList.length === 0) return\n  for (const node of treeList) {\n    if (node.path === id) {\n      return []\n    } else {\n      if (node.children?.length) {\n        let ids = getParentIds(node.children, id)\n        if (ids !== undefined) {\n          return ids.concat(node.path).reverse()\n        }\n      }\n    }\n  }\n}\n\nconst actionConfig = {\n    \"event\": \"\",\n    \"action\": \"setNewValue\",\n    \"option\": {\n        \"to\": {\n            \"type\": \"static\",\n            \"dynamic\": {\n            \"nodePath\": \"tree-1.modelValue.selectKey\"\n            }\n        },\n        \"from\": {\n            \"static\": selectKey\n        }\n    },\n    \"thenActions\": [{\n    \"event\": \"\",\n    \"action\": \"setNewValue\",\n    \"option\": {\n        \"to\": {\n            \"type\": \"static\",\n            \"dynamic\": {\n            \"nodePath\": \"tree-1.modelValue.expandedKeys\"\n            }\n        },\n        \"from\": {\n            \"static\": expandedKeys\n        }\n    },\n    \"thenActions\": []\n}]\n}\nuseAction(actionConfig)\n  \n    return useAction\n  }`,
                        },
                        thenActions: [],
                      },
                    ],
                  },
                  {
                    event: 'treeclick',
                    action: 'depAction',
                    option: {
                      targetId: 'treeclick',
                      source: 'tree-1',
                      sourceKey: 'modelValue.select',
                    },
                    thenActions: [],
                  },
                  // 树节点拖拽落下事件
                  {
                    event: 'drop',
                    action: 'request',
                    option: {
                      baseURL: '',
                      url: '/gw/editor/menus/drag',
                      method: 'post',
                      customOption: {
                        jsonPath: 'body',
                        codeValue: '0000000',
                        codeKey: 'code',
                      },
                      headerPayloads: [
                        {
                          key: 'ram-token',
                          value: 'sessionStorage.ramToken',
                        },
                        {
                          key: 'ram-system',
                          value: window.editorSystem,
                        },
                      ],
                      payloads: {
                        type: 'higher',
                        higher: [
                          {
                            key: 'pid',
                            value: 'tree-1.modelValue.drop.parentData.id',
                          },
                          {
                            key: 'nodeIds',
                            value:
                              'tree-1.modelValue.drop.parentData.children.id',
                          },
                        ],
                      },
                    },
                    thenActions: [
                      {
                        action: 'broadcast',
                        option: {
                          event: 'mounted',
                          targetId: 'tree-1',
                        },
                      },
                    ],
                  },
                  // 页面 params 参数变化时，根据 pageId 回显树节点
                  {
                    event: 'routeParamsChange',
                    action: 'customerJS',
                    option: {
                      customJs: `(useAction,data,context) => {\n    /* 自定义JS使用说明：\n* 1.动作执行函数useAction，可以执行所有类型的动作\n* 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n* 3.通过数据树对象data可以获取当前数据树的所有数据\n* 4.事件配置具体看 事件文档\n*/\n\nconst parts = document.location.href.split('/')\nconst pageId = parts[parts.length - 1].includes('?') ? parts[parts.length - 1].split('?')[0] : parts[parts.length - 1]\nconst treeData = data['tree-1']?.prop?.treeData\nif (!treeData) return\nconst selectKey = getIdByPageId(treeData, pageId)\nconst expandedKeys = getParentIds(treeData, selectKey)\nfunction getIdByPageId(treeList, pageId) {\n  for (const node of treeList) {\n    if (node.path === pageId) {\n        return node.path\n    }\n    if (node?.children?.length) {\n        const id = getIdByPageId(node.children, pageId)\nif (id) {\n  return id\n}\n    }\n  }\n}\n\nfunction getParentIds(treeList, id) {\n  if (treeList.length === 0) return\n  for (const node of treeList) {\n    if (node.path === id) {\n      return []\n    } else {\n      if (node.children?.length) {\n        let ids = getParentIds(node.children, id)\n        if (ids !== undefined) {\n          return ids.concat(node.path).reverse()\n        }\n      }\n    }\n  }\n}\n\nconst actionConfig = {\n    \"event\": \"\",\n    \"action\": \"setNewValue\",\n    \"option\": {\n        \"to\": {\n            \"type\": \"static\",\n            \"dynamic\": {\n            \"nodePath\": \"tree-1.modelValue.selectKey\"\n            }\n        },\n        \"from\": {\n            \"static\": selectKey\n        }\n    },\n    \"thenActions\": [{\n    \"event\": \"\",\n    \"action\": \"setNewValue\",\n    \"option\": {\n        \"to\": {\n            \"type\": \"static\",\n            \"dynamic\": {\n            \"nodePath\": \"tree-1.modelValue.expandedKeys\"\n            }\n        },\n        \"from\": {\n            \"static\": expandedKeys\n        }\n    },\n    \"thenActions\": []\n}]\n}\nuseAction(actionConfig)\n  \n    return useAction\n  }`,
                    },
                    thenActions: [],
                  },
                ],
              },
            ],
          },
          {
            compId: 'col-312333-222-33',
            compName: 'lyy-col',
            style: {
              height: '100%',
            },
            prop: {
              span: 20,
            },
            childrens: [
              {
                compId: 'card11111',
                compName: 'lyy-card',
                style: {
                  backgroundColor: '#f0f0f0',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  border: '1px solid #e8e8e8'
                },
                prop: {
                  title: '页面预览',
                  bodyStyle: {
                    padding: '0 0 0px 0',
                    flex: '1',
                    overflow: 'auto',
                    height: 'auto !import',
                    // marginBottom: '36px',
                  },
                  mainLayout: true,
                  headStyle: {
                    background: '#fff',
                    flexShrink: '0',
                  },
                  bordered: true,
                  extraList: [
                    {
                      compName: 'lyy-button',
                      style: {
                        marginRight: '8px',
                      },
                      prop: {
                        text: '编辑',
                        type: 'primary',
                        forbid: {
                          exp: 'route.params.pageId == 1',
                        },
                      },
                      actions: [
                        {
                          event: 'click',
                          action: 'linkto',
                          option: {
                            url: '/edit${id}',
                            payloads: [
                              {
                                key: 'id',
                                // value: 'route.params.pageId',
                                value: 'route.fullPath',
                              },
                            ],
                          },
                        },
                        // {
                        //   event: 'click',
                        //   action: 'linkto',
                        //   option: {
                        //     url: `/edit${location.hash.replace('#', '')}`,
                        //     payloads: [],
                        //   },
                        // },
                      ],
                    },
                    {
                      compName: 'lyy-button',
                      prop: {
                        text: '发布',
                        forbid: {
                          exp: 'route.params.pageId == 1',
                        },
                      },
                      style: {
                        marginRight: '8px',
                      },
                      actions: [
                        // 打开发布弹窗选择
                        {
                          event: 'click',
                          action: 'openModal',
                          option: {
                            targetId: 'publishModal',
                          },
                        },
                        // 发布请求
                        // {
                        //   event: 'click',
                        //   action: 'request',
                        //   condition: {
                        //     exp: 'route.params.pageId !== "1"',
                        //   },
                        //   option: {
                        //     baseURL: '',
                        //     method: 'post',
                        //     url: '/gw/editor/page/configures/publishConfigureInfo',
                        //     payloads: {
                        //       type: 'higher',
                        //       higher: [
                        //         {
                        //           key: 'configureInfoList',
                        //           value: 'route.params.pageId',
                        //         },
                        //       ],
                        //     },
                        //     interceptors: {
                        //       requestInterceptor: (config) => {
                        //         config.data.configureInfoList = [
                        //           config.data.configureInfoList,
                        //         ]
                        //         return config
                        //       },
                        //     },
                        //   },
                        // },
                      ],
                    },
                    {
                      compName: 'lyy-button',
                      prop: {
                        text: '返回',
                      },
                      actions: [
                        {
                          event: 'click',
                          action: 'linkto',
                          option: {
                            url: `/appList`,
                            mode: 'params',
                            payloads: [],
                          },
                        },
                      ],
                    },
                  ],
                },
                childrens: pageCoing,
              },
            ],
          },
        ],
        prop: {
          gutter: 12,
        },
      },
    ],
  },
  // 添加根级分组 弹窗
  {
    compId: 'createModal',
    compName: 'lyy-modal',
    modelValue: false,
    prop: {
      title: '添加目录',
      destroyOnClose: true,
    },
    actions: [
      {
        event: 'click',
        action: 'validate',
        option: {
          targetId: 'createForm',
        },
        thenActions: [
          {
            action: 'request',
            option: {
              url: '/gw/editor/menus/',
              method: 'post',
              baseURL: '',
              customOption: {
                jsonPath: 'body',
                codeValue: '0000000',
                codeKey: 'code',
                successfulFeedback: {
                  type: 'notification',
                  status: 'success',
                  message: '添加成功',
                },
              },
              headerPayloads: [
                {
                  key: 'ram-token',
                  value: 'sessionStorage.ramToken',
                },
                {
                  key: 'ram-system',
                  value: window.editorSystem,
                },
              ],
              payloads: {
                type: 'higher',
                higher: [
                  {
                    key: 'type',
                    value: 'createForm.prop.form.type',
                    id: 'createForm',
                  },
                  {
                    key: 'name',
                    value: 'createForm.prop.form.name',
                    id: 'createForm',
                  },
                  {
                    key: 'parentId',
                    value: 'createForm.prop.form.parentId',
                    id: 'createForm',
                  },
                  {
                    key: 'authSystemId',
                    value: 'sessionStorage.authSystemId',
                  },
                ],
              },
            },
            thenActions: [
              {
                action: 'resetValue',
                option: {
                  targetId: 'createForm',
                },
                thenActions: [
                  {
                    action: 'closeModal',
                    option: {
                      targetId: 'createModal',
                    },
                  },
                ],
              },
              {
                action: 'broadcast',
                option: {
                  targetId: 'tree-1',
                  event: 'mounted',
                },
              },
            ],
          },
        ],
      },
    ],
    childrens: [
      {
        compId: 'createForm',
        compName: 'lyy-form',
        modelValue: {},
        prop: {
          form: {
            type: 'form',
            parentId: null,
          },
        },
        childrens: [
          {
            compName: 'lyy-text-input',
            compId: 'menuInput',
            prop: {
              field: 'name',
              label: '菜单名称',
              rules: [
                {
                  required: true,
                  message: '请输入菜单名称',
                  trigger: 'blur',
                },
              ],
            },
          },
          {
            compName: 'lyy-select',
            compId: 'menuSelect',
            prop: {
              field: 'type',
              label: '菜单类型',
              placeholder: '请选择',
              rules: [
                {
                  required: true,
                  message: '请选择菜单类型',
                  trigger: 'change',
                },
              ],
              options: [
                { label: '分组', value: 'group' },
                { label: '普通页面', value: 'form' },
                { label: '仪表盘', value: 'dashboard' },
                // { label: '流程表单', value: 'process' },
              ],
            },
          },
        ],
      },
    ],
  },
  // 添加子级分组 弹窗
  {
    compId: 'createChildModal',
    compName: 'lyy-modal',
    modelValue: false,
    prop: {
      title: '添加目录',
      destroyOnClose: true,
    },
    actions: [
      {
        event: 'click',
        action: 'validate',
        option: {
          targetId: 'createChildForm',
        },
        thenActions: [
          {
            action: 'request',
            option: {
              url: '/gw/editor/menus/',
              method: 'post',
              baseURL: '',
              customOption: {
                jsonPath: 'body',
                codeValue: '0000000',
                codeKey: 'code',
                successfulFeedback: {
                  type: 'notification',
                  status: 'success',
                  message: '添加成功',
                },
              },
              headerPayloads: [
                {
                  key: 'ram-token',
                  value: 'sessionStorage.ramToken',
                },
                {
                  key: 'ram-system',
                  value: window.editorSystem,
                },
              ],
              payloads: {
                type: 'higher',
                higher: [
                  {
                    key: 'type',
                    value: 'createChildForm.prop.form.type',
                    id: 'createChildForm',
                  },
                  {
                    key: 'name',
                    value: 'createChildForm.prop.form.name',
                    id: 'createChildForm',
                  },
                  {
                    key: 'value',
                    value: 'createChildForm.prop.form.value',
                    id: 'createChildForm',
                  },
                  {
                    key: 'icon',
                    value: 'createChildForm.prop.form.icon',
                    id: 'createChildForm',
                  },
                  {
                    key: 'hide',
                    value: 'createChildForm.prop.form.hide',
                    id: 'createChildForm',
                  },
                  {
                    key: 'isCache',
                    value: 'createChildForm.prop.form.isCache',
                    id: 'createChildForm',
                  },
                  {
                    key: 'parentId',
                    value: 'createChildForm.prop.form.parentId',
                    id: 'createChildForm',
                  },
                  {
                    key: 'authSystemId',
                    value: 'sessionStorage.authSystemId',
                  },
                ],
              },
            },
            thenActions: [
              {
                action: 'resetValue',
                option: {
                  targetId: 'createChildForm',
                },
                thenActions: [
                  {
                    action: 'closeModal',
                    option: {
                      targetId: 'createChildModal',
                    },
                  },
                ],
              },
              {
                action: 'broadcast',
                option: {
                  targetId: 'tree-1',
                  event: 'mounted',
                },
              },
            ],
          },
        ],
      },
    ],
    childrens: [
      {
        compId: 'createChildForm',
        compName: 'lyy-form',
        modelValue: {},
        prop: {
          form: {
            type: 'form',
            hide: 'N',
          },
        },
        childrens: [
          {
            compName: 'lyy-text-input',
            compId: 'menuInput-2',
            prop: {
              field: 'name',
              label: '菜单名称',
              rules: [
                {
                  required: true,
                  message: '请输入页面名称',
                  trigger: 'blur',
                },
              ],
            },
          },
          {
            compName: 'lyy-select',
            compId: 'menuSelect-2',
            prop: {
              field: 'type',
              label: '菜单类型',
              placeholder: '请选择',
              rules: [
                {
                  required: true,
                  message: '请输入页面名称',
                  trigger: 'change',
                },
              ],
              options: [
                { label: '分组', value: 'group' },
                { label: '普通页面', value: 'form' },
                { label: '仪表盘', value: 'dashboard' },
                // { label: '流程表单', value: 'process' },
              ],
            },
          },
          {
            compName: 'lyy-text-input',
            compId: 'menu-code-input-21323',
            prop: {
              field: 'value',
              label: '菜单编码',
              placeholder: '请输入菜单编码',
            },
          },
          {
            compName: 'lyy-text-input',
            compId: 'iconSelect',
            prop: {
              field: 'icon',
              label: '菜单图标',
              placeholder: '请输入图标名称',
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'menuSwitch',
            prop: {
              field: 'hide',
              label: '是否隐藏菜单',
              checkedValue: 'Y',
              unCheckedValue: 'N',
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'cache-switch',
            prop: {
              field: 'isCache',
              label: '是否开启缓存',
              checkedValue: 'Y',
              unCheckedValue: 'N',
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'auth-switch-btn',
            prop: {
              field: 'queryButtonAuthCode',
              label: '是否有按钮权限',
              checkedValue: true,
              unCheckedValue: false,
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'auth-switch-filed',
            prop: {
              field: 'queryFieldAuthCode',
              label: '是否有字段权限',
              checkedValue: true,
              unCheckedValue: false,
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
        ],
      },
    ],
  },
  // 更新菜单 弹窗
  {
    compId: 'updateGroupModal',
    compName: 'lyy-modal',
    prop: {
      title: '菜单更新',
      destroyOnClose: true,
    },
    actions: [
      {
        event: 'click',
        action: 'validate',
        option: {
          targetId: 'updateGroupForm',
        },
        thenActions: [
          {
            action: 'request',
            option: {
              url: '/gw/editor/menus/',
              method: 'put',
              headerPayloads: [
                {
                  key: 'ram-token',
                  value: 'sessionStorage.ramToken',
                },
                {
                  key: 'ram-system',
                  value: window.editorSystem,
                },
              ],
              payloads: {
                type: 'dynamic',
                dynamic: {
                  nodePath: 'updateGroupForm.modelValue',
                },
                higher: [],
                static: '',
              },
              // interceptors: {
              //   requestInterceptor:
              //     "(config) => {\n  if(config.data.parentId.includes('tree-1.modelValue.operation.parentId')){\n    config.data.parentId=''\n}\n  \n  \n  return config;\n  }",
              //   responseInterceptor:
              //     '(res) => {\n  \n  \n  \n  return res;\n  }',
              // },
              baseURL: '',
              customOption: {
                jsonPath: 'body',
                codeValue: '0000000',
                codeKey: 'code',
                successfulFeedback: {
                  type: 'notification',
                  status: 'success',
                  message: '更新成功',
                },
              },
            },
            thenActions: [
              {
                action: 'resetValue',
                option: {
                  targetId: 'updateGroupForm',
                },
                thenActions: [
                  {
                    action: 'closeModal',
                    option: {
                      targetId: 'updateGroupModal',
                    },
                  },
                ],
              },
              {
                action: 'broadcast',
                option: {
                  targetId: 'tree-1',
                  event: 'mounted',
                },
              },
            ],
          },
        ],
      },
    ],
    childrens: [
      {
        compId: 'updateGroupForm',
        compName: 'lyy-form',
        prop: {
          form: {},
        },
        modelValue: {},
        actions: [
          {
            event: 'mount',
            action: 'setNewValue',
            option: {
              to: {
                type: 'higher',
                dynamic: {
                  nodePath: 'updateGroupForm.prop.form',
                },
              },
              from: {
                higher: [
                  {
                    key: 'authMenuId',
                    value: 'tree-1.modelValue.operation.id',
                  },
                  {
                    key: 'name',
                    value: 'tree-1.modelValue.operation.name',
                  },
                  {
                    key: 'value',
                    value: 'tree-1.modelValue.operation.value',
                  },
                  {
                    key: 'icon',
                    value: 'tree-1.modelValue.operation.iconName',
                  },
                  {
                    key: 'type',
                    value: 'tree-1.modelValue.operation.type',
                  },
                  {
                    key: 'parentId',
                    value: 'tree-1.modelValue.operation.parentId',
                  },
                  {
                    key: 'authSystemId',
                    value: 'sessionStorage.authSystemId',
                  },
                  {
                    key: 'isCache',
                    value: 'tree-1.modelValue.operation.isCache',
                  },
                  {
                    key: 'queryButtonAuthCode',
                    value: 'tree-1.modelValue.operation.queryButtonAuthCode',
                  },
                  {
                    key: 'queryFieldAuthCode',
                    value: 'tree-1.modelValue.operation.queryFieldAuthCode',
                  },
                ],
              },
            },
            thenActions: [],
          },
        ],
        childrens: [
          {
            compName: 'lyy-text-input',
            compId: 'text-input11111',
            prop: {
              field: 'name',
              label: '菜单名称',
              rules: [
                {
                  required: true,
                  message: '请输入菜单名称',
                  trigger: 'blur',
                },
              ],
            },
          },
          {
            compName: 'lyy-text-input',
            compId: 'icon-input-code-12323232',
            prop: {
              field: 'value',
              label: '菜单编码',
              placeholder: '请输入菜单编码',
            },
          },
          {
            compName: 'lyy-text-input',
            compId: 'icon-input11111',
            prop: {
              field: 'icon',
              label: '菜单图标',
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'cache-switch-111',
            prop: {
              field: 'isCache',
              label: '是否开启缓存',
              checkedValue: 'Y',
              unCheckedValue: 'N',
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'auth-switch-btn-01',
            prop: {
              field: 'queryButtonAuthCode',
              label: '是否有按钮权限',
              checkedValue: true,
              unCheckedValue: false,
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
          {
            compName: 'lyy-switch',
            compId: 'auth-switch-filed-01',
            prop: {
              field: 'queryFieldAuthCode',
              label: '是否有字段权限',
              checkedValue: true,
              unCheckedValue: false,
              labelCol: {
                style: {
                  width: '184px',
                },
              },
            },
          },
        ],
      },
    ],
  },
  // 发布 弹窗
  {
    compName: 'lyy-modal',
    compId: 'publishModal',
    compNameCn: '模态框',
    modelValue: false,
    style: {},
    prop: {
      title: '发布页面',
      size: 'middle',
      loading: {
        tip: '加载中',
        spinning: true,
      },
      buttonList: [],
      centered: true,
      maskClosable: false,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '取消',
      destroyOnClose: true,
      footer: true,
    },
    childrens: [
      {
        compName: 'lyy-form',
        compId: 'publishForm',
        compNameCn: '基础表单',
        style: {},
        prop: {
          form: {
            env: undefined,
            globalConfigureFlag: 'N',
          },
          aliasName: '基础表单',
          isSignChange: null,
          mainLayout: false,
          class: '',
        },
        modelValue: {
          env: undefined,
          globalConfigureFlag: 'N',
        },
        actions: [],
        childrens: [
          {
            compName: 'lyy-grid',
            compId: '478c030a-d182-4a63-b7f0-3859643ca14d',
            compNameCn: '栅格布局',
            prop: {
              row: {
                align: '',
                gutter: '',
                justify: '',
                wrap: '',
              },
              size: 'large',
              children: [
                {
                  compName: 'lyy-select',
                  compId: 'dcb90617-b705-4bdf-9d15-64fd56626305',
                  compNameCn: '下拉框',
                  prop: {
                    field: 'env',
                    label: '发布环境',
                    aliasName: '下拉框',
                    placeholder: '请选择',
                    mode: 'combobox',
                    defaultValue: {
                      source: '',
                      sourceKey: '',
                    },
                    rules: [
                      {
                        required: true,
                        message: '请选择发布环境',
                        trigger: 'blur',
                      },
                    ],
                    options: [],
                    fieldNames: {
                      label: 'name',
                      value: 'code',
                    },
                    size: 'default',
                    immediate: true,
                    allowClear: true,
                    readonly: false,
                    showSearch: true,
                    optionFilterProp: '',
                    disabled: false,
                    lastComp: {
                      text: '',
                      icon: '',
                    },
                    labelCol: {
                      style: {},
                    },
                    icon: {
                      iconName: '',
                      popover: {
                        description: [],
                      },
                    },
                    formId: '',
                    show: {
                      exp: '',
                    },
                    forbid: {
                      exp: '',
                    },
                  },
                  actions: [
                    {
                      event: 'mounted',
                      action: 'request',
                      option: {
                        url: '/gw/editor/page/configures/getEnvList',
                        method: 'get',
                        payloads: {
                          type: 'static',
                          static: '',
                        },
                        responseDataKey: 'envList',
                        customOption: {
                          loading: true,
                          nextEventsDelay: 0,
                          parse: false,
                          codeValue: '0000000',
                          codeKey: 'code',
                          jsonPath: 'body',
                        },
                        headerPayloads: [
                          {
                            key: 'ram-token',
                            value: 'sessionStorage.ramToken',
                          },
                          {
                            key: 'ram-system',
                            value: window.editorSystem,
                          },
                        ],
                        baseURL: '',
                      },
                      thenActions: [
                        {
                          event: 'mounted',
                          action: 'setNewValue',
                          option: {
                            from: {
                              dynamic: {
                                nodePath: 'envList',
                              },
                            },
                            to: {
                              type: 'dynamic',
                              dynamic: {
                                nodePath:
                                  '下拉框-dcb90617-b705-4bdf-9d15-64fd56626305.prop.options',
                              },
                            },
                          },
                          thenActions: [],
                        },
                      ],
                    },
                  ],
                },
                {
                  compName: 'lyy-switch',
                  compId: 'e569fbec-3320-4983-96cd-e3feca9f33d2',
                  compNameCn: '开关',
                  prop: {
                    field: 'globalConfigureFlag',
                    label: '发布全局配置',
                    size: 'default',
                    checkedValue: 'Y',
                    unCheckedValue: 'N',
                    checkedChildren: '是',
                    unCheckedChildren: '否',
                    readonly: false,
                    rules: [
                      {
                        required: false,
                        message: '',
                        trigger: '',
                      },
                    ],
                    disabled: false,
                    formId: '',
                  },
                  actions: [],
                },
              ],
            },
            actions: [],
          },
        ],
      },
    ],
    actions: [
      // 校验表单
      {
        event: 'confirm',
        action: 'validate',
        option: {
          targetId: 'publishForm',
        },
        thenActions: [
          // 打开二次确认弹窗
          {
            action: 'openModal',
            option: {
              targetId: 'publishConfirmModal',
            },
            thenActions: [
              {
                event: 'confirm',
                action: 'setNewValue',
                option: {
                  from: {
                    static:
                      '<p>确定发布到【${下拉框-dcb90617-b705-4bdf-9d15-64fd56626305.prop.selectedOption.name}】吗？</p>',
                  },
                  to: {
                    type: 'static',
                    dynamic: {
                      nodePath: 'publishTip.prop.html',
                    },
                  },
                },
                thenActions: [],
              },
            ],
          },
        ],
      },
      // {
      //   event: 'submit',
      //   action: 'request',
      //   condition: {
      //     exp: 'route.params.pageId !== "1"',
      //   },
      //   option: {
      //     baseURL: '',
      //     method: 'post',
      //     url: '/gw/editor/page/configures/publishConfigureInfo',
      //     headerPayloads: [
      //       {
      //         key: 'ram-token',
      //         value: 'sessionStorage.ramToken',
      //       },
      //       {
      //         key: 'ram-system',
      //         value: window.editorSystem,
      //       },
      //     ],
      //     payloads: {
      //       type: 'higher',
      //       higher: [
      //         {
      //           key: 'configureInfoList',
      //           value: 'route.params.pageId',
      //         },
      //         {
      //           key: 'env',
      //           value: 'publishForm.modelValue.env',
      //         },
      //       ],
      //     },
      //     customOption: {
      //       loading: true,
      //       nextEventsDelay: 0,
      //       parse: false,
      //       codeValue: '0000000',
      //       codeKey: 'code',
      //       successfulFeedback: {
      //         type: 'message',
      //         duration: 2,
      //         placement: 'topLeft',
      //         status: 'success',
      //         title: '',
      //         message: '发布成功',
      //       },
      //     },
      //     interceptors: {
      //       requestInterceptor: (config) => {
      //         config.data.configureInfoList = [config.data.configureInfoList]
      //         return config
      //       },
      //     },
      //   },
      //   thenActions: [
      //     {
      //       action: 'closeModal',
      //       option: {
      //         targetId: 'publishModal',
      //       },
      //       thenActions: [
      //         {
      //           action: 'resetValue',
      //           option: {
      //             targetId: 'publishForm',
      //           },
      //           thenActions: [],
      //         },
      //       ],
      //     },
      //   ],
      // },
      {
        event: 'submit',
        action: 'request',
        condition: {
          exp: 'route.params.pageId !== "1"',
        },
        option: {
          baseURL: '',
          url: '/gw/editor/page/configures/publishConfigureInfo',
          method: 'post',
          responseDataKey: 'publishData',
          headerPayloads: [
            {
              key: 'ram-token',
              value: 'sessionStorage.ramToken',
            },
            {
              key: 'ram-system',
              value: window.editorSystem,
            },
          ],
          payloads: {
            type: 'higher',
            higher: [
              {
                key: 'authSystemId',
                value: 'sessionStorage.authSystemId',
              },
              {
                key: 'configureInfoList',
                value: 'route.params.pageId',
              },
              {
                key: 'env',
                value: 'publishForm.modelValue.env',
              },
              {
                key: 'globalConfigureFlag',
                value: 'publishForm.modelValue.globalConfigureFlag',
              },
              {
                key: 'menuIdList',
                value: 'tree-1.modelValue.parentIdsOfSelectedNode',
              },
            ],
          },
          targets: [],
          sourceKey: '',
          targetId: null,
          customOption: {
            loading: true,
            nextEventsDelay: 0,
            parse: false,
            codeValue: '0000000',
            codeKey: 'code',
            successfulFeedback: {
              type: 'message',
              duration: 2,
              placement: 'topLeft',
              status: 'success',
              title: '',
              message: '发布成功',
            },
          },
          interceptors: {
            requestInterceptor: (config) => {
              config.data.configureInfoList = [config.data.configureInfoList]
              return config
            },
          },
        },
        thenActions: [
          {
            action: 'resetValue',
            option: {
              targetId: 'publishForm',
            },
            thenActions: [
              {
                action: 'closeModal',
                option: {
                  targetId: 'publishModal',
                },
                thenActions: [
                  {
                    action: 'openModal',
                    option: {
                      targetId: 'c9f66325e-4861-424d-8761-e0bf9573941e',
                    },
                    thenActions: [
                      {
                        action: 'setNewValue',
                        option: {
                          from: {
                            dynamic: {
                              nodePath: 'publishData',
                            },
                          },
                          to: {
                            type: 'dynamic',
                            dynamic: {
                              nodePath:
                                'c4ae88243-a206-4725-8a1d-598aa2cfa579.modelValue.sqltxt',
                            },
                          },
                        },
                        thenActions: [],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  // 发布后弹框展示sql语句
  {
    compName: 'lyy-modal',
    compId: 'c9f66325e-4861-424d-8761-e0bf9573941e',
    compNameCn: '菜单SQL语句',
    modelValue: false,
    style: {},
    prop: {
      title: '菜单SQL语句',
      size: 'middle',
      loading: {
        tip: '加载中',
        spinning: true,
      },
      buttonList: [],
      centered: true,
      showConfirm: false,
      maskClosable: true,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '关闭',
      destroyOnClose: true,
      footer: true,
    },
    childrens: [
      {
        compName: 'lyy-form',
        compId: 'c4ae88243-a206-4725-8a1d-598aa2cfa579',
        compNameCn: '表单菜单SQL语句',
        style: {},
        prop: {
          form: {
            sqltxt: '',
          },
          aliasName: '表单菜单SQL语句',
          isSignChange: null,
          mainLayout: false,
          labelCol: {
            style: {
              width: '110px',
            },
          },
          class: '',
        },
        modelValue: {},
        actions: [],
        childrens: [
          {
            compName: 'lyy-editor',
            compId: 'c447177a6-bf31-4b28-835a-1923895f62ea',
            compNameCn: '输入框',
            style: {},
            prop: {
              field: 'sqltxt',
              language: 'sql',
              style: {
                height: '250px',
              },
            },
            actions: [],
          },
        ],
      },
    ],
    actions: [],
  },
  // 发布页面二次确认弹窗
  {
    compName: 'lyy-modal',
    compId: 'publishConfirmModal',
    compNameCn: '模态框',
    modelValue: false,
    style: {},
    prop: {
      title: '提示',
      size: 'middle',
      loading: {
        tip: '加载中',
        spinning: true,
      },
      buttonList: [],
      centered: true,
      maskClosable: false,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '取消',
      destroyOnClose: true,
      footer: true,
    },
    childrens: [
      {
        compId: 'publishTip',
        compName: 'lyy-html',
        prop: {
          html: '',
        },
        childrens: [],
        style: {},
        actions: [],
      },
    ],
    actions: [
      {
        event: 'confirm',
        action: 'broadcast',
        option: {
          targetId: 'publishModal',
          event: 'submit',
        },
        thenActions: [
          {
            action: 'closeModal',
            option: {
              targetId: 'publishConfirmModal',
            },
            thenActions: [],
          },
        ],
      },
      {
        event: 'cancel',
        action: 'closeModal',
        option: {
          targetId: 'publishConfirmModal',
        },
      },
    ],
  },
])

const $emit = defineEmits(['treeclick', 'buttonClick'])
onBeforeRouteUpdate((updateGuard) => {
  if (updateGuard.fullPath == '/1') {
    return
  }
  const data = {
    path: updateGuard.fullPath,
  }
  reloadData(data)
})
const isEnableBranchDevops = ref(true) // 是否启用分支管理
window[`Watcher${window.__aliasName__}`].$on('treeclick', (a, data) => {
  if (!branchValue.value && isEnableBranchDevops.value) {
    notification.info({
      description: '请先选择分支',
      message: '提示',
    })
    return
  }
  // 储存树选中节点id，以便回显
  localStorage.setItem('selectKey', data.path)
  const router = useStore().getRouter()
  sessionStorage.setItem('tem-edit-pageName', data.name)
  data.type === 'form' && router.push({ path: data.path })
})
function reloadData(data) {
  const params = {
    id: data.path.replace('/', '').split('?')[0],
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    codeBranchName: branchValue.value?.label ?? '',
    isRemote: branchValue.value?.isRemote ?? '',
  }
  getBranchStatus()
  $emit('treeclick', params)
  systemId.value = data.path.replace('/', '')
}
window[`Watcher${window.__aliasName__}`].$on('buttonClick', (a, data) => {
  $emit('buttonClick', data)
})

onMounted(async () => {
  const token = window.sessionStorage.getItem('ramToken') || ''
  branchValue.value = JSON.parse(
    localStorage.getItem('branch' + localStorage.getItem('authSystemId')),
  )
  if (branchValue.value) {
    sessionStorage.setItem('branchName', branchValue.value.label)
    sessionStorage.setItem('codeBranchName', branchValue.value.label)
    sessionStorage.setItem('isRemote', branchValue.value.isRemote)
  }
  axios({
    method: 'get',
    url: '/gw/editor/system/environments/default',
    headers: {
      'ram-token': token,
      'ram-system': window.editorSystem,
    },
    params: {
      authSystemId: sessionStorage.authSystemId,
    },
  }).then((res) => {
    if (res && res.data && res.data.body) {
      try {
        let config =
          typeof res.data.body.envStr == 'string'
            ? JSON.parse(res.data.body.envStr)
            : res.data.body.envStr
        try {
          config = Object.assign(
            config,
            JSON.parse(
              localStorage['localConfig' + sessionStorage.authSystemId],
            ),
          )
        } catch (error) {
          console.log(error)
        }
        window.__httpConfig__.setHttpConfig(config)
        const path = getCurrentPageName()
        if (!path) {
          return
        }
        const data = {
          path: getCurrentPageName(),
        }
        reloadData(data)
      } catch (error) {
        console.log('edit index 配置 http失败 ======>', error)
      }
    }
  })
  // 通知树组件回显选中节点
  const nodeId = localStorage.getItem('selectKey')
  window[`Watcher${window.__aliasName__}`].$emit(
    'tree-1',
    'setSelectKey',
    nodeId,
  )
  await Promise.all([getUserInfoData()]).then(async () => {
    if (isEnableBranchDevops.value) {
      await Promise.all([getCurrentBranchData(), getCheckoutBranchData()])
    }
  })
})
const visible = ref(false)
const showLocalConfig = ref(true)
const codeValue = ref('')
const handleOk = () => {
  localStorage['localConfig' + sessionStorage.getItem('authSystemId')] =
    codeValue.value
  visible.value = false
}
const showModal = () => {
  jsonValue.value =
    localStorage['localConfig' + sessionStorage.getItem('authSystemId')]
  visible.value = true
}
const jsonValue = ref()
const handleJson = (code) => {
  codeValue.value = code
}

// 获取当前用户信息
const userInfo = ref({})
const getUserInfoData = async () => {
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    }
    const res = await getUserInfo(params)
    userInfo.value = res.body
    localStorage.setItem('userInfo', JSON.stringify(res.body))
    isEnableBranchDevops.value = res.body.enableBranchDevops
  } catch (error) {
    console.error(error)
  }
}

// 跳转管理后台
const goManagement = () => {
  $router.push({
    path: '/management',
    query: {
      pageId: $route.params.pageId,
    },
  })
}
interface Option {
  label: string
  options: Array<Record<string, any>>
}
const branchOptions = ref<Option[]>([
  {
    label: 'local',
    options: [],
  },
  {
    label: 'origin',
    options: [],
  },
])
const branchValue = ref(null)
const branchList = ref([])
const originBranchList = ref([])
const originToBranchList = ref([])
// 获取当前应用当前用户的分支
const getCurrentBranchData = async (data?) => {
  branchOptions.value[0].options = []
  branchOptions.value[1].options = []
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      queryRemote: true,
      branchName: data ? data : null,
    }
    const res = await getCurrentBranch(params)
    if (res && res.body && res.body.length > 0) {
      branchList.value = res.body
      for (const item of res.body) {
        if (item.isRemote) {
          branchOptions.value[1].options.push({
            label: item.codeBranchName,
            value: item.codeBranchName,
            isRemote: item.isRemote,
            codeBranchId: item.codeBranchId,
          })
          originBranchList.value.push({
            label: item.codeBranchShowName,
            value: item.codeBranchName,
          })
          originToBranchList.value.push({
            label: item.codeBranchShowName,
            value: item.codeBranchName,
          })
        } else {
          branchOptions.value[0].options.push({
            label: item.codeBranchName,
            value: item.codeBranchId,
            isRemote: item.isRemote,
            codeBranchId: item.codeBranchId,
          })
        }
      }
    }
  } catch (error) {
    console.error(error)
  }
}
// 获取搜索来源的分支
const getComeSearchCurrentBranchData = async (data) => {
  originBranchList.value = []
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      queryRemote: true,
      branchName: data ? data : null,
    }
    const res = await getCurrentBranch(params)
    if (res && res.body && res.body.length > 0) {
      for (const item of res.body) {
        if (item.isRemote) {
          originBranchList.value.push({
            label: item.codeBranchShowName,
            value: item.codeBranchName,
          })
        }
      }
    }
  } catch (error) {
    console.error(error)
  }
}
// 获取搜索来源的分支
const getToSearchCurrentBranchData = async (data) => {
  originToBranchList.value = []
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      queryRemote: true,
      branchName: data ? data : null,
    }
    const res = await getCurrentBranch(params)
    if (res && res.body && res.body.length > 0) {
      for (const item of res.body) {
        if (item.isRemote) {
          originToBranchList.value.push({
            label: item.codeBranchShowName,
            value: item.codeBranchName,
          })
        }
      }
    }
  } catch (error) {
    console.error(error)
  }
}
const searchCurrentBranch = debounce((data) => getCurrentBranchData(data), 500)
const branchChange = async (data, option) => {
  // 分支选择变更
  branchValue.value = option
  localStorage.setItem(
    'branch' + localStorage.getItem('authSystemId'),
    JSON.stringify(branchValue.value),
  )
  sessionStorage.setItem('branchName', branchValue.value.label)
  sessionStorage.setItem('codeBranchName', branchValue.value.label)
  sessionStorage.setItem('isRemote', branchValue.value.isRemote)
  const params = {
    path: getCurrentPageName(),
  }
  broadcast('ctzlmapxud', 'mounted')
  reloadData(params)
}

// 判断是否禁用操作按钮
const isBtnDisabled = computed(() => {
  return !branchValue.value || branchValue.value.isRemote
})

// 获取选中本地分支的信息 待定
// const getLocalBranchInfo = async () => {
//   try {
//     const params = {
//       codeBranchId:
//     }
//     const res = await getCurrentLocalBranch()
//   }catch(err){
//     console.error(err)
//   }
// }

// 获取当前分支的状态
const branchStatus = ref({})
const broadcast = (targetId, eventName) => {
  window.useAction([
    {
      event: 'update',
      action: 'broadcast',
      option: {
        targetId,
        event: eventName,
      },
    },
  ])
}
const getBranchStatus = async () => {
  if (!branchValue.value) {
    return
  }
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchName: branchValue.value?.label,
      isRemote: branchValue.value?.isRemote,
    }
    const res = await getCurrentBranchStatus(params)
    branchStatus.value = res.body
  } catch (error) {
    console.error(error)
  }
}

// checkout弹窗
const checkoutPopupVisible = ref(false)
const checkoutBranchData = ref(null)
// 获取checkout可选择分支
const getCheckoutBranchData = async (value?) => {
  const data = []
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      branchName: value ? value : null,
    }
    const res = await getCurrentStashBranches(params)
    for (const item of res.body) {
      data.push({
        label: item.name,
        value: item.name,
      })
    }
    checkoutBranchData.value = data
  } catch (error) {
    console.error(error)
  }
}
const checkoutSubmit = async (data) => {
  try {
    const res = await checkoutBranch(data)
    if (res && res.body) {
      notification.success({
        description: '新建成功',
        message: '提示',
      })
      await getCurrentBranchData()
      for (const item of branchOptions.value[0].options) {
        if (item.codeBranchId == res.body.codeBranchId) {
          branchChange(null, item)
        }
      }
      checkoutPopupVisible.value = false
    }
  } catch (error) {
    console.error(error)
  }
}

// pull弹窗
const pullConfirm = () => {
  Modal.confirm({
    title: '提示',
    content: '即将把远端文件覆盖本地文件内容，是否继续',
    async onOk() {
      const params = {
        codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
        codeBranchId: branchValue.value.codeBranchId,
        pageConfigureInfoId: getCurrentPageName(),
      }
      const res = await pullBranch(params)
      if (res && res.body) {
        reloadData({ path: getCurrentPageName() })
      }
    },
    onCancel() {},
  })
}

// commit弹窗
const commitPopupVisible = ref(false)
const commitConfirmLoading = ref(false)
const commitPopupRef = ref()
const commitClick = () => {
  commitPopupVisible.value = true
  jsonData.value = ''
  historyList.length = 0
  getNotSubmitData()
}
// 获取未提交的页面
const notSubmitData = ref([])
const getNotSubmitData = async () => {
  try {
    message.loading('加载中...', 0)
    const res = await getNotSubmit({
      codeBranchId: branchValue.value.codeBranchId,
    })
    message.destroy()
    notSubmitData.value = res && res.body && res.body.length > 0 ? res.body : []
  } catch (error) {
    message.destroy()
    console.error(error)
  }
}
// 点击树组件时获取页面历史修改记录
const historyList = reactive([])
const jsonData = ref(null)
const historyListValue = ref('')
const getHistoryVersionJsDataConfig = async (data) => {
  const res = await getHistoryPageConfig('/editor/page/component/getOne', {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: data.pageConfigureInfoId,
    pageComponentId: data.pageComponentId,
    codeBranchId: branchValue.value.codeBranchId,
  })
  const { body, code } = res
  if (code === '0000000' && body?.def) {
    jsonData.value = body.def
  }
}
const getHistoryListPageConfig = async (data) => {
  if (!data) {
    return
  }
  try {
    const res = await getHistoryPageConfig(
      '/editor/page/configures/histories/',
      {
        pageConfigureInfoId: data,
        limit: 200,
        codeBranchId: branchValue.value.codeBranchId,
      },
    )
    const { body, code } = res
    historyList.length = 0
    if (code === '0000000' && body?.length) {
      historyList.push(...body)
    } else {
      historyList.length = 0
    }
    historyListValue.value = historyList[0].pageConfigureInfoHistoryId
    getHistoryVersion(historyList[0])
  } catch (error) {
    console.error(error)
  }
}
const getHistoryListJsData = async (pageConfigureInfoId, pageComponentId) => {
  // /page/components/histories
  try {
    const res = await getHistoryPageConfig(
      '/editor/page/components/histories',
      {
        pageConfigureInfoId,
        limit: 200,
        codeBranchId: branchValue.value.codeBranchId,
        pageComponentId,
      },
    )
    const { body, code } = res
    historyList.length = 0
    if (code === '0000000' && body?.length) {
      historyList.push(...body)
    } else {
      historyList.length = 0
    }
    historyListValue.value = historyList[0].pageConfigureInfoHistoryId
    getHistoryVersionJsDataConfig({ ...historyList[0], pageComponentId })
  } catch (error) {
    console.error(error)
  }
}
const treeClickGetHistoryList = (pageConfigureInfoId, pageComponentId) => {
  if (pageComponentId) {
    getHistoryListJsData(pageConfigureInfoId, pageComponentId)
  } else {
    getHistoryListPageConfig(pageConfigureInfoId)
  }
}
const getHistoryVersion = async (item) => {
  // 获取页面json配置
  historyListValue.value = item.pageConfigureInfoHistoryId
  const res = await getHistoryPageConfig(
    `/editor/page/configures/histories/${item.pageConfigureInfoHistoryId}`,
    {},
  )
  const { body, code } = res
  jsonData.value =
    code === '0000000' && body.pageStr && body.pageStr !== '{}'
      ? body.pageStr
      : null
}
const commitPopupSumbit = async (data, callback) => {
  try {
    commitConfirmLoading.value = true
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchId: branchValue.value.codeBranchId,
      ...data,
    }
    const res = await commitBranch(params)
    if (res && res.body) {
      commitPopupVisible.value = false
      reloadData({ path: getCurrentPageName() })
      callback && callback()
      notification.success({
        description: '提交成功',
        message: '提示',
      })
    }
    commitConfirmLoading.value = false
  } catch (error) {
    console.error(error)
    commitPopupVisible.value = false
    commitConfirmLoading.value = false
  } finally {
    commitConfirmLoading.value = false
  }
}
const rollbackData = async (data) => {
  Modal.confirm({
    title: '提示',
    content: '是否回退当前修改',
    async onOk() {
      const params = {
        codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
        codeBranchId: branchValue.value.codeBranchId,
        pageConfigureInfoId: data.pageConfigureInfoId,
        contentType: data.contentType,
        pageComponentId: data.pageComponentId,
      }
      try {
        const res = await cancelCommit(params)
        if (res && res.body) {
          notification.success({
            message: '提示',
            description: '回退成功',
          })
          getNotSubmitData()
          reloadData({ path: getCurrentPageName() })
          jsonData.value = ''
          historyList.length = 0
          commitPopupRef.value.selectedKeys.length = 0
          commitPopupRef.value.checkedKeys.length = 0
          commitPopupRef.value.pageConfigureInfoIdList.length = 0
        }
      } catch (error) {
        console.error(error)
      }
    },
    onCancel() {},
  })
}

// 指派人列表
const assignListData = ref([])
const getAssignListData = async (data) => {
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchId: null,
      searchContent: null,
      targetBranchName: null,
      assignType: data.assignType,
    }
    if (data.assignType == 1) {
      params.codeBranchId = data.codeBranchId
    } else if (data.assignType == 2) {
      params.targetBranchName = data.targetBranchName
    }
    const res = await getAssignUserList(params)
    if (res && res.body) {
      const list = []
      for (const item of res.body) {
        item.label = item.codeUserName + ' ' + item.email
        item.value = item.codeUserId
        list.push(item)
      }
      assignListData.value = list
    }
  } catch (error) {
    console.error(error)
  }
}
// push弹窗
const pushPopupVisible = ref(false)
const pushConfirmLoading = ref(false)
const pushBranchData = ref(null)
const pushPopupVisibleOpen = () => {
  pushPopupVisible.value = true
  pushBranchData.value = branchValue.value.codeBranchId
  getPushRcordsData()
}
const pushPopupSubmit = async (data) => {
  try {
    pushConfirmLoading.value = true
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchId: data.codeBranchId,
      assignUserId: data.assignUserId,
      assignType: 1,
    }
    const res = await pushBranch(params)
    if (res && res.body) {
      notification.success({
        description: '推送成功',
        message: '提示',
      })
      reloadData({ path: getCurrentPageName() })
      pushPopupVisible.value = false
    } else {
      getBranchStatus()
    }
    pushConfirmLoading.value = false
  } catch (error) {
    console.error(error)
    pushConfirmLoading.value = false
    $router.push({
      path: '/management',
      query: {
        key: 4,
      },
    })
  } finally {
    pushConfirmLoading.value = false
  }
}
// merge 弹窗
const mergePopupVisible = ref(false)
const mergeConfirmLoading = ref(false)
const mergePopupSubmit = async (data) => {
  try {
    mergeConfirmLoading.value = true
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      sourceBranchName: data.sourceBranchName,
      targetBranchName: data.targetBranchName,
      assignUserId: data.assignUserId,
    }
    const res = await mergeBranch(params)
    if (res && res.body) {
      mergeConfirmLoading.value = false
      notification.success({
        description: res.body,
        message: '提示',
      })
      reloadData({ path: getCurrentPageName() })
      mergePopupVisible.value = false
    }
  } catch (error) {
    mergeConfirmLoading.value = false
    console.error(error)
    $router.push({
      path: '/management',
      query: {
        key: 3,
      },
    })
  } finally {
    mergeConfirmLoading.value = false
  }
}

// 获取推送内容记录
const pushRecordData = ref([])
const getPushRcordsData = async () => {
  try {
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchId: branchValue.value.codeBranchId,
    }
    const res = await pushRecordsList(params)
    if (res && res.body && res.body.length > 0) {
      pushRecordData.value = res.body
    }
  } catch (error) {
    console.error(error)
  }
}
const failureRecordPopupVisible = ref(false) // 失效记录
const pendingPushPopupVisible = ref(false) // 待处理推送记录
const pendingPushPopupRef = ref()

// 处理冲突
const codeConflictShow = ref(false)
const showCodeConflict = ref(false)
const sourceBranchText = ref('')
const targetBranchText = ref('')
const codeCommitDropId = ref(null)
const codeEditorDom = ref()
const mode = ref('json') // 编辑器语言
const pullComparison = async (data) => {
  try {
    codeConflictShow.value = true
    codeCommitDropId.value = data.codeCommitDropId
    const params = {
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchId: branchValue.value.codeBranchId,
      codeCommitDropId: data.codeCommitDropId,
    }
    const res = await getClashContent(params)
    showCodeConflict.value = true
    if (data.fastContentType == 1) {
      sourceBranchText.value = res.body[0]?.content
      targetBranchText.value = res.body[1]?.content
      mode.value = 'javascript'
    } else {
      sourceBranchText.value = JSON.parse(res.body[0]?.content)
      targetBranchText.value = JSON.parse(res.body[1]?.content)
    }
  } catch (error) {
    console.log(error)
  }
}
const codeConflictSubmit = async () => {
  const content = codeEditorDom.value.getValue()
  Modal.confirm({
    title: '提交配置提示',
    content: '即将提交您的本地配置，请确定是否提交',
    async onOk() {
      try {
        const params = {
          codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
          codeBranchId: branchValue.value.codeBranchId,
          codeCommitDropId: codeCommitDropId.value,
          content: content,
        }
        const res = await dealDropOfPull(params)
        if (res && res.body) {
          notification.success({
            description: '处理冲突成功',
            message: '提示',
          })
          codeConflictShow.value = false
          showCodeConflict.value = false
          pendingPushPopupRef.value.getUserTableList()
          getBranchStatus()
        }
      } catch (error) {
        console.error(error)
      }
    },
    onCancel() {},
    class: 'test',
  })
}

// 获取当前页面id
const getCurrentPageName = () => {
  return $route.path.split('/')[1]
}
</script>

<template>
  <div class="preview-box">
    <div class="global-config-wrap">
      <div class="button-list">
        <a-button
          v-if="showLocalConfig"
          @click="showModal"
          type="link"
          class="open"
        >
          打开本地配置
        </a-button>
        <a-button @click="goManagement">打开管理后台</a-button>
        <div class="branch-warper" v-if="isEnableBranchDevops">
          <a-select
            show-search
            v-model:value="branchValue"
            :options="branchOptions"
            style="width: 400px"
            :filter-option="false"
            :show-arrow="false"
            :not-found-content="null"
            @change="branchChange"
            @search="searchCurrentBranch"
          ></a-select>
          <a-button @click="checkoutPopupVisible = true" type="link">
            新建分支
          </a-button>
          <a-button @click="pullConfirm" :disabled="isBtnDisabled" type="link">
            拉取配置
          </a-button>
          <a-button
            @click="commitClick"
            class="branchBtnWarper"
            :disabled="isBtnDisabled"
            type="link"
          >
            <div>提交</div>
            <div v-if="branchStatus.unCommittedNum > 0" class="branchBtnNum">
              {{ branchStatus.unCommittedNum }}
            </div>
          </a-button>
          <a-button
            @click="pushPopupVisibleOpen()"
            class="branchBtnWarper"
            :disabled="isBtnDisabled"
            type="link"
          >
            <div>推送</div>
            <div v-if="branchStatus.unPushedNum > 0" class="branchBtnNum">
              {{ branchStatus.unPushedNum }}
            </div>
          </a-button>
          <a-button @click="mergePopupVisible = true" type="link">
            合并
          </a-button>
        </div>
      </div>
      <div class="global-config-wrap-record" v-if="isEnableBranchDevops">
        <a-button
          @click="failureRecordPopupVisible = true"
          class="branchBtnWarper"
          :disabled="isBtnDisabled"
          type="link"
        >
          <div>失效记录</div>
        </a-button>
        <a-button
          @click="pendingPushPopupVisible = true"
          class="branchBtnWarper"
          :disabled="isBtnDisabled"
          type="link"
        >
          <div>待处理推送记录</div>
          <div v-if="branchStatus.unResolvedDropNum > 0" class="branchBtnNum">
            {{ branchStatus.unResolvedDropNum }}
          </div>
        </a-button>
      </div>
    </div>
    <div style="flex: 1; overflow: hidden">
      <lyy-pc-render :page-config="elements"></lyy-pc-render>
    </div>
    <a-modal v-model:open="visible" title="本地配置" @ok="handleOk">
      <lyy-editor
        :modelValue="jsonValue"
        @update:modelValue="handleJson"
        :prop="{
          field: 'responseInterceptorBody',
          style: {
            height: '250px',
          },
        }"
      ></lyy-editor>
    </a-modal>
    <checkOutPopup
      v-if="checkoutPopupVisible"
      v-model="checkoutPopupVisible"
      :branchesData="checkoutBranchData"
      :isRepositoryOwner="userInfo.allowCreateRemoteBranch"
      @submit="checkoutSubmit"
      @searchBranchesData="getCheckoutBranchData"
    />
    <commitPopup
      v-model="commitPopupVisible"
      v-if="commitPopupVisible"
      ref="commitPopupRef"
      :commitData="notSubmitData"
      @get-history-list="treeClickGetHistoryList"
      :history-list="historyList"
      :json-data="jsonData"
      @update-json="getHistoryVersion"
      :history-list-value="historyListValue"
      @submit="commitPopupSumbit"
      :confirmLoading="commitConfirmLoading"
      @rollback="rollbackData"
    />
    <pushPopup
      v-if="pushPopupVisible"
      v-model="pushPopupVisible"
      :branchesData="branchList"
      :assignList="assignListData"
      :pushRecordData="pushRecordData"
      :pushBranch="pushBranchData"
      @get-assign-data="getAssignListData"
      @submit="pushPopupSubmit"
      :confirm-loading="pushConfirmLoading"
    />
    <mergePopup
      v-if="mergePopupVisible"
      v-model="mergePopupVisible"
      :comeBranchesData="originBranchList"
      :toBranchesData="originToBranchList"
      :assignList="assignListData"
      @get-assign-data="getAssignListData"
      @submit="mergePopupSubmit"
      @searchBranchesData="getComeSearchCurrentBranchData"
      @search-to-branches-data="getToSearchCurrentBranchData"
      :confirm-loading="mergeConfirmLoading"
    />
    <failureRecordPopup
      v-if="failureRecordPopupVisible"
      v-model="failureRecordPopupVisible"
      :codeBranchValue="branchValue"
    />
    <pendingPushPopup
      v-if="pendingPushPopupVisible"
      ref="pendingPushPopupRef"
      v-model="pendingPushPopupVisible"
      :codeBranchValue="branchValue"
      @updateBranchStatus="getBranchStatus"
      @pullComparison="pullComparison"
    />
    <a-modal
      v-model:open="codeConflictShow"
      v-if="codeConflictShow"
      width="100%"
      :closable="false"
      wrap-class-name="full-modal"
      :maskClosable="false"
      @ok="codeConflictSubmit"
    >
      <div class="conflict-con-box">
        <div class="codeConflictBox">
          <codeConflict
            v-if="showCodeConflict && sourceBranchText && targetBranchText"
            ref="codeEditorDom"
            :sourceBranchText="sourceBranchText"
            :targetBranchText="targetBranchText"
            :mode="mode"
          ></codeConflict>
          <a-spin v-else size="large" class="loading-custom" />
        </div>
      </div>
    </a-modal>
  </div>
</template>
<style lang="less" scoped>
.open {
  padding: 0;
}

.global-config-wrap {
  width: 100%;
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;

  .button-list {
    display: flex;
    align-items: center;
    column-gap: 8px;
  }

  .branchBtnWarper {
    position: relative;

    .branchBtnNum {
      position: absolute;
      top: 0;
      right: 0;
      width: 15px;
      height: 15px;
      line-height: 15px;
      border-radius: 50%;
      font-size: 12px;
      background-color: #ff1616;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
