<template>
  <a-modal
    :title="'合并分支'"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    v-model:open="visible"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="来源分支" name="sourceBranchName">
        <a-select
          show-search
          v-model:value="formState.sourceBranchName"
          :filter-option="false"
          :show-arrow="false"
          :not-found-content="null"
          :options="comeBranchesData"
          placeholder="请选择来源分支,可搜素"
          @search="searchBranchesData"
        ></a-select>
      </a-form-item>
      <a-form-item label="目标分支" name="targetBranchName">
        <a-select
          show-search
          v-model:value="formState.targetBranchName"
          :filter-option="false"
          :show-arrow="false"
          :not-found-content="null"
          :options="toBranchesData"
          placeholder="请选择目标分支,可搜素"
          @change="optionChange"
          @search="searchToBranchesData"
        ></a-select>
      </a-form-item>
      <a-form-item label="指派人" name="assignUserId">
        <a-select
          show-search
          v-model:value="formState.assignUserId"
          :filter-option="filterOption"
          :options="assignList"
          placeholder="未选指派人则默认指定为自己"
        ></a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import debounce from 'lodash/debounce'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  comeBranchesData: {
    type: Array,
    default: [],
  },
  toBranchesData: {
    type: Array,
    default: [],
  },
  assignList: {
    type: Array,
    default: [],
  },
  confirmLoading: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits([
  'update:modelValue',
  'submit',
  'getAssignData',
  'searchBranchesData',
  'searchToBranchesData',
])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})

// 校验规则
const rules = {
  sourceBranchName: [{ required: true, trigger: 'change' }],
  targetBranchName: [{ required: true, trigger: 'change' }],
}
const formRef = ref(null)
const formState = ref({
  sourceBranchName: null,
  assignUserId: null,
  targetBranchName: null,
  codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
  assignType: 1,
})

const filterOption = (input: string, option: any) => {
  return option.value
    ? option.label.toLowerCase().includes(input.toLowerCase())
    : false
}

const handleSearch = (value) => {
  emit('searchBranchesData', value)
}
const searchBranchesData = debounce((data) => handleSearch(data), 500)
const handleSearchTo = (value) => {
  emit('searchToBranchesData', value)
}
const searchToBranchesData = debounce((data) => handleSearchTo(data), 500)

const optionChange = (data, option) => {
  formState.value.assignUserId = null
  const params = {
    targetBranchName: formState.value.targetBranchName,
    assignType: 2,
  }
  emit('getAssignData', params)
}

const handleOk = async () => {
  try {
    await formRef.value.validateFields()
    emit('submit', formState.value)
    formRef.value.resetFields()
  } catch (error) {
    console.error(error)
  }
}
</script>

<style scoped lang="less">
.checkBoxBtn {
  margin-left: 106px;
}
</style>
