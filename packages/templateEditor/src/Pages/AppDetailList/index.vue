<script setup lang="ts">
import { onMounted, defineComponent, reactive, ref, provide } from 'vue'
import axios from 'axios'
import { getEditorJson, getAuthCodeFn } from '@/request/api/editorRequest'
import { useRouter, useRoute } from 'vue-router'
import treeRender from './components/treeRender.vue'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import { cloneDeep } from 'lodash'
import LyyContainerModel from '@/Model/layoutItem/lyy-container-model'
import { setCompentFormId, qt } from '@leyaoyao/libs/utils'
import { getJsObject } from '../../request/api/editorRequest'
import { setObject, setDataSetList } from '@leyaoyao/libs/store/jsobject'
import { message } from 'ant-design-vue'
const router = useRouter()
const show = ref(false)
const codeConflictShow = ref(false)

window.lyyIsEditerEdit = false

const isEdit = ref(false)

provide('isEdit', isEdit)

window.ipcRenderer?.on('reload', () => {
  show.value = true
  router.go(0)
})
const containerModal = addAliasNameAnduuid(cloneDeep(LyyContainerModel))
const id = ''
const pageId = '1073631916426510336'
const isclick = ref(true)

const getAuthCode = async (id: string, fn: any) => {
  const res = await getAuthCodeFn('/editor-service/permission/menu/authCode', {
    pageConfigureId: id,
    queryButton: true,
    queryField: true,
  })
  if (res?.body) {
    try {
      const authCodeList = JSON.parse(
        sessionStorage.getItem('service-authCodeList'),
      )
      if (id == pageId) {
        authCodeList[id] = res?.body
      } else {
        authCodeList[id] = {
          buttonAuthCode: res?.body?.buttonAuthCode.concat(
            authCodeList[pageId].buttonAuthCode,
          ),
          fieldPermission: res?.body?.fieldPermission.concat(
            authCodeList[pageId].fieldPermission,
          ),
        }
        //
      }
      sessionStorage.setItem(
        'service-authCodeList',
        JSON.stringify(authCodeList),
      )
    } catch {
      sessionStorage.setItem('service-authCodeList', JSON.stringify({}))
    }
  }
  fn && fn()
}
// getAuthCode(pageId as string, undefined)
const treeRef = ref(null)
const getEditorJsonFn = async (data) => {
  const promisePageJson = getEditorJson(data)
  const params1 = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: data.id,
    type: 1,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  const params2 = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: data.id,
    type: 2,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  const jsObjectJson = getJsObject(params1)
  const dataSetListJson = getJsObject(params2)
  const [res, jsObjectRes, dataSetList] = await Promise.all([
    promisePageJson,
    jsObjectJson,
    dataSetListJson,
  ])
  if (res) {
    show.value = true
    const pgc = []
    setObject(jsObjectRes.body)
    if (dataSetList.body) {
      setDataSetList(dataSetList.body)
    }
    if (res.body?.pageStr) {
      if (res.body?.pageStr === '{}') {
        pgc.push(containerModal)
        setCompentFormId(pgc)
        treeRef.value.setListPage(pgc)
        isclick.value = false
      } else {
        const pageC = JSON.parse(res.body.pageStr)
        setCompentFormId(pageC)
        treeRef.value.setListPage(pageC)
        isclick.value = false
      }
    }
  }
}
const handleClick = (data) => {
  if (data.id == '1') {
    return
  }
  const authCodeList = JSON.parse(
    sessionStorage.getItem('service-authCodeList'),
  )
  // getEditorJsonFn(data)
  getAuthCode(data.id as string, () => {
    getEditorJsonFn(data)
  })
}
const codeConflictFn = () => {
  codeConflictShow.value = true
}
/**
 * 获取系统token
 */
onMounted(() => {
  const tokens = JSON.parse(localStorage.getItem('tokens') as string)
  const authSystemCode = sessionStorage.authSystemCode
  if (tokens[authSystemCode as string]) {
    localStorage.setItem('token', tokens[authSystemCode as string])
    sessionStorage.setItem('token', tokens[authSystemCode as string])
  }
  qt.initBuryPoint({
    appId: sessionStorage.getItem('authSystemCode') + '-pc',
    url: 'https://dsentry-report.leyaoyao.com/report',
  })
})
</script>

<template>
  <div id="canvas-content">
    <treeRender class="treeRender" ref="treeRef" @treeclick="handleClick" />
  </div>
</template>
<script lang="ts">
export default defineComponent({
  name: 'app-detail-list',
})
</script>
<style lang="scss" scoped>
#button-list {
  color: #1890ff;
  padding: 5px 0 5px 5px;
  font-size: 16px;
}
#canvas-content {
  padding: 10px 15px;
  box-sizing: border-box;
  display: flex;
  height: 100vh;
  overflow: hidden;
  .treeRender {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    border: 1px solid #ccc;
    padding: 10px;
    box-sizing: border-box;
  }
  .list {
    flex-grow: 1;
    border: 1px solid #ccc;
    margin-left: 15px;
    padding: 10px;
    .listTitle {
      height: 5%;
      display: flex;
      justify-content: space-between;
      h1 {
        font-size: 20px;
        font-weight: 600;
      }
    }
    .listShow {
      height: 95%;
      position: relative;
      .ant-empty {
        width: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}
</style>
