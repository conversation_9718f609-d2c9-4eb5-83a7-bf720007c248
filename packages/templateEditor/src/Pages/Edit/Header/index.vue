<script setup lang="ts">
import { defineComponent, ref, onBeforeUnmount, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getCurrentPageName, useStore } from '@leyaoyao/libs/store'
import axios from 'axios'
import { PAGE_CONFIG } from '@leyaoyao/libs/constants/store-key'
import { filterPageConfig } from '../../../utils/filter-page-config'
import LyyCanvas from '@/context/canvas'
import myEmitter from '@/eventEmitter/event-emitter'
import {
  setKeyBoard,
  removeAllKeyBoard,
  listenKeyBoard,
} from '@/context/keyboard'
import {
  BACK_KEY,
  FORWARD_KEY,
  P_KEY,
  SAVE_KEY,
  DEL_KEY,
} from '@/context/keyboard-constant'
import { message, notification } from 'ant-design-vue'
import keyboard from './keyboard.png'
import { isMac } from '@/utils'
import _ from 'lodash'
import HistoryVersion from '@/Pages/Edit/Header/historyVersion.vue'
import { saveConfig } from '../../../request/api/editorRequest'
import useClipboard from 'vue-clipboard3'
import { transPageConfig, transToNewForm } from './utils'
defineComponent({
  name: 'editor-header',
})

const props = defineProps<{
  previewDisabled: boolean
}>()

const router = useRouter()
const route = useRoute()
const store = useStore()

const authSystemCode = window.sessionStorage.getItem('authSystemCode')

// ['StarThingERP', 'OverseaDjb']
const showUpgrade = ref(['StarThingERP', 'OverseaDjb'].includes(authSystemCode))

const visible = ref<boolean>(false)
const historyShow = ref<boolean>(false)
const preKey = isMac ? 'command' : 'ctrl'
const tipsList = [
  {
    label: '保存',
    tips: '保存当前所有操作',
    key: `${preKey} + S`,
  },
  {
    label: '预览',
    tips: '保存同时跳转到预览页',
    key: `${preKey} + P`,
  },
  {
    label: '控制台',
    tips: '打开浏览器控制台',
    key: `${preKey} + A`,
  },
  {
    label: '前进',
    tips: '如果曾经有回退的话，当前操作向前一步',
    key: `${preKey} + Y`,
  },
  {
    label: '后退',
    tips: '如果曾经有回退的话，当前操作回退一步',
    key: `${preKey} + Z`,
  },
  {
    label: '刷新',
    tips: '刷新当前页面',
    key: `${preKey} + R`,
  },
  {
    label: '删除选中组件',
    tips: '删除选中组件',
    key: `${preKey} + backspace`,
  },
]

const oepnTips = () => {
  visible.value = true
}

const handleOk = () => {
  visible.value = false
}
// 预览保存加跳转
const preview = () => {
  saveConfiguration(() => {
    setTimeout(() => {
      // const path = route.fullPath.replace('/edit', '')
      router.push(location.hash.replace('#/edit', ''))
    }, 300)
  })
}
// 一键升级新列表
const upGrade = () => {
  const pageConfig = filterPageConfigFn(
    _.cloneDeep(LyyCanvas.getInstance().getInitPageConfig()),
  )
  const res = transPageConfig(pageConfig)
  if (res) {
    LyyCanvas.getInstance().setPageConfig(res)
  }
  // console.log('new pageConfig------------', res)
}

// 一键升级新表单
const upGradeForm = () => {
  const pageConfig = filterPageConfigFn(
    _.cloneDeep(LyyCanvas.getInstance().getInitPageConfig()),
  )
  const res = transToNewForm(pageConfig)
  if (res) {
    LyyCanvas.getInstance().setPageConfig(res)
  }
}

// 删除
const deleteCurrentComp = () => {
  const curComp = LyyCanvas.getInstance().getCurrentComp()
  LyyCanvas.getInstance().deleteComp(curComp.compId)
}
const goBack = () => {
  // router.go(-1)
  router.push(location.hash.replace('#/edit', ''))
}

// 导入模板
// const importModal = () => {
//   console.log('导入模板')
// }
// ctrl+s保存事件判断====================
const ctrlSEvent = () => {
  const editPage = sessionStorage.getItem('lyy-editor-moduleName') ?? ''
  if (editPage == 'JsObject') {
    myEmitter.emit('saveJsObject')
  } else if (editPage == 'DataSet') {
    myEmitter.emit('saveDataSet')
  } else {
    saveConfiguration(null)
  }
}
// ctrl+s保存事件判断==============
// store设置
store.setRoute(route)
store.setRouter(router)
store.setValue(PAGE_CONFIG, LyyCanvas.getInstance().getPageConfig())

// 保存按钮
const saveConfiguration = async (cb) => {
  const pageConfig = filterPageConfigFn(
    _.cloneDeep(LyyCanvas.getInstance().getInitPageConfig()),
  )
  console.log(pageConfig, '=======pageConfig========pageConfig')
  let curId = getCurrentPageName()
  if (curId.includes('?')) {
    curId = curId.split('?')[0]
  }
  const token = window.sessionStorage.getItem('ramToken') || ''
  const branchData = localStorage.getItem(
    'branch' + sessionStorage.getItem('authSystemId'),
  )
  message.loading({ content: '保存中', duration: 0 })
  const saveConfigData = await saveConfig(
    {
      pageConfigureInfoId: curId,
      pageStr: JSON.stringify(pageConfig),
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      codeBranchId: branchData ? JSON.parse(branchData).codeBranchId : null,
    },
    {
      'ram-token': token,
      'ram-system': window.editorSystem,
    },
  ).catch((error) => {
    message.error({ content: '保存失败', duration: 2 })
  })
  if (saveConfigData && saveConfigData.code !== '0000000') {
    message.error({ content: saveConfigData.message, duration: 2 })
    return
  }
  notification.success({
    description: '保存配置成功',
    message: '提示',
    duration: 2,
  })
  message.destroy()
  cb && cb()
}
const filterPageConfigFn = (config) => {
  let result = filterPageConfig(
    config,
    (item: any) => {
      if (['lyy-edit-table'].includes(item.compName)) {
        return true
      }
    },
    (item: any) => {
      item.modelValue = {
        selectedRows: [],
        datasource: [],
        currentRow: {},
      }
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (['lyy-table', 'lyy-table-page'].includes(item.compName)) {
        return true
      }
    },
    (item: any) => {
      item.modelValue = {
        selectedRows: [],
        datasource: [],
        currentRow: {},
        filters: [],
        sortOptions: [],
      }
      item.prop.dynamicDataIndexList = []
      item.prop.showDataIndexList = []
    },
  )
  // 清除配置的modelValue
  result = filterPageConfig(
    config,
    () => true,
    (item) => {
      if (
        Object.prototype.toString.call(item.modelValue) === '[object Object]' &&
        ![
          'lyy-edit-table',
          'lyy-table',
          'lyy-excel-table',
          'lyy-table-page',
        ].includes(item.compName)
      ) {
        item.modelValue = {}
        return
      }
      if (Array.isArray(item.modelValue)) {
        item.modelValue = []
        return
      }
      // 暂时先注释，4月11号erp上线后需要重新打开
      // item.modelValue = undefined
    },
  )
  if (
    ['StarThingERP', 'ram-admin'].includes(
      window.sessionStorage.getItem('authSystemCode'),
    )
  ) {
    result = filterPageConfig(
      config,
      (item: any) => {
        if (['lyy-select'].includes(item.compName)) {
          return true
        }
      },
      (item: any) => {
        item.prop.options = []
      },
    )
  }
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-pagination') {
        return true
      }
    },
    (item: any) => {
      item.prop.total = 0
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-modal') {
        return true
      }
    },
    (item: any) => {
      item.modelValue = false
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-form') {
        return true
      }
    },
    (item: any) => {
      item.modelValue = {}
      item.prop.disabled = undefined
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-tree') {
        return true
      }
    },
    (item: any) => {
      item.prop.checkedNodes = []
      item.prop.options = []
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-directory-tree') {
        return true
      }
    },
    (item: any) => {
      item.modelValue.select = {}
      item.modelValue.expandedKeys = []
      item.modelValue.selectKey = null
      item.prop.treeData = []
      item.childrens = []
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-pull-down-selector-input') {
        return true
      }
    },
    (item: any) => {
      item.childrens.length > 0 ? (item.childrens.length = 1) : ''
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-excel-table') {
        return true
      }
    },
    (item: any) => {
      if (
        ['StarThingERP', 'ram-admin'].includes(
          window.sessionStorage.getItem('authSystemCode'),
        )
      ) {
        const columns = item.prop.columns || []
        for (const val of columns) {
          val.options = []
        }
      }

      if (item.modelValue?.datasource) {
        item.modelValue.datasource = []
        item.modelValue.currentRow = null
      }
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-tabs') {
        return true
      }
    },
    (item: any) => {
      if (_.isNumber(item.modelValue)) {
        item.modelValue = 0
      }
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (item.compName === 'lyy-date-picker') {
        return true
      }
    },
    (item: any) => {
      item.modelValue = ''
    },
  )
  result = filterPageConfig(
    config,
    (item: any) => {
      if (['lyy-cascader', 'lyy-tree-select'].includes(item.compName)) {
        return true
      }
    },
    (item: any) => {
      item.prop.options = []
    },
  )
  return result
}
// // f12设置
// const openDevtools = () => {
//   window.ipcRenderer?.invoke('openDevTools')
// }
// 前进一步

// const forward = () => {
//   LyyCanvas.getInstance().actionForward()
// }
// 返回上一步
// const back = () => {
//   LyyCanvas.getInstance().actionReturn()
// }
// 刷新
// const refresh = () => {
//   window.ipcRenderer?.invoke('tab-reload')
// }

// 设置保存方法
setKeyBoard(SAVE_KEY, ctrlSEvent)
// 设置控制台方法 转到bootstrap 做全局快捷键
// setKeyBoard(F12_KEY, openDevtools)
// 刷新
// setKeyBoard(R_KEY, refresh)
// 返回上一步
// setKeyBoard(BACK_KEY, back)
// 前进一步
// setKeyBoard(FORWARD_KEY, forward)
// 预览
setKeyBoard(P_KEY, preview)
// 删除选中组件
setKeyBoard(DEL_KEY, deleteCurrentComp)

// 监听键盘
onMounted(() => listenKeyBoard())
// 离开时撤销监听
onBeforeUnmount(() => {
  LyyCanvas.getInstance().clearActionList()
  removeAllKeyBoard()
})
const openHistoryModel = () => {
  historyShow.value = true
}
const close = () => {
  historyShow.value = false
}
const { toClipboard } = useClipboard()
const copyPageConfig = async () => {
  try {
    await toClipboard(
      JSON.stringify(_.cloneDeep(LyyCanvas.getInstance().getInitPageConfig())),
    )
    message.success({ content: '复制成功', duration: 2 })
  } catch (error) {
    message.success({ content: '复制失败', duration: 2 })
    console.error(error)
  }
}
</script>

<template>
  <div class="header">
    <div class="main-title">乐摇摇 可视化编辑器</div>
    <div class="button-list">
      <div class="history-box">
        <a-tooltip placement="top">
          <template #title>
            <span>历史版本</span>
          </template>
          <history-outlined @click.stop="openHistoryModel" />
        </a-tooltip>
      </div>
      <img :src="keyboard" class="keyboard-icon" @click="oepnTips" />
      <div class="history-box">
        <a-tooltip placement="top">
          <template #title>
            <span>复制配置代码</span>
          </template>
          <copy-outlined @click.stop="copyPageConfig" class="copy" />
        </a-tooltip>
      </div>
      <a-button
        v-if="showUpgrade"
        class="preview"
        type="primary"
        @click="upGrade"
      >
        <template #icon>
          <TableOutlined />
        </template>
        升级新列表
      </a-button>
      <a-button v-if="showUpgrade" class="preview" @click="upGradeForm">
        <template #icon>
          <!-- <GroupOutlined /> -->
          <ProfileOutlined />
        </template>
        升级新表单
      </a-button>
      <a-button
        class="preview"
        :disabled="props.previewDisabled"
        type="primary"
        @click="preview"
      >
        预览
      </a-button>
      <!-- <a-button class="preview" @click="importModal">导入</a-button> -->
      <a-button @click="goBack">退出</a-button>
    </div>
    <a-modal v-model:open="visible" title="快捷键" @ok="handleOk">
      <p v-for="tip in tipsList" :key="tip.key" class="tip-item">
        <a-tooltip placement="right">
          <template #title>
            <span>{{ tip.tips }}</span>
          </template>
          <span>{{ tip.label }}</span>
        </a-tooltip>
        <span>{{ tip.key }}</span>
      </p>
    </a-modal>
    <HistoryVersion v-model="historyShow" @close="close"></HistoryVersion>
  </div>
</template>

<style lang="less" scoped>
.tip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #828282;
}
.header {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
  .main-title {
    font-weight: 500;
    font-size: 16px;
  }
  .button-list {
    display: flex;
  }
  .preview {
    margin-right: 10px;
    display: inline-flex;
    align-items: center;
  }
  .history-box {
    padding-top: 6px;
    padding-right: 20px;
    cursor: pointer;
  }
  .keyboard-icon {
    height: 16px;
    width: 16px;
    margin-top: 8px;
    margin-right: 20px;
    cursor: pointer;
  }
}
</style>
