<script lang="ts" setup>
import { defineComponent, onMounted, onUnmounted, ref } from 'vue'
import { throttle } from 'lodash'
import { useRoute } from 'vue-router'
import myEmitter from '@/eventEmitter/event-emitter'
import JsPng from './js.png'
import {
  getJsObject,
  deleteJsObject,
  importLogic,
} from '@/request/api/editorRequest'
import {
  getObjectList,
  IJsObject,
  setDataSetList,
  setObject,
} from '@leyaoyao/libs/store/jsobject'
import { setCurJsObject } from '../../Main/JsObject/Store'
import failureRecordPopup from '../../../AppDetailList/components/failureRecordPopup.vue'
import { notification, message } from 'ant-design-vue'
defineComponent({
  name: 'lyy-js-object',
})

const branch = ref(
  JSON.parse(
    localStorage.getItem('branch' + sessionStorage.getItem('authSystemId')),
  ),
)
const enableBranchDevops =
  JSON.parse(localStorage.getItem('userInfo'))?.enableBranchDevops ?? false
const JsObjectList = getObjectList()
// 添加js对象
const addJsObject = throttle(() => {
  myEmitter.emit('addJsObject')
}, 1000)
const route = useRoute()
// 显示状态
const activeKey = ref('')
const selectJsObject = (jsobject: IJsObject) => {
  activeKey.value = jsobject.pageComponentId
  setCurJsObject(jsobject)
  myEmitter.emit('editJsObject')
}
const isActiveKey = (pageComponentId: string) => {
  return activeKey.value === pageComponentId ? { background: '#efefef' } : {}
}
const deleteItem = async (jsobject: IJsObject) => {
  const params = {
    codeBranchId: enableBranchDevops ? branch.value.codeBranchId : null,
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: route.params.id,
    pageComponentId: jsobject.pageComponentId,
    type: 1,
  }
  await deleteJsObject(params)
  await refreshJsObjectList()
  if (JsObjectList.value.length > 0) {
    selectJsObject(JsObjectList.value[0])
  } else {
    myEmitter.emit('openCodeEditor')
  }
}
const refreshJsObjectList = async () => {
  message.loading('加载中...', 0)
  const params = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: route.params.id,
    type: 1,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  const data = await getJsObject(params)
  message.destroy()
  setObject(data.body)
}
const getDataList = async () => {
  const params = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: route.params.id,
    type: 2,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  const data = await getJsObject(params)
  setDataSetList(data.body)
}

// 导入
const failureRecordPopupVisible = ref(false)
const branchValue = ref(
  JSON.parse(
    localStorage.getItem('branch' + sessionStorage.getItem('authSystemId')),
  ),
)
const importJsObject = async (data) => {
  try {
    const params = {
      codeBranchId: enableBranchDevops ? branchValue.value.codeBranchId : null,
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      pageComponentImportId: data.pageComponentImportId,
      pageConfigureInfoId: route.params.id,
      type: 1,
      tempType: data.tempType,
    }
    const res = await importLogic(params)
    if (res && res.body) {
      notification.success({
        message: '提示',
        description: '导入成功',
      })
      refreshJsObjectList()
      failureRecordPopupVisible.value = false
    }
  } catch (error) {
    console.error(error)
  }
}

// 获取列表
onMounted(async () => {
  myEmitter.on('upedateJsobject', async (data) => {
    await refreshJsObjectList()
    await getDataList()
    if (data) {
      const res = JsObjectList.value.find((item) => {
        return item.pageComponentId == data
      })
      selectJsObject(res)
    } else {
      selectJsObject(JsObjectList.value.at(-1))
    }
  })
  try {
    await refreshJsObjectList()
    await getDataList()
  } catch (error) {
    console.log(error, '错误结果')
  }
})
onUnmounted(() => {
  myEmitter.off('upedateJsobject')
})
</script>

<template>
  <div id="js-object-container">
    <div class="js-object-header">
      <h4>JsObject</h4>
      <div>
        <a-button type="text" @click="addJsObject">
          <plus-outlined />
        </a-button>
        <!--        <a-button type="text" @click="failureRecordPopupVisible = true">-->
        <!--          <import-outlined />-->
        <!--        </a-button>-->
      </div>
    </div>
    <div>
      <div
        v-for="jsobject in JsObjectList"
        :key="jsobject.pageComponentId"
        :style="isActiveKey(jsobject.pageComponentId)"
        class="js-item"
        @click="selectJsObject(jsobject)"
      >
        <div class="js-item-left">
          <img :src="JsPng" class="js-icon" />
          <div class="js-object-name">{{ jsobject.name }}</div>
        </div>
        <a-popconfirm
          title="确定要删除吗？"
          ok-text="确定"
          cancel-text="取消"
          @confirm="deleteItem(jsobject)"
        >
          <delete-outlined class="remove-icon" />
        </a-popconfirm>
      </div>
    </div>
    <failureRecordPopup
      v-if="failureRecordPopupVisible"
      v-model="failureRecordPopupVisible"
      :codeBranchValue="branchValue"
      :fastContentType="1"
      :showImport="true"
      @pageComponentImportId="importJsObject"
    />
  </div>
</template>

<style lang="less" scoped>
#js-object-container {
  width: 100%;
  padding-top: 8px;
  height: 100%;

  .js-item {
    display: flex;
    margin: 8px 0;
    height: 28px;
    line-height: 28px;
    padding: 4px;
    cursor: pointer;
    justify-content: space-between;
    align-items: center;

    .js-item-left {
      flex: 1;
      display: inline-flex;
      align-items: center;

      .js-object-name {
        flex: 1;
        // 单行超出省略
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 240px;
      }
    }

    .remove-icon {
      color: red;
      display: none;
      font-size: 12px;
    }
  }

  .js-item:hover {
    background: #eee;

    .remove-icon {
      display: block;
    }
  }

  .js-icon {
    height: 20px;
    width: 20px;
    margin: 0 8px;
  }

  .js-object-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;

    .ant-btn {
      padding: 0 7px;
    }
  }

  .js-object-add {
    cursor: pointer;
  }
}
</style>
