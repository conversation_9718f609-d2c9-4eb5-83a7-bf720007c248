<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  nextTick,
  onMounted,
  computed,
} from 'vue'
import BasicForm from '@/components/Modal/BasicForm/index.vue'
import ErpToolbar from '@/components/Modal/ErpToolbar/index.vue'
import AdvancedForm from '@/components/Modal/AdvancedForm/index.vue'
import NewUIFormModel from '@/components/Modal/newUIFormModal/index.vue'
import BasicDetail from '@/components/Modal/BasicDetail/index.vue'
import BasicFormModal from '@/components/Modal/BasicFormModal/index.vue'
import AdvancedFormModal from '@/components/Modal/AdvancedFormModal/index.vue'

export default defineComponent({
  name: 'comp-group',
  components: {
    BasicForm,
    ErpToolbar,
    AdvancedForm,
    BasicDetail,
    BasicFormModal,
    AdvancedFormModal,
    NewUIFormModel,
  },
})
</script>
<script lang="ts" setup>
import Draggable from 'vuedraggable'
import _ from 'lodash'
import ColumnModal from '@/components/Modal/ColumnModal/index.vue'
import PermissionColumnModal from '@/components/Modal/PermissionColumnModal/index.vue'
import excelColumnModel from '@/components/Modal/excelColumnModal/index.vue'
import EditColumnModal from '@/components/Modal/EditColumnModal/index.vue'
import LyyCanvas from '@/context/canvas'
import CanvasModal from '@/components/Modal/CanvasModal/index.vue'
import CRUD from '@/components/Modal/CRUD/index.vue'
import NewUICRUD from '@/components/Modal/CRUD/newUICRUD.vue'
import GridModal from '@/components/Modal/GridModal/index.vue'
import CompFormItem from '@/Model/formItem'
import LyyBus from '@/bus/bus'
import { Modal, message } from 'ant-design-vue'
import { deleteTemplate } from '@/request/api/editorRequest'
import {
  addAliasNameAnduuid,
  addEntityId,
  resetAliasName,
} from '@/utils/addAliasName'
import { treeFind } from '@leyaoyao/libs/utils/tree-find'
import excelPullDownModal from '@/Model/static/excel-pulldown-modal-model'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import {
  eventManager,
  uuidv4,
  ReactSets,
  isNotResponsive,
  getCurrentPageName,
  getRealH,
} from '@leyaoyao/libs'
import { clearEntityId } from '@leyaoyao/libs/utils/compHash'

const formItemLists = new Set(
  CompFormItem.keys()
    .reduce((pre, key: string) => {
      const config = CompFormItem(key).default
      config.compName == 'lyy-form' ? '' : pre.push(config)
      return pre
    }, [])
    .map((item) => item.compName),
)
const isFeeContainerMap = new Map()
const feeLayOutCompId = ref(null)
const currentFeeConRef = computed(() => {
  return isFeeContainerMap.get(feeLayOutCompId.value)
})
// elCon
const isFeeContainerRef = computed(() => {
  return currentFeeConRef.value ? currentFeeConRef.value.elCon : null
})
const isFeeLayoutRef = computed(() => {
  return currentFeeConRef.value ? currentFeeConRef.value.el : null
})
const feeConClientRect = computed(() => {
  return isFeeContainerRef.value?.getBoundingClientRect() || {}
})
const isFeeConAndIsOtherCon = ref(false) // 在自由容器里面并且在其他的容器里面的时候
const isFeeConResponsive = ref(false)
const margin = ref([5, 5])
const rowHeight = ref(5)
let feeConChildren = reactive([])
const mouseXY = {
  x: null,
  y: null,
}
const props = defineProps<{
  name: string
  list: object[]
}>()
// 选择祖先元素里面是否有某个类名，查找到并返回回来
const hasAncestorWithClass = (element, className) => {
  // 循环遍历当前元素的所有祖先元素
  while (element.parentElement) {
    element = element.parentElement
    // 检查当前祖先元素是否包含指定的 class
    if (element.classList.contains(className)) {
      return element
    }
  }
  // 如果没有找到包含指定 class 的祖先元素，就返回 false
  return undefined
}
const emit = defineEmits(['refreshCompTemplateList'])

const groupCfg = {
  name: 'people',
  pull: 'clone',
  put: false,
}
const modalShow = ref(false)
const permissionModalShow = ref(false)
const excelModalShow = ref(false)
const currentComp = ref({})
const visible = ref(false)
const crudShow = ref(false)
const newUICrudShow = ref(false)
const canvasModalConfig = ref([])
const editModalShow = ref(false)
const gridModalShow = ref(false)

// 重新生成 compId
const generateNewCompId = (element) => {
  const compIds = []
  const compIdMap = {}
  iterateObject(element, compIds, compIdMap)
  return compIdMap
}
const iterateObject = (obj, compIds, compIdMap) => {
  for (const key in obj) {
    if (key === 'compId' && !compIds.includes(obj[key]) && obj[key] != null) {
      compIds.push(obj[key])
      compIdMap[obj[key]] = 'c' + uuidv4()
    }
    if (_.isObject(obj[key])) {
      iterateObject(obj[key], compIds, compIdMap)
    }
    if (_.isArray(obj[key])) {
      iterateArray(obj[key], compIds, compIdMap)
    }
  }
}
const iterateArray = (array, compIds, compIdMap) => {
  if (array.length === 0) return
  for (const item of array) {
    if (_.isObject(item)) {
      iterateObject(item, compIds, compIdMap)
    }
    if (_.isArray(item)) {
      iterateArray(item, compIds, compIdMap)
    }
  }
}

const cloneComp = function (comp) {
  let newComp = addAliasNameAnduuid(_.cloneDeep(comp))
  // 如果是模板内的组件，递归重新生成 compId
  if (['系统区块', '全局区块'].includes(props.name)) {
    // 清除掉区块里面的entityId
    newComp = _.cloneDeep(clearEntityId(comp))
    const newCompIdMap = generateNewCompId(newComp)
    let str = JSON.stringify(newComp)
    for (const key in newCompIdMap) {
      str = str.replaceAll(new RegExp(key, 'g'), newCompIdMap[key])
    }
    currentComp.value = resetAliasName([JSON.parse(str)])
    return currentComp.value
  }
  // 处理布局组件
  if (newComp.compName === 'lyy-row') {
    for (let child of newComp.childrens) {
      child = addAliasNameAnduuid(child)
    }
  }
  if (newComp.compName === 'lyy-search-pro') {
    for (let child of newComp.prop.searchFormList) {
      child = addAliasNameAnduuid(child)
    }
  }
  currentComp.value = reactive(newComp)
  return currentComp.value
}
const dragoverHandler = (e) => {
  eventManager.emit('mouseMoveInCanvas', e)
}
const mouseInFeeCon = (mouseXY) => {
  return (
    feeConClientRect.value?.width &&
    mouseXY.x > feeConClientRect.value.left &&
    mouseXY.x < feeConClientRect.value.right &&
    mouseXY.y > feeConClientRect.value.top &&
    mouseXY.y < feeConClientRect.value.bottom
  )
}
onMounted(() => {
  const canvasContentRef = document.querySelector('#canvasContentRef')
  // canvasContentRef?.removeEventListener('dragover', dragoverHandler)
  // canvasContentRef?.addEventListener('dragover', dragoverHandler)
  // 在整个编辑区域鼠标移到
  eventManager.off('mouseMoveInCanvas')
  eventManager.on('mouseMoveInCanvas', (e) => {
    currentComp.value = currentComp.value?.compId
      ? currentComp.value
      : LyyCanvas.getInstance().getCurrentComp()
    mouseXY.x = e.clientX
    mouseXY.y = e.clientY
    let new_pos = {}
    const index = feeConChildren.findIndex(
      (item) => item.compId === currentComp.value.compId,
    )
    if (mouseInFeeCon(mouseXY)) {
      let reactSets = ReactSets
      if (!isFeeConResponsive.value) {
        // 非响应式布局w宽度需要调整
        reactSets = isNotResponsive
      }
      const wh = reactSets[currentComp.value.compName] || []
      const [w, h] = wh
      currentComp.value.w = w
      // 进行转换reactsSets里面的高度是已30为基础设置的，要转换成rowHeight对应的值
      currentComp.value.h =
        getRealH(rowHeight.value, margin.value[1]) * (h ?? 1)
      const el = isFeeLayoutRef.value.$refs.defaultGridItem
      el.dragging = {
        top: mouseXY.y - feeConClientRect.value.top,
        left: mouseXY.x - feeConClientRect.value.left,
      }
      new_pos = el.calcXY(
        mouseXY.y - feeConClientRect.value.top,
        mouseXY.x - feeConClientRect.value.left,
      )
      currentComp.value.x = new_pos.x
      currentComp.value.y = new_pos.y
      if (index >= 0 && isFeeConAndIsOtherCon.value) {
        feeConChildren.splice(index, 1)
      }
    } else {
      // isFeeContainerRef.value = null
      isFeeContainerMap.delete(feeLayOutCompId.value)
      feeLayOutCompId.value = null
      // feeConClientRect.value = {}
      index >= 0 ? feeConChildren.splice(index, 1) : ''
    }
  })
  // 绑定设置拖拽拖动
  eventManager.off('endDrag')
  eventManager.on('endDrag', () => {
    if (isFeeContainerRef.value && !isFeeConAndIsOtherCon.value) {
      const index = feeConChildren.findIndex(
        (item) => item.compId === currentComp.value.compId,
      )
      index < 0 && feeConChildren.push(currentComp.value)
      const cur = LyyCanvas.getInstance().getCurrentComp()
      LyyCanvas.getInstance().deleteComp(cur.compId, feeLayOutCompId.value)
    }
  })

  // 在自由容器里面拖拽
  eventManager.off('isDragOverFeeContainer')
  eventManager.on('isDragOverFeeContainer', (data) => {
    let container = hasAncestorWithClass(data.event.toElement, 'lyy-container')
    isFeeConAndIsOtherCon.value =
      container && !container.classList?.contains('lyy-fee-container')
        ? true
        : false
    container = null
    dragoverHandler(data.event)
    feeLayOutCompId.value = data.compId
    isFeeConResponsive.value = data.propData.responsive
    rowHeight.value = data.propData.rowHeight
    margin.value = data.propData.margin
    if (!isFeeContainerMap.has(data.compId)) {
      isFeeContainerMap.set(data.compId, data)
    }
    feeConChildren = data.children
  })
})
const startDrag = (a, b, c) => {
  if (props.name === '通用模板') {
    Modal.confirm({
      title: '确定使用该模板么？',
      content: '使用该模板将会替换当前页面配置',
      onOk: () => {
        LyyCanvas.getInstance().setPageConfig([currentComp.value])
        return
      },
      onCancel: () => {
        return
      },
    })
  }
}
const modalList = ref({
  基础表单: {
    compName: 'lyy-form',
    compNameCn: '基础表单',
    comp: 'BasicForm',
    show: false,
  },
  基础表单弹窗: {
    compName: 'lyy-modal',
    compNameCn: '基础表单弹窗',
    comp: 'BasicFormModal',
    show: false,
  },
  高级表单: {
    compName: 'lyy-form',
    compNameCn: '高级表单',
    comp: 'AdvancedForm',
    show: false,
  },
  高级表单弹窗: {
    compName: 'lyy-modal',
    compNameCn: '高级表单弹窗',
    comp: 'AdvancedFormModal',
    show: false,
  },
  新版UI表单: {
    compName: 'lyy-form',
    compNameCn: '新版UI表单',
    comp: 'NewUIFormModel',
    show: false,
  },
  基础详情: {
    compName: 'lyy-form',
    compNameCn: '基础详情',
    comp: 'BasicDetail',
    show: false,
  },
  ERP工具栏: {
    compName: 'lyy-form',
    comp: 'ErpToolbar',
    compNameCn: 'ERP工具栏',
    show: false,
  },
})
const needModalShow = new Set([
  'lyy-table',
  'lyy-permission-table',
  'lyy-excel-table',
  'lyy-edit-table',
  'lyy-modal-selector-input',
  'lyy-pull-down-selector-input',
  'lyy-grid',
  '增删查改',
])
const endDrag = (event) => {
  if (currentComp.value) {
    addEntityId([currentComp.value])
  }
  if (['系统区块', '全局区块'].includes(props.name)) {
    nextTick(() => {
      LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
    })
    return
  }
  if (currentComp.value.compName === 'lyy-table') {
    modalShow.value = true
  }
  if (currentComp.value.compName === 'lyy-permission-table') {
    permissionModalShow.value = true
  }
  if (currentComp.value.compName === 'lyy-excel-table') {
    excelModalShow.value = true
  }
  if (currentComp.value.compName === 'lyy-edit-table') {
    editModalShow.value = true
  }
  if (
    currentComp.value.compName === 'lyy-modal-selector-input' ||
    currentComp.value.compName === 'lyy-pull-down-selector-input'
  ) {
    visible.value = true
    canvasModalConfig.value = currentComp.value.childrens
  }
  if (currentComp.value.compNameCn === '增删查改') {
    crudShow.value = true
  }
  if (currentComp.value.compNameCn === '【新版UI】增删查改') {
    newUICrudShow.value = true
  }
  if (currentComp.value.compName === 'lyy-grid') {
    gridModalShow.value = true
  }
  // try {
  //   console.info('endDrag ====> 更新proxyData:', currentComp.value)
  //   const currentPageName: string = getCurrentPageName()
  //   proxyData.updateProxyData(currentPageName)
  // } catch (error) {
  //   console.error('endDrag ====> 更新proxyData失败 ====>', error)
  // }
  const compNameCnList = Object.keys(modalList.value)
  if (modalList.value[currentComp.value.compNameCn]) {
    modalList.value[currentComp.value.compNameCn].show = true
    return
  }
  if (isFeeContainerRef.value && !isFeeConAndIsOtherCon.value) {
    const curCompId = currentComp.value.compId
    const index = feeConChildren.findIndex((item) => item.compId === curCompId)
    index < 0 && feeConChildren.push(currentComp.value)
    nextTick(() => {
      LyyCanvas.getInstance().deleteComp(
        currentComp.value.compId,
        feeLayOutCompId.value,
      )
      LyyCanvas.getInstance().setCurrentComp(curCompId)
    })
    return
  }
  nextTick(() => {
    LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
  })
}
const okFn = (col, row) => {
  Object.assign(currentComp.value.prop, { columns: col })
  // Object.assign(currentComp.value.modelValue, { datasource: row })
  modalShow.value = false
  permissionModalShow.value = false
  LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
}
const excelOkFn = (col, validRules) => {
  Object.assign(currentComp.value.prop, { columns: col, validRules })
  excelModalShow.value = false
  LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
  addEntityId([currentComp.value])
}
const okCrudFn = (childrens, originData) => {
  crudShow.value = false
  if (currentComp.value.compId) {
    currentComp.value.childrens.length = 0
    currentComp.value.childrens?.push(...childrens)
    Object.assign(currentComp.value, { originData })
    nextTick(() => {
      LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
      addEntityId([currentComp.value])
    })
  } else {
    const currentCompoent = LyyCanvas.getInstance().getCurrentComp()
    currentCompoent.childrens.length = 0
    currentCompoent.childrens?.push(...childrens)
    Object.assign(currentCompoent, {
      originData,
    })
    addEntityId([currentCompoent])
  }
}

const okNewUICrudFn = (childrens) => {
  newUICrudShow.value = false
  if (currentComp.value.compId) {
    currentComp.value.childrens.length = 0
    currentComp.value.childrens?.push(...childrens)
    nextTick(() => {
      LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
      addEntityId([currentComp.value])
    })
  } else {
    const currentCompoent = LyyCanvas.getInstance().getCurrentComp()
    currentCompoent.childrens.length = 0
    currentCompoent.childrens?.push(...childrens)
    addEntityId([currentCompoent])
  }
}
const cancel = () => {
  if (currentComp.value && currentComp.value.originData) {
    // 不需要处理删除组件，只是第二次编辑
  } else {
    LyyCanvas.getInstance().deleteComp(currentComp.value.compId)
  }
}
const editOkFn = (col, row, otherProp) => {
  Object.assign(
    currentComp.value.prop,
    { columns: col },
    { datasource: row },
    otherProp,
  )
  editModalShow.value = false
  LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
  addEntityId([currentComp.value])
}

const gridOkFn = (children) => {
  Object.assign(currentComp.value.prop, { children })
  gridModalShow.value = false
  LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
  addEntityId([currentComp.value])
}

/**
 * @description 通知抽屉组件选中哪个组件
 */
function setSelectedComp(element) {
  LyyBus.$emitUpdateComp('setSelectedComp', undefined, element)
}
const okModalListFn = (comp) => {
  Object.assign(currentComp.value, comp)
  modalList.value[currentComp.value.compNameCn].show = false
  nextTick(() => {
    if (currentComp.value.compName != 'lyy-modal') {
      LyyCanvas.getInstance().setCurrentComp(currentComp.value.compId)
      addEntityId([currentComp.value])
    }
  })
}

/**
 * 删除模板
 */
function handleDelete(element) {
  const pannel = props.name.slice(-2)
  Modal.confirm({
    content: `确定删除【${element.compNameCn}】${pannel}吗?`,
    async onOk() {
      const id = element.pageConfigureTemplateId
      const token = window.sessionStorage.getItem('ramToken') || ''
      const deleteComp = await deleteTemplate(id, {
        'ram-token': token,
        'ram-system': window.editorSystem,
      })
      if (deleteComp.code === '0000000') {
        message.success({ content: '删除成功', duration: 2 })
        emit('refreshCompTemplateList')
      } else {
        message.error({
          content: deleteComp?.message || '操作失败',
          duration: 2,
        })
      }
    },
  })
}
const handleOk = (config) => {
  if (
    currentComp.value.compName === 'lyy-pull-down-selector-input' &&
    currentComp.value.prop?.pulldownChildren?.length === 0
  ) {
    const formEle = _.cloneDeep(
      treeFind(config, (ele) => {
        return ele.compName == 'lyy-form'
      }),
    )
    const tableEle = _.cloneDeep(
      treeFind(config, (ele) => {
        return ele.compName == 'lyy-table'
      }),
    )
    formEle.childrens = [tableEle]
    const newCompIdMap = generateNewCompId(formEle)
    let str = JSON.stringify(formEle)
    for (const key in newCompIdMap) {
      str = str.replaceAll(new RegExp(key, 'g'), newCompIdMap[key])
    }
    const newConfig = resetAliasName([JSON.parse(str)])
    currentComp.value.prop.pulldownChildren = [newConfig]
  }
}
</script>

<template>
  <div class="comp-group-wrap">
    <Draggable
      :list="list"
      :group="groupCfg"
      :sort="false"
      :clone="cloneComp"
      item-key="compId"
      @start="startDrag"
      @end="endDrag"
    >
      <template #item="{ element }">
        <a-button
          size="small"
          :block="true"
          class="comp-group-item"
          :title="element.compNameCn"
          @click="setSelectedComp(element)"
        >
          {{ element.compNameCn }}
          <minus-circle-outlined
            v-if="['系统区块', '全局区块'].includes(props.name)"
            class="delete-icon"
            @click.stop="handleDelete(element)"
          />
        </a-button>
      </template>
    </Draggable>
    <ColumnModal v-model="modalShow" @ok="okFn" @cancel="cancel"></ColumnModal>
    <PermissionColumnModal
      v-model="permissionModalShow"
      @ok="okFn"
      @cancel="cancel"
    ></PermissionColumnModal>
    <excelColumnModel
      v-model="excelModalShow"
      @ok="excelOkFn"
      @cancel="cancel"
    ></excelColumnModel>
    <EditColumnModal
      v-model="editModalShow"
      @ok="editOkFn"
      @cancel="cancel"
    ></EditColumnModal>
    <CRUD v-model="crudShow" @ok="okCrudFn" @cancel="cancel"></CRUD>
    <NewUICRUD
      v-model="newUICrudShow"
      @ok="okNewUICrudFn"
      @cancel="cancel"
    ></NewUICRUD>
    <GridModal
      v-model="gridModalShow"
      @ok="gridOkFn"
      @cancel="cancel"
    ></GridModal>
    <CanvasModal
      v-if="visible"
      v-model:modelValue="visible"
      v-model:config="canvasModalConfig"
      v-on:handleOk="handleOk"
    ></CanvasModal>
    <!-- 遍历弹窗对象 -->
    <template v-for="(modal, key) in modalList" :key="key">
      <component
        :is="modalList[key].comp"
        v-model="modalList[key].show"
        @ok="okModalListFn"
        @cancel="cancel"
      ></component>
    </template>
  </div>
</template>

<style lang="less" scoped>
.comp-group-wrap {
  display: flex;
  flex-wrap: wrap;

  .comp-group-item {
    width: 104px;
    height: 30px;
    margin: 0 4px 8px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;

    :deep(span) {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .delete-icon {
      position: absolute;
      top: -7px;
      right: -7px;
      color: red;
    }
  }
}
</style>
