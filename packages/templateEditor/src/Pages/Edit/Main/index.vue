<template>
  <div class="editor-center">
    <aside>
      <div class="resize"></div>
      <div class="line"></div>
      <section class="left-box">
        <!-- 左侧组件栏 -->
        <Left v-bind="$attrs"></Left>
      </section>
    </aside>
    <!-- 中间组件 -->
    <!-- 逻辑集 -->
    <JsObject v-if="moduleName === 'JsObject'" />
    <!-- 数据集 -->
    <DataSet v-else-if="moduleName === 'DataSet'" />
    <!-- 编辑器 -->
    <Editor v-else v-bind="$attrs" />
  </div>
</template>
<script lang="ts" setup>
import Left from '../Left/index.vue'
import Editor from './Editor/index.vue'
import JsObject from './JsObject'
import DataSet from './DataSet'
import myEmitter from '@/eventEmitter/event-emitter'
import { addObject, getNewObject } from './JsObject/Store'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'

import { addJsObject } from '../../../request/api/editorRequest'
const moduleName = ref('Editor')
const route = useRoute()
const branch = ref(
  JSON.parse(
    localStorage.getItem('branch' + sessionStorage.getItem('authSystemId')),
  ),
)
const enableBranchDevops =
  JSON.parse(localStorage.getItem('userInfo'))?.enableBranchDevops ?? false
onMounted(() => {
  // 新增逻辑集
  myEmitter.on('addJsObject', async () => {
    const newJsObject = getNewObject()
    const params = {
      codeBranchId: enableBranchDevops ? branch.value.codeBranchId : null,
      codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
      pageConfigureInfoId: route.params.id,
      ...newJsObject,
      type: 1,
    }
    message.loading('加载中...', 0)
    const { body } = await addJsObject(params)
    if (!body.isSuccess) {
      return
    }
    addObject({
      pageConfigureInfoId: route.params.id,
      pageComponentId: body.pageComponentId,
      ...newJsObject,
    })
    message.destroy()
    moduleName.value = 'JsObject'
    saveModuleName()
    myEmitter.emit('upedateJsobject')
  })
  // 编辑逻辑集
  myEmitter.on('editJsObject', () => {
    moduleName.value = 'JsObject'
    saveModuleName()
  })
  // 新增数据集
  myEmitter.on('addDataSet', async () => {
    // const newDataSet = getNewDataSet()
    // const data = await addJsObject({
    //   pageConfigureInfoId: route.params.id,
    //   ...newDataSet,
    //   type: 2,
    // })
    // if (!data) {
    //   return
    // }
    // addDataSet({
    //   pageConfigureInfoId: route.params.id,
    //   pageComponentId: data.body,
    //   ...newDataSet,
    // })

    moduleName.value = 'DataSet'
    saveModuleName()
  })
  // 编辑数据集
  myEmitter.on('openEditDataSet', () => {
    moduleName.value = 'DataSet'
    saveModuleName()
  })
  // 打开编辑器
  myEmitter.on('openCodeEditor', () => {
    moduleName.value = 'Editor'
    saveModuleName()
  })
  const saveModuleName = () => {
    sessionStorage.setItem('lyy-editor-moduleName', moduleName.value)
  }
})
onBeforeUnmount(() => {
  myEmitter.off('addJsObject')
  myEmitter.off('editJsObject')
  myEmitter.off('addDataSet')
  myEmitter.off('openEditDataSet')
  myEmitter.off('openCodeEditor')
})
</script>
<style lang="less" scoped>
.editor-center {
  display: flex;
  flex: 1;
}

.editor-right {
  min-width: 280px;
}

aside {
  position: relative;
  align-self: stretch;
  // overflow: hidden;
  flex-shrink: 0;
  /* background-color: antiquewhite; */
}

.resize {
  min-width: 340px;
  height: 16px;
  transform: scaleY(100);
  /* background-color: cadetblue; */
  overflow: scroll;
  resize: horizontal;
  opacity: 0;
}

.line {
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  bottom: 0;
  background-color: royalblue;
  opacity: 0;
  transition: 0.3s;
  pointer-events: none;
}

.resize:hover + .line,
.resize:active + .line {
  opacity: 1;
}

main.canvas {
  flex-grow: 1;
  overflow-x: auto;
  max-height: calc(100vh - 48px);
  border: 1px solid #eee;
  border-top: none;
  border-bottom: none;
  //align-self: stretch;
  //padding: 0 8px;
}

section {
  position: absolute;
  inset: 0 3px 0 0;
  padding: 8px 0 0 0;

  // overflow: auto;
  /* 整个滚动条 */
  &::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 7px;
    /* 对应横向滚动条的宽度 */
    height: 7px;
  }

  /* 滚动条上的滚动滑块 */
  &::-webkit-scrollbar-thumb {
    background-color: #999;
    border-radius: 7px;
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background-color: #fff;
    border-radius: 7px;
    height: 7px;
    width: 0;
  }
}

.resize-left {
  transform: scale(-1, 100);
  min-width: 250px;
}

.resize-left + .line {
  left: 0;
  right: auto;
}

.resize-left ~ section {
  inset: 0 0 0 2px;
}
</style>
