<script lang="ts" setup>
import { computed, defineComponent, ref } from 'vue'
import { getCurJsObject, updateCurName } from '../Store'
import JavascriptIcon from './images/dataSet.png'
defineComponent({
  name: 'lyy-edit-js-object',
})
const curJsObject = ref(getCurJsObject())
const isFoucus = ref(false)
const myInput = ref<HTMLInputElement>()
const onblur = () => {
  isFoucus.value = false
  updateCurName()
}
const onfoucus = () => {
  isFoucus.value = true
}
const domFoucus = () => {
  myInput.value?.focus()
}
const classname = computed(() => {
  return isFoucus.value ? 'foucus' : ''
})
const setData = (data) => {
  curJsObject.value = data
}
defineExpose({
  setData,
})
</script>

<template>
  <div class="input-wrap">
    <img :src="JavascriptIcon" class="js-icon" />
    <a-input
      ref="myInput"
      :class="[classname, 'js-object-name-input']"
      v-model:value="curJsObject.name"
      :bordered="false"
      @blur="onblur"
      @focus="onfoucus"
    ></a-input>
    <edit-outlined @click="domFoucus" />
  </div>
</template>

<style lang="scss" scoped>
.input-wrap {
  display: flex;
  align-items: center;
}
.js-object-name-input {
  width: 120px;
}
.foucus {
  background: #eee;
  height: 32px;
}
.js-icon {
  height: 20px;
  width: 20px;
}
</style>
