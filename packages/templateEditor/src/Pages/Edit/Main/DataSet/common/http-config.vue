<template>
  <a-form :model="formState" class="http-config">
    <a-row>
      <a-col :span="11">
        <a-form-item label="请求处理" name="requestInterceptorBody">
          <lyy-editor
            ref="reqRefs"
            :modelValue="formState.requestInterceptorBody"
            :language="language"
            @update:modelValue="requestInterceptorBodyFn"
            :prop="{
              field: 'requestInterceptorBody',
              language: 'JAVASCRIPT',
              style: {
                height: '200px',
              },
            }"
          ></lyy-editor>
          <tip>函数签名：(config, LYY) => config</tip>
        </a-form-item>
      </a-col>
      <a-col :span="11" :offset="1">
        <a-form-item label="返回体处理" name="responseInterceptorBody">
          <lyy-editor
            ref="resRefs"
            :modelValue="formState.responseInterceptorBody"
            @update:modelValue="responseInterceptorBodyFn"
            :language="language"
            :prop="{
              field: 'responseInterceptorBody',
              language: 'JAVASCRIPT',
              style: {
                height: '200px',
              },
            }"
          ></lyy-editor>
          <tip>函数签名: (res, LYY) => res</tip>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>
<script lang="ts" setup>
import { defineProps, reactive, ref } from 'vue'

const language = 'JAVASCRIPT'
const reqRefs = ref(null)
const resRefs = ref(null)
interface Payloads {
  key?: string
  source: string
  sourceKey?: string
}
interface Icondition {
  exp: string
  payloads: Payloads[]
}
interface IrequestConfig {
  url: string
  method: string
  dataType: string
  condition: Icondition
}
const props = defineProps<{
  requestConfig: IrequestConfig
}>()
const formState = reactive({
  requestInterceptorBody:
    props.requestConfig.option.interceptors.requestInterceptor
      ?.replace(
        `(config, LYY) => {
  `,
        '',
      )
      ?.replace(
        `return config;
  }`,
        '',
      ),
  responseInterceptorBody:
    props.requestConfig.option.interceptors.responseInterceptor
      ?.replace(
        `(res, LYY) => {
  `,
        '',
      )
      ?.replace(
        `return res;
  }`,
        '',
      ),
})
const requestInterceptorBodyFn = (code) => {
  if (code === undefined) return
  const tpl = `(config, LYY) => {
  ${code}
  return config;
  }`
  props.requestConfig.option.interceptors.requestInterceptor = tpl
}
const responseInterceptorBodyFn = (code) => {
  if (code === undefined) return
  let tpl = `(res, LYY) => {
  ${code}
  return res;
  }`
  props.requestConfig.option.interceptors.responseInterceptor = tpl
}
</script>
<style lang="less">
.http-config .ant-form-item {
  margin-bottom: 10px;
}
</style>
