<template>
  <div class="request-custom-option-components">
    <!-- 请求自定义配置组件  -->
    <a-row :gutter="20">
      <a-col :span="8" class="item">
        <a-form-item-rest name="jsonPath">
          <div class="label">取值字段</div>
          <div class="value">
            <a-input
              v-model:value="customOption.jsonPath"
              :maxlength="200"
              placeholder="例如data"
            />
          </div>
        </a-form-item-rest>
      </a-col>
      <a-col :span="8" class="item">
        <a-form-item-rest name="loading">
          <div class="label">显示加载中</div>
          <div class="value">
            <a-switch
              v-model:checked="customOption.loading"
              :checked-value="true"
              :un-checked-value="false"
            ></a-switch>
          </div>
        </a-form-item-rest>
      </a-col>
      <a-col :span="8" class="item">
        <a-form-item-rest name="loading">
          <div class="label">解析成json</div>
          <div class="value">
            <a-switch
              v-model:checked="customOption.parse"
              :checked-value="true"
              :un-checked-value="false"
            ></a-switch>
          </div>
        </a-form-item-rest>
      </a-col>
    </a-row>
    <a-row :gutter="20">
      <a-col :span="8" class="item">
        <a-form-item-rest name="stopFailFeedback">
          <div class="label">关闭默认失败处理</div>
          <div class="value">
            <a-switch
              v-model:checked="customOption.stopFailFeedback"
              :checked-value="true"
              :un-checked-value="false"
            />
          </div>
        </a-form-item-rest>
      </a-col>
      <a-col :span="8" class="item">
        <a-form-item-rest name="unhandled">
          <div class="label">返回原始请求结果</div>
          <div class="value">
            <a-switch
              v-model:checked="customOption.unhandled"
              :checked-value="true"
              :un-checked-value="false"
            />
          </div>
        </a-form-item-rest>
      </a-col>

    </a-row>
    <a-row :gutter="20">
      <!-- 成功弹窗 -->
      <a-col :span="12" class="item">
        <a-form-item-rest name="successfulFeedback">
          <div class="label">成功请求弹窗</div>
          <div class="value">
            <a-switch
              v-model:checked="hasSuccessFulFeedback"
              :checked-value="true"
              :un-checked-value="false"
              checked-children="开启"
              un-checked-children="关闭"
              @click="changeFeedback('success')"
            />
            <a-popover
              class="edit-box"
              placement="bottomLeft"
              v-if="hasSuccessFulFeedback"
            >
              <template #content>
                <div class="box-value">
                  <div class="item-label">弹窗类型</div>
                  <div class="item-value">
                    <a-radio-group
                      v-model:value="customOption.successfulFeedback.type"
                    >
                      <a-radio
                        v-for="item in modalType"
                        :key="item.key"
                        :value="item.key"
                      >
                        {{ item.name }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">信息状态</div>
                  <div class="item-value">
                    <a-radio-group
                      v-model:value="customOption.successfulFeedback.status"
                    >
                      <a-radio
                        v-for="item in getModalOptions.typeList"
                        :key="item.key"
                        :value="item.key"
                      >
                        {{ item.name }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                </div>
                <div
                  class="box-value"
                  v-if="getModalOptions.placementList.length > 0"
                >
                  <div class="item-label">出现位置</div>
                  <div class="item-value">
                    <a-radio-group
                      v-model:value="customOption.successfulFeedback.placement"
                    >
                      <a-radio
                        v-for="item in getModalOptions.placementList"
                        :key="item.key"
                        :value="item.key"
                      >
                        {{ item.name }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">停留时间</div>
                  <div class="item-value">
                    <a-input-number
                      v-model:value="customOption.successfulFeedback.duration"
                      :min="0"
                      :max="10"
                    />
                    <span class="unit">s</span>
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">信息标题</div>
                  <div class="item-value">
                    <a-input
                      v-model:value="customOption.successfulFeedback.title"
                      placeholder="请输入信息标题"
                    />
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">信息内容</div>
                  <div class="item-value">
                    <a-input
                      v-model:value="customOption.successfulFeedback.message"
                      placeholder="请输入信息内容"
                    />
                  </div>
                </div>
              </template>
              <span type="link" class="ant-btn-link">配置弹窗信息</span>
            </a-popover>
          </div>
        </a-form-item-rest>
      </a-col>
      <!-- 失败弹窗 -->
      <a-col :span="12" class="item">
        <a-form-item-rest name="failedFulFeedback">
          <div class="label">失败请求弹窗</div>
          <div class="value">
            <a-switch
              v-model:checked="hasFailedFeedback"
              :checked-value="true"
              :un-checked-value="false"
              checked-children="开启"
              un-checked-children="关闭"
              @click="changeFeedback('failed')"
            />
            <a-popover
              class="edit-box"
              placement="bottomLeft"
              v-if="hasFailedFeedback"
            >
              <template #content>
                <div class="box-value">
                  <div class="item-label">弹窗类型</div>
                  <div class="item-value">
                    <a-radio-group
                      v-model:value="customOption.failedFeedback.type"
                    >
                      <a-radio
                        v-for="item in modalType"
                        :key="item.key"
                        :value="item.key"
                      >
                        {{ item.name }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">信息状态</div>
                  <div class="item-value">
                    <a-radio-group
                      v-model:value="customOption.failedFeedback.status"
                    >
                      <a-radio
                        v-for="item in getFailedModalOptions.typeList"
                        :key="item.key"
                        :value="item.key"
                      >
                        {{ item.name }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                </div>
                <div
                  class="box-value"
                  v-if="getFailedModalOptions.placementList.length > 0"
                >
                  <div class="item-label">出现位置</div>
                  <div class="item-value">
                    <a-radio-group
                      v-model:value="customOption.failedFeedback.placement"
                    >
                      <a-radio
                        v-for="item in getFailedModalOptions.placementList"
                        :key="item.key"
                        :value="item.key"
                      >
                        {{ item.name }}
                      </a-radio>
                    </a-radio-group>
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">停留时间</div>
                  <div class="item-value">
                    <a-input-number
                      v-model:value="customOption.failedFeedback.duration"
                      :min="0"
                      :max="10"
                    />
                    <span class="unit">s</span>
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">信息标题</div>
                  <div class="item-value">
                    <a-input
                      v-model:value="customOption.failedFeedback.title"
                      placeholder="请输入信息标题"
                    />
                  </div>
                </div>
                <div class="box-value">
                  <div class="item-label">信息内容</div>
                  <div class="item-value">
                    <a-input
                      v-model:value="customOption.failedFeedback.message"
                      placeholder="请输入信息内容"
                    />
                  </div>
                </div>
              </template>
              <span type="link" class="ant-btn-link">配置弹窗信息</span>
            </a-popover>
          </div>
        </a-form-item-rest>
      </a-col>
    </a-row>
    <a-row :gutter="20">
      <a-col :span="8" class="item number">
        <a-form-item-rest name="nextEventsDelay">
          <div class="label">下一个事件延迟</div>
          <div class="value">
            <a-input-number
              v-model:value="customOption.nextEventsDelay"
              :min="0"
              :max="60000"
            />
            <span class="unit">ms</span>
          </div>
        </a-form-item-rest>
      </a-col>
      <a-col :span="8" class="item">
        <a-form-item-rest name="codeValue">
          <div class="label">成功码的值</div>
          <div class="value">
            <simpleInput
              v-model:modelValue="customOption.codeValue"
              :maxlength="20"
            />
          </div>
        </a-form-item-rest>
      </a-col>
      <a-col :span="8" class="item">
        <div class="label">成功码的key</div>
        <div class="value">
          <a-input v-model:value="customOption.codeKey" :maxlength="20" />
        </div>
      </a-col>
      <a-col :span="8" class="item">
        <div class="label">消息的key</div>
        <div class="value">
          <a-input v-model:value="customOption.messageKey" :maxlength="20" />
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { requestModalType as modalType } from '../config'
import type { CustomOption } from '../interface/request'
import simpleInput from './simple-input.vue'

const props = defineProps<{
  modelValue: CustomOption
}>()

const emit = defineEmits(['update:modelValue'])

const defaultFeedbackData = reactive({
  type: 'message',
  duration: 2,
  placement: 'topRight',
  status: 'success',
  title: '',
  message: '',
})

const hasSuccessFulFeedback = ref<boolean>(false)
const hasFailedFeedback = ref<boolean>(false)

const customOption = computed({
  get() {
    init()
    return props.modelValue
  },
  set(data) {
    emit('update:modelValue', data)
  },
})

const init = () => {
  const { successfulFeedback, failedFeedback } = props.modelValue ?? {}
  hasSuccessFulFeedback.value = !!successfulFeedback
  hasFailedFeedback.value = !!failedFeedback
}

const changeFeedback = (type: string) => {
  switch (type) {
    case 'success':
      customOption.value.successfulFeedback = hasSuccessFulFeedback.value
        ? reactive({ ...defaultFeedbackData })
        : null
      break
    case 'failed':
      customOption.value.failedFeedback = hasFailedFeedback.value
        ? reactive({ ...defaultFeedbackData, status: 'error' })
        : null
      break
    default:
      break
  }
}

const getModalOptions = computed(() => {
  const data = modalType.find(
    (item) => item.key === customOption.value.successfulFeedback?.type,
  )
  return data
    ? data
    : {
        placementList: [],
        typeList: [],
      }
})
const getFailedModalOptions = computed(() => {
  const data = modalType.find(
    (item) => item.key === customOption.value.failedFeedback?.type,
  )
  return data
    ? data
    : {
        placementList: [],
        typeList: [],
      }
})
onMounted(() => {
  init()
})
</script>

<style scoped lang="less">
.request-custom-option-components {
  .item {
    display: flex;
    justify-content: flex-start;
    align-content: center;
    align-items: baseline;
    margin-bottom: 14px;
    .ant-btn-link {
      margin-left: 20px;
      cursor: pointer;
    }
    &.number {
      .value {
        display: flex;
        justify-content: flex-start;
        align-content: center;
        align-items: baseline;
        :deep(.ant-input-number) {
          width: 100%;
        }
        .unit {
          margin-left: 10px;
        }
      }
    }
    &.box {
      .value {
        border: solid 1px #d9d9d9;
        border-radius: 2px;
        padding: 4px 11px;
        .unit {
          margin-left: 10px;
        }
        .box-value {
          display: flex;
          justify-content: flex-start;
          align-content: center;
          align-items: center;
          vertical-align: middle;
          margin-bottom: 12px;
          &:last-child {
            margin-bottom: 0;
          }
          .item-label {
            margin-right: 10px;
            width: 100px;
            text-align: right;
          }
          .item-value {
            flex: 1;
          }
        }
      }
    }
    .label {
      width: 120px;
      text-align: right;
      margin-right: 10px;
    }
    .value {
      flex: 1;
    }
  }
}
</style>
