<template>
  <div class="payloads-components action-common-components">
    <a-form-item
      :label="props.label"
      :name="['source', 'sourceKey', 'key', 'value', 'decompose']"
    >
      <div class="item-box" v-for="(item, index) in payloads" :key="index">
        <a-form-item-rest name="source">
          <div class="item">
            <div class="label">数据来源:</div>
            <div class="value">
              <a-input
                v-model:value="payloads[index].source"
                @change="updateValue"
                placeholder="source"
              />
            </div>
          </div>
        </a-form-item-rest>
        <a-form-item-rest name="sourceKey">
          <div class="item">
            <div class="label">字段值:</div>
            <div class="value">
              <a-input
                v-model:value="payloads[index].sourceKey"
                @change="updateValue"
                placeholder="sourceKey"
              />
            </div>
          </div>
        </a-form-item-rest>
        <a-form-item-rest name="key">
          <div class="item">
            <div class="label">赋值key:</div>
            <div class="value">
              <a-input
                v-model:value="payloads[index].key"
                @change="updateValue"
                placeholder="key"
              />
            </div>
          </div>
        </a-form-item-rest>
        <a-form-item-rest name="value">
          <div class="item">
            <div class="label">固定值:</div>
            <div class="value">
              <a-input
                v-model:value="payloads[index].value"
                @change="updateValue"
                placeholder="value"
              />
            </div>
          </div>
        </a-form-item-rest>
        <a-form-item-rest name="decompose">
          <div class="item">
            <div class="label">取值解构:</div>
            <div class="value">
              <a-switch
                v-model:checked="payloads[index].decompose"
                :checked-value="true"
                :un-checked-value="false"
                @change="updateValue"
              >
                <template #checkedChildren><CheckOutlined /></template>
                <template #unCheckedChildren><CloseOutlined /></template>
              </a-switch>
            </div>
          </div>
        </a-form-item-rest>
        <a-form-item-rest>
          <div class="item del">
            <a-tooltip placement="top" title="删除" @click="delPayload(index)">
              <DeleteOutlined class="del del-icon" />
            </a-tooltip>
          </div>
        </a-form-item-rest>
      </div>
      <a-form-item-rest>
        <a-button ghost type="primary" @click="addPayload">
          <PlusOutlined />
          添加
        </a-button>
      </a-form-item-rest>
    </a-form-item>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import {
  PlusOutlined,
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'

const emit = defineEmits(['update:modelValue', 'update'])

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  label: {
    type: String,
    default: '取值配置',
  },
})

const payloadsItem = reactive({
  sourceKey: '',
  key: '',
  source: '',
  value: '',
  decompose: false,
})

const payloads = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})

const addPayload = () => {
  payloads.value.push({
    ...payloadsItem,
  })
  updateValue()
}

const delPayload = (index: number) => {
  payloads.value.splice(index, 1)
  updateValue()
}

const updateValue = () => {
  emit(
    'update:modelValue',
    [...payloads.value].map((item) => {
      try {
        const { value } = item
        return {
          ...item,
          value: JSON.parse(value),
        }
      } catch {
        return item
      }
    }),
  )
}
</script>

<style scoped lang="less">
@import '../../style';
.payloads-components {
  .item-box {
    display: flex;
    justify-content: flex-start;
    //vertical-align: middle;
    //align-content: center;
    //align-items: center;
    flex-wrap: wrap;
    .item {
      display: flex;
      justify-content: flex-start;
      vertical-align: middle;
      align-content: center;
      align-items: center;
      margin-bottom: 12px;
      &.switch {
        flex: none;
        width: 140px;
      }
      .label {
        width: 80px;
        text-align: right;
        margin-right: 8px;
      }
      .value {
        flex: 1;
      }
      &.del {
        flex: 1;
        width: 80px;
        color: #ff4d4f;
        padding-left: 20px;
      }
    }
  }
}
</style>
