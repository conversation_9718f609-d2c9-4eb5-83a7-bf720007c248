import dayjs from 'dayjs'
import { ref } from 'vue'
// 系统调试日志
export const SystemLog = ref<
  { message: string; logTime: string; id: number; logTarget: string }[]
>([])

// 劫持日志
let id = 0
export const getLog = (fn: () => void, curJsObject: any, fnName: string) => {
  const originalConsoleLog = console.log
  console.log = function (message) {
    // consoleOutput += message + '\n'
    SystemLog.value.push({
      message: message,
      id: id++,
      logTime: dayjs().format('HH:mm:ss'),
      logTarget: curJsObject.value.name + '.' + fnName,
    })
    originalConsoleLog.apply(console, arguments) // 调用原始的 console.log
  }
  fn()
  console.log = originalConsoleLog
}
