// 返回光标是否在语法糖内
export function isCursorBetweenBraces(editor): boolean {
  const position = editor.getPosition()
  if (!position) {
    // 如果无法获取光标位置，返回 false
    return false
  }

  const model = editor.getModel()
  if (!model) {
    // 如果无法获取编辑器的模型，返回 false
    return false
  }

  const value = editor.getValue()
  const cursorIndex = model.getOffsetAt(position)

  const startToken = '{{'
  const endToken = '}}'

  const start = value.indexOf(startToken)
  const end = value.indexOf(endToken)

  return start >= 0 && end >= 0 && cursorIndex >= start && cursorIndex <= end
}
// 检查系统语法
export function systemCheck(editorInst) {
  if (isCursorBetweenBraces(editorInst)) {
    editorInst?.updateOptions({
      suggestOnTriggerCharacters: true,
      suggest: {
        showDeprecated: true,
        showEnumMembers: true,
        showEnums: true,
        showEvents: true,
        showFields: true,
        showFiles: true,
        showFolders: true,
        showFunctions: true,
        showIcons: true,
        showInlineDetails: true,
        showInterfaces: true,
        showIssues: true,
        showKeywords: true,
        showMethods: true,
        showModules: true,
        showOperators: true,
        showProperties: true,
        showReferences: true,
        showSnippets: true,
        showStatusBar: true,
        showStructs: true,
        showTypeParameters: true,
        showUnits: true,
        showUsers: true,
        showValues: true,
        showVariables: true,
        showWords: true,
        shareSuggestSelections: true,
        showClasses: true,
        showColors: true,
        showConstants: true,
        showConstructors: true,
      },
    }) // 在其他地方禁用代码提示
  } else {
    editorInst?.updateOptions({
      suggestOnTriggerCharacters: false,
      suggest: {
        showDeprecated: false,
        showEnumMembers: false,
        showEnums: false,
        showEvents: false,
        showFields: false,
        showFiles: false,
        showFolders: false,
        showFunctions: false,
        showIcons: false,
        showInlineDetails: false,
        showInterfaces: false,
        showIssues: false,
        showKeywords: false,
        showMethods: false,
        showModules: false,
        showOperators: false,
        showProperties: false,
        showReferences: false,
        showSnippets: false,
        showStatusBar: false,
        showStructs: false,
        showTypeParameters: false,
        showUnits: false,
        showUsers: false,
        showValues: false,
        showVariables: false,
        showWords: false,
        shareSuggestSelections: false,
        showClasses: false,
        showColors: false,
        showConstants: false,
        showConstructors: false,
      },
    }) // 在其他地方禁用代码提示
  }
}
