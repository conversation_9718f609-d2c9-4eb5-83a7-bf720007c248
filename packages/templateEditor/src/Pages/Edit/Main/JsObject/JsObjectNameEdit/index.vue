<script lang="ts" setup>
import { computed, defineComponent, ref } from 'vue'
import { getCurJsObject, updateCurJsObjectName } from '../Store'
import JavascriptIcon from './images/javascript.png'
defineComponent({
  name: 'lyy-edit-js-object',
})
const curJsObject = getCurJsObject()
console.log(curJsObject, '当前选中的js对象')
const isFoucus = ref(false)
const myInput = ref<HTMLInputElement>()
const onblur = () => {
  isFoucus.value = false
  updateCurJsObjectName()
}
const onfoucus = () => {
  isFoucus.value = true
}
const domFoucus = () => {
  myInput.value?.focus()
}
const classname = computed(() => {
  return isFoucus.value ? 'foucus' : ''
})
</script>

<template>
  <div class="input-wrap">
    <img :src="JavascriptIcon" class="js-icon" />
    <a-input
      ref="myInput"
      :class="[classname, 'js-object-name-input']"
      v-model:value="curJsObject.name"
      :bordered="false"
      @blur="onblur"
      @focus="onfoucus"
    ></a-input>
    <edit-outlined @click="domFoucus" />
  </div>
</template>

<style lang="scss" scoped>
.input-wrap {
  display: flex;
  align-items: center;
}
.js-object-name-input {
  width: 120px;
}
.foucus {
  background: #eee;
  height: 32px;
}
.js-icon {
  height: 20px;
  width: 20px;
}
</style>
