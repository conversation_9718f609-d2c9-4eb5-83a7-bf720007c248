<script setup lang="ts">
import {
  defineComponent,
  onBeforeMount,
  onMounted,
  reactive,
  ref,
  provide,
} from 'vue'
import Header from './Header/index.vue'
import { usePageConfig } from '@/hooks/use-current-comp'
import Main from './Main/index.vue'
import { cloneDeep } from 'lodash'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
import LyyCanvas from '@/context/canvas'
import LyyContainerModel from '@/Model/layoutItem/lyy-container-model'
import lyyBus from '@/bus/bus'
import {
  getDefaultHttp,
  getEditorJson,
  getJsObject,
} from '../../request/api/editorRequest'
import { setObject, setDataSetList } from '@leyaoyao/libs/store/jsobject'
import { Modal, message } from 'ant-design-vue'
defineComponent({
  name: 'editor-layout',
})
const pageConfig = usePageConfig()
const router = useRouter()
const show = ref(false)
const isInit = ref(false)
const previewDisabled = ref(false)
const containerModal = addAliasNameAnduuid(cloneDeep(LyyContainerModel))
const branchValue = ref(
  JSON.parse(
    localStorage.getItem('branch' + localStorage.getItem('authSystemId')),
  ),
)
// 刷新界面 配合客户端使用
window.ipcRenderer?.on('reload', () => {
  show.value = true
  router.go(0)
})
const isEdit = ref(true)

provide('isEdit', isEdit)

window.lyyIsEditerEdit = true
onMounted(async () => {
  const { id } = useRoute().params
  const params = {
    id: id,
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    codeBranchName: branchValue.value?.label ?? '',
    isRemote: branchValue.value?.isRemote ?? '',
  }
  const authSystemId = sessionStorage.getItem('authSystemId')
  const token = window.sessionStorage.getItem('ramToken') || ''
  // 请求全局http配置
  const defaultHttp = await getDefaultHttp(
    { authSystemId },
    {
      'ram-token': token,
      'ram-system': window.editorSystem,
    },
  )
  if (defaultHttp) {
    try {
      let config =
        typeof defaultHttp.body.envStr == 'string'
          ? JSON.parse(defaultHttp.body.envStr)
          : defaultHttp.body.envStr
      try {
        config = Object.assign(
          config,
          JSON.parse(localStorage['localConfig' + sessionStorage.authSystemId]),
        )
      } catch {
        // console.log(error)
      }
      window.__httpConfig__.setHttpConfig(config)
    } catch (error) {
      console.log('edit index 配置 http失败 ======>', error)
    }
  }

  //请求页面json，根据url的id
  // const configRes = await getEditorJson(id, {
  //   'ram-token': token,
  //   'ram-system': window.editorSystem,
  // })
  const promisePageJson = getEditorJson(params, {
    'ram-token': token,
    'ram-system': window.editorSystem,
  })
  const params1 = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: id,
    type: 1,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  const params2 = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: id,
    type: 2,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  message.loading('加载中...', 0)
  const jsObjectJson = getJsObject(params1)
  const dataSetListJson = getJsObject(params2)
  const [configRes, jsObject, dataSetList] = await Promise.all([
    promisePageJson,
    jsObjectJson,
    dataSetListJson,
  ])
  console.log(jsObject, '=====我是请求后的数据')
  message.destroy()
  const enableBranchDevops =
    JSON.parse(localStorage.getItem('userInfo'))?.enableBranchDevops ?? false
  if (enableBranchDevops && branchValue.value && branchValue.value.isRemote) {
    Modal.confirm({
      title: '警告⚠️',
      content: '您目前选中的是远程分支，仅支持查看，不允许修改',
      okText: '确认',
      cancelText: '取消',
    })
    previewDisabled.value = true
  }
  setObject(jsObject.body)
  if (dataSetList.body) {
    setDataSetList(dataSetList.body)
  }
  const pgc = []
  if (configRes?.body?.pageStr && configRes?.body?.pageStr !== '{}') {
    LyyCanvas.getInstance().setPageConfig(JSON.parse(configRes.body.pageStr))
  } else {
    pgc.push(containerModal)
    LyyCanvas.getInstance().setPageConfig(pgc)
    // proxyData.updateProxyData(id)
  }
  lyyBus.$emitUpdateComp('initPageConfig')
  isInit.value = true
})

onBeforeRouteLeave(() => {
  LyyCanvas.getInstance().setPageConfig([])
})
</script>

<template>
  <div class="editor-container" v-if="!show">
    <!-- 顶部工作栏 -->
    <Header :previewDisabled="previewDisabled"></Header>
    <Main
      :pageConfig="pageConfig"
      wrapId="canvas-container"
      :isCanvasModal="false"
      v-if="isInit"
    ></Main>
  </div>
  <a-spin v-else size="large" class="edit-loading" />
</template>

<style lang="less" scoped>
.edit-loading {
  position: absolute;
  top: 50%;
  left: 50%;
}

.editor-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
}
</style>
<style lang="less">
body {
  margin: 0;
}

#app {
  height: 100vh;
}

#app.ipcRenderer {
  height: calc(100% - 60px);
}

.scroll-beautify {
  /* 整个滚动条 */
  &::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 7px;
    /* 对应横向滚动条的宽度 */
    height: 7px;
  }

  /* 滚动条上的滚动滑块 */
  &::-webkit-scrollbar-thumb {
    background-color: #999;
    border-radius: 7px;
  }

  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background-color: #fff;
    border-radius: 7px;
    height: 7px;
    width: 0;
  }
}
</style>
