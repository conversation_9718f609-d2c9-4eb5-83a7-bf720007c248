<script lang="ts" setup>
import {
  defineComponent,
  onMounted,
  nextTick,
  onBeforeUnmount,
  ref,
  reactive,
  provide,
} from 'vue'
import { useCurrentComp } from '@/hooks/use-current-comp'
import LyyCanvas from '@/context/canvas'
import lyyBus from '@/bus/bus'

import { eventManager } from '@leyaoyao/libs'
import { IElement } from '@leyaoyao/render-template/src/global'
import LyyRender from '@leyaoyao/render-template'
defineComponent({
  name: 'editor-canvas',
})
const props = defineProps<{
  pageConfig: Array<any>
  wrapId: string
}>()
const currentComp = useCurrentComp()

const compTemplateList = ref([])

const componentList = ref({})

const setContainer = () => {
  const topComp = props.pageConfig[0]
  const { compId, compName } = topComp ?? {}
  if (compName === 'lyy-container') {
    LyyCanvas.getInstance().setCurrentComp(compId)
  }
}

onMounted(() => {
  // 每次打开弹窗要初始化toolbar，以免组件名称展示错误
  LyyCanvas.getInstance().guide.boxInit()
  LyyCanvas.getInstance().guide.createGuide(props.wrapId)
})

lyyBus.$onUpdateComp('initPageConfig', () => {
  nextTick(() => {
    setContainer()
  })
})

/*
 * 拖动组件更新element事件
 * */
eventManager.on('updateElements', (element: IElement) => {
  try {
    if (currentComp.value) {
      const { childrens } = currentComp.value
      if (childrens && childrens.length > 0) {
        for (const child of childrens) {
          if (child.compId === element.compId) {
            child.childrens = [...(element.childrens ?? [])]
          }
        }
        // console.log('update', {
        //   ...currentComp.value,
        //   childrens: childrens,
        // })
        LyyCanvas.getInstance().changeCompConfig({
          ...currentComp.value,
          childrens: childrens,
        })
      } else {
        LyyCanvas.getInstance().changeCompConfig(currentComp.value)
      }
    }
  } catch (error) {
    console.error('updateElements error', error)
  }
})

// 弹窗关闭时，重新执行，解决toolbar 组件名展示错误问题
lyyBus.$onUpdateComp('initGuideBox', () => {
  LyyCanvas.getInstance().guide.boxInit()
})
const pageName = sessionStorage.getItem('tem-edit-pageName')

// 页面配置
</script>

<template>
  <div class="canvas-container" :id="props.wrapId">
    <div class="canvas-header">
      <div>
        {{ pageName ? pageName : '' }}
        {{ pageName && currentComp ? '-' : '' }}
        {{ currentComp ? currentComp.compNameCn : '' }}
        {{
          currentComp &&
          (currentComp.entityId ? currentComp.entityId : currentComp.compId)
        }}
      </div>
    </div>
    <div class="canvas-content" id="canvasContentRef">
      <lyy-pc-render
        :pageConfig="props.pageConfig"
        v-if="props.pageConfig.length > 0"
        v-bind="$attrs"
      />
           <!-- <pc-render-template :elements="props.pageConfig" v-bind="$attrs" /> -->
    </div>
  </div>
</template>

<style lang="less" scoped>
.canvas-container {
  flex: 1;
  background-color: #f7f7f9;
  display: flex;
  height: 100%;
  flex-direction: column;
  overflow: visible;
  .canvas-header {
    //height: 40px;
    //line-height: 40px;
    padding: 0 12px;
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
  }
  .canvas-content {
    // margin: 12px;
    background-color: #f7f7f9;
    flex: 1;
    .comp-wrap {
      position: relative;
    }
    .Draggable {
      position: relative;
    }
    :deep(.draggable-wrap) {
      height: 100%;
      margin-left: 1px;
    }
  }
}
</style>
