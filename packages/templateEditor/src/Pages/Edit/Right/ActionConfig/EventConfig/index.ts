// 组件对应事件列表映射表
import { eventConfig } from '../interface'
import EventType from '@/enum/event-type'

type EventTypeKey = keyof typeof EventType
const defineEvent: Partial<Record<EventTypeKey, any>> = {}
Object.keys(EventType).forEach((key: string) => {
  defineEvent[key] = {
    event: key,
    description: EventType[key],
    children: [],
  }
})

// 表单组件
const formComponents: Record<string, eventConfig[]> = {
  // 验证码 https://leyaoyao.yuque.com/vbutb7/qw48a7/aphpek_fhcnxd
  'lyy-auth-code': [defineEvent.click, defineEvent.update],
  // 按钮
  'lyy-button': [defineEvent.click],
  // 级联选择器 https://leyaoyao.yuque.com/vbutb7/qw48a7/ukxmrx_ni0qgp
  'lyy-cascader': [defineEvent.update, defineEvent.beforeMount],
  // 级联选择器 https://leyaoyao.yuque.com/vbutb7/qw48a7/ukxmrx_ni0qgp
  // 多选框 https://leyaoyao.yuque.com/vbutb7/qw48a7/efsvg3_zu2g0k
  'lyy-checkbox': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.update,
  ],
  // 日期选择器 https://leyaoyao.yuque.com/vbutb7/qw48a7/iq7ycr_gxib2t
  'lyy-date-picker': [defineEvent.update],
  // 日期范围 https://leyaoyao.yuque.com/vbutb7/qw48a7/cco5yc_hm7tik
  'lyy-date-range': [defineEvent.update],
  // 目录树 https://leyaoyao.yuque.com/vbutb7/qw48a7/vwdr49_av674x
  'lyy-directory-tree': [
    defineEvent.mounted,
    defineEvent.select,
    defineEvent.drop,
  ],
  // 树组件(树选择) https://leyaoyao.yuque.com/vbutb7/qw48a7/oc76se_pmnm8t
  'lyy-tree': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.update,
    defineEvent.check,
  ],
  'lyy-erp-sku': [
    defineEvent.upload,
    defineEvent.add,
    defineEvent.editItem,
    defineEvent.prevAdd,
    defineEvent.nextAdd,
  ],
  // 文件上传 https://leyaoyao.yuque.com/vbutb7/qw48a7/vhbrdo
  'lyy-file-uploader': [
    defineEvent.upload,
    defineEvent.change,
    defineEvent.remove,
  ],
  // 表单组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/gc26rg_ra0gnf
  'lyy-form': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.update,
    defineEvent.activated,
    defineEvent.beforeUnmount,
  ],
  // 组合输入框
  'lyy-input-group': [defineEvent.update, defineEvent.focus, defineEvent.blur],
  // 密码输入框 https://leyaoyao.yuque.com/vbutb7/qw48a7/zd4i9z_nkltai
  'lyy-input-password': [defineEvent.update],
  // 范围输入框 https://leyaoyao.yuque.com/vbutb7/qw48a7/czubwu_riplsa
  'lyy-input-range': [defineEvent.update],
  // 弹窗选择器输入框组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/fbo1dt_glyuhr
  'lyy-modal-selector-input': [defineEvent.click, defineEvent.update],
  // 数字输入框
  'lyy-number-input': [defineEvent.update, defineEvent.focus, defineEvent.blur],
  // 分页 https://leyaoyao.yuque.com/vbutb7/qw48a7/nwti2g_tbdrva
  'lyy-pagination': [defineEvent.update],
  // 下拉列表选择
  'lyy-pull-down-selector-input': [
    defineEvent.click,
    defineEvent.update,
    defineEvent.focus,
  ],
  // 单选框 https://leyaoyao.yuque.com/vbutb7/qw48a7/yzc9st_xbzdqy
  'lyy-radio': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.update,
  ],
  // 富文本编辑器 https://leyaoyao.yuque.com/vbutb7/qw48a7/iop43e_gdk01g
  'lyy-rich-editor': [defineEvent.update],
  // 选择器 https://leyaoyao.yuque.com/vbutb7/qw48a7/vg4m3b_rt30id
  'lyy-select': [
    defineEvent.getRemoteOptions,
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.lastCompClick,
    defineEvent.update,
    defineEvent.search,
  ],
  'lyy-tree-select': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.update,
  ],
  // 带添加的选择器
  'lyy-selector-pro': [
    defineEvent.update,
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.add,
    defineEvent.refresh,
  ],
  // 开关 https://leyaoyao.yuque.com/vbutb7/qw48a7/bto5on_ri4fsx
  'lyy-switch': [defineEvent.update, defineEvent.click],
  // 创建标签 https://leyaoyao.yuque.com/vbutb7/qw48a7/vnp6li_edpfwl
  'lyy-tags-create': [defineEvent.update],
  // tags 组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/bgrdfe_hspmha
  'lyy-tags': [defineEvent.mounted, defineEvent.close],
  // 文本域组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/pvbisf_qlrpwr
  'lyy-text-area': [defineEvent.update],
  // 文本输入框组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/gkx3k3_xxt2uz
  'lyy-text-input': [
    defineEvent.blur,
    defineEvent.update,
    defineEvent.mounted,
    defineEvent.enter,
  ],
  // 穿梭框 https://leyaoyao.yuque.com/vbutb7/qw48a7/khl6ce_gtos7o
  'lyy-transfer': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.update,
  ],
  'lyy-tabs': [],
}

// 布局组件
const layoutComponents: Record<string, eventConfig[]> = {
  // 卡片容器 https://leyaoyao.yuque.com/vbutb7/qw48a7/pz7lpv_dd3ezd
  'lyy-card': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 列布局 https://leyaoyao.yuque.com/vbutb7/qw48a7/dle3v6_qedkwc
  'lyy-col': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 容器
  'lyy-container': [
    defineEvent.beforeMount,
    defineEvent.mounted,

    defineEvent.activated,
    defineEvent.deactivated,
    defineEvent.beforeUnmount,
  ],
  // 页脚 https://leyaoyao.yuque.com/vbutb7/qw48a7/mzgrvd_yo68k6
  'lyy-footer': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 栅格布局 https://leyaoyao.yuque.com/vbutb7/qw48a7/mh7d5s_rk3ten
  'lyy-grid': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 行布局 https://leyaoyao.yuque.com/vbutb7/qw48a7/gew12r_ayg8kn
  'lyy-row': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 搜索
  'lyy-search': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 搜索
  'lyy-search-pro': [defineEvent.search, defineEvent.reset],
  // 选项卡
  'lyy-tabs': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.update,
  ],
  // 工具栏 https://leyaoyao.yuque.com/vbutb7/qw48a7/vf368u_gv0ka4
  'lyy-toolbar': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 弹窗 https://leyaoyao.yuque.com/vbutb7/qw48a7/il0v12_diddmm
  'lyy-modal': [defineEvent.confirm, defineEvent.cancel],
  // 弹窗 https://leyaoyao.yuque.com/vbutb7/qw48a7/il0v12_diddmm
  'lyy-drawer': [defineEvent.confirm, defineEvent.cancel],
  'lyy-page-header': [defineEvent.click],
  'lyy-form-layout': [defineEvent.actionClick],
}

// 展示
const showComponents: Record<string, eventConfig[]> = {
  // 警告提示 https://leyaoyao.yuque.com/vbutb7/qw48a7/mk7rr8_yma3hr
  'lyy-alert': [defineEvent.beforeMount, defineEvent.mounted],
  // 头像 https://leyaoyao.yuque.com/vbutb7/qw48a7/dq7gc6_drr9uy
  'lyy-avatar': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
  ],
  'lyy-card-list': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.hover,
    defineEvent.click,
  ],
  'lyy-dropdown': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.hover,
    defineEvent.click,
    defineEvent.update,
  ],
  // 表格编辑lyy-edit-table https://leyaoyao.yuque.com/vbutb7/qw48a7/kgg58z
  'lyy-edit-table': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.update,
  ],
  // 计算组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/gtgnvg_lahiol
  'lyy-formula': [defineEvent.update],
  'lyy-html': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.update,
    defineEvent.beforeUnmount,
  ],
  'lyy-icon': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.hover,
  ],
  'lyy-image': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.hover,
  ],
  // 图片预览组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/rpxegs_tvy0h3
  'lyy-image-previewer': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.hover,
  ],
  // 描述块状组件 lyy-label-block https://leyaoyao.yuque.com/vbutb7/qw48a7/vkqn8t_sez8ig
  'lyy-label-block': [defineEvent.beforeMount, defineEvent.mounted],
  // 描述组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/tg5wdt_zb4s03
  'lyy-label-text': [
    defineEvent.click,
    defineEvent.copyBtnAction,
    defineEvent.detailBtnAction,
  ],
  'lyy-link': [defineEvent.click],
  // 气泡确认框组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/ylm1e6_bk7qzl
  'lyy-popconfirm': [defineEvent.click],
  // 二维码组件 https://leyaoyao.yuque.com/vbutb7/qw48a7/ln14p2_awrzav
  'lyy-qrcode': [defineEvent.beforeMount, defineEvent.mounted],
  'lyy-result': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.hover,
  ],
  // https://leyaoyao.yuque.com/vbutb7/qw48a7/kgvhym_sug45w
  'lyy-table': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.sort,
    defineEvent.saveColumnsSetting,
    defineEvent.restoreColumnsSetting,
    defineEvent.search,
    defineEvent.reset,
    defineEvent.save,
  ],
  'lyy-permission-table': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  'lyy-table-page': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.beforeFetch,
    defineEvent.afterFetch,
    defineEvent.find,
    defineEvent.clear,
    defineEvent.batchButtonClick,
    defineEvent.sort,
    defineEvent.saveColumnsSetting,
    defineEvent.restoreColumnsSetting,
    // defineEvent.search,
    // defineEvent.save,
  ],
  'lyy-excel-table': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.checkBoxChange,
  ],
  'lyy-column': [
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.update,
    defineEvent.blur,
    defineEvent.upload,
    defineEvent.getRemoteOptions,
  ],
  // 时间线 lyy-timeline https://leyaoyao.yuque.com/vbutb7/qw48a7/rb9q8n
  'lyy-timeline': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  'lyy-select-merchant': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
    defineEvent.click,
  ],
  'lyy-steps': [defineEvent.beforeMount, defineEvent.mounted],
  'lyy-echarts': [defineEvent.beforeMount],
  'lyy-iteration': [defineEvent.mounted, defineEvent.update],
}

// const componentActionsConfig: Record<string, eventConfig[]> = {}
// 业务
const businessComponents: Record<string, eventConfig[]> = {
  // ERP操作栏 https://leyaoyao.yuque.com/vbutb7/qw48a7/tmiehh_urttam
  'lyy-erp-action-bar': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 操作栏按钮 https://leyaoyao.yuque.com/vbutb7/qw48a7/oh1hlg_oqr9g3
  'lyy-erp-action-item': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
    defineEvent.beforeUnmount,
    defineEvent.update,
  ],
  // 带展开的盒子 https://leyaoyao.yuque.com/vbutb7/qw48a7/av5a8z_rg1hcc
  'lyy-erp-fold-box': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 顶部信息栏 https://leyaoyao.yuque.com/vbutb7/qw48a7/mow5vu_sbmoui
  'lyy-erp-information-bar': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.beforeUnmount,
  ],
  // 状态 https://leyaoyao.yuque.com/vbutb7/qw48a7/xgn3de_hkm8sv
  'lyy-erp-status-tag': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.click,
  ],
  // crm商户标签组件
  'lyy-crm-tag': [
    defineEvent.beforeMount,
    defineEvent.mounted,
    defineEvent.close,
  ],
  'lyy-iframe': [defineEvent.beforeMount, defineEvent.mounted],
  'lyy-address': [defineEvent.mounted, defineEvent.update],
}

const componentEventList = {
  ...formComponents,
  ...layoutComponents,
  ...showComponents,
  ...businessComponents,
}

export default componentEventList
