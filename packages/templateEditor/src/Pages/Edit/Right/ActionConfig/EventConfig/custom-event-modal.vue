<template>
  <div class="custom-event-modal">
    <a-modal
      v-model:open="visible"
      title="添加自定义事件"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form :model="formData" ref="formRef" :label-col="{ span: 4 }">
        <a-form-item name="event" label="key" :rules="rules.event">
          <a-input
            v-model:value="formData.event"
            placeholder="请输入自定义事件key"
            :maxlength="20"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { eventConfig } from '../../ActionConfig/interface'

const emits = defineEmits(['update:modelValue', 'eventSelect'])

const props = defineProps({
  modelValue: {
    type: <PERSON>olean,
    default: false,
  },
})

const visible = computed({
  get() {
    return props.modelValue
  },
  set(bool: boolean) {
    emits('update:modelValue', bool)
  },
})

const formRef = ref<FormInstance>()
const formData = ref<eventConfig>({
  event: '',
})

const rules = {
  event: [
    { required: true, message: '请输入事件key' },
    {
      validator: (rule: Rule, value: string) => {
        if (!new RegExp('^[\u4E00-\u9FA5_a-zA-Z0-9]+$').test(value)) {
          return Promise.reject('事件key仅支持中英文与下划线，请调整')
        }
        return Promise.resolve()
      },
    },
  ],
}

const handleOk = async () => {
  await formRef.value?.validate()
  emits('eventSelect', {
    ...formData.value,
    description: `自定义事件:${formData.value.event}`,
  })
  handleCancel()
}

const handleCancel = () => {
  formRef.value?.resetFields()
  visible.value = false
}
</script>

<style scoped></style>
