<script setup lang="ts">
import { defineComponent, defineEmits, computed } from 'vue'
import LyyBus from '../../../../../../bus/bus'
import { ATTR_CHANGE } from '../../../../../../enum/bus-typs'
defineComponent({
  name: 'attr-input',
})
const props = defineProps<{
  label: string // 标签
  description?: string //字段解释 暂定 先不处理
  value: string
}>()
const emit = defineEmits(['update:value'])
const value = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('update:value', val || val === 0 ? Number(val) : val)
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  }
})
</script>

<template>
  <div class="horizontal-item">
    <div>{{ props.label }}</div>
    <a-input-number style="width: 140px" v-bind="$attrs" :placeholder="`输入${props.label}`" :controls="false" v-model:value="value"></a-input-number>
  </div>
</template>

<style lang="less" scoped>
.horizontal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  // width: 100%;

  &>* {
    flex-shrink: 0;
  }
}
</style>
