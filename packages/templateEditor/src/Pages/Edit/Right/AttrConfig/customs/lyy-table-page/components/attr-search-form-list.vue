<script lang="ts" setup>
import { defineComponent, defineProps, defineEmits, computed, ref } from 'vue'
import Draggable from 'vuedraggable'
import CompFormItem from '@/Model/formItem'
import CompPresentational from '@/Model/presentational'
import { addAliasNameAnduuid, addEntityId } from '@/utils/addAliasName'
import _ from 'lodash'
import { getEntityId } from '@leyaoyao/libs/utils/compHash'
defineComponent({
  name: 'attr-search-form-list',
})

type SearchFormItem = {
  label: string
  value: string
}
const props = defineProps<{
  label: string
  value: object[]
  formListOptions?: SearchFormItem[]
  maxLength?: number
}>()

const emits = defineEmits(['update:value'])

const defaultOptions = [
  {
    value: 'lyy-text-input',
    label: '文本框',
  },
  {
    value: 'lyy-select',
    label: '下拉框',
  },
  {
    value: 'lyy-date-range',
    label: '日期范围',
  },
  {
    value: 'lyy-date-picker',
    label: '日期',
  },
  {
    value: 'lyy-cascader',
    label: '级联',
  },
  {
    value: 'lyy-input-range',
    label: '范围输入框',
  },
]

const value = computed({
  get() {
    return props.value
  },
  set(val) {
    emits('update:value', val)
  },
})

const options = computed(() => {
  return props.formListOptions || defaultOptions
})

const activeKey = ref('')

const compModels = [CompFormItem, CompPresentational]

const getTargetCompModel = (key) => {
  for (const compList of compModels) {
    if (compList.keys().includes(key)) {
      return compList(key)
    }
  }
}

// 下拉选择时 替换组件模型
const compTypeChange = (newCompName, element, index) => {
  // 取出模型 替换目标组件
  const target = addAliasNameAnduuid(
    _.cloneDeep(getTargetCompModel(`./${newCompName}-model.ts`).default),
  )
  target.prop.label = element.prop.label
  target.prop.field = element.prop.field
  if (Array.isArray(target)) {
    addEntityId(target)
  } else {
    target.entityId = getEntityId(target)
  }
  // 替换某一项
  value.value.splice(index, 1, target)
}
// 删除某一项
const removeFormItem = (index) => {
  value.value.splice(index, 1)
}
// 新增一项
const add = () => {
  const target = addAliasNameAnduuid(
    _.cloneDeep(
      getTargetCompModel(
        `./${
          options.value.length > 0 ? options.value[0].value : 'lyy-text-input'
        }-model.ts`,
      ).default,
    ),
  )
  if (Array.isArray(target)) {
    addEntityId(target)
  } else {
    target.entityId = getEntityId(target)
  }
  value.value.push(target)
}
</script>

<template>
  <div class="object-list">
    <!-- <h4>{{ label }}</h4> -->
    <a-collapse
      v-model:activeKey="activeKey"
      :bordered="false"
      expandIconPosition="end"
      ghost
    >
      <a-collapse-panel key="0" :header="label">
        <div class="config-list" v-if="value.length > 0">
          <caret-up-outlined class="up" />
          <Draggable :list="value" handle=".handle">
            <template #item="{ element, index }">
              <div>
                <div class="config-box">
                  <!-- 拖拽按钮 -->
                  <div class="config-item">
                    <unordered-list-outlined class="handle cursor" />
                  </div>
                  <!-- 标题 -->
                  <div class="config-item grow">
                    <a-input
                      :size="'small'"
                      v-model:value="element.prop.label"
                      placeholder="标题"
                    ></a-input>
                  </div>
                  <!-- 类型 -->
                  <div class="grow config-item">
                    <a-select
                      :value="element.compName"
                      class="select"
                      placeholder="纯文本"
                      size="small"
                      @change="compTypeChange($event, element, index)"
                      :options="options"
                    ></a-select>
                  </div>
                  <!-- 删除按钮 -->
                  <div class="grow">
                    <delete-outlined
                      class="delete-icon"
                      @click="removeFormItem(index)"
                    />
                  </div>
                </div>
                <div class="config-field">
                  <a-input
                    size="small"
                    placeholder="请输入字段名"
                    v-model:value="element.prop.field"
                  ></a-input>
                </div>
              </div>
            </template>
          </Draggable>
        </div>
        <div class="add-button" v-if="!maxLength || value.length < maxLength">
          <a-button block @click="add" size="small">+ 新增</a-button>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<style lang="less" scoped>
.up {
  position: absolute;
  top: -24px;
  left: 12px;
  color: #f7f7f9;
  font-size: 48px;
}
.attr-searchFormList {
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  margin-bottom: 12px;
}
.object-list {
  :deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header) {
    padding: 0 !important;
    font-weight: normal !important;
    .anticon-right {
      right: 0;
    }
    .ant-collapse-header-text {
      color: rgba(0, 0, 0, 0.6);
    }
  }
  :deep(.ant-collapse-content-box) {
    padding: 0 !important;
    margin-top: 8px;
  }
}
.config-list {
  position: relative;
  background-color: #f7f7f9;
  padding: 16px 6px 6px;
  border-radius: 2px;
  margin: 12px 0;
}
.config-box {
  margin-bottom: 4px;
}
.config-field {
  margin-bottom: 8px;
  margin-left: 18px;
  margin-right: 18px;
}
.config-box {
  display: flex;
}
.config-item {
  margin-right: 6px;
  font-size: 12px;
}
.delete-icon {
  color: red;
  font-size: 12px;
  cursor: pointer;
}
.cursor {
  cursor: pointer;
  padding-top: 6px;
}
</style>
