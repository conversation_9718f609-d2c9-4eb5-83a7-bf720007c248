<script setup lang="ts">
import { defineComponent, defineEmits, computed, ref } from 'vue'
import { Input, Image } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import LyyBus from '../../../../../../bus/bus'
import { ATTR_CHANGE } from '../../../../../../enum/bus-typs'
import * as antIcon from '@ant-design/icons-vue'
import { customIcons } from './config'

defineComponent({
  name: 'attr-icon',
})
const props = defineProps<{
  label?: string // 标签
  description?: string //字段解释 暂定 先不处理
  value: string
  placeholder?: string
  width?: string
}>()
const emit = defineEmits(['update:value'])
const open = ref(false)
const value = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('update:value', val)
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  },
})

const placeholder = computed(() => {
  return props.placeholder || `输入${props.label}`
})

const antIconKeys = Object.keys(antIcon)

const icon = computed(() => {
  return antIcon[value.value]
})

const isUrlIcon = computed(() => {
  const pattern = /^(https?:\/\/)/
  return pattern.test(value.value)
})
const handleOpen = () => {
  open.value = true
}

const handleIconClick = (key: string) => {
  value.value = key
  open.value = false
}
</script>

<template>
  <div class="horizontal-item">
    <div>{{ props.label }}</div>
    <a-popover
      overlayClassName="icon-popover-wrap"
      :trigger="value ? 'hover' : 'null'"
    >
      <template #content>
        <Image
          style="width: 24px; height: 24px"
          :src="value"
          v-if="isUrlIcon"
        ></Image>
        <component v-if="antIconKeys.includes(value)" :is="icon" />
        <lyy-icon v-else :prop="{ iconName: value }" />
      </template>
      <Input
        :style="{ width: props.width ?? '140px' }"
        v-bind="$attrs"
        allowClear
        :placeholder="placeholder"
        v-model:value="value"
      >
        <template #suffix>
          <SearchOutlined @click="handleOpen" />
        </template>
      </Input>
    </a-popover>

    <a-modal v-model:open="open" title="选择图标" :style="{ width: '800px' }">
      <div class="icon-modal">
        <template v-for="key in customIcons" :key="key">
          <lyy-icon
            class="icon-item"
            :prop="{ iconName: key }"
            @click="handleIconClick(key)"
          />
        </template>
        <template v-for="key in Object.keys(antIcon)" :key="key">
          <component
            :is="antIcon[key]"
            class="icon-item"
            v-if="key.includes('Outlined') && antIcon[key]"
            @click="handleIconClick(key)"
          />
        </template>
      </div>
    </a-modal>
  </div>
</template>

<style lang="less">
.icon-modal {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .icon-item {
    font-size: 20px;
    cursor: pointer;
  }
}
</style>

<style lang="less" scoped>
.horizontal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  // width: 100%;

  & > * {
    flex-shrink: 0;
  }
}
</style>
<style lang="less" scoped>
.horizontal-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  color: rgba(0, 0, 0, 0.6);

  & > * {
    flex-shrink: 0;
  }
}
.icon-popover-wrap {
  .selected-icon {
    font-size: 24px;
    line-height: 1;
  }
}
</style>
