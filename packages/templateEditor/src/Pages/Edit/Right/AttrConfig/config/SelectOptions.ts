const inputSize = [
  { value: 'large', text: '大' },
  { value: 'default', text: '默认' },
  { value: 'small', text: '小' },
]
const buttonSize = [
  { value: 'large', text: '大' },
  { value: 'middle', text: '中' },
  { value: 'small', text: '小' },
]
const modalSize = [
  { value: 'fullScreen', text: '全屏' },
  { value: 'large', text: '大' },
  { value: 'middle', text: '中' },
  { value: 'small', text: '小' },
]
const editTypeList = [
  { value: 'tabel', text: '表格编辑' },
  { value: 'modal', text: '弹窗编辑' },
]
const buttonType = [
  { value: 'primary', text: '主要' },
  { value: 'ghost', text: '幽灵' },
  { value: 'dashed', text: '虚线' },
  { value: 'link', text: '链接' },
  { value: 'text', text: '文本' },
  { value: 'default', text: '默认' },
]
const labelTextType = [
  { value: 'link', text: '链接' },
  { value: 'tag', text: '标签' },
]
const paginationSize = [
  { value: 'default', text: '默认' },
  { value: 'small', text: '小' },
]
const formLabelAlign = [
  { value: 'left', text: '左对齐' },
  { value: 'right', text: '右对齐' },
]
const layout = [
  { value: 'horizontal', text: '水平' },
  { value: 'vertical', text: '垂直' },
]
const formLayout = [...layout, { value: 'inline', text: '内联' }]

const avatarShape = [
  { value: 'circle', text: '圆形' },
  { value: 'square', text: '正方形' },
]
const authCodeType = [
  { value: 'phone', text: '手机号' },
  { value: 'email', text: '邮箱' },
]
export const gridSize = [
  { value: 'default', text: '四列布局' },
  { value: 'normal', text: '三列布局' },
  { value: 'middle', text: '两列布局' },
  { value: 'large', text: '一列布局' },
]
const alertType = [
  { value: 'success', text: '成功' },
  { value: 'info', text: '默认' },
  { value: 'warning', text: '警告' },
  { value: 'error', text: '错误' },
]
const iconPosition = [
  { value: 'left', text: '左' },
  { value: 'right', text: '右' },
]
const position = [
  { value: 'left', text: '居左' },
  { value: 'right', text: '居右' },
]
const datePicker = [
  { value: 'date', text: '日期' },
  { value: 'week', text: '周' },
  { value: 'month', text: '月份' },
  { value: 'quarter', text: '季度' },
  { value: 'year', text: '年份' },
]
const formulaType = [
  { text: '文本', value: 'text' },
  { text: '输入框', value: 'default' },
]
const richEditorOutputFormat = [
  { value: 'html', text: '代码块' },
  { value: 'text', text: '纯文本' },
]
const selectMode = [
  { value: 'multiple', text: '多选' },
  { value: 'tags', text: '标签' },
  { value: 'combobox', text: '下拉框' },
]
const resultStatus = [
  { value: 'success', text: '成功' },
  { value: 'error', text: '错误' },
  { value: 'info', text: '默认' },
  { value: 'warning', text: '警告' },
  { value: '403', text: '403' },
  { value: '404', text: '404' },
  { value: '500', text: '500' },
]
const rowAlign = [
  { value: 'top', text: '顶端对齐' },
  { value: 'middle', text: '居中对齐' },
  { value: 'bottom', text: '底端对齐' },
]
const rowJustify = [
  { value: 'start', text: '起始端对齐' },
  { value: 'center', text: '居中对齐' },
  { value: 'end', text: '末尾端对齐' },
  { value: 'space-around', text: '均匀分布（元素等间距）' },
  { value: 'space-between', text: '均匀分布（首尾对齐）' },
]
const justifyContent = [
  { value: 'flex-start', text: '起始端对齐' },
  { value: 'center', text: '居中对齐' },
  { value: 'flex-end', text: '末尾端对齐' },
]
const colAlign = [
  { value: 'left', text: '左对齐' },
  { value: 'center', text: '居中对齐' },
  { value: 'right', text: '右对齐' },
]
const radioOptionsType = [
  { value: 'default', text: '默认' },
  { value: 'button', text: '按钮' },
]
const dbuttonStyle = [
  { value: 'outline', text: '描边' },
  { value: 'solid', text: '填色' },
]
const validateTrigger = [
  { value: 'change', text: '数据改变' },
  { value: 'blur', text: '失去焦点' },
]
const placement = [
  { value: 'top', text: '上方' },
  { value: 'left', text: '左边' },
  { value: 'right', text: '右边' },
  { value: 'bottom', text: '下方' },
  { value: 'topLeft', text: '上左' },
  { value: 'topRight', text: '上右' },
  { value: 'bottomLeft', text: '下左' },
  { value: 'bottomRight', text: '下右' },
  { value: 'leftTop', text: '左上' },
  { value: 'leftBottom', text: '左下' },
  { value: 'rightTop', text: '右上' },
  { value: 'rightBottom', text: '右下' },
]
export const tableColType = [
  { value: 'text', text: '纯文本' },
  { value: 'number', text: '数字' },
  { value: 'tags', text: '标签' },
  { value: 'image', text: '图片' },
  { value: 'link', text: '连接跳转' },
  { value: 'join', text: '合并' },
  { value: 'time', text: '日期' },
  { value: 'panel', text: 'panel' },
  { value: 'html', text: 'html' },
  { value: 'operation', text: '操作' },
]
const tableColAlign = [
  { value: 'left', text: '左对齐' },
  { value: 'right', text: '右对齐' },
  { value: 'center', text: '居中对齐' },
]
const tablePlacement = [
  { value: 'table', text: '表格中' },
  { value: 'top', text: '表格顶部' },
  { value: 'bottom', text: '表格底部' },
]
const showSelectionOption = [
  { value: '', text: '无' },
  { value: true, text: '多选' }, // 为了兼容之前有的已经是true表示多选
  { value: 'radio', text: '单选' },
]
const dateRangeDefaultValueOptions = [
  { value: '昨天', text: '昨天' },
  { value: '今天', text: '今天' },
  { value: '近3天', text: '近3天' },
  { value: '近7天', text: '近7天' },
  { value: '近14天', text: '近14天' },
  { value: '近30天', text: '近30天' },
  { value: '近60天', text: '近60天' },
  { value: '近90天', text: '近90天' },
  { value: '近半年', text: '近半年' },
  { value: '近一年', text: '近一年' },
  { value: '上个月', text: '上个月' },
  { value: '本月', text: '本月' },
  { value: '近15分钟', text: '近15分钟' },
  { value: '近30分钟', text: '近30分钟' },
  { value: '近1小时', text: '近1小时' },
  { value: '近2小时', text: '近2小时' },
  { value: '近6小时', text: '近6小时' },
]
const datePickerDefaultValueOptions = [
  { value: '今天', text: '今天' },
  { value: '昨天', text: '昨天' },
  { value: '明天', text: '明天' },
  { value: '月初', text: '月初' },
  { value: '月末', text: '月末' },
  { value: '年初', text: '年初' },
  { value: '年末', text: '年末' },
]
const lineNumbersType = [
  { value: 'off', text: '关闭' },
  { value: 'on', text: '开启' },
]

const getLanguageOptions = (e: any) => {
  return Object.values(e).map((item: string) => {
    return {
      value: item,
      text: item,
    }
  })
}

const specificColor = [
  { value: '#000000E0', text: '一级文本' },
  { value: '#000000A6', text: '二级文本' },
  { value: '#00000040', text: '禁用字体' },
  { value: '#52c41a', text: '成功色' },
  { value: '#faad14', text: '警告色' },
  { value: '#f5222d', text: '错误色' },
]

const specificSize = [
  { value: '30px', text: '标题1' },
  { value: '24px', text: '标题2' },
  { value: '20px', text: '标题3' },
  { value: '16px', text: '标题4' },
  { value: '14px', text: '正文' },
]

const textAlign = [
  { value: 'center', text: '居中' },
  { value: 'left', text: '左对齐' },
  { value: 'right', text: '右对齐' },
]

const chartsType = [
  { value: 'bar', text: '柱状图' },
  { value: 'line', text: '折线图' },
  { value: 'pie', text: '饼图' },
  { value: 'scatter', text: '散点图' },
]

export const formItemCompList = [
  {
    value: 'lyy-text-input',
    text: '文本框',
  },
  {
    value: 'lyy-select',
    text: '下拉框',
  },
  {
    value: 'lyy-pull-down-selector-input',
    text: '下拉列表选择',
  },
  {
    value: 'lyy-date-picker',
    text: '日期',
  },
  {
    value: 'lyy-date-range',
    text: '日期范围',
  },
  {
    value: 'lyy-text-area',
    text: '多行文本输入',
  },
  {
    value: 'lyy-file-uploader',
    text: '文件上传',
  },
  {
    value: 'lyy-cascader',
    text: '级联选择框',
  },
  {
    value: 'lyy-input-group',
    label: '组合输入框',
  },
  {
    value: 'lyy-switch',
    text: '开关',
  },
  {
    value: 'lyy-number-input',
    text: '数字输入框',
  },
  {
    value: 'lyy-input-range',
    text: '范围输入框',
  },
  {
    value: 'lyy-formula',
    label: '计算组件',
  },
  {
    value: 'lyy-selector-pro',
    text: '带新增的下拉框',
  },
  {
    value: 'lyy-tree-select',
    label: '树下拉框',
  },
  {
    value: 'lyy-time-range',
    text: '时间范围',
  },
  {
    value: 'lyy-radio',
    text: '单选框',
  },
  {
    value: 'lyy-checkbox',
    text: '多选框',
  },
  {
    value: 'operation',
    text: '操作',
  },
]
export const imgSizeType = [
  {
    value: 'large',
    text: '大',
  },

  {
    value: 'middle',
    text: '中',
  },
  {
    value: 'mini',
    text: '小',
  },
]
export const commonSelectOptions = {
  validateTrigger: validateTrigger,
  size: inputSize,
  picker: datePicker,
  mode: selectMode,
  placement: placement,
  justifyContent: justifyContent,
  imgSize: imgSizeType,
  editType: editTypeList,
}
export const uploadType = [
  { value: 'image', text: '上传图片' },
  { value: 'other', text: '上传其他文件' },
]
/** oss上传平台配置 */
export const ossUploadPlatform = [
  { value: 'aliyun', text: '阿里云' },
  { value: 'tencent', text: '腾讯云' },
]
export const operationModeOptions = [
  { value: 'normal', text: '常规' },
  { value: 'hover', text: '悬停节点时' },
]
export const expandActionModeOptions = [
  { value: false, text: '不收起' },
  { value: 'click', text: '单击收起' },
  { value: 'dblclick', text: '双击收起' },
]
export const editTableColumnType = [
  { value: 'operation', text: '操作' },
  { value: 'text', text: '文本' },
]
export const dividerType = [
  { value: 'horizontal', text: '水平' },
  { value: 'vertical', text: '垂直' },
]
export const dividerOrientation = [
  { value: 'left', text: '左边' },
  { value: 'right', text: '右边' },
  { value: 'center', text: '居中' },
]
export const excelTableTypeOptions = [
  {
    value: 'seq',
    text: '序号',
  },
  {
    value: 'text',
    text: '文本输入框',
  },
  {
    value: 'pullDown',
    text: '下拉列表选择',
  },
  {
    value: 'select',
    text: '下拉框',
  },
  {
    value: 'switch',
    text: '开关',
  },
  {
    value: 'checkbox',
    text: '多选框',
  },
  {
    value: 'radio',
    text: '单选',
  },
  {
    value: 'file-uploader',
    text: '文件上传',
  },
  {
    value: 'cascader',
    text: '级联选择',
  },
  {
    value: 'tree-select',
    text: '树状下拉框',
  },
  // {
  //   value: 'number',
  //   text: '数字输入框',
  // },
  {
    value: 'date',
    text: '日期选择',
  },
  {
    value: 'datetime',
    text: '日期时间',
  },
  {
    value: 'image',
    text: '图片',
  },
  {
    value: 'operation',
    text: '操作',
  },
  {
    text: '数量子表',
    value: 'subtable',
  },
]
export const upLoadConfig = {
  type: 'primary',
  uploadType: 'image',
  fileSize: 50,
  maxCount: 1,
  accept: '.png,.jpg,.jpeg,.txt,.DOC,.DOCX,.XLSX,.CSV,.PPT,.PPTX,.PDF',
  validateTrigger: '',
  disabled: false,
  multiple: false,
  auto: true,
  showCover: false,
  showTip: true,
  download: false,
  fieldNames: {
    name: 'fileName',
    url: 'path',
  },
}
export const treeSelectConfig = {
  placeholder: '请选择',
  multiple: false,
  treeCheckable: false,
  showSearch: true,
  treeNodeFilterProp: '',
  size: 'default',
  immediate: true,
  allowClear: false,
  readonly: false,
  disabled: false,
  labelCol: {
    style: {
      width: 0,
    },
  },
}
const languageOptions = [
  { text: 'JAVASCRIPT', value: 'JAVASCRIPT' },
  { text: 'JSON', value: 'JSON' },
  { text: 'MARKDOWN', value: 'MARKDOWN' },
  { text: 'SQL', value: 'SQL' },
  { text: 'TXT', value: 'TXT' },
  { text: 'TYPESCRIPT', value: 'TYPESCRIPT' },
  { text: 'XML', value: 'XML' },
  { text: 'YAML', value: 'YAML' },
  { text: 'YML', value: 'YML' },
]

const fastOptions = [
  { text: '昨天', value: '昨天' },
  { text: '今天', value: '今天' },
  { text: '近3天', value: '近3天' },
  { text: '近7天', value: '近7天' },
  { text: '近14天', value: '近14天' },
  { text: '近30天', value: '近30天' },
  { text: '近60天', value: '近60天' },
  { text: '近90天', value: '近90天' },
  { text: '近半年', value: '近半年' },
  { text: '近一年', value: '近一年' },
  { text: '上个月', value: '上个月' },
  { text: '本月', value: '本月' },
  { text: '下个月', value: '下个月' },
]

const tabsTypeOptions = [
  { text: '线条', value: 'line' },
  { text: '卡片', value: 'card' },
  { text: '可编辑', value: 'editable-card' },
]
// 下拉选项配置
export const selectOptions = {
  ['lyy-text-comp']: {
    specificColor: specificColor,
    specificSize: specificSize,
    textAlign: textAlign,
  },
  ['lyy-divider']: {
    type: dividerType,
    orientation: dividerOrientation,
  },
  ['lyy-editor']: {
    lineNumbers: lineNumbersType,
    language: languageOptions,
  },
  ['lyy-button']: {
    type: buttonType,
    size: buttonSize,
    position: position,
  },
  ['lyy-label-text']: {
    type: labelTextType,
  },
  ['lyy-pagination']: {
    size: paginationSize,
  },
  ['lyy-search-pro']: {
    colSpan: gridSize,
  },
  ['lyy-form']: {
    layout: formLayout,
    labelAlign: formLabelAlign,
  },
  ['lyy-avatar']: {
    shape: avatarShape,
  },
  ['lyy-auth-code']: {
    type: authCodeType,
  },
  ['lyy-alert']: {
    type: alertType,
  },
  ['lyy-formula']: {
    type: formulaType,
    iconPosition: iconPosition,
  },
  ['lyy-grid']: {
    size: gridSize,
  },
  ['lyy-table']: {
    size: buttonSize,
    expandFixed: iconPosition,
    type: tableColType,
    align: tableColAlign,
    showSelection: showSelectionOption,
    summary: {
      placement: tablePlacement,
    },
    fixed: iconPosition,
  },
  ['lyy-switch']: {
    size: paginationSize,
    validateTrigger: validateTrigger,
  },
  ['lyy-rich-editor']: {
    outputFormat: richEditorOutputFormat,
  },
  ['lyy-popconfirm']: {
    okType: buttonType,
    type: buttonType,
  },
  ['lyy-modal-selector-input']: {
    modalSize: buttonSize,
  },
  ['lyy-message']: {
    type: alertType,
  },
  ['lyy-result']: {
    status: resultStatus,
  },
  ['lyy-radio']: {
    optionType: radioOptionsType,
    size: inputSize,
    dbuttonStyle: dbuttonStyle,
  },
  ['lyy-row']: {
    align: rowAlign,
    justify: rowJustify,
  },
  ['lyy-col']: {
    align: colAlign,
  },
  ['lyy-modal']: {
    size: modalSize,
    okType: buttonType,
  },
  ['lyy-file-uploader']: {
    uploadType: uploadType,
    ossPlatform: ossUploadPlatform,
  },
  ['lyy-directory-tree']: {
    operationMode: operationModeOptions,
    expandAction: expandActionModeOptions,
  },
  ['lyy-date-range']: {
    defaultValue: dateRangeDefaultValueOptions,
    fastOptions,
  },
  ['lyy-date-picker']: {
    defaultValue: datePickerDefaultValueOptions,
  },
  ['lyy-edit-table']: {
    type: editTableColumnType,
    comp: formItemCompList,
  },
  ['lyy-excel-table']: {
    type: excelTableTypeOptions,
    fixed: [
      { value: 'left', text: '左边' },
      { value: 'right', text: '右边' },
    ],
    align: [
      { value: 'left', text: '左对齐' },
      { value: 'center', text: '居中对齐' },
      { value: 'right', text: '右对齐' },
    ],
  },
  ['lyy-steps']: {
    direction: [
      { value: 'horizontal', text: '水平方向' },
      { value: 'vertical', text: '垂直方向' },
    ],
    status: [
      { value: 'wait', text: '等待' },
      { value: 'process', text: '进行中' },
      { value: 'finish', text: '结束' },
      { value: 'error', text: '错误' },
    ],
  },
  ['lyy-echarts']: {
    type: chartsType,
  },
  ['lyy-iterator']: {
    layout: layout,
  },
  ['lyy-tabs']: {
    type: tabsTypeOptions,
  },
}
