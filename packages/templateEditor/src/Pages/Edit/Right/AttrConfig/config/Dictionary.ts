// 字典
export enum Dictionary {
  'ossRadio' = '开启OSS直传',
  'ossPlatform' = 'OSS平台',
  'onlyOssFileName' = '只保留OSS文件名',
  'ossTokenUrl' = '获取OSS-token链接',
  'title' = '标题',
  'field' = '字段名',
  'fields' = '字段名',
  'label' = '标签',
  'text' = '文本',
  'placeholder' = '提示文本',
  'danger' = '危险按钮',
  'disabled' = '禁用',
  'ghost' = '背景透明',
  'precision' = '数值精度',
  'configTable' = '表格配置',
  'type' = '类型',
  'step' = '步数',
  'decimalSeparator' = '小数点',
  'bordered' = '边框',
  'controls' = '显示增减按钮',
  'max' = '最大值',
  'min' = '最小值',
  'defaultValue' = '默认值',
  'value' = '默认值',
  'formId' = '表单id',
  'compact' = '紧凑',
  'row' = '行栅格',
  'cols' = '列栅格组',
  'isMd5' = 'MD5加密',
  'icon' = '图标',
  'size' = '尺寸',
  'aliasName' = '组件别名',
  'addonAfter' = '后置标签',
  'maxlength' = '文本最大长度',
  'showCount' = '展示字数',
  'allowClear' = '展示清除图标',
  'colon' = 'label后面的冒号',
  'validateTrigger' = '字段校验时机',
  'rules' = '校验规则',
  'dynamicRules' = '动态规则',
  'slotLabel' = '带图标标签',
  'suffix' = '输入框后缀',
  'rows' = '行数',
  'autosize' = '自适应内容高度',
  'minRows' = '最小行数',
  'maxRows' = '最大行数',
  'visibilityToggle' = '显示切换按钮',
  'suffixIcon' = '字段值后的图标',
  'prefixIcon' = '标签后的图标',
  'flag' = '字段值后的标识',
  'textStyle' = '字段值的样式',
  'setDefaultWidth' = '设置列宽',
  'seperator' = '文本的分隔符',
  'labelWidth' = '标签文本的宽度',
  'decimalPoint' = '小数点保留位数',
  'isThousands' = '数字千分位展示',
  'tooltip' = '文本过长时使用tooltip',
  'total' = '总条数',
  'defaultCurrent' = '默认页码',
  'defaultPageSize' = '默认条数',
  'hideOnSinglePage' = '单页隐藏分页器',
  'showQuickJumper' = '快速跳转至某页',
  'showSizeChanger' = '展示条数切换',
  'simple' = '简单分页',
  'closable' = '关闭按钮',
  'path' = '取值路径',
  'color' = '颜色',
  'html' = '代码块',
  'form' = '表单默认值',
  'isSignChange' = '开启表单改变数据',
  'payloads' = 'payloads 取值',
  'layout' = '表单布局',
  'labelCol' = '标签宽度',
  'wrapperCol' = '输入控件布局',
  'labelAlign' = '标签的文本对齐方式',
  'class' = 'css类名',
  'tabs' = '标签页配置',
  'defaultSrc' = '头像默认地址',
  'alt' = '头像文本',
  'shape' = '头像形状',
  'linkField' = '关联字段名',
  'seconds' = '秒数',
  'inputStyle' = '输入框样式',
  'buttonStyle' = '按钮样式',
  'pattern' = '正则',
  'extra' = '卡片头部元素',
  'content' = '内容元素',
  'mainLayout' = '高度100%',
  'showIcon' = '显示辅助图标',
  'message' = '提示内容',
  'description' = '提示的详细描述',
  'buttonGap' = '组件间距',
  'leftStyle' = '左边盒子样式',
  'rightStyle' = '右边盒子样式',
  'position' = '位置',
  'autofocus' = '自动获取焦点',
  'changeOnSelect' = '选择即改变',
  'fieldNames' = '字段映射',
  'maxTagCount' = '最多显示的tag数',
  'multiple' = '多选',
  'treeCheckable' = '显示 checkbox',
  'optionList' = '默认选项列表',
  'emitPath' = '是否返回节点全路径',
  'lazy' = '开启懒加载',
  'parentId' = '懒加载入参字段名',
  'forbid' = '禁用配置',
  'show' = '显隐配置',
  'options' = '选项列表',
  // 'showLine' = '展示连接线',
  'root' = '根节点配置',
  'defaultExpandAll' = '默认展开所有节点',
  'selectable' = '节点可选择',
  'blockNode' = '节点是否占据一行',
  'draggable' = '节点可拖拽',
  'dragRules' = '节点拖拽规则',
  'prefixIcons' = '节点前缀图标',
  // 'prefixIcon' = '节点前缀统一显示图标名称',
  'isDefaultSelect' = '默认选中第一个节点',
  'isDefaultSelectChild' = '默认选中第一个末级节点',
  'src' = '路径',
  'radius' = '圆角大小',
  'dropdownList' = '菜单选项',
  'picker' = '日期类型',
  'valueFormat' = '日期值格式化',
  'showNow' = '展示“现在“按钮',
  'showToday' = '展示“今天”按钮',
  'showTime' = '增加时间选择功能',
  'key' = '键名',
  'textPrefix' = '前置文本',
  'textSuffix' = '后置文本',
  'pipes' = '数据格式化',
  'iconPosition' = '标签图标位置',
  'exp' = '表达式',
  'openFormatDocument' = '开启格式化',
  'pipe' = '管道类型',
  'option' = '选项',
  'textMap' = '文本映射',
  'origin' = '原始值',
  'result' = '结果',
  'fastOptions' = '快捷选项(可多选)',
  'disabledOption' = '禁选日期配置',
  'groupMax' = '条件组数量',
  'conditionMax' = '每组条件数量',
  'groupOptions' = '条件组判断枚举',
  'fieldOptions' = '查询字段列表',
  'selectOptions' = '枚举子数据',
  'operatorOptions' = '操作符枚举',
  'btnText' = '筛选按钮文本',
  'width' = '宽度',
  'okText' = '确定按钮文本',
  'mask' = '显示遮罩层',
  'maskClosable' = '点击蒙层关闭',
  'conditionOptions' = '条件判断关系枚举',
  'selectFieldNames' = '枚举值映射',
  'sticky' = '表头吸顶',
  'columns' = '表格列配置',
  'customRowEvent' = '自定义表格点击事件',
  'rowKey' = '表格键值',
  'scroll' = '表格水平、垂直滚动',
  'showIndex' = '展示序号',
  'isServerSorter' = '开启服务端排序',
  'showSelection' = '展示多/单选',
  'rowSelection' = '列表选择行配置',
  'isPagingSelected' = '开启跨页选择',
  'operationMax' = '操作了按钮个数设置',
  'disabledRow' = '禁用行设置',
  'backgroundRows' = '行背景色设置',
  'summary' = '合计配置',
  'expandIconColumnIndex' = '展开图标的列位置',
  'expandFixed' = '展开图标是否固定',
  'colspanConfig' = '合并单元格列',
  'rowExpandIconWidth' = '展开图标宽度',
  'expandFetch' = '展开请求回填',
  'checkedValue' = '选中时的值',
  'unCheckedValue' = '未选中时的值',
  'col' = '列栅格分配数',
  'checkedChildren' = '选中时的内容',
  'unCheckedChildren' = '未选中时的内容',
  'readonly' = '是否只读',
  'readOnly' = '是否只读',
  'isOPenMode' = '是否开启弹框',
  'openEdit' = '下拉框编辑',
  'openModalEdit' = '编辑弹框',
  'showChildLine' = '显示多少行组件',
  'dropdown' = '下拉按钮列表配置',
  'infoData' = '用户信息数组',
  'companyList' = '企业/门店列表',
  'search' = '搜索配置',
  'company' = '企业信息配置',
  'userInfo' = '用户信息配置',
  'height' = '初始高度',
  'outputFormat' = '内容输出格式',
  'fontSize' = '字号',
  'zIndex' = '层级',
  'gapX' = '水平间距',
  'gapY' = '垂直间距',
  'offsetTop' = '距离顶部距离',
  'offsetLeft' = '距离左侧距离',
  'rotate' = '旋转角度',
  'image' = '图片',
  'fontStyle' = '字样式',
  'fontFamily' = '字体',
  'fontWeight' = '字加粗',
  'fontColor' = '字颜色',
  'containerHeight' = '容器高度',
  'caontainerHeightDynamic' = '容器高度动态',
  'optionFilterProp' = '搜索字段',
  'showSearch' = '开启搜索',
  'mode' = '模式',
  'immediate' = '触发选择事件',
  'lastComp' = '添加选项配置',
  'customAdd' = '自定义新增',
  'buttonList' = '按钮列表',
  'colConfig' = '列自适应配置',
  'formItemMarginbuttom' = 'formItem每项底部的边距',
  'bodyStyle' = '内容区样式',
  'fixedCount' = '固定展示的数量',
  'trigger' = '校验时机',
  'required' = '必填',
  'source' = '数据源',
  'sourceKey' = '数据源中的键值',
  'decompose' = '解构',
  'iconName' = '图标名称',
  'placement' = '气泡位置',
  'iconProps' = '图标属性',
  'popover' = '气泡弹窗',
  'customOption' = '个性化选项',
  'style' = '样式',
  'spin' = '是否有旋转动画',
  'twoToneColor' = '设置双色图标的主要颜色',
  'hideAtRoot' = '根结点的操作图标是否隐藏',
  'menuList' = '菜单选项',
  'visible' = '显示',
  'okType' = '确定按钮类型',
  'cancelText' = '取消按钮文本',
  'labelField' = '展示在输入框的文本字段',
  'modalSize' = '模态框尺寸',
  'centered' = '模态框是否居中',
  // 'destroyOnClose' = '模态框关闭时是否销毁',
  'tableCompId' = '关联的table组件id',
  'fileSize' = '文件大小限制(M)',
  'maxCount' = '限制上传数量',
  'accept' = '接受上传的文件类型',
  'auto' = '自动上传',
  'showTip' = '展示提示',
  'tip' = '提示文案',
  'templateUrl' = '下载模板链接地址',
  'download' = '展示下载模板',
  'showCover' = '展示封面标识',
  'uploadType' = '上传类型',
  'jsonPath' = '兼容字段值为对象数组时，指定url对应的路径',
  'dataIndex' = '字段名',
  'comp' = '内容类型',
  'xxl' = '分辨率≥1600px时的栅格数',
  'xl' = '分辨率≥1200px时的栅格数',
  'lg' = '分辨率≥992px时的栅格数',
  'md' = '分辨率≥768px时的栅格数',
  'sm' = '分辨率≥576px时的栅格数',
  'xs' = '分辨率<576px时的栅格数',
  'subTitle' = '副标题',
  'status' = '状态',
  'titles' = '标题集合',
  'locale' = '配置项文案',
  'itemUnit' = '选中数据单位配置项文案',
  'itemsUnit' = '未选中数据单位配置项文案',
  'notFoundContent' = '列表为空文案',
  'searchPlaceholder' = '搜索框文案',
  'eleItems' = '循环主体',
  'prop' = '属性',
  'defaultSrcs' = '默认图片资源地址（支持多张）',
  'name' = '字段名',
  'margin' = '外边距',
  'url' = '地址',
  'logoUrl' = 'logo地址',
  'errorText' = '加载错误时显示的文案',
  'checkedFromPreFetch' = '用于预请求选择某些指定的数据点',
  'expandedKeys' = '展开的树节点',
  'checkable' = '节点前添加 Checkbox 复选框',
  'isCheckAndSelect' = '允许勾中并选中',
  'onlySaveLeaves' = '仅保存叶子结点',
  'unit' = '时长单位',
  'duration' = '持续时长',
  'preFetch' = '懒加载请求配置',
  'method' = '请求方式',
  'headerPayloads' = '请求头取值',
  'children' = '子数据字段',
  'optionType' = '单选框选项类型',
  'dbuttonStyle' = '单选框按钮样式风格',
  'align' = '对齐方式',
  'gutter' = '栅格间隔',
  'justify' = '水平排列方式',
  'wrap' = '自动换行',
  'flex' = '布局填充',
  'offset' = '栅格左侧的间隔格数',
  'order' = '栅格顺序',
  'pull' = '栅格向左移动格数',
  'push' = '栅格向右移动格数',
  'span' = '栅格占位格数',
  'loading' = 'loading',
  'spinning' = '旋转',
  'isSorter' = '排序',
  'replaceNull' = '占位符',
  'resizable' = '列宽度拖动',
  'uncheckedValue' = '未选中时的值',
  'columnWidth' = '列宽',
  'selectedRowKeys' = '选中的key',
  'checkStrictly' = 'checkable状态下父子节点不关联',
  'getCheckboxProps' = '选择框的默认属性配置',
  'fixed' = '固定',
  'mergeField' = '合并行字段',
  'mergeUniqueField' = '合并行判断字段',
  'background' = '背景色',
  'summarys' = '合计列',
  'sumFields' = '合计值字段',
  'averageFields' = '平均值字段',
  'joins' = '拼接字段组',
  'separator' = '拼接符号',
  'format' = '格式化',
  'preappend' = '列前置内容',
  'append' = '列后置内容',
  'colors' = '颜色',
  'toolTip' = '提示文本',
  'formStorage' = '收纳',
  'showHeader' = '是否显示头部',
  'thousands' = '千分位',
  'currency' = '币种',
  'prefix' = '金额前置符号',
  'justifyContent' = '组件水平布局',
  'extraList' = '插槽',
  'footer' = '展示默认底部按钮',
  'showSummary' = '是否展示合计',
  'validRules' = '列校验规则',
  'defaultRow' = '默认行数',
  'isShowAddFooterButton' = '是否展示新增一行',
  'summaryOption' = '合计配置',
  'pullDown' = '下拉搜索',
  'subtableField' = '子表字段',
  'isNumber' = '数字类型',
  'hashTableTransfer' = '字段映射',
  'border' = '边框',
  'logicDelete' = '逻辑删除',
  'editConfig' = '可编辑配置项',
  'titleComp' = '标题组件',
  'dashed' = '虚线',
  'orientation' = '分割线变体的位置',
  'orientationMargin' = '标题和最近边框的距离',
  'plain' = '普通正文格式',
  'isSummary' = '是否开启合计',
  'responsive' = '开启响应式',
  'preventCollision' = '防止碰撞',
  'verticalCompact' = '开启浮动布局',
  'authCode' = '权限码',
  'destroyInactiveTabPane' = '是否销毁隐藏tabs',
  'operationButtons' = '标题右侧按钮',
  'operationIcons' = '节点操作配置',
  'descriptions' = '详情配置',
  'tabSize' = '代码缩进',
  'lineNumbers' = '行号显示配置',
  'showColumnsSetting' = '开启列设置',
  'showLocalColumnsSetting' = '开启列设置（本地模式）',
  'showMessage' = '显示消息通知',
  'expandAction' = '子节点收起模式',
  'headField' = '头像字段名',
  'changePassword' = '展示修改密码',
  'logout' = '展示退出按钮',
  'showAddRow' = '展示“添加一行数据”按钮',
  'showInsertBackwards' = '展示"向下插入一行”按钮',
  'showInsertForward' = '展示“向上插入一行”按钮',
  'isShowDelBtn' = '展示"删除“',
  'typeConfiguration' = '展示类型',
  'disabledSelect' = '禁选日期配置',
  'autoHeight' = '表格高度自适应',
  'isMountToBody' = '选项菜单是否挂载在body上',
  // 'selectList' = '下拉选项',
  'isMaskFullScreen' = '遮罩层是否遮罩全屏',
  'isStopUpdate' = '禁止更新数据',
  'language' = '语言类型',
  'current' = '当前步骤序号',
  'direction' = '步骤条方向',
  'steps' = '步骤项',
  'subtitle' = '子标题',
  'placeSearch' = '开启地点搜索服务',
  'searchKey' = '搜索关键字',
  'placeSearchOptions' = '搜索服务配置',
  'city' = '城市',
  'citylimit' = '在限定城市内搜索',
  'panel' = '展示结果的dom节点id',
  'autoFitView' = '自动调整地图视野',
  'autoComplete' = '开启搜索关键字提示',
  'autoCompleteOptions' = '关键字提示配置',
  'input' = '搜索输入框dom节点id',
  'pageSize' = '每页条数',
  'pageIndex' = '页码',
  'emptyText' = '空数据描述文案',
  'customFilterDropdown' = '关键字搜索',
  'specificColor' = '文本颜色',
  'specificSize' = '字体大小',
  'textAlign' = '文字对齐',
  'isBackgroud' = '是否填充背景',
  'imgSize' = '图片大小',
  'isFullScreen' = '开启全屏按钮',
  'backgroundImg' = '背景图片',
  'minHeight' = '最小高度',
  'filter' = '快捷过滤',
  'upDownRule' = '增减符号',
  'showArrow' = '是否展示底部描述',
  'numFontSize' = '数据字体大小',
  'numUnit' = '数据单位',
  'indexShowImg' = '前三是否展示图标',
  'number' = '数据字段名',
  'xxxlCol' = '大于等于2000px展示的列数',
  'xxlCol' = '大于等于1600px展示的列数',
  'xlCol' = '大于等于1200px展示的列数',
  'lgCol' = '大于等于992px展示的列数',
  'mdCol' = '大于等于768px展示的列数',
  'smCol' = '大于等于576px展示的列数',
  'xsCol' = '小于576px展示的列数',
  'rightShow' = '是否展示设备信息icon',
  'iconShow' = '是否展示右上角内容',
  'maxLevel' = '选项最深层级',
  'payloadFieldNames' = '入参字段映射',
  'defaultOptions' = '默认选项',
  'defaultFirstOptionChecked' = '默认选中第一项',
  'rowDefaultValue' = '列默认值设置',
  'isInternational' = '是否国际化',
  // 'iconPosition' = '图标位置',
  'allowBreak' = '允许折行',
  'isAddable' = '是否可输入',
  'editType' = '编辑类型',
  'isShowCols' = '是否展示多列',
  'leftColumns' = '左边列配置',
  'rightColumns' = '右边列配置',
  'enableEnterShortcut' = '开启enter搜索快捷键',
  'dataset' = '数据集',
  'isPageable' = '开启分页',
  'pageField' = '请求入参页码字段',
  'sizeField' = '请求入参条数字段',
  'recordsField' = '请求返参数据集合字段',
  'totalField' = '请求返参总条数字段',
  'immediateFetch' = '开启立即请求数据',
  'isDefaultFindFunc' = '启用默认搜索功能',
  'isDefaultClearFunc' = '启用默认清空功能',
  'showConfirm' = '展示确认按钮',
  'showCancel' = '展示取消按钮',
  'isPadding' = '开启内边距',
  'showDragArea' = '开启拖拽/粘贴上传',
  'uploadImageListSortable' = '开启上传后图片拖拽排序',
  'labelSuffixIcon' = '标签后置图标',
  'isQueryOnNoPlan' = '无筛选方案时查询全部',
  'innerCard' = '关闭左右内边距',
}
export const selfdomDictionary = {
  ['lyy-rich-editor']: {
    height: '容器初始高度',
  },
  ['lyy-footer']: {
    leftExtra: '右侧组件',
  },
  ['lyy-erp-sku']: {
    id: 'key字段',
    name: '标题',
    isrequire: '必填',
    hasIcon: '有图标',
    standsField: 'sku列表字段',
    skuField: 'sku组合字段',
    uploadActions: '上传事件',
  },
  ['lyy-date-range']: {
    disabledOption: '日期选择范围配置',
  },
  ['lyy-edit-table']: {
    type: '单元格内容类型',
    actions: '交互事件',
  },
  ['lyy-excel-table']: {
    trigger: '触发方式',
    mode: '编辑模式',
    origin: '原始键值',
    target: '目标键值',
    field: '字段名',
    title: '表头名称',
    width: '列宽度',
    forbid: '禁止编辑配置',
    actions: '交互事件',
    disabled: '禁止编辑',
    show: '显隐配置',
  },
  ['lyy-icon']: {
    tooltip: '提示文本',
  },
  ['lyy-qrcode']: {
    title: '下载时展示的文件名称',
    size: '二维码尺寸',
    url: '二维码内容文本',
    download: '展示下载按钮',
  },
  ['lyy-row']: {
    insertNewRowBelow: '下方插入一行',
    insertNewRowAbove: '上方插入一行',
    align: '垂直对齐方式',
    justify: '水平对齐方式',
    gutter: '栅格间隔',
    wrap: '是否自动换行',
    responsive: '是否支持响应式',
    xxxlCol: '大于等于2000px展示的列数',
    xxlCol: '大于等于1600px展示的列数',
    xlCol: '大于等于1200px展示的列数',
    lgCol: '大于等于992px展示的列数',
    mdCol: '大于等于768px展示的列数',
    smCol: '大于等于576px展示的列数',
    xsCol: '小于576px展示的列数',
    lineGutter: '列间距',
    rowGutter: '行间距',
  },
  ['lyy-col']: {
    insertNewColumnLeft: '左侧插入一列',
    insertNewColumnRight: '右侧插入一列',
    span: '栅格占位格数',
    flex: '布局填充',
    offset: '栅格左侧的间隔格数',
    order: '栅格顺序，flex 布局模式下有效',
    pull: '栅格向左移动格数',
    push: '栅格向右移动格数',
    align: '水平对齐方式',
    // gap: '元素直接的间隔',
  },
  ['lyy-modal-selector-input']: {
    title: '弹窗标题',
    okText: '确定按钮文本',
    cancelText: '取消按钮文本',
  },
  ['lyy-table']: {
    width: '列宽',
    option: '列配置',
    childrens: '子组件',
    actions: '交互事件',
    dataset: '动态选项数据集',
    fieldNames: '动态选项字段映射',
  },
  ['lyy-table-page']: {
    filter: '开启高级筛选',
  },
  ['lyy-card']: {
    mainLayout: '高度撑满剩余屏幕',
  },
  ['lyy-cascader']: {
    fieldNames: '选项字段映射',
    children: '子节点字段',
    value: '值字段',
    label: '标签字段',
    level: '层级字段',
    customOption: '自定义设置',
    jsonPath: '取值字段',
  },
  ['lyy-checkbox']: {
    label: '标签字段',
    value: '值字段',
  },
  ['lyy-card-list']: {
    titleComp: '标题组件',
    extraList: '标题右侧组件',
    children: '内容区组件',
  },
  ['lyy-select']: {
    label: '显示文本',
    options: '下拉选项',
    value: '参数值',
  },
  ['lyy-label-text']: {
    type: '文本类型',
    showCopyBtn: '复制按钮',
    showDetailBtn: '详情按钮(点击事件)',
  },
  ['lyy-directory-tree']: {
    fieldNames: '数据字段映射',
    value: '值',
    rejectedChildren: '禁止拖拽的子节点',
  },
  ['lyy-image-previewer']: {
    width: '图片宽度',
    height: '图片高度',
  },
  ['lyy-input-group']: {
    align: 'flex下垂直对齐方式',
    justify: 'flex下水平排列方式',
    flex: '宽度',
    cols: '列宽设置',
    row: '行设置',
    gutter: '间隔宽度',
    wrap: '换行设置',
  },
  ['lyy-search-pro']: {
    colSpan: '展示列数',
  },
  ['lyy-popconfirm']: {
    type: '按钮类型',
  },
  ['lyy-grid']: {
    size: '栅格',
  },
  ['lyy-file-uploader']: {
    type: '触发按钮类型',
    tooltip: '说明文案',
  },
  ['lyy-iterator-form']: {
    span: '列数量',
  },
  ['lyy-advanced-filter']: {
    title: '弹窗标题',
  },
  ['lyy-select-merchant']: {
    mainTitle: '主面板标题',
    childTitle: '子面板标题',
    config: '主面板卡片字段配置',
    childConfig: '子面板卡片字段配置',
    titleField: '卡片标题字段',
    items: '卡片内容',
    tag: '父卡片标识文案',
  },
  ['lyy-steps']: {
    status: '当前步骤状态',
    description: '步骤的详情描述',
  },
  ['lyy-text-comp']: {
    text: '内容',
  },
  ['lyy-echarts']: {
    type: '图表类型',
    data: '图表数据',
  },
  ['lyy-iterator']: {
    layout: '排列方向',
  },
  ['lyy-address']: {
    addressField: '地址字段名',
    addressIdField: '地址ID字段名',
    addressLngLatField: '经纬度字段名',
    citylimit: '限制搜索城市',
    needDistrict: '需要省市区',
  },
  ['lyy-tabs']: {
    type: '样式类型',
  },
}

export const innerPropDictionary = {
  pipes: {
    fixed: '保留小数位数',
    suffix: '金额后置符号',
  },
  fieldOptions: {
    value: '值',
  },
}
export const columnsType = [
  {
    value: 'text',
    label: '纯文本',
  },
  {
    value: 'number',
    label: '数字',
  },
  {
    value: 'tags',
    label: '标签',
  },
  {
    value: 'image',
    label: '图片',
  },
  {
    value: 'link',
    label: '连接跳转',
  },
  {
    value: 'html',
    label: 'html',
  },
  {
    value: 'join',
    label: '合并',
  },
  {
    value: 'panel',
    label: 'panel',
  },
  {
    value: 'operation',
    label: '操作',
  },
]
export const excelColType = [
  {
    value: 'text',
    label: '纯文本',
  },
  {
    value: 'seq',
    label: '序号',
  },
  {
    value: 'switch',
    label: '开关',
  },
  {
    value: 'checkbox',
    label: '多选',
  },
  {
    value: 'file-uploader',
    label: '文件上传',
  },
  {
    value: 'cascader',
    label: '级联',
  },
  {
    value: 'number',
    label: '数字',
  },
  {
    value: 'date',
    label: '日期',
  },
  {
    value: 'image',
    label: '图片',
  },
  {
    value: 'operation',
    label: '操作',
  },
  {
    value: '数量子表',
    label: 'subtable',
  },
]
