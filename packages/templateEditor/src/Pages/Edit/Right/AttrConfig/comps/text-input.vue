<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { Dictionary, selfdomDictionary } from '../config'
import LyyBus from '@/bus/bus'
import { ATTR_CHANGE } from '@/enum/bus-typs'
import attrDescription from './attr-description.vue'
import simpleInput from './simple-input.vue'
defineComponent({
  name: 'text-input',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  originData: any // 数据源头
  dataKey: string // 当前键
}>()
const value = computed({
  get() {
    return props.data
  },
  set(value) {
    Object.assign(props.originData, {
      [props.dataKey]: value,
    })
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  },
})
const keyText = ref(
  selfdomDictionary[props.currentComp.compName] &&
    selfdomDictionary[props.currentComp.compName][props.dataKey]
    ? selfdomDictionary[props.currentComp.compName][props.dataKey]
    : Dictionary[props.dataKey],
)
</script>

<template>
  <div class="base-config">
    <div class="config-label">
      {{ keyText }}
      <attrDescription :dataKey="dataKey" :currentComp="currentComp" />
      :
    </div>
    <simpleInput
      v-model:modelValue="value"
      :placeholder="keyText"
    ></simpleInput>
  </div>
</template>

<style lang="less" scoped>
.config-label {
  margin-bottom: 8px;
}
.base-config {
  margin-bottom: 6px;
}
</style>
