<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'
import { Dictionary, selfdomDictionary } from '../config'
import LyyBus from '@/bus/bus'
import { ATTR_CHANGE } from '@/enum/bus-typs'
import attrDescription from './attr-description.vue'
import { evaluate } from 'amis-formula'
import ExpModal from '@/components/Modal/ExpModal/index.vue'
import { expNoWrap } from './complex'
defineComponent({
  name: 'text-input',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  originData: any // 数据源头
  dataKey: string // 当前键
  dataKeyText?: string // 键文本
}>()
const value = computed({
  get() {
    return props.data
  },
  set(value) {
    Object.assign(props.originData, {
      [props.dataKey]: value,
    })
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  },
})
// const keyText = ref(
//   selfdomDictionary[props.currentComp.compName] &&
//     selfdomDictionary[props.currentComp.compName][props.dataKey]
//     ? selfdomDictionary[props.currentComp.compName][props.dataKey]
//     : Dictionary[props.dataKey],
// )

const keyText = computed(() => {
  return props.dataKeyText ?? (selfdomDictionary[props.currentComp.compName] &&
    selfdomDictionary[props.currentComp.compName][props.dataKey]
    ? selfdomDictionary[props.currentComp.compName][props.dataKey]
    : Dictionary[props.dataKey])
})

const wrapValue = computed(() => {
  return !expNoWrap.includes(props.dataKey)
})
</script>

<template>
  <div class="base-config">
    <div class="config-label">
      {{ keyText }}
      <attrDescription :dataKey="dataKey" :currentComp="currentComp" />
      :
    </div>
    <ExpModal
      :hasLabel="false"
      v-model:modelValue="value"
      :show-textarea="true"
      :wrapValue="wrapValue"
    />
  </div>
</template>

<style lang="less" scoped>
.config-label {
  margin-bottom: 8px;
}
.base-config {
  margin-bottom: 6px;
}
</style>
