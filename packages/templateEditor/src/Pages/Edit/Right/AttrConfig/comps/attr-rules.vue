<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'
import combinationInput from './combination-input.vue'
import LyyBus from '@/bus/bus'
import { ATTR_CHANGE } from '@/enum/bus-typs'
defineComponent({
  name: 'attr-rules',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  originData: any // 当前数据源
  dataKey: string // 当前键
}>()
const value = computed({
  get() {
    return props.data
  },
  set(value) {
    Object.assign(props.originData, {
      [props.dataKey]: formatValue(value),
    })
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  },
})
const formatValue = (list) => {
  for (const e of list) {
    if (e.pattern) {
      e.pattern = new RegExp(e.pattern + '')
    }
  }
  return list
}
</script>

<template>
  <combinationInput
    :dataKey="dataKey"
    v-model:data="value"
    :currentComp="currentComp"
  ></combinationInput>
</template>

<style lang="less" scoped></style>
