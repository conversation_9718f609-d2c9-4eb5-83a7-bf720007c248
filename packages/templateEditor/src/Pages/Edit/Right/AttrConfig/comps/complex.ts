export const itemComp = {
  required: 'switch',
  decompose: 'switch',
  trigger: 'select',
  unit: 'select',
  mode: 'select',
  disabled: 'switch',
  hideAtRoot: 'switch',
  show: 'switch',
  checkStrictly: 'switch',
  fixed: 'switch',
  option: 'complex',
  fields: 'array',
  sumFields: 'array',
  averageFields: 'array',
  fieldNames: 'object',
  style: 'object',
  spin: 'switch',
  pipe: 'select',
  resizable: 'switch',
  logicDelete: 'switch',
  description: 'array',
  thousands: 'switch',
  currency: 'select',
  colSpan: 'select',
  pipes: {
    fixed: 'input',
  },
  pattern: 'input',
  rules: {
    pattern: 'input',
  },
  type: 'select',
  expandAction: 'select',
  // columns: {
  //   type: 'select',
  // },
  defaultValue: 'dynamicObject',
  align: 'select',
  justify: 'select',
  exp: 'exp',
  placement: 'select',
  color: 'colorPicker',
  menuList: 'buttonList',
  changePassword: 'switch',
  logout: 'switch',
  allowClear: 'switch',
  closable: 'switch',
  mask: 'switch',
  maskClosable: 'switch',
  citylimit: 'switch',
  autoFitView: 'switch',
  customFilterDropdown: 'switch',
  iconName: 'icon',
  icon: 'icon',
}
export const itemOptions = {
  ['trigger']: [
    { text: '失去焦点', value: 'blur' },
    { text: '值变动', value: 'change' },
  ],
  ['unit']: [
    { text: '日', value: 'day' },
    { text: '月', value: 'month' },
    { text: '年', value: 'year' },
  ],
  ['pipe']: [
    { text: '数字', value: 'decimal', option: { fixed: 2, thousands: true } },
    {
      text: '金额',
      value: 'currency',
      option: {
        currency: '',
        prefix: '',
        suffix: '',
        fixed: 2,
        thousands: true,
      },
    },
    { text: '百分比', value: 'percent', option: { fixed: 2 } },
    { text: '日期', value: 'date', option: { format: '' } },
    {
      text: '枚举映射',
      value: 'textmap',
      option: { textMap: [{ origin: '', result: '' }] },
    },
    {
      text: '数组字符拼接',
      value: 'join',
      option: { separator: '', filterField: '' },
    },
  ],
  ['currency']: [
    { value: 'CNY', text: 'CNY' },
    { value: 'USD', text: 'USD' },
    { value: 'EUR', text: 'EUR' },
    { value: 'GBP', text: 'GBP' },
    { value: 'CHF', text: 'CHF' },
    { value: 'JPY', text: 'JPY' },
    { value: 'KRW', text: 'KRW' },
    { value: 'HKD', text: 'HKD' },
  ],
  ['type']: [
    { value: 'operation', text: '操作' },
    { value: 'other', text: '其他' },
    { value: 'number', text: '数字' },
    { value: 'string', text: '字符串' },
  ],
  ['align']: [
    { value: 'top', text: '居上对齐' },
    { value: 'middle', text: '居中对齐' },
    { value: 'bottom', text: '居下对齐' },
  ],
  ['justify']: [
    { value: 'start', text: '从起始线开始排列' },
    { value: 'end', text: '从终止线开始排列' },
    { value: 'center', text: '在中间排列' },
    { value: 'space-between', text: '元素之间间隔相等' },
    { value: 'space-around', text: '元素左右空间相等' },
  ],
  ['placement']: [
    { value: 'top', text: '表格顶部' },
    { value: 'bottom', text: '表格底部' },
    { value: 'table', text: '属于表格里面的底部' },
  ],
  ['color']: [
    { value: '#0987f5', text: '主体色' },
    { value: '#52c41a', text: '成功色' },
    { value: '#f5222d', text: '危险色' },
    { value: '#fa8c16', text: '警告色' },
    { value: '#bfbfbf', text: '禁用色' },
    { value: '#fadb14', text: '鹅黄色' },
    { value: '#13c2c2', text: '湖蓝色' },
    { value: '#722ed1', text: '酱紫色' },
    { value: '#eb2f96', text: '洋红色' },
  ],
}
export const keyOptionsSelf = {
  ['editConfig']: {
    trigger: [
      { text: '单击', value: 'click' },
      { text: '双击', value: 'dblclick' },
    ],
    mode: [
      { text: '单元格编辑模式', value: 'cell' },
      { text: '行编辑模式', value: 'row' },
    ],
  },
  ['descriptions']: {
    type: [
      { text: '文字描述', value: 'text' },
      { text: '表格', value: 'table' },
    ],
  },
  ['columns']: {
    fixed: [
      { value: 'left', text: '左边' },
      { value: 'right', text: '右边' },
    ],
  },
  ['rules']: {
    type: [
      { text: '字符串', value: 'string' },
      { text: '数字', value: 'number' },
      { text: '数组', value: 'array' },
    ],
  },
  ['typeConfiguration']: {
    type: [
      {
        text: '弹窗',
        value: 'modal',
        option: {
          btnText: '筛选',
          title: '筛选',
          width: '',
          okText: '',
          closable: false,
          mask: true,
          maskClosable: false,
        },
      },
      {
        text: '盒子',
        value: 'box',
        option: {},
      },
    ],
  },
  ['disabledSelect']: {
    type: [
      { text: '往前禁选', value: 'befor' },
      { text: '往后禁选', value: 'after' },
    ],
  },
  ['disabledOption']: {
    type: [
      { text: '往前禁选', value: 'befor' },
      { text: '往后禁选', value: 'after' },
    ],
  },
  ['options']: {
    color: [
      { value: 'processing', text: '主要' },
      { value: 'default', text: '默认' },
      { value: 'success', text: '成功' },
      { value: 'error', text: '失败' },
      { value: 'warning', text: '警告' },
      { value: 'magenta', text: '洋红色' },
      { value: 'red', text: '红色' },
      { value: 'volcano', text: '橙红色' },
      { value: 'orange', text: '橙色' },
      { value: 'gold', text: '金色' },
      { value: 'lime', text: '浅绿色' },
      { value: 'green', text: '绿色' },
      { value: 'pink', text: '粉色' },
      { value: 'cyan', text: '蓝绿色' },
      { value: 'blue', text: '蓝色' },
      { value: 'geekblue', text: '深蓝色' },
      { value: 'purple', text: '紫色' },
    ],
  },
}
const formItems = [
  {
    value: 'lyy-text-input',
    label: '文本框',
  },
  {
    value: 'lyy-number-input',
    label: '数字输入框',
  },
  {
    value: 'lyy-select',
    label: '下拉框',
  },
  {
    value: 'lyy-select-pro',
    label: '下拉框-带添加',
  },
  {
    value: 'lyy-date-range',
    label: '日期范围',
  },
  {
    value: 'lyy-date-picker',
    label: '日期',
  },
  {
    value: 'lyy-time-range',
    label: '时间范围',
  },
  {
    value: 'lyy-radio',
    label: '单选框',
  },
  {
    value: 'lyy-checkbox',
    label: '多选框',
  },
]
const formItemList = [
  {
    value: 'lyy-input-range',
    label: '范围输入框',
  },
  {
    value: 'lyy-text-area',
    label: '多行输入框',
  },
  {
    value: 'lyy-switch',
    label: '开关',
  },
  {
    value: 'lyy-input-group',
    label: '组合表单',
  },
  {
    value: 'lyy-input-password',
    label: '密码输入框',
  },
  {
    value: 'lyy-cascader',
    label: '级联选择框',
  },
  {
    value: 'lyy-file-uploader',
    label: '文件上传',
  },
  {
    value: 'lyy-editor',
    label: '代码编辑器',
  },
  {
    value: 'lyy-rich-editor',
    label: '富文本编辑器',
  },
  {
    value: 'lyy-tree-select',
    label: '树下拉框',
  },
  {
    value: 'lyy-pull-down-selector-input',
    label: '下拉列表选择',
  },
  {
    value: 'lyy-formula',
    label: '计算组件',
  },
]
export const searchFormListOptions = {
  children: [
    {
      value: 'lyy-label-text',
      label: '描述组件',
    },
    {
      value: 'lyy-label-block',
      label: '块状描述组件',
    },
    ...formItems,
  ],
  ['lyy-input-group']: {
    children: [...formItems],
  },
  ['lyy-grid']: {
    children: [
      {
        value: 'lyy-label-text',
        label: '描述组件',
      },
      {
        value: 'lyy-label-block',
        label: '块状描述组件',
      },
      {
        value: 'lyy-tags',
        label: '标签组',
      },
      {
        value: 'lyy-tags-create',
        label: '创建标签',
      },
      ...formItems,
      ...formItemList,
    ],
  },
  ['lyy-erp-action-bar']: {
    extraList: [
      {
        value: 'lyy-text-input',
        label: '输入框',
      },
      {
        value: 'lyy-label-text',
        label: '描述组件',
      },
    ],
  },
  ['lyy-footer']: {
    extraList: [
      {
        value: 'lyy-formula',
        label: '计算组件',
      },
      {
        value: 'lyy-label-text',
        label: '描述组件',
      },
    ],
  },
}

export const searchFormListTitle = {
  children: '子组件',
  extraList: '右侧组件',
}

export const searchFormListLength = {
  ['lyy-input-group']: {
    children: 2,
  },
}

export const expNoWrap = ['exp']
