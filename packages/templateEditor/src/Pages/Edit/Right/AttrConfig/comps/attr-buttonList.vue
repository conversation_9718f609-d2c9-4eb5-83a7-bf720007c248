<script lang="ts" setup>
import { defineComponent, defineProps, ref } from 'vue'
import Draggable from 'vuedraggable'
import { Dictionary, selfdomDictionary } from '../config'
import CompFormItem from '@/Model/formItem'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import ActionConfig from '@/Pages/Edit/Right/ActionConfig'
import _ from 'lodash'
defineComponent({
  name: 'attr-button-list',
})

const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  originData: any // 当前数据源
  dataKey: string // 当前键
}>()

const compModels = [CompFormItem]

const getTargetCompModel = (key) => {
  for (const compList of compModels) {
    if (compList.keys().includes(key)) {
      return compList(key)
    }
  }
}
// 删除某一项
const removeItem = (index) => {
  props.data.splice(index, 1)
}
// 新增一项
const add = () => {
  const target = addAliasNameAnduuid(
    _.cloneDeep(getTargetCompModel('./lyy-button-model.ts').default),
  )
  props.data.push(target)
}
</script>

<template>
  <div class="attr-searchFormList">
    <h4>
      {{
        selfdomDictionary[currentComp.compName] &&
        selfdomDictionary[currentComp.compName][dataKey]
          ? selfdomDictionary[currentComp.compName][dataKey]
          : Dictionary[dataKey]
      }}
    </h4>

    <div class="config-list" v-if="props.data.length > 0">
      <caret-up-outlined class="up" />
      <Draggable :list="props.data" handle=".handle">
        <template #item="{ element, index }">
          <div>
            <div class="config-box">
              <!-- 拖拽按钮 -->
              <div class="config-item">
                <unordered-list-outlined class="handle cursor" />
              </div>
              <!-- 文本 -->
              <div class="config-item grow">
                <a-input
                  :size="'small'"
                  v-model:value="element.prop.text"
                  placeholder="文本"
                ></a-input>
              </div>
              <!-- 图标 -->
              <div class="grow config-item">
                <a-input
                  size="small"
                  placeholder="请输入图标"
                  v-model:value="element.prop.icon"
                ></a-input>
              </div>
              <!-- 删除按钮 -->
              <div class="grow">
                <delete-outlined
                  class="delete-icon"
                  @click="removeItem(index)"
                />
              </div>
            </div>
          </div>
        </template>
      </Draggable>
    </div>
    <div class="add-button" v-if="!maxLength || props.data.length < maxLength">
      <a-button block @click="add">+ 新增</a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.up {
  position: absolute;
  top: -24px;
  left: 12px;
  color: #f7f7f9;
  font-size: 48px;
}
.attr-searchFormList {
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  margin-bottom: 12px;
}
.config-list {
  position: relative;
  background-color: #f7f7f9;
  padding: 16px 6px 6px;
  border-radius: 2px;
  margin: 12px 0;
}
.config-box {
  margin-bottom: 4px;
}
.config-field {
  margin-bottom: 8px;
  margin-left: 18px;
  margin-right: 18px;
}
.config-box {
  display: flex;
}
.config-item {
  margin-right: 6px;
  font-size: 12px;
}
.delete-icon {
  color: red;
  font-size: 12px;
  cursor: pointer;
}
.cursor {
  cursor: pointer;
  padding-top: 6px;
}
</style>
