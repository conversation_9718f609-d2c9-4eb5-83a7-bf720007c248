<script setup lang="ts">
import { computed, defineComponent } from 'vue'
import { Dictionary, selfdomDictionary } from '../config'
import LyyBus from '@/bus/bus'
import { ATTR_CHANGE } from '@/enum/bus-typs'
import attrDescription from './attr-description.vue'
defineComponent({
  name: 'textarea-input',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  dataKey: string // 当前键
}>()
const value = computed({
  get() {
    return props.data
  },
  set(value) {
    let val
    try {
      val = eval(value)
    } catch {
      val = value
    }
    Object.assign(props.currentComp.prop, {
      [props.dataKey]: val,
    })
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  },
})
</script>

<template>
  <div class="base-config">
    <div class="config-label">
      {{
        selfdomDictionary[currentComp.compName] &&
        selfdomDictionary[currentComp.compName][dataKey]
          ? selfdomDictionary[currentComp.compName][dataKey]
          : Dictionary[dataKey]
      }}
      <attrDescription :dataKey="dataKey" :currentComp="currentComp" />
      :
    </div>
    <a-textarea v-model:value="value" :rows="7"></a-textarea>
  </div>
</template>

<style lang="less" scoped>
.config-label {
  margin-bottom: 8px;
}
.base-config {
  margin-bottom: 6px;
}
</style>
