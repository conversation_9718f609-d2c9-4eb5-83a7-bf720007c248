<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'
import attrIconItem from './attr-icon-item.vue'
import LyyBus from '@/bus/bus'
import { ATTR_CHANGE } from '@/enum/bus-typs'
defineComponent({
  name: 'attr-icon',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  dataKey: string // 当前键
}>()
const value = computed({
  get() {
    return props.data
  },
  set(value) {
    // eslint-disable-next-line vue/no-mutating-props
    Object.assign(props.currentComp.prop, {
      [props.dataKey]: value,
    })
    LyyBus.$emitUpdateComp(ATTR_CHANGE)
  },
})
</script>

<template>
  <attrIconItem
    :dataKey="dataKey"
    v-model:data="value"
    :currentComp="currentComp"
  ></attrIconItem>
</template>
