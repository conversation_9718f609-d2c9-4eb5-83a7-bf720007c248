<script lang="ts" setup>
import { defineComponent, defineProps } from 'vue'
import { Dictionary, selfdomDictionary } from '../config'
import Draggable from 'vuedraggable'
import CompFormItem from '@/Model/formItem'
import CompPresentational from '@/Model/presentational'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import _ from 'lodash'
defineComponent({
  name: 'attr-card-list-item',
})

const props = defineProps<{
  currentComp: any // 当前组件配置
  data: any // 当前值
  originData: any // 当前数据源
  dataKey: string // 当前键
}>()

const compModels = [CompFormItem, CompPresentational]

const getTargetCompModel = (key) => {
  for (const compList of compModels) {
    if (compList.keys().includes(key)) {
      return compList(key)
    }
  }
}

// 下拉选择时 替换组件模型
const compTypeChange = (newCompName, element, index) => {
  // 取出模型 替换目标组件
  const target = addAliasNameAnduuid(
    _.cloneDeep(getTargetCompModel(`./${newCompName}-model.ts`).default),
  )
  target.prop.field = element.prop.field
  // 替换某一项
  props.data.splice(index, 1, target)
}
// 删除某一项
const removeFormItem = (index) => {
  props.data.splice(index, 1)
}
// 新增一项
const add = () => {
  const target = addAliasNameAnduuid(
    _.cloneDeep(getTargetCompModel('./lyy-html-model.ts').default),
  )
  props.data.push(target)
}

const defaultOptions = [
  {
    value: 'lyy-html',
    label: '文本',
  },
  {
    value: 'lyy-link',
    label: '链接',
  },
  {
    value: 'lyy-button',
    label: '按钮',
  },
  {
    value: 'lyy-label-text',
    label: '描述',
  },
  {
    value: 'lyy-tags',
    label: '标签',
  },
  {
    value: 'lyy-qrcode',
    label: '二维码',
  },
]

const options = defaultOptions
const maxLength = undefined

const listTitle = {
  children: '子组件',
  extra: '标题右侧组件',
  title: '标题组件',
}
const title = listTitle[props.dataKey] ?? '组件配置'
</script>

<template>
  <div class="attr-searchFormList">
    <h4>{{ title }}</h4>

    <div class="config-list">
      <caret-up-outlined class="up" />
      <Draggable :list="props.data" handle=".handle">
        <template #item="{ element, index }">
          <div class="config-box">
            <!-- 拖拽按钮 -->
            <div class="config-item">
              <unordered-list-outlined class="handle cursor" />
            </div>
            <!-- 字段 -->
            <div class="config-item grow">
              <a-input
                :size="'small'"
                v-model:value="element.prop.field"
                placeholder="字段"
              ></a-input>
            </div>
            <!-- 类型 -->
            <div class="grow config-item">
              <a-select
                :value="element.compName"
                class="select"
                placeholder="文本"
                size="small"
                @change="compTypeChange($event, element, index)"
                :options="options"
              ></a-select>
            </div>
            <!-- 删除按钮 -->
            <div class="grow">
              <delete-outlined
                class="delete-icon"
                @click="removeFormItem(index)"
              />
            </div>
          </div>
        </template>
      </Draggable>
    </div>
    <div class="add-button" v-if="!maxLength || props.data.length < maxLength">
      <a-button block @click="add">+ 新增</a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.up {
  position: absolute;
  top: -24px;
  left: 12px;
  color: #f7f7f9;
  font-size: 48px;
}
.attr-searchFormList {
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  margin-bottom: 12px;
}
.config-list {
  position: relative;
  background-color: #f7f7f9;
  padding: 16px 6px 6px;
  border-radius: 2px;
  margin: 12px 0;
}
.config-box {
  margin-bottom: 8px;
}
.config-box {
  display: flex;
}
.config-item {
  margin-right: 6px;
  font-size: 12px;
}
.delete-icon {
  color: red;
  font-size: 12px;
  cursor: pointer;
}
.cursor {
  cursor: pointer;
  padding-top: 6px;
}
</style>
