<script setup lang="ts">
import { defineComponent } from 'vue'
import LyyCanvas from '@/context/canvas'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import _ from 'lodash'

defineComponent({
  name: 'row-attr',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
}>()

const handleClick = (direction: number) => {
  const pageConfig = LyyCanvas.getInstance().getPageConfig()
  insertNewRow(direction)(props.currentComp, pageConfig)
}
// 循环页面配置，根据 compId 寻找目标，处理对应 callback
function loop(pageConfig, compId, callback) {
  pageConfig.some((compConfig, index) => {
    if (compConfig.compId === compId) {
      callback(pageConfig, compId, index)
      return true
    }
    if (compConfig?.childrens?.length) {
      loop(compConfig.childrens, compId, callback)
    }
  })
}

/**
 *
 * @param direction 插入方向
 * @returns
 */
function insertNewRow(direction = 0) {
  return (currentComp, pageConfig) => {
    const { compId } = currentComp
    // 复制行
    const cloneCurrentComp = addAliasNameAnduuid(_.cloneDeep(currentComp))
    // 清空行的事件
    cloneCurrentComp.actions = []
    cloneCurrentComp.childrens = cloneCurrentComp.childrens.map((child) => {
      // 清空列的内容、事件
      child.childrens = []
      child.actions = []
      return addAliasNameAnduuid(child)
    })
    // 插入到页面配置
    function cb(configList, compId, index) {
      // 插入位置
      const insertPosition = index + direction
      if (insertPosition < 0) {
        configList.unshift(cloneCurrentComp)
        return
      }
      if (insertPosition >= configList.length) {
        configList.push(cloneCurrentComp)
        return
      }
      configList.splice(insertPosition, 0, cloneCurrentComp)
    }
    loop(pageConfig, compId, cb)
  }
}
</script>

<template>
  <div class="base-config">
    <a-button
      @click="handleClick(1)"
      :style="{
        'margin-right': '8px',
      }"
    >
      <template #icon>
        <vertical-align-bottom-outlined />
      </template>
      下方插入一行
    </a-button>
    <a-button
      @click="handleClick(0)"
      :style="{
        'margin-top': '8px',
      }"
    >
      <template #icon>
        <vertical-align-top-outlined />
      </template>
      上方插入一行
    </a-button>
  </div>
</template>

<style lang="less" scoped>
.config-label {
  margin-bottom: 8px;
}
.base-config {
  margin-bottom: 6px;
}
</style>
