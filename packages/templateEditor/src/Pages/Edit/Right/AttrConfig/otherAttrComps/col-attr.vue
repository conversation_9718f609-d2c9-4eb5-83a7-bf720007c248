<script setup lang="ts">
import { defineComponent } from 'vue'
import LyyCanvas from '@/context/canvas'
import _ from 'lodash'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import { message } from 'ant-design-vue'

defineComponent({
  name: 'col-attr',
})
const props = defineProps<{
  currentComp: any // 当前组件配置
}>()

const handleClick = (direction: number) => {
  const pageConfig = LyyCanvas.getInstance().getPageConfig()
  insertNewColumn(direction)(props.currentComp, pageConfig)
}

// 循环页面配置，根据 compId 寻找目标，处理对应 callback
function loop(pageConfig, compId, callback) {
  pageConfig.some((compConfig, index) => {
    if (compConfig.compId === compId) {
      callback(pageConfig, compId, index)
      return true
    }
    if (compConfig?.childrens?.length) {
      loop(compConfig.childrens, compId, callback)
    }
  })
}

/**
 *
 * @param direction 插入方向
 * @returns
 */
function insertNewColumn(direction = 0) {
  return (currentComp, pageConfig) => {
    const { compId } = currentComp

    // 当前有多少列
    let colCount = 0
    // 重新计算每一列的 span 占比
    let newSpan: number | null = null
    function calcSpanCol(configList) {
      colCount = configList.length
      if (colCount > 3) {
        return
      }
      const averageSpan = 24 / colCount
      const isAverage = configList.every((comp) => {
        return comp.prop.span === averageSpan
      })
      if (isAverage) {
        newSpan = 24 / (colCount + 1)
        for (const comp of configList) {
          comp.prop.span = newSpan
        }
      } else {
        newSpan = currentComp.prop.span / 2
        currentComp.prop.span = newSpan
      }
    }
    loop(pageConfig, compId, calcSpanCol)
    // 最多只能添加四列布局
    // if (colCount > 3) {
    //   message.warning('根据设计规范，最多只能四列布局')
    //   return
    // }

    // 复制列并且生成新的compId
    let newComp=_.cloneDeep(currentComp)
    newComp.compId=""
    const cloneCurrentComp = addAliasNameAnduuid(newComp)
    // 清空列的事件
    cloneCurrentComp.actions = []
    // 插入到页面配置
    function cb(configList, compId, index) {
      // 插入位置
      const insertPosition = index + direction
      if (insertPosition < 0) {
        configList.unshift(cloneCurrentComp)
        return
      }
      if (insertPosition >= configList.length) {
        configList.push(cloneCurrentComp)
        return
      }
      configList.splice(insertPosition, 0, cloneCurrentComp)
    }
    loop(pageConfig, compId, cb)
  }
}
</script>

<template>
  <div class="base-config">
    <a-button
      @click="handleClick(0)"
      :style="{
        'margin-right': '8px',
      }"
    >
      <template #icon>
        <vertical-align-left-outlined />
      </template>
      左侧插入一列
    </a-button>
    <a-button
      @click="handleClick(1)"
      :style="{
        'margin-top': '8px',
      }"
    >
      <template #icon>
        <vertical-align-right-outlined />
      </template>
      右侧插入一列
    </a-button>
  </div>
</template>

<style lang="less" scoped>
.config-label {
  margin-bottom: 8px;
}
.base-config {
  margin-bottom: 6px;
}
</style>
