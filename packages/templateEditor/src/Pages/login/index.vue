<template>
  <div class="loading-wrap">
    <a-spin size="large" tip="正在登陆..." />
  </div>
</template>
<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { onBeforeMount, defineComponent, ref } from 'vue'
import { getLoginUrlApi, loginApi } from '../../request/api/sso'
import { notification } from 'ant-design-vue'
const route = useRoute()

const router = useRouter()
const systemBaseUrl = ref('gw/editor')
const getLogin = async () => {
  const res = await getLoginUrlApi(systemBaseUrl.value)
  const { resultCode, data, description, result, code, body } = res
  if (result === 0 || code === '0000000') {
    const datatemp = data || body
    if (datatemp.ssoLogin) {
      location.href = `${datatemp.ssoLoginUrl}&returnUrl=${encodeURIComponent(
        location.href,
      )}`
    } else {
      notification.error({
        message: '登陆失败',
        description:
          description || '该系统还没有接入统一认证系统，请联系管理员！',
        duration: 2,
      })
    }
  }
}

const login = async () => {
  const { ticket } = route.query
  const res = await loginApi({ ticket: ticket as string }, systemBaseUrl.value)
  const { result, data, code, body } = res
  if (result === 0 || code === '0000000') {
    const resData = data || body
    if (systemBaseUrl.value === 'gw/editor') {
      sessionStorage.setItem('ramToken', resData.token)
    } else {
      sessionStorage.setItem('token', resData.token || '')
    }
    router.push({
      path: '/applist',
    })
  } else {
    // getLogin()
    notification.error({
      message: '登陆失败',
      duration: 2,
    })
    router.push('/appList')
  }
}

const loginInit = () => {
  const ticket = route.query.ticket
  // ticket存在自动登陆, 不存在获取登陆地址，跳转到统一认证中心
  if (ticket) {
    login()
  } else {
    getLogin()
  }
}

onBeforeMount(() => {
  switch (route.name) {
    case 'selogin': {
      systemBaseUrl.value = 'equipment'

      break
    }
    case 'ramlogin': {
      systemBaseUrl.value = 'gw/ram-admin'

      break
    }
    default: {
      systemBaseUrl.value = 'gw/editor'
    }
  }
  loginInit()
  console.log('~~~~~~~~~~~~~~~~``login~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~`')
})
</script>
<script lang="ts">
export default defineComponent({
  name: 'login',
})
</script>
<style lang="scss" scoped>
.loading-wrap {
  width: 100%;
  height: 100%;
}
</style>
