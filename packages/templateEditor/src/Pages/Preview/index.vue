<template>
  <a-spin :tip="tip" :spinning="spinning" size="large">
    <lyy-container
      :childrens="[]"
      :modelValue="useValue(ctb6532d6s)"
      :prop="ctb6532d6s.prop"
      :style="{ background: '#F3F6FB', padding: '0px' }"
      :compId="'ctb6532d6s'"
      id="ctb6532d6s"
      @update:modelValue="handleUpdate($event, ctb6532d6s)"
      @action="handleAction($event, ctb6532d6s)"
    >
      <lyy-form
        :childrens="[]"
        :modelValue="useValue(clgypcoir7)"
        :prop="clgypcoir7.prop"
        :style="{}"
        :compId="'clgypcoir7'"
        id="clgypcoir7"
        @update:modelValue="handleUpdate($event, clgypcoir7)"
        @action="handleAction($event, clgypcoir7)"
      >
        <lyy-row
          :childrens="[]"
          :modelValue="useValue(row7mdgp9k2y)"
          :prop="row7mdgp9k2y.prop"
          :style="{}"
          :compId="'row7mdgp9k2y'"
          id="row7mdgp9k2y"
          @update:modelValue="handleUpdate($event, row7mdgp9k2y)"
          @action="handleAction($event, row7mdgp9k2y)"
        >
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cb85bt6eow)"
            :prop="cb85bt6eow.prop"
            :style="{}"
            :compId="'cb85bt6eow'"
            id="cb85bt6eow"
            @update:modelValue="handleUpdate($event, cb85bt6eow)"
            @action="handleAction($event, cb85bt6eow)"
          >
            <lyy-row
              :childrens="[]"
              :modelValue="useValue(cth7ovfbei)"
              :prop="cth7ovfbei.prop"
              :style="{}"
              :compId="'cth7ovfbei'"
              id="cth7ovfbei"
              @update:modelValue="handleUpdate($event, cth7ovfbei)"
              @action="handleAction($event, cth7ovfbei)"
            >
              <lyy-col
                :childrens="[]"
                :modelValue="useValue(c7oea581q8)"
                :prop="c7oea581q8.prop"
                :style="{}"
                :compId="'c7oea581q8'"
                id="c7oea581q8"
                @update:modelValue="handleUpdate($event, c7oea581q8)"
                @action="handleAction($event, c7oea581q8)"
              >
                <lyy-select
                  :childrens="[]"
                  :modelValue="useValue(cxlmk7uz6n)"
                  :prop="cxlmk7uz6n.prop"
                  :style="{}"
                  :compId="'cxlmk7uz6n'"
                  id="cxlmk7uz6n"
                  @update:modelValue="handleUpdate($event, cxlmk7uz6n)"
                  @action="handleAction($event, cxlmk7uz6n)"
                ></lyy-select>
              </lyy-col>
              <lyy-col
                :childrens="[]"
                :modelValue="useValue(cjkzqj5cp9)"
                :prop="cjkzqj5cp9.prop"
                :style="{}"
                :compId="'cjkzqj5cp9'"
                id="cjkzqj5cp9"
                @update:modelValue="handleUpdate($event, cjkzqj5cp9)"
                @action="handleAction($event, cjkzqj5cp9)"
              >
                <lyy-select
                  :childrens="[]"
                  :modelValue="useValue(c69txqij6e)"
                  :prop="c69txqij6e.prop"
                  :style="{}"
                  :compId="'c69txqij6e'"
                  id="c69txqij6e"
                  @update:modelValue="handleUpdate($event, c69txqij6e)"
                  @action="handleAction($event, c69txqij6e)"
                ></lyy-select>
              </lyy-col>
              <lyy-col
                :childrens="[]"
                :modelValue="useValue(c3dsns6s2w)"
                :prop="c3dsns6s2w.prop"
                :style="{}"
                :compId="'c3dsns6s2w'"
                id="c3dsns6s2w"
                @update:modelValue="handleUpdate($event, c3dsns6s2w)"
                @action="handleAction($event, c3dsns6s2w)"
              >
                <lyy-select
                  :childrens="[]"
                  :modelValue="useValue(cstjzstrxl)"
                  :prop="cstjzstrxl.prop"
                  :style="{}"
                  :compId="'cstjzstrxl'"
                  id="cstjzstrxl"
                  @update:modelValue="handleUpdate($event, cstjzstrxl)"
                  @action="handleAction($event, cstjzstrxl)"
                ></lyy-select>
              </lyy-col>
            </lyy-row>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cfvfjcjrwe)"
            :prop="cfvfjcjrwe.prop"
            :style="{}"
            :compId="'cfvfjcjrwe'"
            id="cfvfjcjrwe"
            @update:modelValue="handleUpdate($event, cfvfjcjrwe)"
            @action="handleAction($event, cfvfjcjrwe)"
          >
            <lyy-row
              :childrens="[]"
              :modelValue="useValue(lebmut4xp)"
              :prop="lebmut4xp.prop"
              :style="{}"
              :compId="'lebmut4xp'"
              id="lebmut4xp"
              @update:modelValue="handleUpdate($event, lebmut4xp)"
              @action="handleAction($event, lebmut4xp)"
            >
              <lyy-col
                :childrens="[]"
                :modelValue="useValue(ca0z6payji)"
                :prop="ca0z6payji.prop"
                :style="{}"
                :compId="'ca0z6payji'"
                id="ca0z6payji"
                @update:modelValue="handleUpdate($event, ca0z6payji)"
                @action="handleAction($event, ca0z6payji)"
              >
                <lyy-select
                  :childrens="[]"
                  :modelValue="useValue(select6ypm0fka0)"
                  :prop="select6ypm0fka0.prop"
                  :style="{}"
                  :compId="'select6ypm0fka0'"
                  id="select6ypm0fka0"
                  @update:modelValue="handleUpdate($event, select6ypm0fka0)"
                  @action="handleAction($event, select6ypm0fka0)"
                ></lyy-select>
              </lyy-col>
              <lyy-col
                :childrens="[]"
                :modelValue="useValue(cy8t6542tt)"
                :prop="cy8t6542tt.prop"
                :style="{}"
                :compId="'cy8t6542tt'"
                id="cy8t6542tt"
                @update:modelValue="handleUpdate($event, cy8t6542tt)"
                @action="handleAction($event, cy8t6542tt)"
              >
                <lyy-date-picker
                  v-if="showo92oqfvia"
                  :childrens="[]"
                  :modelValue="useValue(o92oqfvia)"
                  :prop="o92oqfvia.prop"
                  :style="{}"
                  :compId="'o92oqfvia'"
                  id="o92oqfvia"
                  @update:modelValue="handleUpdate($event, o92oqfvia)"
                  @action="handleAction($event, o92oqfvia)"
                ></lyy-date-picker>
                <lyy-date-picker
                  v-if="showucoo5crdv"
                  :childrens="[]"
                  :modelValue="useValue(ucoo5crdv)"
                  :prop="ucoo5crdv.prop"
                  :style="{}"
                  :compId="'ucoo5crdv'"
                  id="ucoo5crdv"
                  @update:modelValue="handleUpdate($event, ucoo5crdv)"
                  @action="handleAction($event, ucoo5crdv)"
                ></lyy-date-picker>
                <lyy-date-picker
                  v-if="showdate2yn2r12np"
                  :childrens="[]"
                  :modelValue="useValue(date2yn2r12np)"
                  :prop="date2yn2r12np.prop"
                  :style="{}"
                  :compId="'date2yn2r12np'"
                  id="date2yn2r12np"
                  @update:modelValue="handleUpdate($event, date2yn2r12np)"
                  @action="handleAction($event, date2yn2r12np)"
                ></lyy-date-picker>
                <lyy-date-picker
                  v-if="showoo5gme9fg"
                  :childrens="[]"
                  :modelValue="useValue(oo5gme9fg)"
                  :prop="oo5gme9fg.prop"
                  :style="{}"
                  :compId="'oo5gme9fg'"
                  id="oo5gme9fg"
                  @update:modelValue="handleUpdate($event, oo5gme9fg)"
                  @action="handleAction($event, oo5gme9fg)"
                ></lyy-date-picker>
              </lyy-col>
              <lyy-col
                :childrens="[]"
                :modelValue="useValue(c4bqbu1c6g)"
                :prop="c4bqbu1c6g.prop"
                :style="{ 'padding-top': '6px' }"
                :compId="'c4bqbu1c6g'"
                id="c4bqbu1c6g"
                @update:modelValue="handleUpdate($event, c4bqbu1c6g)"
                @action="handleAction($event, c4bqbu1c6g)"
              >
                <lyy-icon
                  :childrens="[]"
                  :modelValue="useValue(u7nkezbz1)"
                  :prop="u7nkezbz1.prop"
                  :style="{
                    background: '#fff',
                    padding: '5px 7px',
                    'border-radius': '2px',
                    'margin-top': '1px',
                    border: '1px solid #d9d9d9',
                  }"
                  :compId="'u7nkezbz1'"
                  id="u7nkezbz1"
                  @update:modelValue="handleUpdate($event, u7nkezbz1)"
                  @action="handleAction($event, u7nkezbz1)"
                ></lyy-icon>
              </lyy-col>
            </lyy-row>
          </lyy-col>
        </lyy-row>
        <lyy-row
          :childrens="[]"
          :modelValue="useValue(c8kf886be9)"
          :prop="c8kf886be9.prop"
          :style="{}"
          :compId="'c8kf886be9'"
          id="c8kf886be9"
          @update:modelValue="handleUpdate($event, c8kf886be9)"
          @action="handleAction($event, c8kf886be9)"
        >
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cii7nm389m)"
            :prop="cii7nm389m.prop"
            :style="{}"
            :compId="'cii7nm389m'"
            id="cii7nm389m"
            @update:modelValue="handleUpdate($event, cii7nm389m)"
            @action="handleAction($event, cii7nm389m)"
          >
            <lyy-data-statistic
              :childrens="[]"
              :modelValue="useValue(c3zjiu913m)"
              :prop="c3zjiu913m.prop"
              :style="{}"
              :compId="'c3zjiu913m'"
              id="c3zjiu913m"
              @update:modelValue="handleUpdate($event, c3zjiu913m)"
              @action="handleAction($event, c3zjiu913m)"
            ></lyy-data-statistic>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(c316hjssz1)"
            :prop="c316hjssz1.prop"
            :style="{}"
            :compId="'c316hjssz1'"
            id="c316hjssz1"
            @update:modelValue="handleUpdate($event, c316hjssz1)"
            @action="handleAction($event, c316hjssz1)"
          >
            <lyy-data-statistic
              :childrens="[]"
              :modelValue="useValue(cpfuyn8h7x)"
              :prop="cpfuyn8h7x.prop"
              :style="{}"
              :compId="'cpfuyn8h7x'"
              id="cpfuyn8h7x"
              @update:modelValue="handleUpdate($event, cpfuyn8h7x)"
              @action="handleAction($event, cpfuyn8h7x)"
            ></lyy-data-statistic>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cw7yg6wury)"
            :prop="cw7yg6wury.prop"
            :style="{}"
            :compId="'cw7yg6wury'"
            id="cw7yg6wury"
            @update:modelValue="handleUpdate($event, cw7yg6wury)"
            @action="handleAction($event, cw7yg6wury)"
          >
            <lyy-data-statistic
              :childrens="[]"
              :modelValue="useValue(cu579okmly)"
              :prop="cu579okmly.prop"
              :style="{}"
              :compId="'cu579okmly'"
              id="cu579okmly"
              @update:modelValue="handleUpdate($event, cu579okmly)"
              @action="handleAction($event, cu579okmly)"
            ></lyy-data-statistic>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(ciml423jpd)"
            :prop="ciml423jpd.prop"
            :style="{}"
            :compId="'ciml423jpd'"
            id="ciml423jpd"
            @update:modelValue="handleUpdate($event, ciml423jpd)"
            @action="handleAction($event, ciml423jpd)"
          >
            <lyy-data-statistic
              :childrens="[]"
              :modelValue="useValue(cjz4gtpex5)"
              :prop="cjz4gtpex5.prop"
              :style="{}"
              :compId="'cjz4gtpex5'"
              id="cjz4gtpex5"
              @update:modelValue="handleUpdate($event, cjz4gtpex5)"
              @action="handleAction($event, cjz4gtpex5)"
            ></lyy-data-statistic>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(co9lm0d0v1)"
            :prop="co9lm0d0v1.prop"
            :style="{}"
            :compId="'co9lm0d0v1'"
            id="co9lm0d0v1"
            @update:modelValue="handleUpdate($event, co9lm0d0v1)"
            @action="handleAction($event, co9lm0d0v1)"
          >
            <lyy-data-statistic
              :childrens="[]"
              :modelValue="useValue(c0mojz030m)"
              :prop="c0mojz030m.prop"
              :style="{}"
              :compId="'c0mojz030m'"
              id="c0mojz030m"
              @update:modelValue="handleUpdate($event, c0mojz030m)"
              @action="handleAction($event, c0mojz030m)"
            ></lyy-data-statistic>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cnnze6yj2m)"
            :prop="cnnze6yj2m.prop"
            :style="{}"
            :compId="'cnnze6yj2m'"
            id="cnnze6yj2m"
            @update:modelValue="handleUpdate($event, cnnze6yj2m)"
            @action="handleAction($event, cnnze6yj2m)"
          >
            <lyy-data-statistic
              :childrens="[]"
              :modelValue="useValue(c5045tfcqz)"
              :prop="c5045tfcqz.prop"
              :style="{}"
              :compId="'c5045tfcqz'"
              id="c5045tfcqz"
              @update:modelValue="handleUpdate($event, c5045tfcqz)"
              @action="handleAction($event, c5045tfcqz)"
            ></lyy-data-statistic>
          </lyy-col>
        </lyy-row>
        <lyy-row
          :childrens="[]"
          :modelValue="useValue(copu41f1ou)"
          :prop="copu41f1ou.prop"
          :style="{ margin: '8px 0', 'padding-left': '8px' }"
          :compId="'copu41f1ou'"
          id="copu41f1ou"
          @update:modelValue="handleUpdate($event, copu41f1ou)"
          @action="handleAction($event, copu41f1ou)"
        >
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(c79wev8emw)"
            :prop="c79wev8emw.prop"
            :style="{}"
            :compId="'c79wev8emw'"
            id="c79wev8emw"
            @update:modelValue="handleUpdate($event, c79wev8emw)"
            @action="handleAction($event, c79wev8emw)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(c2vawcjg0c)"
              :prop="c2vawcjg0c.prop"
              :style="{}"
              :compId="'c2vawcjg0c'"
              id="c2vawcjg0c"
              @update:modelValue="handleUpdate($event, c2vawcjg0c)"
              @action="handleAction($event, c2vawcjg0c)"
            ></lyy-statistic-item>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(c57y9ugy63)"
            :prop="c57y9ugy63.prop"
            :style="{}"
            :compId="'c57y9ugy63'"
            id="c57y9ugy63"
            @update:modelValue="handleUpdate($event, c57y9ugy63)"
            @action="handleAction($event, c57y9ugy63)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(csj443nppy)"
              :prop="csj443nppy.prop"
              :style="{}"
              :compId="'csj443nppy'"
              id="csj443nppy"
              @update:modelValue="handleUpdate($event, csj443nppy)"
              @action="handleAction($event, csj443nppy)"
            ></lyy-statistic-item>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(ckceitf2ee)"
            :prop="ckceitf2ee.prop"
            :style="{}"
            :compId="'ckceitf2ee'"
            id="ckceitf2ee"
            @update:modelValue="handleUpdate($event, ckceitf2ee)"
            @action="handleAction($event, ckceitf2ee)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(c44lonoqo1)"
              :prop="c44lonoqo1.prop"
              :style="{}"
              :compId="'c44lonoqo1'"
              id="c44lonoqo1"
              @update:modelValue="handleUpdate($event, c44lonoqo1)"
              @action="handleAction($event, c44lonoqo1)"
            ></lyy-statistic-item>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cijrg9uqz6)"
            :prop="cijrg9uqz6.prop"
            :style="{}"
            :compId="'cijrg9uqz6'"
            id="cijrg9uqz6"
            @update:modelValue="handleUpdate($event, cijrg9uqz6)"
            @action="handleAction($event, cijrg9uqz6)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(ckr3z2geam)"
              :prop="ckr3z2geam.prop"
              :style="{}"
              :compId="'ckr3z2geam'"
              id="ckr3z2geam"
              @update:modelValue="handleUpdate($event, ckr3z2geam)"
              @action="handleAction($event, ckr3z2geam)"
            ></lyy-statistic-item>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(c05lrz1lac)"
            :prop="c05lrz1lac.prop"
            :style="{}"
            :compId="'c05lrz1lac'"
            id="c05lrz1lac"
            @update:modelValue="handleUpdate($event, c05lrz1lac)"
            @action="handleAction($event, c05lrz1lac)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(csms3vumd3)"
              :prop="csms3vumd3.prop"
              :style="{}"
              :compId="'csms3vumd3'"
              id="csms3vumd3"
              @update:modelValue="handleUpdate($event, csms3vumd3)"
              @action="handleAction($event, csms3vumd3)"
            ></lyy-statistic-item>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(cibzhkzpit)"
            :prop="cibzhkzpit.prop"
            :style="{}"
            :compId="'cibzhkzpit'"
            id="cibzhkzpit"
            @update:modelValue="handleUpdate($event, cibzhkzpit)"
            @action="handleAction($event, cibzhkzpit)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(cdt2nnch9n)"
              :prop="cdt2nnch9n.prop"
              :style="{}"
              :compId="'cdt2nnch9n'"
              id="cdt2nnch9n"
              @update:modelValue="handleUpdate($event, cdt2nnch9n)"
              @action="handleAction($event, cdt2nnch9n)"
            ></lyy-statistic-item>
          </lyy-col>
          <lyy-col
            :childrens="[]"
            :modelValue="useValue(c28anqj9zd)"
            :prop="c28anqj9zd.prop"
            :style="{}"
            :compId="'c28anqj9zd'"
            id="c28anqj9zd"
            @update:modelValue="handleUpdate($event, c28anqj9zd)"
            @action="handleAction($event, c28anqj9zd)"
          >
            <lyy-statistic-item
              :childrens="[]"
              :modelValue="useValue(cjbrueix22)"
              :prop="cjbrueix22.prop"
              :style="{}"
              :compId="'cjbrueix22'"
              id="cjbrueix22"
              @update:modelValue="handleUpdate($event, cjbrueix22)"
              @action="handleAction($event, cjbrueix22)"
            ></lyy-statistic-item>
          </lyy-col>
        </lyy-row>
        <lyy-card
          :childrens="[]"
          :modelValue="useValue(cu7bs9l6r1)"
          :prop="cu7bs9l6r1.prop"
          :style="{ background: '', 'border-radius': '8px' }"
          :compId="'cu7bs9l6r1'"
          id="cu7bs9l6r1"
          @update:modelValue="handleUpdate($event, cu7bs9l6r1)"
          @action="handleAction($event, cu7bs9l6r1)"
        >
          <lyy-sort-button
            :childrens="[]"
            :modelValue="useValue(cgeh7187lj)"
            :prop="cgeh7187lj.prop"
            :style="{}"
            :compId="'cgeh7187lj'"
            id="cgeh7187lj"
            @update:modelValue="handleUpdate($event, cgeh7187lj)"
            @action="handleAction($event, cgeh7187lj)"
          ></lyy-sort-button>
          <lyy-layout-responsive
            :childrens="[]"
            :modelValue="useValue(cr3yqteswl)"
            :prop="cr3yqteswl.prop"
            :style="{}"
            :compId="'cr3yqteswl'"
            id="cr3yqteswl"
            @update:modelValue="handleUpdate($event, cr3yqteswl)"
            @action="handleAction($event, cr3yqteswl)"
          >
            <lyy-card-statistic
              :childrens="[]"
              :modelValue="useValue(swzqcmwec)"
              :prop="swzqcmwec.prop"
              :style="{}"
              :compId="'swzqcmwec'"
              id="swzqcmwec"
              @update:modelValue="handleUpdate($event, swzqcmwec)"
              @action="handleAction($event, swzqcmwec)"
            >
              <lyy-row
                :childrens="[]"
                :modelValue="useValue(clqha3ta24)"
                :prop="clqha3ta24.prop"
                :style="{}"
                :compId="'clqha3ta24'"
                id="clqha3ta24"
                @update:modelValue="handleUpdate($event, clqha3ta24)"
                @action="handleAction($event, clqha3ta24)"
              >
                <lyy-col
                  :childrens="[]"
                  :modelValue="useValue(ceeqjz96m1)"
                  :prop="ceeqjz96m1.prop"
                  :style="{}"
                  :compId="'ceeqjz96m1'"
                  id="ceeqjz96m1"
                  @update:modelValue="handleUpdate($event, ceeqjz96m1)"
                  @action="handleAction($event, ceeqjz96m1)"
                >
                  <lyy-statistic-item
                    :childrens="[]"
                    :modelValue="useValue(knn2fxaee)"
                    :prop="knn2fxaee.prop"
                    :style="{}"
                    :compId="'knn2fxaee'"
                    id="knn2fxaee"
                    @update:modelValue="handleUpdate($event, knn2fxaee)"
                    @action="handleAction($event, knn2fxaee)"
                  ></lyy-statistic-item>
                </lyy-col>
                <lyy-col
                  :childrens="[]"
                  :modelValue="useValue(cp7l7saivk)"
                  :prop="cp7l7saivk.prop"
                  :style="{}"
                  :compId="'cp7l7saivk'"
                  id="cp7l7saivk"
                  @update:modelValue="handleUpdate($event, cp7l7saivk)"
                  @action="handleAction($event, cp7l7saivk)"
                >
                  <lyy-statistic-item
                    :childrens="[]"
                    :modelValue="useValue(x5yakgvbx)"
                    :prop="x5yakgvbx.prop"
                    :style="{}"
                    :compId="'x5yakgvbx'"
                    id="x5yakgvbx"
                    @update:modelValue="handleUpdate($event, x5yakgvbx)"
                    @action="handleAction($event, x5yakgvbx)"
                  ></lyy-statistic-item>
                </lyy-col>
                <lyy-col
                  :childrens="[]"
                  :modelValue="useValue(cc10xhpn0j)"
                  :prop="cc10xhpn0j.prop"
                  :style="{}"
                  :compId="'cc10xhpn0j'"
                  id="cc10xhpn0j"
                  @update:modelValue="handleUpdate($event, cc10xhpn0j)"
                  @action="handleAction($event, cc10xhpn0j)"
                >
                  <lyy-statistic-item
                    :childrens="[]"
                    :modelValue="useValue(gedg6qtbv)"
                    :prop="gedg6qtbv.prop"
                    :style="{}"
                    :compId="'gedg6qtbv'"
                    id="gedg6qtbv"
                    @update:modelValue="handleUpdate($event, gedg6qtbv)"
                    @action="handleAction($event, gedg6qtbv)"
                  ></lyy-statistic-item>
                </lyy-col>
              </lyy-row>
              <lyy-row
                :childrens="[]"
                :modelValue="useValue(fce1osd93)"
                :prop="fce1osd93.prop"
                :style="{}"
                :compId="'fce1osd93'"
                id="fce1osd93"
                @update:modelValue="handleUpdate($event, fce1osd93)"
                @action="handleAction($event, fce1osd93)"
              >
                <lyy-col
                  :childrens="[]"
                  :modelValue="useValue(cge6aqj7a7)"
                  :prop="cge6aqj7a7.prop"
                  :style="{}"
                  :compId="'cge6aqj7a7'"
                  id="cge6aqj7a7"
                  @update:modelValue="handleUpdate($event, cge6aqj7a7)"
                  @action="handleAction($event, cge6aqj7a7)"
                >
                  <lyy-statistic-item
                    :childrens="[]"
                    :modelValue="useValue(i64bizs6y)"
                    :prop="i64bizs6y.prop"
                    :style="{}"
                    :compId="'i64bizs6y'"
                    id="i64bizs6y"
                    @update:modelValue="handleUpdate($event, i64bizs6y)"
                    @action="handleAction($event, i64bizs6y)"
                  ></lyy-statistic-item>
                </lyy-col>
                <lyy-col
                  :childrens="[]"
                  :modelValue="useValue(cgsybu8spt)"
                  :prop="cgsybu8spt.prop"
                  :style="{}"
                  :compId="'cgsybu8spt'"
                  id="cgsybu8spt"
                  @update:modelValue="handleUpdate($event, cgsybu8spt)"
                  @action="handleAction($event, cgsybu8spt)"
                >
                  <lyy-statistic-item
                    :childrens="[]"
                    :modelValue="useValue(gt4k6ylzi)"
                    :prop="gt4k6ylzi.prop"
                    :style="{}"
                    :compId="'gt4k6ylzi'"
                    id="gt4k6ylzi"
                    @update:modelValue="handleUpdate($event, gt4k6ylzi)"
                    @action="handleAction($event, gt4k6ylzi)"
                  ></lyy-statistic-item>
                </lyy-col>
                <lyy-col
                  :childrens="[]"
                  :modelValue="useValue(cqf3vrfqer)"
                  :prop="cqf3vrfqer.prop"
                  :style="{}"
                  :compId="'cqf3vrfqer'"
                  id="cqf3vrfqer"
                  @update:modelValue="handleUpdate($event, cqf3vrfqer)"
                  @action="handleAction($event, cqf3vrfqer)"
                >
                  <lyy-statistic-item
                    :childrens="[]"
                    :modelValue="useValue(statist502515vs2)"
                    :prop="statist502515vs2.prop"
                    :style="{}"
                    :compId="'statist502515vs2'"
                    id="statist502515vs2"
                    @update:modelValue="handleUpdate($event, statist502515vs2)"
                    @action="handleAction($event, statist502515vs2)"
                  ></lyy-statistic-item>
                </lyy-col>
              </lyy-row>
            </lyy-card-statistic>
          </lyy-layout-responsive>
        </lyy-card>
      </lyy-form>
    </lyy-container>
  </a-spin>
</template>
<script lang="ts" setup>
import jsonData from './jsonData.json'
import defaultConfig from './defaultConfig.json'
import { computed, getCurrentInstance, reactive } from 'vue'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import { useStore } from '@leyaoyao/libs/store'
import { preHandler, useUpdate, useAction, useValue } from '@leyaoyao/libs'
const store = useStore()
const handleAction = (ev, element) => {
  if (element.compName === 'lyy-form' && ['beforeMount'].includes(ev.event)) {
    store.setValue(element.compId, element.prop.form)
    const origin = store.getValue('origin', element.compId)
    if (!origin) {
      store.setValue(
        'origin',
        JSON.parse(JSON.stringify(element.prop.form)),
        element.compId,
      )
    }
  }
  const actions = element.actions?.filter((item) => item.event === ev.event)
  actions && useAction(actions, element)
}
const handleUpdate = (newValue, element) => {
  useUpdate(newValue, element)
}

const elements = reactive([])
const internalInstance = getCurrentInstance()
const {
  $router = window.$router,
  $route = window.$route,
  $store = {},
} = internalInstance?.appContext.config.globalProperties || {}
// 预处理页面配置
preHandler(elements)
store.setRoute($route)
store.setRouter($router)
store.setStore($store)
store.setValue('pageConfig', elements)
store.setValue('loading', { spinning: false })
const tip = computed(() => store.getValue('loading', 'tip') ?? '加载中...')
const spinning = computed(() => store.getValue('loading', 'spinning') ?? false)

const {
  ctb6532d6s,
  clgypcoir7,
  row7mdgp9k2y,
  cb85bt6eow,
  cth7ovfbei,
  c7oea581q8,
  cxlmk7uz6n,
  cjkzqj5cp9,
  c69txqij6e,
  c3dsns6s2w,
  cstjzstrxl,
  cfvfjcjrwe,
  lebmut4xp,
  ca0z6payji,
  select6ypm0fka0,
  cy8t6542tt,
  o92oqfvia,
  ucoo5crdv,
  date2yn2r12np,
  oo5gme9fg,
  c4bqbu1c6g,
  u7nkezbz1,
  c8kf886be9,
  cii7nm389m,
  c3zjiu913m,
  c316hjssz1,
  cpfuyn8h7x,
  cw7yg6wury,
  cu579okmly,
  ciml423jpd,
  cjz4gtpex5,
  co9lm0d0v1,
  c0mojz030m,
  cnnze6yj2m,
  c5045tfcqz,
  copu41f1ou,
  c79wev8emw,
  c2vawcjg0c,
  c57y9ugy63,
  csj443nppy,
  ckceitf2ee,
  c44lonoqo1,
  cijrg9uqz6,
  ckr3z2geam,
  c05lrz1lac,
  csms3vumd3,
  cibzhkzpit,
  cdt2nnch9n,
  c28anqj9zd,
  cjbrueix22,
  cu7bs9l6r1,
  cgeh7187lj,
  cr3yqteswl,
  swzqcmwec,
  clqha3ta24,
  ceeqjz96m1,
  knn2fxaee,
  cp7l7saivk,
  x5yakgvbx,
  cc10xhpn0j,
  gedg6qtbv,
  fce1osd93,
  cge6aqj7a7,
  i64bizs6y,
  cgsybu8spt,
  gt4k6ylzi,
  cqf3vrfqer,
  statist502515vs2,
} = proxyData.getProxyData()

window.__httpConfig__.setHttpConfig(defaultConfig)
const showo92oqfvia = computed(() => {
  return clgypcoir7.modelValue.pickerType == 'year'
})
const showucoo5crdv = computed(() => {
  return clgypcoir7.modelValue.pickerType == 'month'
})
const showdate2yn2r12np = computed(() => {
  return clgypcoir7.modelValue.pickerType == 'week'
})
const showoo5gme9fg = computed(() => {
  return clgypcoir7.modelValue.pickerType == 'date'
})
</script>
