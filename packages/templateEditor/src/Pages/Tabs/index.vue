<script lang="ts" setup>
import { defineComponent, ref } from 'vue'
import { uuidv4 } from '@leyaoyao/libs'
defineComponent({
  name: 'edit-tabs',
})

const panes = ref<
  { title: string; content: string; key: string; closable?: boolean }[]
>([
  {
    title: '应用页',
    content: 'Content of Tab 1',
    key: 'home',
    closable: false,
  },
])
const activeKey = ref(panes.value[0].key)
// 添加标签页
const add = () => {
  const newPane = {
    title: '应用列表',
    content: '',
    key: 'c' + uuidv4(),
  }
  panes.value.push(newPane)
  ipcRenderer.invoke('tab-add', JSON.stringify(newPane))
}
// 删除标签
const remove = (targetKey) => {
  let lastIndex = 0
  for (const [i, pane] of panes.value.entries()) {
    if (pane.key === targetKey) {
      lastIndex = i - 1
    }
  }
  panes.value = panes.value.filter((pane) => pane.key !== targetKey)
  if (panes.value.length > 0 && activeKey.value === targetKey) {
    activeKey.value =
      lastIndex >= 0 ? panes.value[lastIndex].key : panes.value[0].key
  }
  ipcRenderer.invoke('tab-remove', targetKey, activeKey.value)
}
const onEdit = (targetKey: string | MouseEvent, action: string) => {
  if (action === 'add') {
    add()
  } else {
    remove(targetKey as string)
  }
}

const onChange = (targetKey: string) => {
  ipcRenderer.invoke('tab-change', targetKey)
}
const redo = () => {
  ipcRenderer.invoke('tab-reload')
}
ipcRenderer.on('tab-title-change', (e, title) => {
  const curPane = panes.value.find((pane) => pane.key === activeKey.value)
  title && (curPane.title = title)
})
const go = () => {
  ipcRenderer.invoke('route-go')
}
const back = () => {
  ipcRenderer.invoke('route-back')
}
</script>

<template>
  <div class="tab-wrap">
    <!-- <arrow-left-outlined class="route-icon" @click="back" />
    <arrow-right-outlined class="route-icon" @click="go" /> -->
    <redo-outlined class="route-icon" @click="redo" />
    <a-tabs
      v-model:activeKey="activeKey"
      type="editable-card"
      @edit="onEdit"
      @change="onChange"
    >
      <a-tab-pane
        v-for="pane in panes"
        :key="pane.key"
        :closable="pane.closable"
      >
        <template #tab>
          <span>
            {{ pane.title }}
          </span>
        </template>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="less" scoped>
.tab-wrap {
  display: flex;
}
.route-icon {
  padding: 12px 8px 0;
}
</style>
