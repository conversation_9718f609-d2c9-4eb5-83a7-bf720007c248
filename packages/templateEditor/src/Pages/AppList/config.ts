import addModal from './config/addModal'
import configModal from './config/configModal'
export default [
  {
    compName: 'lyy-container',
    compId: '94d9385a-73c3-429e-bf3b-ca615b9889f9',
    compNameCn: '容器',
    prop: {},
    actions: [],
    childrens: [
      {
        compName: 'lyy-container',
        compId: '203d247f-bc71-4493-8150-057038cacb8d',
        compNameCn: '增删查改',
        prop: {},
        actions: [],
        childrens: [
          {
            compName: 'lyy-form',
            compId: '52d43f0f-7b62-4823-b0e3-60449f75c2b3',
            compNameCn: '表单',
            style: {},
            prop: {
              form: { current: 1, size: 10 },
              aliasName: '表单',
              isSignChange: null,
              mainLayout: false,
              class: '',
            },
            modelValue: { current: 1, size: 10 },
            actions: [
              {
                event: 'mounted',
                action: 'request',
                option: {
                  url: '/gw/editor/systems',
                  method: 'get',
                  payloads: {
                    type: 'dynamic',
                    dynamic: {
                      nodePath:
                        '表单-52d43f0f-7b62-4823-b0e3-60449f75c2b3.modelValue',
                    },
                    higher: [],
                    static: '',
                  },
                  responseDataKey: 'CRUD-52d43f0f-7b62-4823-b0e3-60449f75c2b3',
                  customOption: {
                    jsonPath: 'body',
                    loading: false,
                    codeValue: '0000000',
                  },
                  headerPayloads: [],
                },
                thenActions: [
                  {
                    event: 'mounted',
                    action: 'setValue',
                    option: {
                      type: '',
                      sourceKey: 'records',
                      targetId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                      targetKey: 'datasource',
                      payloads: [
                        { source: 'CRUD-52d43f0f-7b62-4823-b0e3-60449f75c2b3' },
                      ],
                    },
                    thenActions: [
                      {
                        event: 'mounted',
                        action: 'setValue',
                        option: {
                          type: '',
                          sourceKey: 'total',
                          targetId: 'ed78cd2e-44ee-47b5-b8d0-0cc7cd28f416',
                          targetKey: 'prop.total',
                          payloads: [
                            {
                              source:
                                'CRUD-52d43f0f-7b62-4823-b0e3-60449f75c2b3',
                            },
                          ],
                        },
                        thenActions: [],
                      },
                    ],
                  },
                ],
              },
            ],
            childrens: [
              {
                compName: 'lyy-search-pro',
                compId: '244a1262-6252-4a27-b5c9-9edd829cef0d',
                compNameCn: '搜索容器',
                prop: {
                  searchFormList: [
                    {
                      compName: 'lyy-text-input',
                      compId: '009d5202-6940-427d-bd16-01d262ec6fff',
                      compNameCn: '输入框',
                      style: {},
                      prop: {
                        field: 'value',
                        label: '系统标识',
                        aliasName: '文本输入框',
                        placeholder: '请输入',
                        defaultValue: '',
                        validateTrigger: 'change',
                        maxlength: null,
                        size: 'default',
                        suffix: '',
                        addonAfter: '',
                        rules: [
                          {
                            required: false,
                            message: '',
                            trigger: '',
                            pattern: '',
                          },
                        ],
                        disabled: false,
                        showCount: false,
                        allowClear: true,
                        icon: { iconName: '', popover: { description: [] } },
                        formId: '',
                        show: {
                          exp: '',
                        },
                        forbid: {
                          exp: '',
                        },
                      },
                      modelValue: '',
                      actions: [],
                    },
                    {
                      compName: 'lyy-text-input',
                      compId: '90113971-3358-41c7-bbd3-5c6f7f251d00',
                      compNameCn: '输入框',
                      style: {},
                      prop: {
                        field: 'name',
                        label: '应用名称',
                        aliasName: '文本输入框',
                        placeholder: '请输入',
                        defaultValue: '',
                        validateTrigger: 'change',
                        maxlength: null,
                        size: 'default',
                        suffix: '',
                        addonAfter: '',
                        rules: [
                          {
                            required: false,
                            message: '',
                            trigger: '',
                            pattern: '',
                          },
                        ],
                        disabled: false,
                        showCount: false,
                        allowClear: true,
                        icon: { iconName: '', popover: { description: [] } },
                        formId: '',
                        show: {
                          exp: '',
                        },
                        forbid: {
                          exp: '',
                          payloads: [
                            {
                              key: '',
                              source: '',
                              sourceKey: '',
                              decompose: false,
                              value: '',
                            },
                          ],
                        },
                      },
                      modelValue: '',
                      actions: [],
                    },
                  ],
                  aliasName: '搜索容器',
                  formStorage: true,
                },
                actions: [],
              },
              {
                compName: 'lyy-card',
                compId: '1eff2de5-6b0b-476c-8f45-2f4c05c8f7c9',
                compNameCn: '卡片',
                prop: {
                  showHeader: false,
                  title: '卡片容器',
                  aliasName: '卡片容器',
                  extraList: [],
                  mainLayout: false,
                  bordered: true,
                },
                actions: [],
                childrens: [
                  {
                    compName: 'lyy-toolbar',
                    compId: 'fb30c1cf-d858-4a8e-921b-7ebcf101b072',
                    compNameCn: '工具栏',
                    style: {},
                    prop: { title: '应用列表', aliasName: '工具栏' },
                    childrens: [
                      {
                        compName: 'lyy-button',
                        compId: 'd7d7c118-df7e-4d6e-a531-c44b58d3a27f',
                        compNameCn: '按钮',
                        actions: [
                          {
                            event: 'click',
                            action: 'openModal',
                            option: {
                              targetId: '466b89c5-9079-4462-a602-397ec871a968',
                            },
                            thenActions: [
                              {
                                event: 'click',
                                action: 'resetValue',
                                option: {
                                  targetId:
                                    '035f644a-d40c-4a98-aeba-da46484b6a86',
                                },
                                thenActions: [],
                              },
                            ],
                          },
                        ],
                        childrens: [],
                        style: {},
                        prop: {
                          text: '新增应用',
                          type: 'primary',
                          size: 'middle',
                          aliasName: '按钮',
                          ghost: false,
                          danger: false,
                          disabled: false,
                          icon: '',
                          show: {
                            exp: '',
                          },
                          forbid: {
                            exp: '',
                            payloads: [
                              {
                                key: '',
                                source: '',
                                sourceKey: '',
                                decompose: false,
                                value: '',
                              },
                            ],
                          },
                        },
                      },
                    ],
                  },
                  {
                    compName: 'lyy-table',
                    compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                    field: 'name',
                    compNameCn: '表格',
                    prop: {
                      columns: [
                        {
                          title: '企业id',
                          dataIndex: 'authEnterpriseId',
                          type: 'text',
                          width: 82.281_25,
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [{ pipe: '', option: {} }],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                          compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                        },
                        {
                          title: '应用名称',
                          dataIndex: 'name',
                          type: 'text',
                          width: '',
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [{ pipe: '', option: {} }],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                          compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                        },
                        {
                          title: '系统id',
                          dataIndex: 'authSystemId',
                          type: 'text',
                          width: '',
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [{ pipe: '', option: {} }],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                          compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                        },
                        {
                          title: '系统标识',
                          dataIndex: 'value',
                          type: 'text',
                          width: '',
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [{ pipe: '', option: {} }],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                          compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                        },
                        {
                          title: '系统域名',
                          dataIndex: 'domainName',
                          type: 'text',
                          width: '',
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [{ pipe: '', option: {} }],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                          compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                        },
                        {
                          title: 'SAAS系统',
                          dataIndex: 'isSaas',
                          type: 'text',
                          width: 137,
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [
                            {
                              pipe: 'textmap',
                              option: {
                                textMap: [
                                  { origin: 'Y', result: '是' },
                                  { origin: 'N', result: '否' },
                                ],
                              },
                            },
                          ],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                          compId: '62da04c0-12c8-4e68-8c4f-86338c7f6bae',
                        },
                        {
                          title: '操作栏',
                          dataIndex: '',
                          type: 'operation',
                          width: '',
                          align: 'left',
                          replaceNull: '-',
                          separator: '',
                          actions: [],
                          sorter: false,
                          fixed: '',
                          resizable: true,
                          headComp: [],
                          prevComp: [],
                          nextComp: [],
                          childrens: [
                            {
                              compName: 'lyy-button',
                              compNameCn: '按钮',
                              actions: [
                                {
                                  event: 'click',
                                  action: 'linkto',
                                  condition: {
                                    exp: '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.value === "sp"',
                                  },
                                  option: {
                                    mode: 'query',
                                    url: '/splogin',
                                  },
                                },
                                {
                                  event: 'click',
                                  action: 'linkto',
                                  condition: {
                                    exp: '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.value === "se"',
                                  },
                                  option: {
                                    mode: 'query',
                                    url: '/selogin',
                                  },
                                },
                                {
                                  event: 'click',
                                  action: 'linkto',
                                  condition: {
                                    exp: '!(表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.value === "se" || 表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.value === "sp")',
                                  },
                                  option: {
                                    mode: 'query',
                                    url: '/ramlogin',
                                  },
                                },
                              ],
                              childrens: [],
                              style: {},
                              prop: {
                                text: '登录',
                                type: 'link',
                                size: 'middle',
                                aliasName: '按钮',
                                ghost: false,
                                danger: false,
                                disabled: false,
                                icon: '',
                                // show: {
                                //   exp: `['sp', 'se'].includes(value)`,
                                // },
                                forbid: {
                                  exp: '',
                                  payloads: [
                                    {
                                      key: '',
                                      source: '',
                                      sourceKey: '',
                                      decompose: false,
                                      value: '',
                                    },
                                  ],
                                },
                              },
                            },
                            {
                              compName: 'lyy-button',
                              compId: '4ae256e2-184d-4aa5-83d2-6418b31b9708',
                              compNameCn: '按钮',
                              actions: [
                                {
                                  event: 'click',
                                  action: 'setNewValue',
                                  option: {
                                    from: {
                                      higher: [
                                        {
                                          key: 'authSystemId',
                                          value:
                                            '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                                          id: '66677',
                                        },
                                      ],
                                    },
                                    to: {
                                      type: 'higher',
                                      dynamic: {
                                        nodePath: 'sessionStorage',
                                      },
                                    },
                                  },
                                  thenActions: [
                                    {
                                      event: 'click',
                                      action: 'setNewValue',
                                      option: {
                                        from: {
                                          higher: [
                                            {
                                              key: 'authSystemCode',
                                              value:
                                                '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.value',
                                              id: '88999',
                                            },
                                          ],
                                        },
                                        to: {
                                          type: 'dynamic',
                                          dynamic: {
                                            nodePath: 'sessionStorage',
                                          },
                                        },
                                      },
                                      thenActions: [
                                        {
                                          event: 'click',
                                          action: 'linkto',
                                          option: {
                                            url: '/1',
                                            go: 0,
                                            tab: '_self',
                                            mode: 'params',
                                            payloads: [
                                              // {
                                              //   key: 'authSystemId',
                                              //   value:
                                              //     '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                                              //   id: '453c7984-fb84-4893-a939-7ab13456a304',
                                              // },
                                              // {
                                              //   key: 'authSystemCode',
                                              //   value:
                                              //     '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.value',
                                              //   id: '453c7984-fb84-4893-a939-7ab13456a304',
                                              // },
                                            ],
                                          },
                                          thenActions: [],
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                              childrens: [],
                              style: {},
                              prop: {
                                text: '详情',
                                type: 'link',
                                size: 'middle',
                                aliasName: '按钮',
                                ghost: false,
                                danger: false,
                                disabled: false,
                                icon: '',
                                show: {
                                  exp: '',
                                },
                                forbid: {
                                  exp: '',
                                  payloads: [
                                    {
                                      key: '',
                                      source: '',
                                      sourceKey: '',
                                      decompose: false,
                                      value: '',
                                    },
                                  ],
                                },
                              },
                            },
                            {
                              compName: 'lyy-button',
                              compNameCn: '按钮',
                              actions: [
                                {
                                  event: 'click',
                                  action: 'openModal',
                                  option: {
                                    targetId: 'publish-modal',
                                  },
                                  thenActions: [],
                                },
                              ],
                              childrens: [],
                              style: {},
                              prop: {
                                text: '发布',
                                type: 'link',
                                size: 'middle',
                                aliasName: '按钮',
                                ghost: false,
                                danger: false,
                                disabled: false,
                                icon: '',
                                show: {
                                  exp: '',
                                },
                                forbid: {
                                  exp: '',
                                  payloads: [
                                    {
                                      key: '',
                                      source: '',
                                      sourceKey: '',
                                      decompose: false,
                                      value: '',
                                    },
                                  ],
                                },
                              },
                            },
                            {
                              compName: 'lyy-button',
                              compId: 'dfed62ed-d4bd-4e15-8762-e9b073f9c31e',
                              compNameCn: '按钮',
                              actions: [
                                {
                                  event: 'click',
                                  action: 'request',
                                  option: {
                                    url: '/gw/editor/system/environments/default',
                                    method: 'get',
                                    payloads: {
                                      type: 'higher',
                                      higher: [
                                        {
                                          key: 'authSystemId',
                                          value:
                                            '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                                          id: 'fe9ad0df-b982-4605-808f-d367b98ed1cc',
                                        },
                                      ],
                                    },
                                    responseDataKey:
                                      '表单值-90007d0f-bb29-4d83-bb1c-84503e40f5a3',
                                    customOption: {
                                      jsonPath: 'body',
                                      loading: false,
                                      successfulFeedback: null,
                                      failedFeedback: null,
                                      nextEventsDelay: 0,
                                      codeValue: '0000000',
                                      codeKey: 'code',
                                      messageKey: 'message',
                                      parse: false,
                                    },
                                    headerPayloads: [],
                                    interceptors: {
                                      requestInterceptor:
                                        '(config) => {\n  \n  return config;\n  }',
                                      responseInterceptor:
                                        "(res) => {\n  if(res.data.code=='0000000'){\r\n    if(!res.data.body){\r\n        res.data.body={\r\n            authSystemEnvId:'',\r\n            envStr:''\r\n        }\r\n    }\r\n}\r\nconsole.log(res)\n  return res;\n  }",
                                    },
                                  },
                                  thenActions: [
                                    {
                                      event: 'click',
                                      action: 'setNewValue',
                                      option: {
                                        to: {
                                          type: 'higher',
                                          dynamic: {
                                            nodePath:
                                              '表单-90007d0f-bb29-4d83-bb1c-84503e40f5a3.prop.form',
                                          },
                                        },
                                        from: {
                                          higher: [
                                            {
                                              key: 'envStr',
                                              value:
                                                '表单值-90007d0f-bb29-4d83-bb1c-84503e40f5a3.envStr',
                                            },
                                            {
                                              key: 'authSystemId',
                                              value:
                                                '表单值-90007d0f-bb29-4d83-bb1c-84503e40f5a3.authSystemId',
                                              id: '347ec80d-c10f-44a0-8a2a-8949547f7b68',
                                            },
                                          ],
                                        },
                                      },
                                      thenActions: [
                                        {
                                          event: 'click',
                                          action: 'openModal',
                                          option: {
                                            targetId:
                                              '321dfd36-cd21-4e9a-9982-1c48da5469de',
                                          },
                                          thenActions: [],
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                              childrens: [],
                              style: {},
                              prop: {
                                text: '配置',
                                type: 'link',
                                size: 'middle',
                                aliasName: '按钮',
                                ghost: false,
                                danger: false,
                                disabled: false,
                                icon: '',
                                show: {
                                  exp: '',
                                },
                                forbid: {
                                  exp: '',
                                  payloads: [
                                    {
                                      key: '',
                                      source: '',
                                      sourceKey: '',
                                      decompose: false,
                                      value: '',
                                    },
                                  ],
                                },
                              },
                            },
                            {
                              compName: 'lyy-popconfirm',
                              compId: 'd2e92e40-ffa9-40e4-87d8-6481aeefa8af',
                              compNameCn: '按钮',
                              style: {},
                              prop: {
                                text: '删除',
                                aliasName: '气泡确认框',
                                title: '您确认要删除吗？',
                                placement: 'top',
                                okType: 'text',
                                okText: '确定',
                                cancelText: '取消',
                                danger: true,
                                disabled: false,
                                showCancel: true,
                              },
                              actions: [
                                {
                                  event: 'click',
                                  action: 'request',
                                  option: {
                                    url: '/gw/editor/systems/${id}',
                                    method: 'delete',
                                    payloads: {
                                      type: 'higher',
                                      higher: [
                                        {
                                          key: 'id',
                                          value:
                                            '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                                          id: 'edd6e376-615f-40e0-8ecf-b2df07088783',
                                        },
                                      ],
                                    },
                                    responseDataKey: '',
                                    customOption: {
                                      jsonPath: '',
                                      loading: false,
                                      successfulFeedback: null,
                                      failedFeedback: null,
                                      nextEventsDelay: 0,
                                      codeValue: '0000000',
                                      codeKey: 'code',
                                      messageKey: 'message',
                                      parse: false,
                                    },
                                    headerPayloads: [],
                                  },
                                  thenActions: [
                                    {
                                      event: 'click',
                                      action: 'broadcast',
                                      option: {
                                        targetId:
                                          '52d43f0f-7b62-4823-b0e3-60449f75c2b3',
                                        event: 'mounted',
                                      },
                                      thenActions: [],
                                    },
                                  ],
                                },
                              ],
                              childrens: [],
                            },
                          ],
                          option: { format: '', preappend: '', append: '' },
                          joins: [],
                          pipes: [{ pipe: '', option: {} }],
                          colors: [{ exp: '', color: '' }],
                          show: {
                            exp: '',
                          },
                        },
                      ],
                      datasource: [],
                      rowKey: '',
                      aliasName: '表格',
                      size: 'small',
                      sticky: true,
                      scroll: { x: '100%' },
                      showIndex: true,
                      showSelection: false,
                      rowSelection: { getCheckboxProps: '' },
                      disabledRow: { exp: '', background: '' },
                      backgroundRows: [{ exp: '', background: '' }],
                      summary: {
                        placement: '',
                        summarys: [
                          {
                            label: '',
                            field: '',
                            value: '',
                            pipes: [{ pipe: '', option: {} }],
                          },
                        ],
                        payloads: [
                          {
                            key: '',
                            source: '',
                            sourceKey: '',
                            decompose: false,
                            value: '',
                          },
                        ],
                        sumFields: '',
                        averageFields: '',
                      },
                      rowExpandIconWidth: '',
                      mergeField: [],
                      mergeUniqueField: [],
                      colspanConfig: [{ exp: '', fields: [] }],
                      fieldNames: {},
                      expandFetch: {
                        url: '',
                        method: '',
                        payloads: [
                          {
                            key: '',
                            source: '',
                            sourceKey: '',
                            decompose: false,
                            value: '',
                          },
                        ],
                        headerPayloads: [
                          {
                            key: '',
                            source: '',
                            sourceKey: '',
                            decompose: false,
                            value: '',
                          },
                        ],
                      },
                    },
                    modelValue: {},
                    actions: [],
                  },
                  {
                    compName: 'lyy-pagination',
                    compId: 'ed78cd2e-44ee-47b5-b8d0-0cc7cd28f416',
                    compNameCn: '分页',
                    prop: {
                      fields: ['current', 'size'],
                      aliasName: '分页',
                      defaultCurrent: 1,
                      defaultPageSize: 10,
                      total: 0,
                      size: 'default',
                      hideOnSinglePage: null,
                      showQuickJumper: null,
                      showSizeChanger: null,
                      simple: null,
                      show: {
                        exp: '',
                      },
                      formId: '',
                    },
                    style: {},
                    actions: [
                      {
                        event: 'update',
                        action: 'broadcast',
                        option: {
                          targetId: '52d43f0f-7b62-4823-b0e3-60449f75c2b3',
                          event: 'mounted',
                        },
                      },
                    ],
                    modelValue: [1, 10],
                  },
                ],
              },
            ],
          },
          {
            compName: 'lyy-modal',
            compId: '57f64069-3092-4078-aeb4-1826839c6663',
            compNameCn: '模态框',
            modelValue: false,
            style: {},
            prop: {
              title: '编辑',
              size: 'middle',
              loading: { tip: '加载中', spinning: true },
              buttonList: [],
              centered: true,
              maskClosable: true,
              mask: true,
              closable: true,
              okText: '确定',
              okType: 'primary',
              cancelText: '取消',
              destroyOnClose: true,
            },
            childrens: [],
            actions: [],
          },
          {
            compName: 'lyy-modal',
            compId: 'e4e50148-6735-40a6-bc8e-c4e7081f83c0',
            compNameCn: '模态框',
            modelValue: false,
            style: {},
            prop: {
              title: '详情',
              size: 'middle',
              loading: { tip: '加载中', spinning: true },
              buttonList: [],
              centered: true,
              maskClosable: true,
              mask: true,
              closable: true,
              okText: '确定',
              okType: 'primary',
              cancelText: '取消',
              destroyOnClose: true,
            },
            childrens: [],
            actions: [],
          },
        ],
      },
    ],
  },
  // 发布弹窗
  {
    compName: 'lyy-modal',
    compId: 'publish-modal',
    compNameCn: '模态框',
    modelValue: false,
    style: {},
    prop: {
      title: '发布页面',
      size: 'middle',
      loading: {
        tip: '加载中',
        spinning: true,
      },
      buttonList: [],
      centered: true,
      maskClosable: false,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '取消',
      destroyOnClose: true,
      footer: true,
    },
    childrens: [
      {
        compName: 'lyy-form',
        compId: 'publish-form',
        compNameCn: '基础表单',
        style: {},
        prop: {
          form: {
            configureInfoList: [],
            globalConfigureFlag: 'N',
          },
          aliasName: '基础表单',
          isSignChange: null,
          mainLayout: false,
          class: '',
        },
        modelValue: {
          configureInfoList: [],
          globalConfigureFlag: 'N',
        },
        actions: [
          {
            event: 'mounted',
            action: 'setNewValue',
            option: {
              to: {
                type: 'dynamic',
                dynamic: {
                  nodePath: 'publish-form.modelValue.authSystemId',
                },
              },
              from: {
                dynamic: {
                  nodePath:
                    '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                },
              },
            },
            thenActions: [],
          },
        ],
        childrens: [
          {
            compName: 'lyy-grid',
            compId: '8ec4cb3b-ae6b-41c9-82d2-ebe12a47f894',
            compNameCn: '栅格布局',
            prop: {
              row: {
                align: '',
                gutter: '',
                justify: '',
                wrap: '',
              },
              size: 'large',
              children: [
                {
                  compName: 'lyy-select',
                  compId: '9d3bb4ea-c231-46a6-8855-5e630407db07',
                  compNameCn: '下拉框',
                  prop: {
                    field: 'env',
                    label: '发布环境',
                    aliasName: '下拉框',
                    placeholder: '请选择发布环境',
                    mode: 'combobox',
                    defaultValue: {
                      source: '',
                      sourceKey: '',
                    },
                    rules: [
                      {
                        required: true,
                        message: '请选择发布环境',
                        trigger: 'blur',
                      },
                    ],
                    options: [
                      {
                        code: 'DEV',
                        name: '开发',
                        disabled: false,
                        show: true,
                      },
                      {
                        code: 'SIT',
                        name: '测试',
                        disabled: false,
                        show: true,
                      },
                      {
                        code: 'UAT',
                        name: '预发',
                        disabled: false,
                        show: true,
                      },
                      {
                        code: 'PROD',
                        name: '生产',
                        disabled: false,
                        show: true,
                      },
                    ],
                    fieldNames: {
                      label: 'name',
                      value: 'code',
                    },
                    size: 'default',
                    immediate: true,
                    allowClear: true,
                    readonly: false,
                    showSearch: true,
                    optionFilterProp: '',
                    disabled: false,
                    lastComp: {
                      text: '',
                      icon: '',
                    },
                    labelCol: {
                      style: {},
                    },
                    icon: {
                      iconName: '',
                      popover: {
                        description: [],
                      },
                    },
                    formId: '',
                    show: {
                      exp: '',
                    },
                    forbid: {
                      exp: '',
                    },
                  },
                  actions: [
                    {
                      event: 'mounted',
                      action: 'request',
                      option: {
                        url: '/gw/editor/page/configures/getEnvList',
                        method: 'get',
                        payloads: {
                          type: 'static',
                          static: '',
                        },
                        responseDataKey: 'env',
                        customOption: {
                          jsonPath: 'body',
                          loading: true,
                          nextEventsDelay: 0,
                          codeValue: '0000000',
                          codeKey: 'code',
                          messageKey: 'message',
                          parse: false,
                        },
                        headerPayloads: [
                          {
                            key: 'ram-token',
                            value: 'sessionStorage.ramToken',
                            id: 'f0f31952-2460-4b8f-923f-6f694cd2e175',
                          },
                          {
                            key: 'ram-system',
                            value: '1054762269392478208',
                            id: '47dce57e-98e5-43e4-8dac-1f70fcca3648',
                          },
                        ],
                        interceptors: {
                          requestInterceptor: '',
                          responseInterceptor: '',
                        },
                      },
                      thenActions: [
                        {
                          event: 'mounted',
                          action: 'setNewValue',
                          option: {
                            from: {
                              dynamic: {
                                nodePath: 'env',
                              },
                            },
                            to: {
                              type: 'dynamic',
                              dynamic: {
                                nodePath:
                                  '下拉框-9d3bb4ea-c231-46a6-8855-5e630407db07.prop.options',
                              },
                            },
                          },
                          thenActions: [],
                        },
                      ],
                    },
                  ],
                },
                {
                  compName: 'lyy-switch',
                  compId: 'e569fbec-3320-4983-96cd-e3feca9f33d1',
                  compNameCn: '开关',
                  prop: {
                    field: 'globalConfigureFlag',
                    label: '发布全局配置',
                    size: 'default',
                    checkedValue: 'Y',
                    unCheckedValue: 'N',
                    checkedChildren: '是',
                    unCheckedChildren: '否',
                    readonly: false,
                    rules: [
                      {
                        required: false,
                        message: '',
                        trigger: '',
                      },
                    ],
                    disabled: false,
                    formId: '',
                  },
                  actions: [],
                },
                {
                  compName: 'lyy-tree',
                  compId: '3cae0d5a-00b0-4e40-8773-b400fa46c64c',
                  compNameCn: '树选择',
                  prop: {
                    field: 'configureInfoList',
                    label: '发布页面',
                    options: [],
                    fieldNames: {
                      key: 'path',
                      title: 'name',
                      children: 'children',
                      checked: 'selected',
                    },
                    validateTrigger: 'change',
                    expandedKeys: [],
                    rules: [
                      {
                        required: true,
                        message: '请选择发布页面',
                        trigger: 'blur',
                        type: 'array',
                      },
                    ],
                    defaultExpandAll: true,
                    checkable: true,
                    checkStrictly: false,
                    showIcon: true,
                    onlySaveLeaves: false,
                    selectable: false,
                    prefixIcons: [
                      {
                        iconName: 'folder-open-outlined',
                        iconProps: {
                          style: {
                            color: '#ffa05c',
                          },
                        },
                        customOption: {
                          key: 'type',
                          value: 'group',
                        },
                      },
                      {
                        iconName: 'file-text-outlined',
                        iconProps: {
                          style: {
                            color: 'rgb(84 190 15)',
                          },
                        },
                        customOption: {
                          key: 'type',
                          value: 'form',
                        },
                      },
                    ],
                  },
                  actions: [
                    {
                      event: 'mounted',
                      action: 'request',
                      option: {
                        url: '/gw/editor/menus/tree',
                        method: 'get',
                        payloads: {
                          type: 'higher',
                          static: '',
                          higher: [
                            {
                              key: 'authSystemId',
                              value:
                                '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                              id: '359c6b90-c666-4211-b56a-03958db3bfad',
                            },
                          ],
                        },
                        responseDataKey: 'treeData',
                        customOption: {
                          loading: true,
                          nextEventsDelay: 0,
                          parse: false,
                          codeValue: '0000000',
                          codeKey: 'code',
                          messageKey: 'message',
                          jsonPath: 'body',
                        },
                        headerPayloads: [
                          {
                            key: 'ram-token',
                            value: 'sessionStorage.ramToken',
                            id: 'eaeafa1b-7a20-4e3e-bce8-a0c3688c4c41',
                          },
                          {
                            key: 'ram-system',
                            value: '1054762269392478208',
                            id: '81646175-9739-4e06-b576-fed7462ec45f',
                          },
                        ],
                        interceptors: {
                          requestInterceptor:
                            '(config) => {\n  \n  return config;\n  }',
                          responseInterceptor:
                            "(res) => {\n  const list = res.data.body\r\nfunction setGroupPath(list) {\r\n    for (const item of list) {\r\n        if (item.type !== 'form') {\r\n            item.path = 'group-' + item.id\r\n        }\r\n        if (item.children && item.children.length) {\r\n            setGroupPath(item.children)\r\n        }\r\n    }\r\n}\r\nif (list.length) {\r\n    setGroupPath(list)\r\n}\r\nreturn res\r\n  \r\n  \n  return res;\n  }",
                        },
                      },
                      thenActions: [
                        {
                          event: 'mounted',
                          action: 'setNewValue',
                          option: {
                            to: {
                              type: 'dynamic',
                              dynamic: {
                                nodePath:
                                  '3cae0d5a-00b0-4e40-8773-b400fa46c64c.prop.options',
                              },
                            },
                            from: {
                              dynamic: {
                                nodePath: 'treeData',
                              },
                            },
                          },
                          thenActions: [],
                        },
                      ],
                    },
                    {
                      event: 'update',
                      action: 'customerJS',
                      option: {
                        customJs:
                          '(useAction,data,context) => {\n    /* 自定义JS使用说明：\n* 1.动作执行函数useAction，可以执行所有类型的动作\n* 2.通过上下文对象context可以获取当前组件实例，例如context.prop可以获取该组件相关属性\n* 3.通过数据树对象data可以获取当前数据树的所有数据\n* 4.事件配置具体看 事件文档\n*/\n return useAction\n  }',
                      },
                      thenActions: [],
                    },
                  ],
                  childrens: [],
                  modelValue: [],
                },
              ],
            },
            actions: [],
          },
        ],
      },
    ],
    actions: [
      // 校验表单
      {
        event: 'confirm',
        action: 'validate',
        option: {
          targetId: 'publish-form',
        },
        thenActions: [
          {
            action: 'setNewValue',
            option: {
              to: {
                type: 'dynamic',
                dynamic: {
                  nodePath: 'publish-form.modelValue.menuIdList',
                },
              },
              from: {
                dynamic: {
                  nodePath:
                    '3cae0d5a-00b0-4e40-8773-b400fa46c64c.prop.halfCheckedKeys',
                },
              },
            },
            thenActions: [],
          },
          // 打开二次确认弹窗
          {
            action: 'openModal',
            option: {
              targetId: 'publish-confirm-modal',
            },
          },
        ],
      },
      // 提交请求
      {
        event: 'submit',
        action: 'download',
        option: {
          baseURL: '',
          url: '/gw/editor/page/configures/publishConfigureInfo',
          method: 'post',
          payloads: {
            type: 'dynamic',
            static: '',
            dynamic: {
              nodePath: '基础表单-publish-form.modelValue',
            },
          },
          targets: [],
          responseType: 'blob',
          sourceKey: '',
          targetId: null,
          customOption: {
            loading: true,
            nextEventsDelay: 0,
            codeValue: '0000000',
            codeKey: 'code',
            messageKey: 'message',
            parse: false,
            successfulFeedback: {
              type: 'message',
              duration: 2,
              placement: 'topLeft',
              status: 'success',
              title: '',
              message: '发布成功',
            },
          },
          headerPayloads: [
            {
              key: 'ram-token',
              value: 'sessionStorage.ramToken',
            },
            {
              key: 'ram-system',
              value: '1054762269392478208',
            },
          ],
          interceptors: {
            requestInterceptor(config) {
              // 处理文件夹id
              const menuIdList = config.data.configureInfoList
                .filter((item) => item.includes('group'))
                .map((item) => item.replace('group-', ''))
              config.data.menuIdList = menuIdList.concat(
                config.data.menuIdList.map((item) =>
                  item.replace('group-', ''),
                ),
              )
              // 处理页面id
              const configureInfoList = config.data.configureInfoList.filter(
                (item) => !item.includes('group'),
              )
              config.data.configureInfoList = configureInfoList
              return config
            },
            responseInterceptor: '(res) => {\n  \n  return res;\n  }',
          },
        },
        thenActions: [
          {
            action: 'resetValue',
            option: {
              targetId: 'publish-form',
            },
            thenActions: [
              {
                action: 'closeModal',
                option: {
                  targetId: 'publish-modal',
                },
              },
            ],
          },
        ],
      },
    ],
  },
  // 发布页面二次确认弹窗
  {
    compName: 'lyy-modal',
    compId: 'publish-confirm-modal',
    compNameCn: '模态框',
    modelValue: false,
    style: {},
    prop: {
      title: '提示',
      size: 'middle',
      loading: {
        tip: '加载中',
        spinning: true,
      },
      buttonList: [],
      centered: true,
      maskClosable: true,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '取消',
      destroyOnClose: true,
      footer: true,
    },
    childrens: [
      {
        compName: 'lyy-html',
        prop: {
          html: '<p>确定发布吗?</p>',
        },
        childrens: [],
        style: {},
        actions: [],
      },
    ],
    actions: [
      {
        event: 'confirm',
        action: 'broadcast',
        option: {
          targetId: 'publish-modal',
          event: 'submit',
        },
        thenActions: [
          {
            action: 'closeModal',
            option: {
              targetId: 'publish-confirm-modal',
            },
            thenActions: [],
          },
        ],
      },
      {
        event: 'cancel',
        action: 'closeModal',
        option: {
          targetId: 'publish-confirm-modal',
        },
      },
    ],
  },
  {
    compName: 'lyy-modal',
    compId: '321dfd36-cd21-4e9a-9982-1c48da5469de',
    compNameCn: '模态框',
    modelValue: false,
    style: {},
    prop: {
      title: '全局配置',
      size: 'middle',
      loading: { tip: '加载中', spinning: true },
      buttonList: [],
      centered: true,
      maskClosable: false,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '取消',
      destroyOnClose: true,
    },
    childrens: [
      {
        compName: 'lyy-form',
        compId: '90007d0f-bb29-4d83-bb1c-84503e40f5a3',
        compNameCn: '表单',
        style: {},
        prop: {
          form: {
            envStr: '',
            authSystemId: '',
          },
          aliasName: '表单',
          isSignChange: null,
          mainLayout: false,
          class: '',
        },
        modelValue: { envStr: '' },
        actions: [],
        childrens: [
          {
            compName: 'lyy-editor',
            compId: '72ac4f2c-3d36-48aa-bba4-0c3b2b78adca',
            compNameCn: '代码编辑器',
            style: {},
            prop: {
              field: 'envStr',
              rules: [
                { required: false, message: '', trigger: '', pattern: '' },
              ],
              style: {},
            },
            actions: [],
            modelValue: '',
            relativeCompId: '321dfd36-cd21-4e9a-9982-1c48da5469de',
            relativeCompName: 'lyy-modal',
          },
        ],
        relativeCompId: '321dfd36-cd21-4e9a-9982-1c48da5469de',
        relativeCompName: 'lyy-modal',
      },
    ],
    actions: [
      {
        event: 'confirm',
        action: 'validate',
        option: { targetId: '90007d0f-bb29-4d83-bb1c-84503e40f5a3' },
        thenActions: [
          {
            event: 'confirm',
            action: 'request',
            option: {
              url: '/gw/editor/system/environments/',
              method: 'put',
              payloads: {
                type: 'higher',
                higher: [
                  {
                    key: 'envStr',
                    value:
                      '表单-90007d0f-bb29-4d83-bb1c-84503e40f5a3.modelValue.envStr',
                  },
                  {
                    key: 'authSystemId',
                    value:
                      '表格-62da04c0-12c8-4e68-8c4f-86338c7f6bae.modelValue.currentRow.authSystemId',
                    id: '7c0c54f8-93dc-4c57-9a98-a01ce481dcd7',
                  },
                  {
                    key: 'authSystemEnvId',
                    value:
                      '表单值-90007d0f-bb29-4d83-bb1c-84503e40f5a3.authSystemEnvId',
                    id: '63276498-bbd7-48a9-872a-998a74330d11',
                  },
                ],
              },
              responseDataKey: '',
              customOption: {
                jsonPath: '',
                loading: false,
                successfulFeedback: {
                  type: 'message',
                  duration: 2,
                  placement: 'topLeft',
                  status: 'success',
                  title: '',
                  message: '成功',
                },
                failedFeedback: null,
                nextEventsDelay: 0,
                codeValue: '0000000',
                codeKey: 'code',
                messageKey: 'message',
                parse: false,
              },
              headerPayloads: [],
            },
            thenActions: [
              {
                event: 'confirm',
                action: 'closeModal',
                option: { targetId: '321dfd36-cd21-4e9a-9982-1c48da5469de' },
                thenActions: [],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    compName: 'lyy-modal',
    compId: '466b89c5-9079-4462-a602-397ec871a968',
    compNameCn: '模态框',
    modelValue: false,
    style: {},
    prop: {
      title: '新增应用',
      size: 'middle',
      loading: { tip: '加载中', spinning: true },
      buttonList: [],
      centered: true,
      maskClosable: true,
      mask: true,
      closable: true,
      okText: '确定',
      okType: 'primary',
      cancelText: '取消',
      destroyOnClose: true,
    },
    childrens: [
      {
        compName: 'lyy-form',
        compId: '035f644a-d40c-4a98-aeba-da46484b6a86',
        compNameCn: '表单',
        style: {},
        prop: {
          form: { isInside: 'N', isSaas: 'N' },
          aliasName: '表单',
          isSignChange: null,
          mainLayout: false,
          class: '',
        },
        modelValue: { authSystemId: '', isInside: 'N', isSaas: 'N' },
        actions: [],
        childrens: [
          {
            compName: 'lyy-select',
            compId: 'd98a168b-6e7f-4de9-856a-1ab6216696f0',
            compNameCn: '下拉框',
            prop: {
              field: 'authEnterpriseId',
              label: '企业Id',
              aliasName: '下拉框',
              placeholder: '请选择企业Id',
              mode: 'combobox',
              defaultValue: { source: '', sourceKey: '' },
              rules: [
                { required: true, message: '请选择企业Id', trigger: 'change' },
              ],
              options: [],
              fieldNames: { label: 'name', value: 'authEnterpriseId' },
              size: 'default',
              immediate: true,
              allowClear: true,
              readonly: false,
              showSearch: true,
              optionFilterProp: '',
              disabled: false,
              icon: { iconName: '', popover: { description: [] } },
              formId: '',
              show: {
                exp: '',
              },
              forbid: {
                exp: '',
                payloads: [
                  {
                    key: '',
                    source: '',
                    sourceKey: '',
                    decompose: false,
                    value: '',
                  },
                ],
              },
            },
            modelValue: '',
            actions: [
              {
                event: 'mounted',
                action: 'request',
                option: {
                  url: '/gw/editor/enterprises/list',
                  method: 'get',
                  payloads: { type: 'static', static: '' },
                  responseDataKey: '下拉框值-enterprisesList',
                  customOption: {
                    jsonPath: 'body',
                    loading: false,
                    successfulFeedback: null,
                    failedFeedback: null,
                    nextEventsDelay: 0,
                    codeValue: '0000000',
                    codeKey: 'code',
                    messageKey: 'message',
                    parse: false,
                  },
                  headerPayloads: [],
                },
                thenActions: [
                  {
                    event: 'mounted',
                    action: 'setNewValue',
                    option: {
                      from: {
                        dynamic: { nodePath: '下拉框值-enterprisesList' },
                      },
                      to: {
                        type: 'dynamic',
                        dynamic: {
                          nodePath:
                            '下拉框-d98a168b-6e7f-4de9-856a-1ab6216696f0.prop.options',
                        },
                      },
                    },
                    thenActions: [],
                  },
                ],
              },
            ],
            relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
            relativeCompName: 'lyy-modal',
          },
          {
            compName: 'lyy-text-input',
            compId: 'b0712a50-a8ae-4a43-8003-18b4888d43fb',
            compNameCn: '输入框',
            style: {},
            prop: {
              field: 'name',
              label: '系统名称',
              aliasName: '文本输入框',
              placeholder: '请输入系统名称',
              defaultValue: '',
              validateTrigger: 'change',
              maxlength: null,
              size: 'default',
              suffix: '',
              addonAfter: '',
              rules: [
                {
                  required: true,
                  message: '请输入系统名称',
                  trigger: 'blur',
                  pattern: '',
                },
              ],
              disabled: false,
              showCount: false,
              allowClear: true,
              icon: { iconName: '', popover: { description: [] } },
              formId: '',
              show: {
                exp: '',
              },
              forbid: {
                exp: '',
                payloads: [
                  {
                    key: '',
                    source: '',
                    sourceKey: '',
                    decompose: false,
                    value: '',
                  },
                ],
              },
            },
            modelValue: '',
            actions: [],
            relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
            relativeCompName: 'lyy-modal',
          },
          {
            compName: 'lyy-text-input',
            compId: 'd1653896-c0c2-4033-9f77-dbe589cdbe5e',
            compNameCn: '输入框',
            style: {},
            prop: {
              field: 'value',
              label: '系统标识',
              aliasName: '文本输入框',
              placeholder: '请输入系统标识',
              defaultValue: '',
              validateTrigger: 'change',
              maxlength: null,
              size: 'default',
              suffix: '',
              addonAfter: '',
              rules: [
                {
                  required: true,
                  message: '请输入系统标识',
                  trigger: 'blur',
                  pattern: '',
                },
              ],
              disabled: false,
              showCount: false,
              allowClear: true,
              icon: { iconName: '', popover: { description: [] } },
              formId: '',
              show: {
                exp: '',
              },
              forbid: {
                exp: '',
                payloads: [
                  {
                    key: '',
                    source: '',
                    sourceKey: '',
                    decompose: false,
                    value: '',
                  },
                ],
              },
            },
            modelValue: '',
            actions: [],
            relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
            relativeCompName: 'lyy-modal',
          },
          {
            compName: 'lyy-switch',
            compId: 'c725569e-c16c-4954-a2fd-39e1c8d62cb7',
            compNameCn: '开关',
            prop: {
              field: 'isInside',
              label: '是否是内部系统',
              rules: [{ required: false, message: '', trigger: '' }],
              size: 'default',
              checkedValue: 'Y',
              unCheckedValue: 'N',
              checkedChildren: '',
              unCheckedChildren: '',
              readonly: false,
              disabled: false,
              formId: '',
            },
            actions: [],
            relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
            relativeCompName: 'lyy-modal',
          },
          {
            compName: 'lyy-switch',
            compId: '591e8a30-d559-423f-925f-3e6316a30fe7',
            compNameCn: '开关',
            prop: {
              field: 'isSaas',
              label: '是否是SASS系统',
              rules: [{ required: false, message: '', trigger: '' }],
              size: 'default',
              checkedValue: 'Y',
              unCheckedValue: 'N',
              checkedChildren: '',
              unCheckedChildren: '',
              readonly: false,
              disabled: false,
              formId: '',
            },
            actions: [],
            relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
            relativeCompName: 'lyy-modal',
          },
          {
            compName: 'lyy-text-input',
            compId: 'b2ad0c93-9e8e-44a9-bace-562681130188',
            compNameCn: '输入框',
            style: {},
            prop: {
              field: 'domainName',
              label: '系统域名',
              aliasName: '文本输入框',
              placeholder: '请输入系统域名',
              defaultValue: '',
              validateTrigger: 'change',
              maxlength: null,
              size: 'default',
              suffix: '',
              addonAfter: '',
              rules: [
                {
                  required: true,
                  message: '请输入系统域名',
                  trigger: 'blur',
                  pattern: '',
                },
              ],
              disabled: false,
              showCount: false,
              allowClear: true,
              icon: { iconName: '', popover: { description: [] } },
              formId: '',
              show: {
                exp: '',
              },
              forbid: {
                exp: '',
                payloads: [
                  {
                    key: '',
                    source: '',
                    sourceKey: '',
                    decompose: false,
                    value: '',
                  },
                ],
              },
            },
            modelValue: '',
            actions: [],
            relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
            relativeCompName: 'lyy-modal',
          },
        ],
        relativeCompId: '466b89c5-9079-4462-a602-397ec871a968',
        relativeCompName: 'lyy-modal',
      },
    ],
    actions: [
      {
        event: 'confirm',
        action: 'validate',
        option: { targetId: '035f644a-d40c-4a98-aeba-da46484b6a86' },
        thenActions: [
          {
            event: 'confirm',
            action: 'request',
            option: {
              url: '/gw/editor/systems/',
              method: 'post',
              payloads: {
                type: 'dynamic',
                dynamic: {
                  nodePath:
                    '表单-035f644a-d40c-4a98-aeba-da46484b6a86.modelValue',
                },
              },
              responseDataKey: '',
              customOption: {
                jsonPath: '',
                loading: false,
                successfulFeedback: null,
                failedFeedback: null,
                nextEventsDelay: 0,
                codeValue: '0000000',
                codeKey: 'code',
                messageKey: 'message',
                parse: false,
              },
              headerPayloads: [],
            },
            thenActions: [
              {
                event: 'confirm',
                action: 'closeModal',
                option: { targetId: '466b89c5-9079-4462-a602-397ec871a968' },
                thenActions: [
                  {
                    event: 'confirm',
                    action: 'broadcast',
                    option: {
                      targetId: '52d43f0f-7b62-4823-b0e3-60449f75c2b3',
                      event: 'mounted',
                    },
                    thenActions: [],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
]

/*
 * 项目信息
 * */
export type appInfo = {
  authMenuId: string
  authSystemId: string
  children: appInfo[] | null
  entryType: number
  hide: 'Y' | 'N'
  iconName: string | null
  id: string
  isCache: 'Y' | 'N'
  name: string
  parentId: string | null
  queryButtonAuthCode: string | null
  queryFieldAuthCode: string | null
  seq: number
  type: string
  value: string | number | null
}
