<template>
  <div class="warper">
    <div class="warper-table">
      <div class="warper-input">
        <a-form layout="inline" :model="fromState" @finish="handleFinish">
          <a-row style="width: 100%">
            <a-col :span="6">
              <a-form-item label="姓名:" name="codeUserName">
                <a-input
                  v-model:value="fromState.codeUserName"
                  placeholder="请输入姓名"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="手机号:">
                <a-input
                  v-model:value="fromState.phone"
                  placeholder="请输入手机号"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-button type="primary" html-type="submit">搜索</a-button>
                <a-button style="margin-left: 20px" @click="reset">
                  重置
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <a-card title="账号管理列表">
        <template #extra>
          <a-button type="primary" @click="openAddUserPopup(false, null)">
            新增账号
          </a-button>
        </template>
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          :pagination="pagination"
          size="small"
          :loading="isloading"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'allowedAppCount'">
              <div
                class="appCount"
                @click="openAddressablePopupVisible(record)"
              >
                {{ text }}
              </div>
            </template>
            <template v-if="column.dataIndex === 'active'">
              <div>{{ text ? '启用' : '禁用' }}</div>
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <div class="operationList">
                <div v-if="!record.active" class="appCount">
                  <a-popconfirm
                    title="确认启用该账号？"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="enableClick(true, record)"
                  >
                    启用
                  </a-popconfirm>
                </div>
                <div v-if="record.active" class="appCount disabledCount">
                  <a-popconfirm
                    title="确认禁用该账号？"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="enableClick(false, record)"
                  >
                    禁用
                  </a-popconfirm>
                </div>
              </div>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
    <addUserPopup
      v-if="addUserPopupVisible"
      v-model="addUserPopupVisible"
      :edit-data="addUserEditData"
      :user-list="userListData"
      :is-edit="isEdit"
      @submit="addUserPopupSumbit"
      @get-user-list="searchUserData"
    />
    <addressablePopup
      v-if="addressablePopupVisible && userAppList.length > 0"
      v-model="addressablePopupVisible"
      :app-list="userAppList"
      @submit="addressablePopupSubmit"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import {
  getUserTableData,
  getApplicationManager,
  enAbleUser,
  getUserAppList,
  editUserAppList,
  addUser,
} from '@/request/api/applist'
import addUserPopup from './components/addUserPopup.vue'
import addressablePopup from './components/addressablePopup.vue'
import { message } from 'ant-design-vue'

const fromState = reactive({
  codeUserName: '',
  phone: '',
})
const dataSource = ref(null)
const columns = [
  {
    title: '姓名',
    dataIndex: 'codeUserName',
    resizable: true,
    width: 250,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    resizable: true,
    width: 250,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    resizable: true,
    width: 250,
  },
  {
    title: '可访问应用数量',
    dataIndex: 'allowedAppCount',
    resizable: true,
    width: 250,
  },
  {
    title: '状态',
    dataIndex: 'active',
    resizable: true,
    width: 250,
  },
  {
    title: '操作栏',
    dataIndex: 'operation',
    fixed: 'right',
    resizable: true,
    width: 250,
  },
]
const current = ref<number>(1)
const size = ref<number>(20)
const isloading = ref<boolean>(true)
// 分页处理
const pagination = reactive<any>({
  current: current,
  pageSize: size,
  hideOnSinglePage: false,
  showSizeChanger: true,
  size: 'normal',
  total: undefined,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total) => `共${total}条`,
  onChange: (current, pageSize) => {
    pagination.current = current
    if (pagination.pageSize != pageSize) {
      pagination.pageSize = pageSize
      pagination.current = 1
    }
    getUserTableList()
  },
})

// 获取账号管理列表数据
const getUserTableList = async () => {
  try {
    isloading.value = true
    const params = {
      current: current.value,
      size: size.value,
      codeUserName: fromState.codeUserName,
      phone: fromState.phone,
    }
    const res = await getUserTableData(params)
    pagination.total = res.body.total
    if (res.body.records) {
      dataSource.value = res.body.records
      isloading.value = false
    }
  } catch {}
}

const handleFinish = () => {
  pagination.current = 1
  pagination.pageSize = 20
  getUserTableList()
}
const reset = () => {
  fromState.codeUserName = ''
  fromState.phone = ''
  getUserTableList()
}

// 新增账号弹窗
const addUserPopupVisible = ref(false)
const isEdit = ref(false)
// 打开新增账号弹窗
const addUserEditData = ref({})
const openAddUserPopup = (isTrue, data) => {
  isEdit.value = isTrue
  addUserEditData.value = data
  addUserPopupVisible.value = true
}

// 获取员工账号列表
const userListData = ref<Array<any>>([])
const getUserList = async (data?) => {
  try {
    const params = {
      queryInside: true,
      searchContent: data ? data : '',
      current: 1,
      size: 10,
    }
    const res = await getApplicationManager(params)
    userListData.value = res.body.records
  } catch (error) {
    console.error(error)
  }
}

const enableClick = async (isEnable, data) => {
  try {
    const params = {
      codeUserId: data.codeUserId,
      isActive: isEnable ? 1 : 0,
    }
    const res = await enAbleUser(params)
    if (res && res.code == '0000000') {
      message.success(isEnable ? '启用成功' : '禁用成功')
      getUserTableList()
    }
  } catch (error) {
    console.error(error)
  }
}

// 搜索用户列表
const searchTimer = ref<any>(null) // 用于设置和清除延时定时器
const searchUserData = (data) => {
  // 如果之前有定时器，清除它
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  // 设置新的定时器，在延时后执行搜索
  searchTimer.value = setTimeout(() => {
    // 这里执行真实的搜索逻辑，例如发起API请求
    getUserList(data)
  }, 500) // 延时300毫秒
}

// 新增账号弹窗提交
const addUserPopupSumbit = async (data) => {
  const params = {
    authAccountId: data.authAccountId,
    codeUserName: data.codeUserName,
  }
  try {
    const res = await addUser(params)
    if (res && res.body) {
      message.success('添加成功')
      getUserTableList()
      addUserPopupVisible.value = false
    }
  } catch (error) {
    console.error(error)
  }
}

// 可访问系统弹窗
const addressablePopupVisible = ref(false)
const nowCodeUserId = ref(null)
const openAddressablePopupVisible = async (data) => {
  if (data.allowedAppCount == 0) {
    message.info('您选择的账号暂无可访问应用')
    return
  }
  userAppList.value = []
  nowCodeUserId.value = data.codeUserId
  getRessableList()
  addressablePopupVisible.value = true
}
// 获取用户可访问系统
const userAppList = ref([])
const getRessableList = async () => {
  try {
    const res = await getUserAppList({ codeUserId: nowCodeUserId.value })
    const arr = []
    for (const item of res.body) {
      const obj = {}
      obj.label = item.isRepositoryOwner
        ? item.name + ' (应用负责人)'
        : item.name
      obj.value = item.codeRepositoryId
      obj.disabled = item.isRepositoryOwner
      arr.push(obj)
    }
    userAppList.value = arr
  } catch (error) {
    console.error(error)
  }
}
// 可访问系统弹窗提交
const addressablePopupSubmit = async (data) => {
  try {
    const params = {
      codeUserId: nowCodeUserId.value,
      codeRepositoryIdList: data,
    }
    const res = await editUserAppList(params)
    if (res && res.body) {
      addressablePopupVisible.value = false
      getUserTableList()
    }
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  getUserTableList()
  getUserList()
})
</script>

<style lang="less" scoped>
.warper {
  padding: 45px;
  padding-top: 20px;
  .warper-input {
    margin-bottom: 20px;
    border: 1px solid #f0f0f0;
    padding: 15px;
  }
}
.more {
  transform: rotate(90deg);
}
.ant-dropdown-link {
  display: inline-block;
}
.operation-btns {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  column-gap: 8px;
  .ant-btn {
    padding: 0;
  }
}
.appCount {
  color: #0987f5;
  cursor: pointer;
}
.operationList {
  display: flex;
  align-items: center;
  gap: 4px;
  .appCount {
    color: #0987f5;
    cursor: pointer;
  }
  .disabledCount {
    color: #ff4d4f;
  }
}
</style>
