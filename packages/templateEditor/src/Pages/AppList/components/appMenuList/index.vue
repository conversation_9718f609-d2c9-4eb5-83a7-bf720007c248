<template>
  <a-spin :spinning="spinning">
    <div class="warper">
      <div class="warper-input">
        <a-form layout="inline" :model="fromState" @finish="handleFinish">
          <a-row style="width: 100%">
            <a-col :span="6">
              <a-form-item label="系统标识:">
                <a-input
                  v-model:value="fromState.value"
                  placeholder="请输入系统标识"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="应用名称:">
                <a-input
                  v-model:value="fromState.name"
                  placeholder="请输入应用名称"
                ></a-input>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item>
                <a-button type="primary" html-type="submit">搜索</a-button>
                <a-button style="margin-left: 20px" @click="reset">
                  重置
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="warper-table">
        <a-card title="应用列表">
          <template #extra>
            <a-button type="primary" @click="showAddAppModal = true">
              新增应用
            </a-button>
          </template>
          <a-table
            :dataSource="dataSource"
            :columns="columns"
            :pagination="pagination"
            size="small"
            :loading="isloading"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'isSaas'">
                <span>{{ record.isSaas === 'Y' ? '是' : '否' }}</span>
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <div
                  class="operation-btns"
                  v-if="filterOperationBtn(record, elements).length > max"
                >
                  <template
                    v-for="(element, index) in filterOperationBtn(
                      record,
                      elements,
                    ).slice(0, max - 1)"
                    :key="index"
                  >
                    <a-popconfirm
                      title="Are you sure delete this task?"
                      ok-text="Yes"
                      cancel-text="No"
                      @confirm="deleteApp(record)"
                      v-if="element.label === '删除'"
                    >
                      <a-button
                        :type="element.type"
                        :danger="element.danger"
                        :size="element.size"
                        v-show="element.isShow"
                      >
                        {{ element.label }}
                      </a-button>
                    </a-popconfirm>
                    <a-button
                      v-else
                      :type="element.type"
                      :danger="element.danger"
                      :size="element.size"
                      v-show="element.isShow"
                      @click="clickBtn(element, record)"
                    >
                      {{ element.label }}
                    </a-button>
                  </template>
                  <a-dropdown>
                    <a class="ant-dropdown-link" @click.prevent>
                      更多
                      <MoreOutlined class="more" />
                    </a>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item
                          v-for="(element, index) in filterOperationBtn(
                            record,
                            elements,
                          ).slice(max - 1)"
                          :key="index"
                        >
                          <a-popconfirm
                            title="您确定删除吗"
                            ok-text="确定"
                            cancel-text="取消"
                            @confirm="deleteAppDetail(record)"
                            v-if="element.label === '删除'"
                          >
                            <a-button
                              :type="element.type"
                              :danger="element.danger"
                              :size="element.size"
                              v-show="element.isShow"
                              style="padding: 0"
                            >
                              {{ element.label }}
                            </a-button>
                          </a-popconfirm>
                          <a-button
                            v-else
                            :type="element.type"
                            :danger="element.danger"
                            :size="element.size"
                            v-show="element.isShow"
                            @click="clickBtn(element, record)"
                            style="padding: 0"
                          >
                            {{ element.label }}
                          </a-button>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
                <div v-else>
                  <template
                    v-for="(element, index) in filterOperationBtn(
                      record,
                      elements,
                    )"
                    :key="index"
                  >
                    <a-button
                      :type="element.type"
                      :danger="element.danger"
                      :size="element.size"
                      v-show="element.isShow"
                    >
                      {{ element.label }}
                    </a-button>
                  </template>
                </div>
              </template>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>
  </a-spin>
  <addAppModal
    v-if="showAddAppModal"
    v-model="showAddAppModal"
    @submit="handleSubmit"
  ></addAppModal>
  <editAppModal
    v-if="showeditAppModal"
    v-model="showeditAppModal"
    :editData="editData"
    @submit="editHandleSubmit"
  ></editAppModal>
  <releaseListModal
    v-if="showReleaseModal"
    v-model="showReleaseModal"
    :data="currentRowData"
    @submit="handleSubmitRelease"
  ></releaseListModal>
  <a-modal
    v-model:visible="visible"
    title="全局配置"
    style="width: 800px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <lyy-editor
      :modelValue="jsonValue"
      @update:modelValue="handleJson"
      :prop="{
        field: 'responseInterceptorBody',
        style: {
          height: '450px',
        },
      }"
    ></lyy-editor>
  </a-modal>
  <a-modal
    v-model:visible="sqlModal"
    title="菜单脚本"
    style="width: 600px"
    @ok="sqlModalFn"
    @cancel="sqlModalFn"
  >
    <lyy-editor
      :modelValue="sqlTxt"
      :prop="{
        style: {
          height: '350px',
        },
      }"
    ></lyy-editor>
  </a-modal>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import {
  getAgentTokenApi,
  getCarCharingApi,
  getPublicKeyApi,
  getSmallGroupTokenApi,
  getWawawuTokenApi,
  getYiPuleTokenApi,
  getBLogin,
} from '@/request/api/getTokens'
import {
  getData,
  deleteApp,
  getpageConfig,
  updateConfig,
} from '@/request/api/applist'
import router from '@/Router'
import axios from 'axios'
import md5 from 'js-md5'
import { getAuthCodeFn } from '@/request/api/editorRequest'

import addAppModal from './addAppModal.vue'
import editAppModal from './editAppModal.vue'
import releaseListModal from './releaseListModal.vue'
import { message } from 'ant-design-vue'
import { uuidv4 } from '@leyaoyao/libs'

const activeKey = ref('1')
const sqlModal = ref(false)
const sqlTxt = ref('')
const fromState = reactive({
  value: '',
  name: '',
})
const dataSource = ref([])
const spinning = ref<boolean>(false)

const columns = [
  // {
  //   title: '企业id',
  //   dataIndex: 'authEnterpriseId',
  //   resizable: true,
  //   width: 250,
  // },
  {
    title: '应用名称',
    dataIndex: 'name',
    resizable: true,
    ellipsis: true,
  },
  {
    title: '系统id',
    dataIndex: 'authSystemId',
    resizable: true,
    ellipsis: true,
  },
  {
    title: '系统标识',
    dataIndex: 'value',
    resizable: true,
    ellipsis: true,
  },
  {
    title: '系统域名',
    dataIndex: 'domainName',
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'Git仓库地址',
    dataIndex: 'gitRepositoryUrl',
    resizable: true,
    ellipsis: true,
  },
  {
    title: '应用负责人',
    dataIndex: 'ownerAuthAccountName',
    resizable: true,
    ellipsis: true,
    width: 100,
  },
  {
    title: 'SAAS系统',
    dataIndex: 'isSaas',
    resizable: true,
    ellipsis: true,
    width: 80,
  },
  {
    title: '操作栏',
    dataIndex: 'operation',
    fixed: 'right',
    resizable: true,
    width: 150,
  },
]

// 表格操作列按钮列表
const elements = [
  {
    label: '登录',
    type: 'link',
    size: 'small',
    danger: false,
    isShow: true,
  },
  {
    label: '详情',
    type: 'link',
    size: 'small',
    danger: false,
    isShow: true,
  },
  {
    label: '编辑',
    type: 'link',
    size: 'small',
    danger: false,
    isShow: true,
  },
  {
    label: '发布',
    type: 'link',
    size: 'small',
    danger: false,
    isShow: true,
  },
  {
    label: '配置',
    type: 'link',
    size: 'small',
    danger: false,
    isShow: true,
  },
  {
    label: '删除',
    type: 'link',
    size: 'small',
    danger: true,
    isShow: true,
  },
]

const max = 3
const showAddAppModal = ref<boolean>(false)
const showeditAppModal = ref<boolean>(false)
const editData = ref<any>({})
const showReleaseModal = ref<boolean>(false)
const current = ref<number>(1)
const size = ref<number>(20)
const isloading = ref<boolean>(true)
const currentRowData = ref(null)
import JSEncrypt from 'jsencrypt'
// 分页处理
const pagination = reactive({
  current: current,
  pageSize: size,
  hideOnSinglePage: false,
  showSizeChanger: true,
  size: 'normal',
  total: undefined,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total) => `共${total}条`,
  onChange: (current, pageSize) => {
    pagination.current = current
    if (pagination.pageSize != pageSize) {
      pagination.pageSize = pageSize
      pagination.current = 1
    }
    getListData()
  },
})
const sqlModalFn = () => {
  sqlModal.value = false
}
// 获取表格数据
async function getListData() {
  isloading.value = true
  const dataList = await getData({
    value: fromState.value === '' ? undefined : fromState.value,
    name: fromState.name === '' ? undefined : fromState.name,
    current: pagination.current,
    size: pagination.pageSize,
  })
  pagination.total = dataList.body.total
  if (dataList?.body.records) {
    dataSource.value = dataList.body.records
    isloading.value = false
  }
}

// 搜索
function handleFinish() {
  pagination.current = 1
  pagination.pageSize = 20
  getListData()
}

// 重置
function reset() {
  fromState.value = ''
  fromState.name = ''
  pagination.current = 1
  pagination.pageSize = 20
  getListData()
}

// 处理操作列
function filterOperationBtn(currentRow, buttonlist) {
  return buttonlist.filter(Boolean)
}

// 配置弹窗
const visible = ref(false)
const jsonValue = ref()
const currentData = ref()
const codeValue = ref()
async function handleOk() {
  try {
    const { code } = await updateConfig({
      envStr: codeValue.value,
      authSystemId: currentData.value.authSystemId,
      authSystemEnvId: currentData.value.authSystemEnvId,
    })
    if (code === '0000000') {
      visible.value = false
      message.success('更新成功')
      getListData()
    }
  } catch {
    console.log('更新失败')
  }
}
function handleCancel() {
  jsonValue.value = ''
  currentData.value = ''
  codeValue.value = ''
}
const setAgentToken = async () => {
  const res = await getAgentTokenApi()
  const { data } = res
  if (data.result === 0) {
    tokens.value.agent = data.data.ticket
    sessionStorage.setItem('token', data.data.ticket)
    localStorage.setItem('tokens', JSON.stringify(tokens.value))
  }
}
/**
 * 设置生意经系统 token
 */
const setBToken = async () => {
  const params = new FormData()
  params.append('userName', '15899974725') // ***********
  params.append('password', md5('123456')) // 123456
  const res = await getBLogin(params)
  const { data } = res
  if (data.result === 0) {
    tokens.value['lyy-test'] = data.data.ticket
    localStorage.setItem('AuthorizationBar', data.data.ticket)
    localStorage.setItem('tokens', JSON.stringify(tokens.value))
  }
}
/**
 * 设置汽充桩后台系统 token
 */
const setCarCharingToken = async (obj) => {
  const params = new FormData()
  params.append('userName', obj.userName) // ***********
  params.append('password', md5(obj.password)) // 123456
  const res = await getCarCharingApi(params)
  const { data } = res
  if (data.result === 0) {
    tokens.value.carcharging_b_pc = data.data.ticket
    // tokens.value['lyy-test'] = data.data.ticket
    localStorage.setItem('authorizationBar', data.data.ticket)
    localStorage.setItem('tokens', JSON.stringify(tokens.value))
  }
}
/**
 * 设置娃娃屋 token
 */
const setWawawuToken = async () => {
  axios({
    method: 'get',
    url: '/wawawu/rest/verifycode?1698492661308',
  }).then(async () => {
    const res = await getWawawuTokenApi()
    const data = res.data
    if (data.result === 0) {
      tokens.value.wa = data.data.ticket
      sessionStorage.setItem('token', data.data.ticket)
      localStorage.setItem('tokens', JSON.stringify(tokens.value))
    }
  })
}
/**
 * 设置多金宝后台系统 token
 */
const setSmallGroupToken = async () => {
  const res = await getSmallGroupTokenApi()
  const { data } = res
  if (data.code === 200) {
    tokens.value.smallgroupb = data.data.token
    sessionStorage.setItem('token', data.data.token)
    localStorage.setItem('tokens', JSON.stringify(tokens.value))
    axios({
      method: 'get',
      url: '/gw/venue/api/v1/merchant/store/staff/resources?storeId=751&merchantId=1444907&adOrganizationId=10181',
    })
  }
}
const encryption = (password, publicKey) => {
  return new Promise((resolve) => {
    const encrypt = new JSEncrypt() // 创建加密实例
    encrypt.setPublicKey(publicKey)
    password = encrypt.encrypt(password)
    resolve(password)
  })
}
const setYiPuleToken = async () => {
  const publicKeyRes = await getPublicKeyApi()
  const publicKey = publicKeyRes.data?.body?.loginPublicKey
  const params = {
    password: await encryption('123456', publicKey),
    username: 'adminV2',
  }
  const res = await getYiPuleTokenApi(params)
  const { data } = res
  const { body, code } = data
  if (code === '0000000') {
    tokens.value.yipule_v2 = body.token
    localStorage.setItem('tokens', JSON.stringify(tokens.value))
    sessionStorage.setItem('token', body.token)
    sessionStorage.setItem('accountId', body.accountId)
    sessionStorage.setItem('roleId', body.roleId)
  }
}
// 下发事件
async function clickBtn(btn, currentRow) {
  if (btn.label === '登录') {
    login(currentRow)
  }
  if (btn.label === '详情') {
    switch (currentRow.value) {
      case 'wa': {
        setWawawuToken()
        break
      }
      case 'agent': {
        setAgentToken()
        break
      }
      case 'smallgroupb': {
        setSmallGroupToken()
        break
      }
      case 'carcharging_b_pc': {
        setCarCharingToken({
          userName: '***********',
          password: '123456',
        })
        break
      }
      case 'yipule_v2': {
        setYiPuleToken()
        sessionStorage.setItem('token', tokens.value.yipule_v2)
        break
      }
      case 'lyy-test': {
        setBToken()
        break
      }
    }
    linktoDetail(currentRow)
  }
  if (btn.label === '编辑') {
    editModalDetail(currentRow)
  }
  if (btn.label === '发布') {
    releaseApp(currentRow)
  }
  if (btn.label === '配置') {
    jsonValue.value = ''
    spinning.value = true
    const res = await getpageConfig({ authSystemId: currentRow.authSystemId })
    jsonValue.value = res.body.envStr
    currentData.value = res.body
    if (res) {
      spinning.value = false
      visible.value = true
    }
  }
}

const handleJson = (code) => {
  codeValue.value = code
}

// 登录
function login(currentRow) {
  if (currentRow.value === 'se') {
    router.push({
      path: '/selogin',
    })
  }
  if (currentRow.value !== 'se') {
    router.push({
      path: '/ramlogin',
    })
  }
}

// 详情
function linktoDetail(currentRow) {
  sessionStorage.setItem('authSystemId', currentRow.authSystemId)
  localStorage.setItem('authSystemId', currentRow.authSystemId)
  sessionStorage.setItem('authSystemCode', currentRow.value)
  sessionStorage.setItem('codeRepositoryId', currentRow.codeRepositoryId ?? '')
  router.push({
    path: '/1',
  })
}

// 发布
function releaseApp(currentRow) {
  showReleaseModal.value = true
  currentRowData.value = currentRow
}

// 删除
async function deleteAppDetail(currentRow) {
  try {
    const res = await deleteApp(currentRow.codeRepositoryId)
    if (res && res.body) {
      message.success('删除成功')
      getListData()
    }
  } catch {
    message.error('删除失败')
  }
}

// 编辑
function editModalDetail(data) {
  showeditAppModal.value = true
  editData.value = data
}

// 新增弹窗
function handleSubmit() {
  showAddAppModal.value = false
  getListData()
}

// 编辑弹窗提交
const editHandleSubmit = () => {
  showeditAppModal.value = false
  getListData()
}

// 发布弹窗
function handleSubmitRelease(txt) {
  showReleaseModal.value = false
  getListData()
  sqlModal.value = true
  sqlTxt.value = txt
}

const tokens = ref({
  carcharging_b_pc: '',
  smallgroupb: '',
  agent: '',
  overseasMerchant: '',
  merchant_mobile: '',
})
const pageId = '1053984249962536960'

/**
 * 设置1.0移动B端 token
 */
function setMerchantToken() {
  const params = {
    userName: '15899974725',
    password: md5('123456'),
  }
  axios({
    method: 'post',
    url: '/lyy/rest/group/distributor/login',
    headers: {
      'Authorization-Identifier': uuidv4(),
    },
    params,
  }).then((res) => {
    const data = res.data
    if (data.result === 0) {
      tokens.value.merchant_mobile = data.data.ticket
      localStorage.setItem('tokens', JSON.stringify(tokens.value))
      sessionStorage.setItem('authorizationBar', data.data.ticket)
      localStorage.setItem('authorizationBar', data.data.ticket)
    }
  })
}

function setOversearMerchant() {
  const region = 'Indonesia'
  const data = {
    clientType: 'h5',
    account: '***********',
    password: md5(md5('111222') + '47712'),
    verifyCode: '47712',
    region: region,
  }
  axios({
    method: 'post',
    url: '/gw/merchant/common/login',
    data,
  }).then((res) => {
    const data = res.data
    if (data.code === '0000000') {
      const { token, tenantId } = data.body
      tokens.value.overseasMerchant = token
      localStorage.setItem('tokens', JSON.stringify(tokens.value))
      sessionStorage.setItem('tenantId', tenantId)
      sessionStorage.setItem('region', region)
    }
  })
}
const getAuthCode = async (id: string) => {
  const res = await getAuthCodeFn('/editor-service/permission/menu/authCode', {
    pageConfigureId: id,
    queryButton: true,
    queryField: true,
  })
  if (res?.body) {
    try {
      const authCodeList = JSON.parse(
        sessionStorage.getItem('service-authCodeList'),
      )
      authCodeList[pageId] = res?.body
      sessionStorage.setItem(
        'service-authCodeList',
        JSON.stringify(authCodeList),
      )
    } catch {
      sessionStorage.setItem('service-authCodeList', JSON.stringify({}))
    }
  }
}
if (sessionStorage.getItem('service-authCodeList')) {
  const authCodeList = JSON.parse(
    sessionStorage.getItem('service-authCodeList'),
  )
  if (!authCodeList[pageId]) {
    getAuthCode(pageId as string)
  }
} else {
  sessionStorage.setItem('service-authCodeList', JSON.stringify({}))
  getAuthCode(pageId as string)
}

onMounted(() => {
  getListData()
  localStorage.setItem('tokens', JSON.stringify(tokens.value))
  setOversearMerchant()
  setMerchantToken()
})
</script>

<style lang="less" scoped>
.warper {
  padding: 45px;
  padding-top: 20px;
  .warper-input {
    margin-bottom: 20px;
    border: 1px solid #f0f0f0;
    padding: 15px;
  }
}
.more {
  transform: rotate(90deg);
}
.ant-dropdown-link {
  display: inline-block;
}
.operation-btns {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  column-gap: 8px;
  .ant-btn {
    padding: 0;
  }
}
</style>
