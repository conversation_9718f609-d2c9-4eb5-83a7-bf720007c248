<template>
  <a-modal title="编辑应用" @ok="handleOk" v-model:visible="visible">
    <a-form
      ref="formRef"
      :model="fromState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="企业" name="authEnterpriseId">
        <a-select
          v-model:value="fromState.authEnterpriseId"
          placeholder="请选择企业"
          :disabled="true"
        >
          <a-select-option
            v-for="(item, index) in enterpriseList"
            :key="index"
            :value="item.authEnterpriseId"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="系统名称" name="name">
        <a-input
          v-model:value="fromState.name"
          placeholder="请输入系统名称"
        ></a-input>
      </a-form-item>
      <a-form-item label="系统标识" name="value">
        <a-input
          :disabled="true"
          v-model:value="fromState.value"
          placeholder="请输入系统标识"
        ></a-input>
      </a-form-item>
      <a-form-item label="内部系统" name="isInside">
        <a-switch
          :disabled="true"
          v-model:checked="fromState.isInside"
          checkedValue="Y"
          unCheckedValue="N"
        ></a-switch>
      </a-form-item>
      <a-form-item label="SAAS系统" name="isSaas">
        <a-switch
          :disabled="true"
          v-model:checked="fromState.isSaas"
          checkedValue="Y"
          unCheckedValue="N"
        ></a-switch>
      </a-form-item>
      <a-form-item label="开启分支管理" name="enableBranchDevops">
        <a-switch
          :disabled="true"
          v-model:checked="fromState.enableBranchDevops"
          :checkedValue="true"
          :unCheckedValue="false"
        ></a-switch>
      </a-form-item>
      <a-form-item label="系统域名" name="domainName">
        <a-input
          v-model:value="fromState.domainName"
          placeholder="请输入系统域名"
        ></a-input>
      </a-form-item>
      <a-form-item label="应用负责人" name="ownerAuthAccountId">
        <a-select
          v-model:value="fromState.ownerAuthAccountId"
          show-search
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="
            ownerAuthAccountIdList.map((item) => ({
              value: item.authAccountId,
              label: item.name + ' ' + item.telephone,
            }))
          "
          placeholder="请选择应用负责人"
          @search="searchUserData"
        ></a-select>
      </a-form-item>
      <a-form-item label="git仓库地址" name="gitRepositoryUrl">
        <a-input
          :disabled="true"
          v-model:value="fromState.gitRepositoryUrl"
          placeholder="请输入git仓库地址，未填写将自动创建"
        ></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import {
  getEnterpriseList,
  editSystems,
  getApplicationManager,
} from '@/request/api/applist'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: {},
  },
})
const emit = defineEmits(['update:modelValue', 'submit'])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})

// 校验规则
const rules = {
  authEnterpriseId: [{ required: true, trigger: 'change' }],
  name: [{ required: true, trigger: 'change' }],
  value: [{ required: true, trigger: 'change' }],
  domainName: [{ required: true, trigger: 'change' }],
  ownerAuthAccountId: [{ required: true, trigger: 'change' }],
}

// 表单数据
const fromState = ref({
  authEnterpriseId: '1050802731642732544',
  name: '',
  value: '',
  isInside: 'N',
  isSaas: 'N',
  enableBranchDevops: false,
  domainName: '',
  ownerAuthAccountId: '',
  gitRepositoryUrl: '',
})
const formRef = ref(null)

// 企业列表
const enterpriseList = ref([])

// 提交表单
async function handleOk() {
  try {
    await formRef.value.validateFields()
    const { code, message } = await editSystems(fromState.value)
    if (code === '0000000') {
      formRef.value.resetFields()
      emit('submit')
    } else {
      message.error(message)
    }
  } catch {
    console.log('编辑失败')
  }
}

// 获取企业列表
async function getEnterprise() {
  const list = await getEnterpriseList()
  enterpriseList.value = list?.body
}

// 获取用户列表
const ownerAuthAccountIdList = ref([])
const getApplicationManagerData = async (data?) => {
  try {
    const res = await getApplicationManager({
      queryInside: true,
      searchContent: data ? data : '',
      current: 1,
      size: 10,
    })
    ownerAuthAccountIdList.value = res.body.records
  } catch (error) {
    console.error(error)
  }
}

// 搜索用户列表
const searchTimer = ref<any>(null) // 用于设置和清除延时定时器
const searchUserData = (data) => {
  // 如果之前有定时器，清除它
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  // 设置新的定时器，在延时后执行搜索
  searchTimer.value = setTimeout(() => {
    // 这里执行真实的搜索逻辑，例如发起API请求
    getApplicationManagerData(data)
  }, 500) // 延时300毫秒
}

getEnterprise()
getApplicationManagerData()

onMounted(() => {
  fromState.value = props.editData
  getApplicationManagerData(props.editData.ownerAuthAccountName)
})
</script>

<style scoped lang="less"></style>
