function configModal() {
  return {
    compId: 'configModal',
    compName: 'lyy-modal',
    prop: {
      width: 1200,
      title: '全局配置',
    },
    actions: [
      {
        event: 'confirm',
        action: 'broadcast',
        option: {
          targetId: 'configForm',
          event: 'validateForm',
        },
        thenActions: [
          {
            action: 'closeModal',
            option: {
              targetId: 'configModal',
            },
          },
        ],
      },
      {
        event: 'mounted',
        action: 'request',
        option: {
          url: '/gw/editor/system/environments/default',
          method: 'get',
          customOption: {
            jsonpath: 'body',
          },
          payloads: [
            {
              key: 'authSystemId',
              source: 'table-1',
              sourceKey: 'currentRow.authSystemId',
            },
          ],
          targets: [
            {
              targetId: 'configForm',
              sourceKey: 'envStr',
              targetPath: 'prop.form.envStr',
            },
          ],
        },
      },
    ],
    childrens: [
      {
        compName: 'lyy-form',
        compId: 'configForm',
        prop: {
          form: {
            authSystemEnvId: null,
          },
        },
        actions: [
          {
            event: 'validateForm',
            action: 'validate',
            option: {
              targetId: 'configForm',
            },
            thenActions: [
              // 更新
              {
                action: 'request',
                option: {
                  url: '/system/environments/',
                  method: 'put',
                  customOption: {
                    jsonPath: 'body',
                    successfulFeedback: {
                      type: 'notification',
                      status: 'success',
                      message: '成功',
                    },
                  },
                  payloads: [
                    {
                      source: 'configForm',
                    },
                    {
                      source: 'table-1',
                      sourceKey: 'currentRow.authSystemId',
                      key: 'authSystemId',
                    },
                  ],
                },
              },
            ],
          },
        ],
        childrens: [
          {
            compName: 'lyy-editor',
            compId: 'editor-1',
            prop: {
              field: 'envStr',
              style: {
                height: '500px',
              },
            },
          },
        ],
      },
    ],
  }
}

export default configModal
