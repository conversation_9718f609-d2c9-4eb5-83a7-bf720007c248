const fns = {}
const notCtrl = new Set([])
function keydownFn(e) {
  for (const keyCode of Object.keys(fns)) {
    if (
      e.keyCode == keyCode &&
      ((navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey) ||
        notCtrl.has(keyCode))
    ) {
      e.preventDefault()
      fns[keyCode]()
    }
  }
}

// 设置快捷键
export const setKeyBoard = (keyCode, fn) => {
  fns[keyCode] = fn
}
// 监听按钮事件
export const listenKeyBoard = () => {
  document.addEventListener('keydown', keydownFn)
}
// 取消快捷键
export const removeAllKeyBoard = () => {
  document.removeEventListener('keydown', keydownFn)
  for (const keyCode of Object.keys(fns)) {
    delete fns[keyCode]
  }
}
