export default {
  compName: 'lyy-select',
  compId: null,
  compNameCn: '下拉框',
  modelValue: undefined,
  prop: {
    field: '',
    label: '标签名称',
    /**
     * 组件别名用于数据选取
     */
    aliasName: '下拉框',
    placeholder: '请选择',
    mode: 'combobox',
    maxTagCount: '',
    isAddable: false,
    options: [
      {
        label: '',
        value: '',
        disabled: false,
      },
    ],
    defaultOptions: [
      {
        label: '',
        value: '',
        disabled: false,
        show: true,
      },
    ],
    // defaultValue: {
    //   source: '',
    //   sourceKey: '',
    // },
    rules: [
      // {
      //   required: false,
      //   message: '',
      //   trigger: '',
      // },
    ],
    fieldNames: {
      label: 'label',
      value: 'value',
    },
    size: 'default',
    defaultFirstOptionChecked: false,
    immediate: false,
    allowClear: true,
    readonly: false,
    showSearch: true,
    isMountToBody: true,
    optionFilterProp: '', //搜索时过滤对应的 option 属性 默认是label的属性名
    disabled: false,
    lastComp: {
      text: '',
      icon: '',
    },
    labelCol: {
      style: {},
    },
    icon: {
      iconName: '',
      popover: {
        description: [],
      },
    }, //图标
    formId: '',
    show: {
      exp: '',
    },
    forbid: {
      exp: '',
    },
  },
  actions: [],
}
