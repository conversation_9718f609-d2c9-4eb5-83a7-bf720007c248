export default {
  compName: 'lyy-time-range',
  compId: null,
  compNameCn: '时间范围',
  style: {},
  prop: {
    fields: ['startTime', 'endTime'],
    label: '标签名称', // 标签名称
    aliasName: '时间范围选择框',
    rules: [],
    disabled: false,
    allowClear: true,
    valueFormat: 'HH:mm:ss',
    colon:true,
    labelCol: {
      style: {},
    },
    icon: {
      iconName: '',
      popover: {
        description: [],
      },
    }, //图标
    show: {
      exp: '',
    },
    forbid: {
      exp: '',
    },
  },
  modelValue: undefined,
  actions: [],
}
