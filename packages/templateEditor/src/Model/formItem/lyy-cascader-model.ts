export default {
  compName: 'lyy-cascader',
  compId: null,
  compNameCn: '级联选择器',
  modelValue: [],
  style: {},
  prop: {
    field: '',
    label: '标签名称',
    /**
     * 组件别名用于数据选取
     */
    aliasName: '级联选择器',
    placeholder: '请选择',
    defaultValue: null,
    maxLevel: 3,
    multiple: false,
    showSearch: false,
    emitPath: true,
    allowClear: true,
    lazy: false,
    preFetch: {
      url: '',
      method: 'get',
      payloadFieldNames: {
        parentId: 'parentId',
        level: 'level',
      },
      customOption: {
        jsonPath: 'data',
      },
    },
    options: [],
    rules: [
      {
        required: false,
        message: '',
      },
    ],
    fieldNames: {
      label: 'label',
      value: 'value',
      children: 'children',
      level: 'level',
    },
    showCheckedStrategy: 'SHOW_CHILD',
    changeOnSelect: null,
    maxTagCount: null,
    show: {
      exp: '',
    },
    forbid: {
      exp: '',
    },
  },
  actions: [],
  childrens: [],
}
