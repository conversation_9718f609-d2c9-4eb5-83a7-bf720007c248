export default {
  compName: 'lyy-number-input',
  compId: null,
  compNameCn: '数字输入框',
  style: {},
  prop: {
    field: '',
    label: '标签名称', // 标签名称
    aliasName: '数字输入框',
    placeholder: '请输入',
    defaultValue: '',
    precision: 2, // 数值精度
    step: 1, //每次改变步数，可以为小数
    max: null, // 最大值
    min: null, // 最小值
    decimalSeparator: null, //小数点
    addonBefore: null, // 前置标签
    addonAfter: '', //带标签的 input，设置后置标签
    labelDesc: '',
    dynamicRules: [
      {
        // required: false,
        trigger: '',
        message: '',
        // payloads: [],
        exp: '',
      },
    ],
    rules: [
      {
        required: false,
        message: '',
        trigger: '',
      },
    ],
    validateTrigger: '',
    disabled: false,
    controls: null, //是否显示增减按钮
    size: 'default', // 输入框大小
    formId: '',
    labelCol: {
      style: {},
    },
    forbid: {
      exp: '',
    },
    show: {
      exp: '',
    },
  },
  modelValue: null,
  actions: [],
}
