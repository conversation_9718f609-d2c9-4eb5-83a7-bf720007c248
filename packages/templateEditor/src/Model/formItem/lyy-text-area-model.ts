export default {
  compName: 'lyy-text-area',
  compId: null,
  compNameCn: '多行输入框',
  style: {},
  prop: {
    field: '',
    label: '标签名称', // 标签名称
    aliasName: '多行输入框',
    placeholder: '请输入',
    defaultValue: '',
    maxlength: null,
    rows: 1, //行数
    rules: [
      {
        required: false,
        message: '',
        trigger: '',
      },
    ],
    validateTrigger: 'change',
    autosize: false, //自适应内容高度
    disabled: false,
    showCount: false,
    allowClear: true,
    labelDesc: '',
    icon: {
      iconName: '',
      popover: {
        description: [],
      },
    }, //图标
    formId: '',
    show: {
      exp: '',
    },
    forbid: {
      exp: '',
    },
  },
  actions: [],
}
