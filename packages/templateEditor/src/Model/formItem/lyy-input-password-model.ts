export default {
  compName: 'lyy-input-password',
  compId: null,
  compNameCn: '密码输入框',
  style: {},
  prop: {
    field: '',
    label: '密码', // 标签名称
    aliasName: '密码输入框',
    placeholder: '请输入密码',
    rules: [
      {
        required: false,
        message: '',
        trigger: '',
      },
    ],
    validateTrigger: '',
    disabled: false,
    isMd5: true, // 是否将密码MD5加密
    visibilityToggle: true, //是否显示切换按钮
    allowClear: true,
    size: 'default', // 输入框大小
    formId: '',
    show: {
      exp: '',
    },
    forbid: {
      exp: '',
    },
  },
  actions: [],
}
