export default {
  compName: 'lyy-search-pro',
  compId: null,
  compNameCn: '搜索容器',
  prop: {
    searchFormList: [
      {
        compName: 'lyy-text-input',
        compId: 'awc',
        compNameCn: '输入框',
        style: {},
        prop: {
          field: '',
          label: '标签名称', // 标签名称
          aliasName: '输入框',
          placeholder: '请输入',
          defaultValue: '',
          rules: [
            {
              required: false,
              message: '',
              trigger: '',
            },
          ],
          validateTrigger: 'change',
          disabled: false,
          allowClear: true,
          showCount: false,
          maxlength: null,
          size: 'default', // 输入框大小
          addonAfter: '', //带标签的 input，设置后置标签
          icon: {
            iconName: '',
            popover: {
              description: [],
            },
          }, //图标
          suffix: '', //输入框后缀
          formId: '',
          show: {
            exp: '',
          },
          forbid: {
            exp: '',
          },
        },
        actions: [],
      },
    ],
    enableEnterShortcut: true,
    /**
     * 组件别名用于数据选取
     */
    aliasName: '搜索容器',
    colSpan: '',
    // 是否收纳
    formStorage: true,
  },
  actions: [],
}
