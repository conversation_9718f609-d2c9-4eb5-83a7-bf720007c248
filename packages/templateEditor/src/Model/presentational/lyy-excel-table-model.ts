export default {
  compName: 'lyy-excel-table',
  compId: null,
  compNameCn: 'excel编辑表格',
  prop: {
    columns: [],
    field: '',
    aliasName: 'excel编辑表格',
    configTable: {
      border: true,
      key: '',
      showFooter: true,
      exportConfig: { tableName: '表格' },
      editConfig: {
        trigger: 'click',
        mode: 'cell',
        showStatus: true,
        showIcon: true,
      },
      logicDelete: true,
      columnConfig: { resizable: true },
      rowConfig: { isCurrent: true, isHover: true },
    },
    onfooterMethod: '',
    disabled: false,
    autoHeight: false,
    showSummary: true,
    validRules: {},
    defaultRow: 1,
    heightPercentage: 60,
    calculatedHeightDom: false,
    isShowAddFooterButton: false,
    altitudeIntercept: 195,
    summaryOption: {},
    tableCompId: '',
    rowDefaultValue: {},
    hashTableTransfer: [
      {
        origin: '',
        target: '',
      },
    ],
    preFetch: '',
    staticData: '',
  },
  actions: [],
}
