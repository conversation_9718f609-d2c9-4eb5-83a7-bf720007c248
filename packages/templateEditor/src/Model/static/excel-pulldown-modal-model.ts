export default {
  compName: 'lyy-modal',
  compId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
  compNameCn: '模态框',
  modelValue: true,
  style: {},
  prop: {
    title: '弹窗标题',
    size: 'large',
    loading: {
      tip: '加载中',
      spinning: true,
    },
    buttonList: [],
    centered: true,
    maskClosable: true,
    mask: true,
    closable: true,
    okText: '确定',
    okType: 'primary',
    cancelText: '取消',
    destroyOnClose: true,
    footer: true,
  },
  childrens: [
    {
      compName: 'lyy-row',
      compId: 'c85cc6a06-dba9-4264-ab65-93e1c9da52aa',
      compNameCn: '行布局',
      actions: [],
      childrens: [
        {
          compName: 'lyy-col',
          compId: 'ce20a653a-48be-4bf1-a53f-8b7c5895ac05',
          compNameCn: '列布局',
          actions: [],
          childrens: [
            {
              compName: 'lyy-directory-tree',
              compId: 'c131b10ce-4806-47a9-abf3-6e9025ed1d1b',
              compNameCn: '目录树',
              modelValue: {
                selectKey: '',
                expandedKeys: [],
                parentIdsOfSelectedNode: [],
              },
              prop: {
                title: '',
                placeholder: '请输入搜索内容',
                showSearch: true,
                selectable: true,
                isDefaultSelect: false,
                blockNode: true,
                draggable: false,
                dragRules: [
                  {
                    key: 'type',
                    value: 'form',
                    rejectedChildren: [
                      {
                        key: 'type',
                        value: 'group',
                      },
                      {
                        key: 'type',
                        value: 'form',
                      },
                    ],
                  },
                ],
                fieldNames: {
                  key: 'id',
                  title: 'name',
                  children: 'children',
                },
                operationButtons: [],
                prefixIcons: [
                  {
                    iconName: '',
                    iconProps: {
                      style: {
                        color: '',
                      },
                    },
                    customOption: {
                      key: '',
                      value: '',
                    },
                  },
                ],
                operationIcons: [],
                treeData: [],
                select: {},
                expandedKeys: [],
                selectKey: null,
              },
              actions: [
                {
                  event: 'mounted',
                  action: 'request',
                  option: {
                    url: 'https://mock.apifox.cn/m1/1296500-0-default/template/category/all',
                    method: 'get',
                    payloads: {
                      type: 'static',
                      static: '',
                    },
                    responseDataKey:
                      'directory-tree-c131b10ce-4806-47a9-abf3-6e9025ed1d1b',
                    customOption: {
                      loading: true,
                      nextEventsDelay: 0,
                      parse: false,
                    },
                    headerPayloads: [],
                    interceptors: {
                      requestInterceptor: '',
                      responseInterceptor: '',
                    },
                  },
                  thenActions: [
                    {
                      event: 'mounted',
                      action: 'setNewValue',
                      option: {
                        to: {
                          type: 'dynamic',
                          dynamic: {
                            nodePath:
                              'c131b10ce-4806-47a9-abf3-6e9025ed1d1b.prop.treeData',
                          },
                        },
                        from: {
                          dynamic: {
                            nodePath:
                              'directory-tree-c131b10ce-4806-47a9-abf3-6e9025ed1d1b.records',
                          },
                        },
                      },
                      thenActions: [
                        {
                          event: 'mounted',
                          action: 'setNewValue',
                          option: {
                            to: {
                              type: 'dynamic',
                              dynamic: {
                                nodePath:
                                  'c3356a5e7-282f-4e23-af7f-a4c7892eb724.prop.options',
                              },
                            },
                            from: {
                              dynamic: {
                                nodePath:
                                  'directory-tree-c131b10ce-4806-47a9-abf3-6e9025ed1d1b.records',
                              },
                            },
                          },
                          thenActions: [],
                        },
                      ],
                    },
                  ],
                },
                {
                  event: 'select',
                  action: 'broadcast',
                  option: {
                    targetId: 'c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c',
                    event: 'mounted',
                  },
                  thenActions: [],
                },
              ],
              childrens: [],
              compPropName: '',
              relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
              relativeCompName: 'lyy-modal',
            },
          ],
          prop: {
            span: 4,
            offset: 0,
            order: 0,
            pull: 0,
            push: 0,
            align: 'left',
          },
          compPropName: '',
          relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
          relativeCompName: 'lyy-modal',
          style: {},
        },
        {
          compName: 'lyy-col',
          compId: 'cab2c8013-b22a-4b3b-bf96-2a58800a2cf8',
          compNameCn: '列布局',
          actions: [],
          childrens: [
            {
              compName: 'lyy-form',
              compId: 'c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c',
              compNameCn: '表单',
              prop: {
                form: {
                  current: 1,
                  size: 10,
                  category: null,
                },
                aliasName: '表单',
                isSignChange: null,
                mainLayout: true,
                labelCol: {
                  style: {
                    width: '100px',
                  },
                },
                class: '',
              },
              modelValue: {
                current: 1,
                size: 10,
                category: '',
              },
              actions: [
                {
                  event: 'mounted',
                  action: 'setNewValue',
                  option: {
                    from: {
                      dynamic: {
                        nodePath:
                          'c131b10ce-4806-47a9-abf3-6e9025ed1d1b.modelValue.selectKey',
                      },
                    },
                    to: {
                      type: 'dynamic',
                      dynamic: {
                        nodePath:
                          'c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c.modelValue.category',
                      },
                    },
                  },
                  thenActions: [
                    {
                      event: 'mounted',
                      action: 'request',
                      option: {
                        url: 'https://mock.apifox.cn/m1/1296500-0-default/template/goods/list',
                        method: 'get',
                        payloads: {
                          type: 'dynamic',
                          dynamic: {
                            nodePath:
                              'c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c.modelValue',
                          },
                          higher: [],
                          static: '',
                        },
                        responseDataKey:
                          '表格数据-c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c',
                        customOption: {
                          loading: true,
                        },
                        headerPayloads: [],
                        interceptors: {},
                      },
                      thenActions: [
                        {
                          action: 'setNewValue',
                          event: 'mounted',
                          option: {
                            from: {
                              dynamic: {
                                nodePath:
                                  '表格数据-c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c.records',
                              },
                            },
                            to: {
                              type: 'dynamic',
                              dynamic: {
                                nodePath:
                                  'ce7450cf5-34ae-47a3-a776-8d9af513d7fb.modelValue.datasource',
                              },
                            },
                          },
                          thenActions: [
                            {
                              action: 'setNewValue',
                              event: 'mounted',
                              option: {
                                from: {
                                  dynamic: {
                                    nodePath:
                                      '表格数据-c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c.total',
                                  },
                                },
                                to: {
                                  type: 'dynamic',
                                  dynamic: {
                                    nodePath:
                                      'c3c9c0940-5ede-45b3-8119-f87ebe44bf3a.prop.total',
                                  },
                                },
                              },
                              thenActions: [],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
              childrens: [
                {
                  compName: 'lyy-search-pro',
                  compId: 'ce63cac17-8932-443c-a55e-9d15d01b6a91',
                  compNameCn: '搜索容器',
                  prop: {
                    searchFormList: [],
                    aliasName: '搜索容器',
                    formStorage: true,
                  },
                  actions: [],
                  compPropName: '',
                  relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
                  relativeCompName: 'lyy-modal',
                },
                {
                  compName: 'lyy-card',
                  compId: 'c615b0620-dd5e-4ca4-8fd9-2ea4582f023d',
                  compNameCn: '卡片',
                  prop: {
                    showHeader: false,
                    title: '卡片容器',
                    aliasName: '卡片容器',
                    extraList: [],
                    mainLayout: true,
                    bordered: false,
                  },
                  actions: [],
                  childrens: [
                    {
                      compName: 'lyy-toolbar',
                      compId: 'cc28e9042-6a48-43ea-a68b-14a123484f63',
                      compNameCn: '工具栏',
                      style: {},
                      prop: {
                        title: '',
                        aliasName: '工具栏cc28e',
                        show: {
                          exp: '',
                        },
                      },
                      childrens: [
                        {
                          compName: 'lyy-button',
                          compId: 'c0d780a1f-76c6-43f4-a31b-fa16af9fb774',
                          compNameCn: '按钮',
                          actions: [],
                          childrens: [],
                          style: {
                            'margin-right': '12px',
                          },
                          prop: {
                            text: '新增',
                            type: 'primary',
                            size: 'middle',
                            aliasName: '按钮',
                            authCode: '',
                            ghost: false,
                            danger: false,
                            disabled: false,
                            icon: '',
                            show: {
                              exp: '',
                            },
                            forbid: {
                              exp: '',
                            },
                          },
                        },
                        {
                          compName: 'lyy-button',
                          compId: 'c95f94548-f61b-4deb-97cc-018136638172',
                          compNameCn: '按钮',
                          actions: [],
                          childrens: [],
                          style: {},
                          prop: {
                            text: '刷新',
                            type: 'default',
                            size: 'middle',
                            aliasName: '按钮',
                            authCode: '',
                            ghost: false,
                            danger: false,
                            disabled: false,
                            icon: '',
                            show: {
                              exp: '',
                            },
                            forbid: {
                              exp: '',
                            },
                          },
                        },
                      ],
                    },
                    {
                      compName: 'lyy-table',
                      compId: 'ce7450cf5-34ae-47a3-a776-8d9af513d7fb',
                      field: 'name',
                      compNameCn: '表格',
                      prop: {
                        columns: [],
                        datasource: [],
                        rowKey: 'id',
                        aliasName: '表格',
                        size: 'small',
                        sticky: false,
                        scroll: {
                          x: '100%',
                        },
                        showIndex: true,
                        showSelection: true,
                        rowSelection: {
                          getCheckboxProps: '',
                        },
                        disabledRow: {
                          exp: '',
                          background: '',
                        },
                        backgroundRows: [
                          {
                            exp: '',
                            background: '',
                          },
                        ],
                        summary: {
                          placement: '',
                          summarys: [
                            {
                              label: '',
                              field: '',
                              value: '',
                              pipes: [
                                {
                                  pipe: '',
                                  option: {},
                                },
                              ],
                            },
                          ],
                          payloads: [
                            {
                              key: '',
                              source: '',
                              sourceKey: '',
                              decompose: false,
                              value: '',
                            },
                          ],
                          sumFields: '',
                          averageFields: '',
                        },
                        rowExpandIconWidth: '',
                        mergeField: [],
                        mergeUniqueField: [],
                        colspanConfig: [
                          {
                            exp: '',
                            fields: [],
                          },
                        ],
                        fieldNames: {},
                        expandFetch: {
                          url: '',
                          method: '',
                          payloads: [
                            {
                              key: '',
                              source: '',
                              sourceKey: '',
                              decompose: false,
                              value: '',
                            },
                          ],
                          headerPayloads: [
                            {
                              key: '',
                              source: '',
                              sourceKey: '',
                              decompose: false,
                              value: '',
                            },
                          ],
                        },
                        autoHeight: true,
                        dynamicDataIndexList: [],
                        showDataIndexList: [],
                      },
                      modelValue: {
                        selectedRows: [],
                        datasource: [],
                        currentRow: {},
                        selectedRowKeys: [],
                      },
                      actions: [],
                      compPropName: '',
                      childrens: [],
                      relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
                      relativeCompName: 'lyy-modal',
                    },
                    {
                      compName: 'lyy-pagination',
                      compId: 'c3c9c0940-5ede-45b3-8119-f87ebe44bf3a',
                      compNameCn: '分页',
                      prop: {
                        fields: ['current', 'size'],
                        aliasName: '分页',
                        defaultCurrent: 1,
                        defaultPageSize: 10,
                        total: '64',
                        size: 'default',
                        hideOnSinglePage: null,
                        showQuickJumper: null,
                        showSizeChanger: null,
                        simple: null,
                        show: {
                          exp: '',
                        },
                        formId: '',
                      },
                      actions: [
                        {
                          event: 'update',
                          action: 'broadcast',
                          option: {
                            targetId: 'c3cbb9dbd-13f0-4371-ab8b-5d177e03d93c',
                            event: 'mounted',
                          },
                        },
                      ],
                      modelValue: [1, 10],
                      compPropName: '',
                      childrens: [],
                      relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
                      relativeCompName: 'lyy-modal',
                      style: {},
                    },
                  ],
                  compPropName: '（卡片容器）',
                  relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
                  relativeCompName: 'lyy-modal',
                },
              ],
              compPropName: '',
              relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
              relativeCompName: 'lyy-modal',
              style: {},
            },
          ],
          prop: {
            span: 20,
            offset: 0,
            order: 0,
            pull: 0,
            push: 0,
            align: 'left',
          },
          compPropName: '',
          relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
          relativeCompName: 'lyy-modal',
          style: {},
        },
      ],
      prop: {
        align: 'top',
        justify: 'start',
        gutter: 8,
        wrap: false,
        show: {
          exp: '',
        },
      },
      compPropName: '',
      relativeCompId: 'c410e0db3-06f3-4e6b-ae90-9fda52b80e45',
      relativeCompName: 'lyy-modal',
      style: {},
    },
  ],
  actions: [],
  compPropName: '（弹窗标题）',
}
