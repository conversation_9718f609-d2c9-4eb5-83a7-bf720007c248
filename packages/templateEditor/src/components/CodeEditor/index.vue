<script lang="ts" setup>
import { editor } from 'monaco-editor'
import {
  defineComponent,
  onMounted,
  ref,
  defineExpose,
  nextTick,
  toRaw,
} from 'vue'
import { throttle, isUndefined } from 'lodash'
import * as monaco from 'monaco-editor'
import { setLodashsnippet, setApiSnippet, setJsObject } from './jssnippet'
import { allResource, apiFns, mergeApiFn } from '@leyaoyao/libs/store/jsobject'
import { ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons-vue'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import { actionHoc } from '@leyaoyao/libs'
defineComponent({
  name: 'js-object-editor',
})

const props = defineProps<{
  code: string
  onDidBlurEditorWidget: (newCode: string) => void
  onDidChangeModelContent: (newCode: string) => void
  style: StyleSheet
  lineNumbers: boolean
  showFullScreen: boolean
}>()

const jsobjecteditor = ref<HTMLElement>()
const autoStyle = ref<any>({})
const isLarge = ref(false)
let editorInst: ReturnType<typeof editor.create> | undefined
const createEditor = () => {
  if (!jsobjecteditor.value) return
  // 生成编辑器
  editorInst = editor.create(jsobjecteditor.value, {
    value: props.code,
    language: 'javascript',
    scrollBeyondLastLine: true,
    lineNumbers: isUndefined(props.lineNumbers) ? true : props.lineNumbers,
  })

  // 失焦时
  editorInst.onDidBlurEditorWidget(() => {
    editorInst?.layout()
    props.onDidBlurEditorWidget &&
      editorInst &&
      props.onDidBlurEditorWidget(editorInst.getValue())
  })
  // 代码改变时
  editorInst.onDidChangeModelContent(
    throttle(() => {
      if (editorInst && props.onDidChangeModelContent) {
        props.onDidChangeModelContent(editorInst.getValue())
        // const lineCount = editorInst.getModel()?.getLineCount()
        // const lineHeight = editorInst.getOption(
        //   monaco.editor.EditorOption.lineHeight,
        // )
        // autoStyle.value.height = lineCount * lineHeight + 'px'
        nextTick(() => {
          editorInst?.layout()
        })
      }
    }, 200),
  )
}

const maxEditor = () => {
  isLarge.value = !isLarge.value
  jsobjecteditor.value?.classList.add('editor-fullscreen')
  editorInst?.layout({
    height: document.body.clientHeight,
    width: document.body.clientWidth,
  })
}

const minEditor = () => {
  isLarge.value = !isLarge.value
  jsobjecteditor.value?.classList.remove('editor-fullscreen')
  editorInst?.layout({
    height: document.querySelector('#editorBox')?.clientHeight,
    width: document.querySelector('#editorBox')?.clientWidth,
  })
}

const setEditorData = (newValue) => {
  editorInst?.setValue(newValue)
}

defineExpose({
  setEditorData,
})

onMounted(() => {
  createEditor()
  setLodashsnippet()
  setApiSnippet()
  setJsObject()
  const data = mergeApiFn(proxyData.getProxyData())
  allResource.globalData = data
  allResource.globalData.route = {}
  const keysObjs = {
    globalData: allResource.globalData,
    data: allResource.globalData,
    LYY: actionHoc,
    ...allResource.globalData,
    ...apiFns(),
  }
  for (const key of Object.keys(keysObjs)) {
    monaco.languages.typescript.javascriptDefaults.addExtraLib(
      `const ${key} = ${JSON.stringify(keysObjs[key], (key, value) => {
        if (typeof value === 'function') {
          return value.toString()
        }
        return value
      })}
`,
      `@types/${key}/index.d.ts`,
    )
  }
})
</script>

<template>
  <div id="editorBox" ref="jsobjecteditor" :style="{ ...style, ...autoStyle }">
    <div v-if="showFullScreen" class="position">
      <shrink-outlined v-if="isLarge" @click.stop="minEditor"></shrink-outlined>
      <arrows-alt-outlined v-else @click.stop="maxEditor"></arrows-alt-outlined>
    </div>
  </div>
</template>
<style lang="less" scoped>
.position {
  font-size: 16px;
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 1000;
  cursor: pointer;
}
.editor-fullscreen {
  position: fixed !important;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100% !important;
  z-index: 9999;
}
</style>
