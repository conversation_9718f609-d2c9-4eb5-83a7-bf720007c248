// import * as monaco from 'monaco-editor'
// import LODASH_index from '!raw-loader!@types/lodash/index.d.ts'
// import LODASH_common from '!raw-loader!@types/lodash/common/common.d.ts'
// import LODASH_array from '!raw-loader!@types/lodash/common/array.d.ts'
// import LODASH_collection from '!raw-loader!@types/lodash/common/collection.d.ts'
// import LODASH_date from '!raw-loader!@types/lodash/common/date.d.ts'
// import LODASH_function from '!raw-loader!@types/lodash/common/function.d.ts'
// import LODASH_lang from '!raw-loader!@types/lodash/common/lang.d.ts'
// import LODASH_math from '!raw-loader!@types/lodash/common/math.d.ts'
// import LODASH_number from '!raw-loader!@types/lodash/common/number.d.ts'
// import LODASH_object from '!raw-loader!@types/lodash/common/object.d.ts'
// import LODASH_seq from '!raw-loader!@types/lodash/common/seq.d.ts'
// import LODASH_string from '!raw-loader!@types/lodash/common/string.d.ts'
// import LODASH_util from '!raw-loader!@types/lodash/common/util.d.ts'

export const setLodashsnippet = () => {
  // // lodash模块
  // monaco.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_index,
  //   '@types/lodash/index.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_common,
  //   '@types/lodash/common/common.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_array,
  //   '@types/lodash/common/array.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_collection,
  //   '@types/lodash/common/collection.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_date,
  //   '@types/lodash/common/date.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_function,
  //   '@types/lodash/common/function.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_lang,
  //   '@types/lodash/common/lang.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_math,
  //   '@types/lodash/common/math.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_number,
  //   '@types/lodash/common/number.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_object,
  //   '@types/lodash/common/object.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_seq,
  //   '@types/lodash/common/seq.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_string,
  //   '@types/lodash/common/string.d.ts',
  // )
  // monaco?.languages.typescript.javascriptDefaults.addExtraLib(
  //   LODASH_util,
  //   '@types/lodash/common/util.d.ts',
  // )
}
