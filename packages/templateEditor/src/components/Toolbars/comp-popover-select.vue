<template>
  <a-popover
    v-model:open="visible"
    title="选择组件"
    trigger="click"
    placement="bottom"
  >
    <template #content>
      <div class="complist-search-input">
        <a-input
          v-model:value="filterText"
          placeholder="输入关键字查询组件"
          allow-clear
        ></a-input>
      </div>
      <div class="select-comp-area">
        <a-collapse
          v-model:activeKey="collapseActiveKey"
          ghost
          expandIconPosition="right"
          :accordion="true"
        >
          <a-collapse-panel
            v-for="(panel, i) in filterCompList"
            :key="i"
            :header="panel.name"
          >
            <a-button
              class="select-comp-button"
              :class="
                selectedComp.compName === item.compName ? 'ant-btn-primary' : ''
              "
              type="default"
              v-for="item in panel.list"
              :key="item.compName"
              @click="
                () => {
                  selectedComp = _.cloneDeep(item)
                }
              "
            >
              {{ item.compNameCn }}
            </a-button>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <div class="select-comp-footer">
        <a-button type="primary" @click="handleAddComp">确定添加</a-button>
      </div>
    </template>
    <slot></slot>
  </a-popover>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { uuidv4 } from '@leyaoyao/libs'
import _ from 'lodash'
import LyyCanvas from '@/context/canvas'
import { findParent } from '@leyaoyao/libs/utils/tree-find'

const props = defineProps<{
  insertDirection: number // 插入方向（0：前面，1：后面）
}>()

const visible = ref<boolean>(false)

const componentList = ref(null)

const selectedComp = ref({}) // 选中的组件

const filterText = ref('')

const collapseActiveKey = ref(0)

watch(visible, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      selectedComp.value = {}
    })
  }
})
// 当前组件
const currentComp = computed(() => {
  return LyyCanvas.getInstance().getCurrentComp()
})

const handleAddComp = () => {
  const isSelected = Object.keys(selectedComp.value).length > 0
  if (isSelected) {
    selectedComp.value = { ...selectedComp.value, compId: uuidv4() }
    // emit('hamdle-select', selectedComp.value)
    handleInsert()
    hide()
  } else {
    return
  }
}

const getCompList = () => {
  componentList.value = window.lyyComponentList
}

defineExpose({
  getCompList: getCompList,
})

const filterCompList = computed(() => {
  if (!filterText.value) {
    return componentList.value
  }
  let newarr = componentList.value?.map((config) => {
    return {
      name: config.name,
      list: config.list.filter((i) => i.compNameCn.includes(filterText.value)),
    }
  })
  return newarr.filter((arr) => arr.list.length > 0)
})

// 处理插入
function handleInsert() {
  const canvasInstance = LyyCanvas.getInstance()
  const pageConfig = canvasInstance.getPageConfig()
  // const insertComp = selectedComp.value
  let insertComp = null
  if (selectedComp.value?.pageConfigureTemplateId) {
    const newCompIdMap = generateNewCompId(selectedComp.value)
    let str = JSON.stringify(selectedComp.value)
    for (const key in newCompIdMap) {
      str = str.replace(new RegExp(key, 'g'), newCompIdMap[key])
    }
    insertComp = JSON.parse(str)
  } else {
    insertComp = selectedComp.value
  }
  const insertCompId = selectedComp.value.compId

  function cb(configList, compId, index) {
    // 插入位置
    const insertPosition = index + props.insertDirection
    if (insertPosition < 0) {
      configList.unshift(insertComp)
      // handleCloseDrawer(insertCompId)
      return
    }
    if (insertPosition >= configList.length) {
      configList.push(insertComp)
      // handleCloseDrawer(insertCompId)
      return
    }
    configList.splice(insertPosition, 0, insertComp)
    // handleCloseDrawer(insertCompId)
  }
  loop(pageConfig, currentComp.value.compId, cb)
}

// 循环页面配置，根据 compId 寻找目标，处理对应 callback
function loop(pageConfig, compId, callback) {
  pageConfig.some((compConfig, index) => {
    if (compConfig.compId === compId) {
      callback(pageConfig, compId, index)
      return true
    }
    if (
      ['lyy-edit-table', 'lyy-table', 'lyy-excel-table'].includes(
        compConfig.compName,
      )
    ) {
      // 兼容表格操作列插入组件功能
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.columns) {
        const columns = compConfig?.prop?.columns
        let operationConfig
        // 是否有“操作”列
        const hasOperation = columns?.some((column) => {
          if (column?.type === 'operation') {
            operationConfig = column
            return true
          }
          return false
        })
        if (hasOperation && operationConfig?.childrens) {
          loop(operationConfig?.childrens, compId, callback)
        }
      }
    }
    if (
      [
        'lyy-card',
        'lyy-card-list',
        'lyy-select-merchant',
        'lyy-footer',
        'lyy-erp-action-bar',
        'lyy-page-header',
        'lyy-form-card',
      ].includes(compConfig.compName) &&
      compConfig.prop.extraList.length > 0
    ) {
      loop(compConfig.prop.extraList, compId, callback)
    }
    if (compConfig.compName === 'lyy-search-pro') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.searchFormList.length > 0) {
        loop(compConfig.prop.searchFormList, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-tabs') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.tabs.length > 0) {
        loop(compConfig.prop.tabs, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-erp-action-bar') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.leftExtra.length > 0) {
        loop(compConfig.prop.leftExtra, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-modal') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.buttonList.length > 0) {
        loop(compConfig.prop.buttonList, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-directory-tree') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.operationButtons.length > 0) {
        loop(compConfig.prop.operationButtons, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-dropdown') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.dropdownList.length > 0) {
        loop(compConfig.prop.dropdownList, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-pull-down-selector-input') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.pulldownChildren.length > 0) {
        loop(compConfig.prop.pulldownChildren, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-form-layout') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.cardList.length > 0) {
        loop(compConfig.prop.cardList, compId, callback)
      }
    }
    if (compConfig.compName === 'lyy-form-card') {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.childList.length > 0) {
        loop(compConfig.prop.childList, compId, callback)
      }
    }
    if (
      [
        'lyy-grid',
        'lyy-input-group',
        'lyy-erp-action-bar',
        'lyy-card-list',
        'lyy-erp-fold-box',
      ].includes(compConfig.compName)
    ) {
      // eslint-disable-next-line unicorn/no-lonely-if
      if (compConfig?.prop?.children.length > 0) {
        loop(compConfig.prop.children, compId, callback)
      }
    }
    if (compConfig?.childrens?.length) {
      loop(compConfig.childrens, compId, callback)
    }
  })
}

// 重新生成 compId
const generateNewCompId = (element) => {
  const compIds = []
  const compIdMap = {}
  iterateObject(element, compIds, compIdMap)
  return compIdMap
}
const iterateObject = (obj, compIds, compIdMap) => {
  for (const key in obj) {
    if (key === 'compId' && !compIds.includes(obj[key])) {
      compIds.push(obj[key])
      compIdMap[obj[key]] = 'c' + uuidv4()
    }
    if (_.isObject(obj[key])) {
      iterateObject(obj[key], compIds, compIdMap)
    }
    if (_.isArray(obj[key])) {
      iterateArray(obj[key], compIds, compIdMap)
    }
  }
}
const iterateArray = (array, compIds, compIdMap) => {
  if (array.length === 0) return
  for (const item of array) {
    if (_.isObject(item)) {
      iterateObject(item, compIds, compIdMap)
    }
    if (_.isArray(item)) {
      iterateArray(item, compIds, compIdMap)
    }
  }
}

const hide = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.select-comp-area {
  width: 560px;
  // height: 400px;
}

.select-comp-footer {
  display: flex;
  flex-direction: row-reverse;
}

.select-comp-button {
  width: 104px;
  height: 30px;
  margin: 0 4px 8px;
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
