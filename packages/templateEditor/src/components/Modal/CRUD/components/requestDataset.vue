<template>
  <div class="pt-8">
    <a-form-item
      label="选择数据集"
      :labelCol="{ style: { width: '100px' } }"
    >
      <a-select
        v-model:value="dataset"
        :fieldNames="{ label: 'name', value: 'name' }"
        :options="dataSetList"
      >
      <a-select-option
        v-for="option in dataSetList"
        :key="option.pageComponentId"
        :value="option.name"
      >
        {{ option.name }}
      </a-select-option>

    </a-select>
    </a-form-item>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getJsObject } from '@/request/api/editorRequest'
const props = defineProps<{
  value: string
}>()

const emit = defineEmits(['update:value'])

const dataset = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('update:value', val)
  }
}) 

const dataSetList = ref([])

onMounted(async () => {
  const { id } = useRoute().params
  const params = {
    codeRepositoryId: sessionStorage.getItem('codeRepositoryId'),
    pageConfigureInfoId: id,
    type: 2,
    codeBranchName: sessionStorage.getItem('codeBranchName'),
    isRemote: sessionStorage.getItem('isRemote'),
  }
  const res = await getJsObject(params)
  if (res.code == '0000000') {
    dataSetList.value = [...res.body]
  }
})
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 8px;
}
:deep(.ant-form-item-control-input-content) {
  display: flex;
  gap: 8px;
}
.pt-8 {
  padding-top: 8px;
}
.mb-8 {
  margin-bottom: 8px;
}
</style>
