<template>
  <div class="add-modal-form">
    <base-com
      v-model:formActionConfig="requestOption"
      ref="baseComRef"
      :isShowCol="true"
      :isShowForm="isShowFormFlag"
    ></base-com>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineComponent, onMounted, reactive, ref } from 'vue'
import baseCom from '../baseCom.vue'
import { clickRequest } from '@/components/Modal/modelUtils/actionModel'
import _ from 'lodash'
import { setEditModal } from '@/components/Modal/CRUD/utils'
const baseComRef = ref(null)
const requestOption = reactive(_.cloneDeep(clickRequest))
const isShowFormFlag = computed(() => {
  return props.isShowForm ?? true
})
const getEditModal = (formId, tableId) => {
  const gridList = baseComRef.value.getGridForm()
  return gridList
    ? setEditModal([gridList], formId, tableId, requestOption)
    : null
}
const getOriginData = () => {
  return baseComRef.value?.getOriginData()
}
defineComponent({
  name: 'edit-modal-form',
})
const props = withDefaults(
  defineProps<{
    data: any
    isShowForm: boolean
  }>(),
  {
    isShowForm: true,
  },
)
onMounted(() => {
  if (props.data && props.data.colNumber) {
    Object.assign(requestOption, _.cloneDeep(props.data.formActionConfig))
    baseComRef.value?.setData(_.cloneDeep(props.data))
  }
})
defineExpose({
  getData: (formId, tableId) => {
    return getEditModal(formId, tableId)
  },
  setData(data) {
    baseComRef.value?.setData(data)
  },
  getOriginData,
})
</script>

<style scoped></style>
