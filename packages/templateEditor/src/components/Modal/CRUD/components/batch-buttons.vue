<script setup lang="ts">
import { defineComponent, ref, computed } from 'vue'
import Draggable from 'vuedraggable'
import AttrInput from '@/Pages/Edit/Right/AttrConfig/customs/components/input.vue'
import AttrIcon from '@/Pages/Edit/Right/AttrConfig/customs/components/icon.vue'
import {
  PlusOutlined,
  CloseOutlined,
} from '@ant-design/icons-vue'
defineComponent({
  name: 'object-list',
})
const props = defineProps<{
  value: any // 当前值
}>()

const emits = defineEmits(['update:value'])
const list = computed({
  get() {
    return props.value
  },
  set(val) {
    emits('update:value', val)
  },
})
// 删除某一项
const removeFormItem = (index) => {
  const _data = [...props.value]
  _data.splice(index, 1)
  emits('update:value', [..._data])
}
// 新增一项
const add = () => {
  list.value.push({ key: `key${list.value.length + 1}`, text: '按钮', icon: '', authCode: '', show: '' })
}
</script>

<template>
  <div class="config-list">
    <div class="block-title">批量操作按钮组：</div>
    <Draggable :list="list" handle=".handle">
      <template #item="{ element, index }">
        <div class="config-list-cell">
          <div class="config-list-cell-tool">
            <unordered-list-outlined class="handle cursor" />
          </div>
          <div class="config-cell-content">
            <AttrInput v-model:value="element.text" placeholder="请输入按钮名称" width="100%" />
            <AttrInput v-model:value="element.key" placeholder="请输入按钮标识" width="100%" />
            <AttrIcon v-model:value="element.icon" placeholder="请选择按钮图标" width="100%" />
          </div>
          <CloseOutlined
              class="delete-icon"
              @click="removeFormItem(index)"
            />
        </div>
      </template>
    </Draggable>
    <a-button class="add-btn" type="link" @click="add" size="small">
      <template #icon><PlusOutlined/></template>
       新增按钮</a-button>
  </div>
</template>

<style lang="less" scoped>
.config-list {
  position: relative;
  background-color: #f7f7f9;
  padding: 8px;
  border-radius: 2px;
  margin: 8px 0;
  .block-title {
    margin-bottom: 8px;
  }

  .config-list-cell {
    display: flex;
    gap: 8px;
    position: relative;
    margin-bottom: 10px;

    &:not(:last-child) {
    //   padding-bottom: 10px;
    }
    .horizontal-item {
      flex: 1;
      gap: 0;
    }

    .delete-icon {
        cursor: pointer;
      }

    .config-list-cell-tool {
      display: flex;
      .handle {
        font-size: 14px;
        cursor: pointer;
      }
    }
    .config-cell-content {
      flex: 1;
      display: flex;
      gap: 8px;
    }
  }
}
</style>
