<template>
  <a-modal
    v-if="visible"
    v-model:open="visible"
    :maskClosable="false"
    cancelText="取消"
    okText="确认"
    title="增删改查快速开始-CRUD"
    class="crud-modal"
    width="850px"
    :forceRender="true"
    @ok="confirm"
    @cancel="cancel"
  >
    <!--功能启用-->
    <section>
      <div class="funcTit padding4px">启用功能</div>
      <div class="padding4px">
        <a-checkbox-group v-model:value="funcVal" :options="funcOptions" />
      </div>
    </section>
    <!-- tab菜单栏 -->
    <div class="tabs-box">
      <a-tabs
        v-model:activeKey="activeKey"
        tab-position="left"
        animated
        type="card"
        class="editor-left-tabs"
      >
        <a-tab-pane :key="0" tab="基本功能">
          <section>
            <RequestAction
              v-model:modelValue="formActionConfig"
            ></RequestAction>
          </section>
          <!--搜索表单-->
          <searchPro
            ref="SearchProRef"
            v-if="isSelectSearch"
            :isShowCol="false"
          ></searchPro>
          <!---表格-->
          <tableSet
            :types="types"
            ref="TableRef"
            :operatebtns="operatebtns"
          ></tableSet>
        </a-tab-pane>
        <a-tab-pane
          :key="1"
          tab="操作栏编辑"
          v-if="funcVal.includes('操作栏-编辑')"
        >
          <editModalForm
            ref="editModalRef"
            :data="originData.editData"
          ></editModalForm>
        </a-tab-pane>
        <a-tab-pane
          :key="2"
          tab="操作栏详情"
          v-if="funcVal.includes('操作栏-详情')"
        >
          <detailModalForm
            ref="detailModalRef"
            :data="originData.detailData"
          ></detailModalForm>
        </a-tab-pane>
        <a-tab-pane :key="3" tab="新增" v-if="funcVal.includes('新增')">
          <addModalForm
            ref="addModalRef"
            :data="originData.addData"
          ></addModalForm>
        </a-tab-pane>
        <a-tab-pane :key="4" tab="导出" v-if="funcVal.includes('导出')">
          <addModalForm
            ref="exportRef"
            :data="originData.exportData"
            :isShowForm="false"
          ></addModalForm>
        </a-tab-pane>
        <a-tab-pane :key="5" tab="导入" v-if="funcVal.includes('导入')">
          <addModalForm
            ref="importRef"
            :data="originData.importData"
            :isShowForm="false"
          ></addModalForm>
        </a-tab-pane>
      </a-tabs>
    </div>
    <!---接口配置-->
    <!-- <requestModal
      v-model="urlSetModal"
      @getActionData="getActionData"
      :formActionConfig="formActionConfig"
    ></requestModal> -->
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, defineComponent, reactive, ref } from 'vue'
import _ from 'lodash'
import lyySearchProModel from '@/Model/layoutItem/lyy-search-pro-model'
import lyyFormModel from '@/Model/formItem/lyy-form-model'
import requestModal from '../RequestModal/index.vue'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import actionCrudDefaultModel from '@/Model/static/action-crud-default-model'
import tableSet from './components/table.vue'
import searchPro from './components/searchPro.vue'
import baseCom from './components/baseCom.vue'
import addModalForm from './components/addModalForm/index.vue'
import editModalForm from './components/editModalForm/index.vue'
import detailModalForm from './components/detailModalForm/index.vue'
import { toolBar } from './model/toolBarModal'
import { SettingOutlined } from '@ant-design/icons-vue'
import {
  lyyPaginationHandle,
  setPaginationAction,
} from '@/components/Modal/CRUD/model/paginationModel'
import lyyCardModel from '@/Model/layoutItem/lyy-card-model'
import {
  genBtn,
  genPopConfirmBtn,
} from '@/components/Modal/CRUD/model/genButton'
import { genModal } from '@/components/Modal/CRUD/model/createModel'
import LyyCanvas from '@/context/canvas'
import {
  actionOpenModal,
  setAction,
  delAction,
  setEditModal,
  restActionConfig,
  setAddModal,
  getDefaultGridList,
  genForm,
  searchFormListHandle,
  mergeFormList,
} from './utils'
import { clickRequest } from '@/components/Modal/modelUtils/actionModel'
import RequestAction from './components/request-action.vue'
const emit = defineEmits(['update:modelValue', 'ok', 'cancel'])
const currentComp = computed(() => {
  return LyyCanvas.getInstance().getCurrentComp()
})

const TableRef = ref(null)
const requestOption = reactive(_.cloneDeep(clickRequest))
const SearchProRef = ref(null)
const addModalRef = ref(null)
const detailModalRef = ref(null)
const exportRef = ref(null)
const importRef = ref(null)
const editModalRef = ref(null)
const funcVal = ref(['搜索容器', '分页'])
const urlSetModal = ref(false)
const toolAndOpBtns = ref<any>([]) // 二次编辑的时候工具栏和表格操作栏的按钮集合
const url = ref('')
const formReqMode = ref(1)
const funcOptions = [
  '搜索容器',
  '分页',
  '操作栏-编辑',
  '操作栏-详情',
  '操作栏-删除',
  '新增',
  '批量选择',
  '批量删除',
  '导入',
  '导出',
]
const activeKey = ref(0)
let formActionConfig = reactive(_.cloneDeep(actionCrudDefaultModel))
const visible = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})
defineComponent({
  name: 'column-modal',
})
const isSelectSearch = computed(() => {
  return funcVal.value.includes('搜索容器')
})
const isSelectPl = computed(() => {
  return (
    funcVal.value.includes('批量删除') || funcVal.value.includes('批量选择')
  )
})
const props = defineProps<{
  modelValue: boolean
  types?: any
  templateData?: object
}>()

const children: object[] = []
const cardChild: object[] = []
const toolsbtns: object[] = []
const modals: object[] = []
const searchFormListArr: object[] = []
const operatebtns: object[] = reactive([])
const urlSetModalFn = () => {
  urlSetModal.value = true
}
const findOperateBtn = (text) => {
  return operatebtns.find((val) => val._aliasName === text)
}
const findToolBtn = (text) => {
  return toolsbtns.find((val) => val._aliasName === text)
}
const funcChange = (formId) => {
  children.length =
    cardChild.length =
    operatebtns.length =
    toolsbtns.length =
    modals.length =
    searchFormListArr.length =
      0
  if (funcVal.value.length > 0)
    for (const item of funcVal.value) {
      switch (item) {
        case '搜索容器': {
          const lyySearch = addAliasNameAnduuid(_.cloneDeep(lyySearchProModel))
          const searchFormList = SearchProRef.value.searchProHandle()
          lyySearch.prop.searchFormList.length = 0
          lyySearch.prop.searchFormList.push(...searchFormList)
          searchFormListArr.push(...searchFormList)
          children.push(lyySearch)
          break
        }
        case '操作栏-删除': {
          const delBtn = genPopConfirmBtn({
            text: '删除',
            title: '您确认要删除吗？',
          })
          delBtn._aliasName = '操作栏-编辑'
          const oldDelBtn = toolAndOpBtns.value.find(
            (v) => v._aliasName === '操作栏-删除',
          )
          if (oldDelBtn) {
            delBtn.prop.text = oldDelBtn.prop.text
          }
          delBtn.actions = delAction(formId)
          operatebtns.push(delBtn)
          break
        }
        case '操作栏-编辑': {
          const btnEdit = genBtn({ text: '编辑', type: 'link' })
          btnEdit._aliasName = '操作栏-编辑'
          const oldBtnEdit = toolAndOpBtns.value.find(
            (v) => v._aliasName === '操作栏-编辑',
          )
          if (oldBtnEdit) {
            btnEdit.prop.text = oldBtnEdit.prop.text
          }
          operatebtns.push(btnEdit)
          break
        }
        case '操作栏-详情': {
          const btnDetail = genBtn({ text: '详情', type: 'link' })
          btnDetail._aliasName = '操作栏-详情'
          const oldBtnDetail = toolAndOpBtns.value.find(
            (v) => v._aliasName === '操作栏-详情',
          )
          if (oldBtnDetail) {
            btnDetail.prop.text = oldBtnDetail.prop.text
          }
          operatebtns.push(btnDetail)
          break
        }
      }
    }
}

const setInit = () => {
  children.length = cardChild.length = operatebtns.length = toolsbtns.length = 0
  restActionConfig(formActionConfig)
  funcVal.value.length = 0
  activeKey.value = 0
  funcVal.value.push('搜索容器', '分页')
}
const toolBtnExportImport = (text) => {
  const exportBtn = genBtn({ text })
  let actionClick = _.cloneDeep(clickRequest)
  if (['导入'].includes(text)) {
    const origin = importRef.value?.getOriginData() || {}
    actionClick = origin.formActionConfig
    originData.importData = origin
  }
  if (['导出'].includes('导出')) {
    const origin = exportRef.value?.getOriginData() || {}
    actionClick = origin.formActionConfig
    originData.exportData = origin
  }
  exportBtn._aliasName = text
  actionClick && exportBtn.actions.push(actionClick)
  toolsbtns.push(exportBtn)
}
// 设置toolbar
const setToolbar = (table) => {
  if (funcVal.value.length > 0) {
    for (const item of funcVal.value) {
      switch (item) {
        case '新增': {
          const addBtn = genBtn({ text: '新增', type: 'primary' })
          const oldAddBtn = toolAndOpBtns.value.find(
            (v) => v._aliasName === '新增',
          )
          if (oldAddBtn) {
            addBtn.prop.text = oldAddBtn.prop.text
          }
          addBtn._aliasName = '新增'
          toolsbtns.push(addBtn)
          break
        }
        case '批量选择': {
          const moreChoseBtn = genBtn({ text: '批量选择' })
          const oldMoreChoseBtn = toolAndOpBtns.value.find(
            (v) => v._aliasName === '批量选择',
          )
          if (oldMoreChoseBtn) {
            moreChoseBtn.prop.text = oldMoreChoseBtn.prop.text
          }
          moreChoseBtn._aliasName = '批量选择'
          moreChoseBtn.prop.forbid.exp = `表格-${table.compId}.modelValue.selectedRows.length==0`
          toolsbtns.push(moreChoseBtn)
          break
        }
        case '批量删除': {
          const moreDelBtn = genBtn({ text: '批量删除' })
          const oldMoreDelBtn = toolAndOpBtns.value.find(
            (v) => v._aliasName === '批量删除',
          )
          if (oldMoreDelBtn) {
            moreDelBtn.prop.text = oldMoreDelBtn.prop.text
          }
          moreDelBtn._aliasName = '批量删除'
          moreDelBtn.prop.forbid.exp = `表格-${table.compId}.modelValue.selectedRows.length==0`
          toolsbtns.push(moreDelBtn)
          break
        }
        case '导入': {
          const oldImportBtn = toolAndOpBtns.value.find(
            (v) => v._aliasName === '导入',
          )
          if (oldImportBtn) {
            toolsbtns.push(oldImportBtn)
          } else {
            toolBtnExportImport('导入')
          }
          break
        }
        case '导出': {
          const oldexportBtn = toolAndOpBtns.value.find(
            (v) => v._aliasName === '导出',
          )
          if (oldexportBtn) {
            toolsbtns.push(oldexportBtn)
          } else {
            toolBtnExportImport('导出')
          }
          break
        }
      }
    }
  }
  if (toolsbtns.length > 0) {
    cardChild.push(toolBar(toolsbtns))
  }
}
const isHasRef = (title) => {
  switch (title) {
    case '新增': {
      return addModalRef.value
    }
    case '操作栏-编辑': {
      return editModalRef.value
    }
    case '详情': {
      return detailModalRef.value
    }
    case '导入': {
      return importRef.value
    }
    case '导出': {
      return exportRef.value
    }
    // No default
  }
  return undefined
}
const getModal = (title) => {
  return currentComp.value?.childrens.find((v) => {
    return v.prop.title === title
  })
}
// 获取新建弹框modal
const genAddEditModal = (lyyForm, table, title) => {
  let modal = null
  const currentModal = getModal(title)
  if (currentModal && !isHasRef(title)) {
    const btn = findToolBtn(title)
    btn && actionOpenModal(btn, currentModal.compId)
    return currentModal
  }
  if (title == '新增') {
    modal = addModalRef.value?.getData(lyyForm.compId, toolsbtns)
    originData.addData = addModalRef.value?.getOriginData() || {}
  } else if (title == '操作栏-编辑') {
    modal = editModalRef.value?.getData(lyyForm.compId, table.compId)
    originData.editData = editModalRef.value?.getOriginData() || {}
  }
  if (funcVal.value.includes(title) && !modal) {
    // 使用默认值生成
    const tableList = table.prop.columns.filter((v) => v.type !== 'operation')
    const gridList = getDefaultGridList(tableList, 'lyy-text-input')
    const listTable = table.prop.columns.map((val) => {
      return {
        label: val.title,
        field: val.dataIndex,
        type: 'lyy-text-input',
      }
    })
    if (title == '新增') {
      modal = setAddModal([gridList], lyyForm.compId, toolsbtns)
      originData.addData = {
        lists: _.cloneDeep(listTable),
        colNumber: 'middle',
        formActionConfig: _.cloneDeep(requestOption),
      }
    } else if (title == '操作栏-编辑') {
      modal = setEditModal([gridList], lyyForm.compId, table.compId)
      originData.editData = {
        lists: _.cloneDeep(listTable),
        colNumber: 'middle',
        formActionConfig: _.cloneDeep(requestOption),
      }
    }
  }
  return modal
}
// 获取详情弹框
const setDetailModal = (btnDetail, table) => {
  const edModal = getModal('详情')
  if (!isHasRef('详情') && edModal) {
    actionOpenModal(btnDetail, edModal.compId)
    return edModal
  }
  const modal = genModal({
    title: '详情',
  })
  if (detailModalRef.value) {
    modal.childrens = [detailModalRef.value.getData()]
    originData.detailData = detailModalRef.value.getOriginData()
  } else {
    const tableList = table.prop.columns.filter((v) => v.type !== 'operation')
    const gridList = getDefaultGridList(tableList, 'lyy-label-text', 8)
    const formConfig = genForm()
    formConfig.childrens = [gridList]
    modal.childrens = [formConfig]
    const id = Math.random()
    originData.detailData = {
      funcVal: ['表单模块'],
      formActionConfig: {},
      formLists: [
        {
          title: '',
          size: 'default',
          id,
        },
      ],
      tableLists: [],
      formFliedList: {
        [id]: tableList.map((val) => {
          return {
            label: val.title,
            field: val.dataIndex,
            type: '',
          }
        }),
      }, // 表单字段列表
      tableFliedList: {},
    }
  }
  actionOpenModal(btnDetail, modal.compId)
  return modal
}
const confirm = () => {
  // 表单处理
  const lyyForm = addAliasNameAnduuid(_.cloneDeep(lyyFormModel))
  Object.assign(lyyForm.prop.form, {
    size: 10,
    current: 1,
  })
  // 先对勾选的功能进行处理
  funcChange(lyyForm.compId)
  // 给表单加上搜索容器中的字段
  mergeFormList(lyyForm, searchFormListHandle(searchFormListArr) as [])
  /**
   * 处理卡片包裹工具栏、table、分页 start
   * **/
  const table = TableRef.value.tableHandle()
  setToolbar(table)
  const lyyCard = addAliasNameAnduuid(_.cloneDeep(lyyCardModel))
  lyyCard.prop.bordered = false
  lyyCard.prop.mainLayout = true
  table.prop.showSelection = isSelectPl.value
  cardChild.push(table)
  // 设置基础功能的原始数据
  originData.baseFunctionData = {
    tableLists: _.cloneDeep(TableRef.value.getOriginData()),
    formActionConfig: _.cloneDeep(formActionConfig),
    funcVal: _.cloneDeep(funcVal.value),
    formLists: _.cloneDeep(SearchProRef.value?.getOriginData()),
  }
  // 基础功能搜索容器选项
  SearchProRef.value?.setRestLists()
  // 添加分页
  const pagination = lyyPaginationHandle()
  if (funcVal.value.includes('分页')) {
    cardChild.push(pagination)
    setPaginationAction(pagination, lyyForm.compId)
  }
  const addModelRes = genAddEditModal(lyyForm, table, '新增')
  addModelRes && modals.push(addModelRes)
  lyyCard.prop.showHeader = false
  lyyCard.childrens.push(...cardChild)
  children.push(lyyCard)
  // 操作栏-编辑
  if (funcVal.value.includes('操作栏-编辑')) {
    const editModal = genAddEditModal(lyyForm, table, '操作栏-编辑')
    const btnEdit = findOperateBtn('操作栏-编辑')
    editModal && actionOpenModal(btnEdit, editModal.compId)
    editModal && modals.push(editModal)
  }
  // 操作栏-详情
  if (funcVal.value.includes('操作栏-详情')) {
    const btnEdit = findOperateBtn('操作栏-详情')
    modals.push(setDetailModal(btnEdit, table))
  }
  lyyForm.childrens.push(...children)
  // 表单事件处理
  if (formActionConfig.action == 'request' && formActionConfig.option.url) {
    const url =
      formActionConfig.option.url[0] == '/'
        ? formActionConfig.option.url.slice(1)
        : formActionConfig.option.url
    const keyName = 'Api' + Number.parseInt(Math.random() * 1000)
    // `${url.replace('/', '-')}-${lyyForm.compId.slice(0, 5)}`
    formActionConfig.option.responseDataKey = keyName

    if (formActionConfig.option.customOption) {
      formActionConfig.option.customOption.loading = true
    } else {
      formActionConfig.option.customOption = {
        loading: true,
      }
    }
    formActionConfig.option.payloads.dynamic.nodePath =
      lyyForm.compId + '.modelValue'
    const actionArr = [
      {
        fromNodePath: keyName + '.records',
        toNodePath: table.compId + '.modelValue.datasource',
      },
    ]
    if (funcVal.value.includes('分页')) {
      actionArr.push({
        fromNodePath: keyName + '.total',
        toNodePath: pagination.compId + '.prop.total',
      })
    }
    actionArr.reduce((prev, next) => {
      return setAction(prev, next.fromNodePath, next.toNodePath)
    }, formActionConfig)
    lyyForm.actions.push(formActionConfig)
  }
  if (
    formActionConfig.action == 'requestDataset' &&
    formActionConfig.option.name
  ) {
    const { responseDataKey } = formActionConfig.option
    delete formActionConfig.option.responseDataKey
    const actionArr = [
      {
        fromNodePath: responseDataKey + '.records',
        toNodePath: table.compId + '.modelValue.datasource',
      },
    ]
    if (funcVal.value.includes('分页')) {
      actionArr.push({
        fromNodePath: responseDataKey + '.total',
        toNodePath: pagination.compId + '.prop.total',
      })
    }
    actionArr.reduce((prev, next) => {
      return setAction(prev, next.fromNodePath, next.toNodePath)
    }, formActionConfig)
    lyyForm.actions.push(formActionConfig)
  }
  emit('ok', _.cloneDeep([lyyForm, ...modals]), originData)
  TableRef.value.setRestLists()
  setInit()
}
const cancel = () => {
  emit('cancel')
}
const getActionData = (actionConfig) => {
  formActionConfig = Object.assign(formActionConfig, { ...actionConfig })
}
const idForm = Math.random()
const originData = reactive({
  editData: {},
  exportData: {},
  importData: {},
  detailData: {
    funcVal: ['表单模块'],
    formActionConfig: {},
    formLists: [
      {
        id: idForm,
        title: '',
        size: 'default',
      },
    ],
    tableLists: [],
    formFliedList: {
      [idForm]: [
        {
          filed: '',
          type: '',
          label: '标签名称',
        },
      ],
    }, // 表单字段列表
    tableFliedList: {}, // 表格字段列表
  },
  addData: {},
  baseFunctionData: {
    tableLists: [],
    formActionConfig: {},
    formLists: {},
    funcVal: [],
  },
})
const setData = (data) => {
  Object.assign(originData, _.cloneDeep(data))
  // 设置功能选择项
  funcVal.value.length = 0
  funcVal.value.push(...originData.baseFunctionData.funcVal)
  // 设置搜索容器
  SearchProRef.value?.setData(
    _.cloneDeep(originData.baseFunctionData.formLists),
  )
  // 设置表格列
  TableRef.value?.setData(originData.baseFunctionData.tableLists)
  Object.assign(formActionConfig, originData.baseFunctionData.formActionConfig)
  const cardComp = currentComp.value.childrens
    .find((v) => v.compName === 'lyy-form')
    ?.childrens?.find((v) => v.compName === 'lyy-card')
  const cardBtn =
    cardComp?.childrens.find((v) => v.compName === 'lyy-toolbar')?.childrens ||
    []
  const tableBtn =
    cardComp?.childrens
      .find((v) => v.compName === 'lyy-table')
      ?.prop.columns.find((v) => v.type === 'operation')?.childrens || []
  toolAndOpBtns.value = [...cardBtn, ...tableBtn]
}
defineExpose({
  setData,
})
</script>
<style lang="scss">
.crud-modal {
  .ant-modal-body {
    padding: 8px 24px;
  }
}
</style>
<style scoped lang="scss">
.btn-box {
  padding-top: 8px;
  color: #40a9ff;
  .btn {
    padding-left: 4px;
  }
}
.padding4px {
  padding: 4px 0;
}
.url-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .setIcon {
    margin-left: 8px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
.tabs-box {
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}
</style>
