<template>
  <a-modal
    v-model:open="visible"
    :maskClosable="false"
    title="基础表单快速开始-BasicForm"
    class="Toolbar-modal"
    width="800px"
    @ok="confirm"
    @cancel="cancel"
    :destroyOnClose="true"
  >
    <!--功能启用-->
    <section>
      <div class="funcTit padding4px">启用功能</div>
      <div class="padding4px">
        <a-checkbox-group v-model:value="funcVal" :options="funcOptions" />
      </div>
    </section>
    <!-- 保存配置 -->
    <section v-if="funcVal.includes('保存')">
      <div class="funcTit padding4px">保存接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonActionConfig.option.url"
          placeholder="Http://"
        />
        <setting-outlined class="setIcon" @click="urlSaveModal = true" />
      </div>
    </section>
    <!-- 详情请求配置 -->
    <section v-if="funcVal.includes('详情请求')">
      <div class="funcTit padding4px">详情接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="formActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlSetModal = true" />
      </div>
    </section>
    <!--表单配置-->
    <formItemList ref="formItemListRef" />
    <!---详情接口配置-->
    <requestModal
      v-model="urlSetModal"
      @getActionData="getActionData"
      :formActionConfig="formActionConfig"
    ></requestModal>
    <!---保存接口配置-->
    <requestModal
      v-model="urlSaveModal"
      @getActionData="getButtonActionData"
      :formActionConfig="buttonActionConfig"
    ></requestModal>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, defineComponent, onMounted, reactive, ref } from 'vue'
import _ from 'lodash'
import formItemList from './formItemList.vue'
import requestModal from '../RequestModal/index.vue'
import LyyBasicFormModel from '../../../Model/static/lyy-basic-form-model'
import lyyButtonGroup from '../../../Model/layoutItem/lyy-button-group-model'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import { genBtn, genGrid } from '../../../components/Modal/modelUtils/genModel'
import {
  detailRequest,
  setValRequest,
  clickRequest,
  validateRequest,
} from '../../../components/Modal/modelUtils/actionModel'
import LyyCanvas from '@/context/canvas'
defineComponent({
  name: 'basic-form-modal',
})
const emit = defineEmits(['update:modelValue', 'ok', 'cancel'])
// 表单元素列表
const formItemListRef = ref(null)
// 详情请求弹窗控制
const urlSetModal = ref(false)
// 保存请求弹窗控制
const urlSaveModal = ref(false)
// 启用功能
const funcVal = ref(['保存'])
const funcOptions = ['保存', '详情请求', '取消按钮']
const visible = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})
const props = defineProps<{
  modelValue: boolean
  types?: any
  templateData?: object
}>()

let children: object[] = []
// 保存按钮
let saveBtn = genBtn({
  text: '保存',
  type: 'primary',
  style: {
    'margin-left': '100px',
    'margin-right': '12px',
  },
})
// 取消按钮
let cancelBtn = genBtn({
  text: '取消',
})
const buttonGroup = ref(addAliasNameAnduuid(_.cloneDeep(lyyButtonGroup)))
const funcChange = (formId) => {
  children = []
  const formList = formItemListRef.value.listHandle()
  let lyyGrid = genGrid({ size: 'large' })
  lyyGrid.prop.children.push(...formList)
  children.push(lyyGrid)
  formItemListRef.value.setRestLists()
  if (funcVal.value.length > 0)
    for (const item of funcVal.value) {
      switch (item) {
        case '保存':
          if (buttonActionConfig.option.url) {
            // 设置获取值
            buttonActionConfig.option.payloads.dynamic.nodePath =
              formId + '.modelValue'
            buttonActionConfig.option.payloads.type = 'dynamic'
            let validateRequestAction = _.cloneDeep(validateRequest)
            validateRequestAction.thenActions.push(buttonActionConfig)
            validateRequestAction.option.targetId = formId
            saveBtn.actions = [validateRequestAction]
          }
          buttonGroup.value.childrens.push(saveBtn)
          break
        case '取消按钮':
          buttonGroup.value.childrens.push(cancelBtn)
          break
      }
    }
}
const confirm = () => {
  const lyyForm = addAliasNameAnduuid(_.cloneDeep(LyyBasicFormModel))
  if (funcVal.value.includes('详情请求') && formActionConfig.option.url) {
    formActionConfig.option.responseDataKey = `BasicForm-${lyyForm.compId}`
    let setValRequestAction = _.cloneDeep(setValRequest)
    // 请求参数配置
    formActionConfig.option.payloads.higher.push({
      key: 'id',
      value: 'route.query.id',
    })
    // 设置值配置
    setValRequestAction.option.to.dynamic.nodePath =
      lyyForm.compId + '.modelValue'
    setValRequestAction.option.from.dynamic.nodePath = `BasicForm-${lyyForm.compId}`
    formActionConfig.thenActions.push(setValRequestAction)
    lyyForm.actions = [formActionConfig]
  }
  funcChange(lyyForm.compId)
  lyyForm.childrens.push(...children)
  if (funcVal.value.includes('保存') || funcVal.value.includes('取消')) {
    lyyForm.childrens.push(buttonGroup.value)
  }
  children = []
  emit('ok', _.cloneDeep(lyyForm))
  setInit()
}
const cancel = () => {
  emit('cancel')
}
// 详情请求事件
let formActionConfig = reactive(_.cloneDeep(detailRequest))
const getActionData = (actionConfig) => {
  formActionConfig = Object.assign(formActionConfig, { ...actionConfig })
}
// 保存请求事件
let buttonActionConfig = reactive(_.cloneDeep(clickRequest))
const getButtonActionData = (actionConfig) => {
  buttonActionConfig = Object.assign(buttonActionConfig, { ...actionConfig })
}
// 重置请求参数
const restActionConfig = () => {
  // 详情请求
  formActionConfig.option.url = ''
  formActionConfig.option.interceptors = {}
  formActionConfig.option.method = 'get'
  formActionConfig.option.payloads = {
    type: 'higher',
    dynamic: {
      nodePath: '',
    },
    higher: [],
    static: '',
  }
  formActionConfig.option.headerPayloads = []
  formActionConfig.thenActions = []
  //保存请求
  buttonActionConfig.option.url = ''
  buttonActionConfig.option.interceptors = {}
  buttonActionConfig.option.method = 'post'
  buttonActionConfig.option.payloads = {
    type: 'higher',
    dynamic: {
      nodePath: '',
    },
    higher: [],
    static: '',
  }
  buttonActionConfig.option.headerPayloads = []
  buttonActionConfig.thenActions = []
}
// 重置表单数据
const setInit = () => {
  children.length = buttonGroup.value.childrens.length = 0
  restActionConfig()
  funcVal.value.length = 0
  funcVal.value.push('保存')
  buttonGroup.value = addAliasNameAnduuid(buttonGroup.value)
}
</script>
<style scoped lang="scss">
.crud-modal {
  .ant-modal-body {
    padding: 8px 24px;
  }
}
.btn-box {
  padding-top: 8px;
  color: #40a9ff;
  .btn {
    padding-left: 4px;
  }
}
.padding4px {
  padding: 4px 0;
}
.url-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .setIcon {
    margin-left: 8px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
</style>
