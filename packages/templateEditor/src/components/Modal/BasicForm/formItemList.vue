<template>
  <section>
    <div class="title">表单字段设置</div>
    <div class="form-area">
      <div class="drag-area">
        <Draggable
          :list="lists"
          class="list-group"
          handle=".handle"
          group="people"
          item-key="name"
          @start="dragStart"
          @end="dragEnd"
        >
          <template #item="{ element, index }">
            <div class="list-box">
              <div class="item shrink">
                <a-tooltip>
                  <template #title>拖拽排序</template>
                  <unordered-list-outlined class="handle" />
                </a-tooltip>
              </div>
              <div class="item grow">
                <a-input
                  v-model:value="element.label"
                  placeholder="标签"
                ></a-input>
              </div>
              <div class="item grow">
                <a-input
                  v-model:value="element.field"
                  placeholder="字段名"
                ></a-input>
              </div>
              <div class="item grow">
                <a-select
                  v-model:value="element.type"
                  class="select"
                  placeholder="文本框"
                  size="middle"
                  :options="options"
                ></a-select>
              </div>
              <close-outlined class="item shrink" @click="removeAt(index)" />
            </div>
          </template>
        </Draggable>
      </div>
      <div class="btn-box">
        <plus-outlined @click="addRow" />
        <a-button type="link" class="btn" @click="addRow">新增</a-button>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { computed, defineComponent, reactive, ref } from 'vue'
import Draggable from 'vuedraggable'
import _ from 'lodash'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import CompFormItem from '@/Model/formItem'
import { searchFormListOptions } from '@/Pages/Edit/Right/AttrConfig/comps/complex'
// 下拉选择时 替换组件模型
const compTypeChange = (newCompName, element, index) => {
  // 取出模型 替换目标组件
  const target = addAliasNameAnduuid(
    _.cloneDeep(CompFormItem(`./${newCompName}-model.ts`).default),
  )
  // 替换某一项
  props.data.splice(index, 1, target)
}
const props = defineProps<{ id: number }>()
const defaultRow = {
  label: '标签名称',
  field: '',
  type: 'lyy-text-input',
}
let lists = reactive([_.cloneDeep(defaultRow)])
const removeAt = (idx) => {
  lists.splice(idx, 1)
}
const addRow = () => {
  lists.push(_.cloneDeep(defaultRow))
}
const isDraging = ref(false)
const dragStart = () => {
  isDraging.value = true
}
const dragEnd = () => {
  isDraging.value = false
}
const defaultOptions = [...searchFormListOptions['lyy-grid'].children]
const options = computed(() => {
  return defaultOptions
})
const setFormList = (item, data) => {
  const formList = addAliasNameAnduuid(_.cloneDeep(data))
  formList.prop.field = item.field
  formList.prop.label = item.label
  return formList
}
const listHandle = () => {
  let formList = []
  for (const item of lists) {
    const target = addAliasNameAnduuid(
      _.cloneDeep(CompFormItem(`./${item.type}-model.ts`).default),
    )
    target.prop.field = item.field
    target.prop.label = item.label
    formList.push(target)
  }
  return formList
}
defineComponent({
  name: 'form-item-list',
})
defineExpose({
  listHandle,
  id: props.id,
  setRestLists: () => {
    lists.length = 1
  },
})
</script>

<style scoped lang="scss">
.form-area {
  padding: 8px;
  border: 1px solid #ebeaea;
  border-radius: 4px;
}
.title {
  padding: 4px 0;
}
.list-box {
  display: flex;
  padding: 4px 0;
  align-items: center;
  justify-content: space-around;
  .item {
    padding-left: 8px;
    &:first-child {
      padding-left: 0;
    }
    .select {
      width: 100%;
    }
  }
  .grow {
    flex-grow: 1;
    width: 30%;
  }
  .shrink {
    flex-shrink: 0;
  }
}
</style>
