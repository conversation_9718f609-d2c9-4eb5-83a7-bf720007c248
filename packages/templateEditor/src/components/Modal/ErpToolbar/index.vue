<template>
  <a-modal
    v-model:open="visible"
    :maskClosable="false"
    title="ERP工具栏快速开始-Toolbar"
    class="Toolbar-modal"
    width="800px"
    @ok="confirm"
    @cancel="cancel"
  >
    <!--功能启用-->
    <section>
      <div class="funcTit padding4px">启用功能</div>
      <div class="padding4px">
        <a-checkbox-group v-model:value="funcVal" :options="funcOptions" />
      </div>
    </section>
    <section>
      <div class="funcTit padding4px">表单key值字段</div>
      <div class="url-input">
        <a-input v-model:value="formKey" placeholder=""></a-input>
      </div>
    </section>
    <!-- 详情配置 -->
    <section>
      <div class="funcTit padding4px">详情接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="formActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlDetailModal = true" />
      </div>
    </section>
    <!-- 保存配置 -->
    <section v-if="funcVal.includes('保存')">
      <div class="funcTit padding4px">新增接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonSaveActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlSaveModal = true" />
      </div>
    </section>
    <!-- 编辑配置 -->
    <section v-if="funcVal.includes('保存')">
      <div class="funcTit padding4px">编辑接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonEditActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlEditModal = true" />
      </div>
    </section>
    <!-- 复制配置 -->
    <section v-if="funcVal.includes('复制')">
      <div class="funcTit padding4px">复制接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonCopyActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlCopyModal = true" />
      </div>
    </section>
    <!-- 提交配置 -->
    <section v-if="funcVal.includes('提交')">
      <div class="funcTit padding4px">提交接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonPostActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlPostModal = true" />
      </div>
    </section>
    <!-- 审核配置 -->
    <section v-if="funcVal.includes('审核')">
      <div class="funcTit padding4px">审核接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonAuditActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlAuditModal = true" />
      </div>
    </section>
    <!-- 反审配置 -->
    <section v-if="funcVal.includes('反审')">
      <div class="funcTit padding4px">反审接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonOppositeAuditConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined
          class="setIcon"
          @click="urlOppositeAuditModal = true"
        />
      </div>
    </section>
    <!-- 关闭配置 -->
    <section v-if="funcVal.includes('关闭')">
      <div class="funcTit padding4px">关闭接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonCloseActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlCloseModal = true" />
      </div>
    </section>
    <!-- 作废配置 -->
    <section v-if="funcVal.includes('作废')">
      <div class="funcTit padding4px">作废接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonCancelActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlCancelModal = true" />
      </div>
    </section>
    <!-- 冲红配置 -->
    <section v-if="funcVal.includes('冲红')">
      <div class="funcTit padding4px">冲红接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonFlushRedActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlFlushRedModal = true" />
      </div>
    </section>
    <!-- 下推配置 -->
    <section v-if="funcVal.includes('下推')">
      <div class="funcTit padding4px">下推接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonPushDownActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlPushDownModal = true" />
      </div>
    </section>
    <!-- 删除配置 -->
    <section v-if="funcVal.includes('删除')">
      <div class="funcTit padding4px">删除接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonDelActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlDelModal = true" />
      </div>
    </section>
    <!---详情接口配置-->
    <requestModal
      v-model="urlDetailModal"
      @getActionData="getActionData"
      :formActionConfig="formActionConfig"
    ></requestModal>
    <!---新增接口配置-->
    <requestModal
      v-model="urlSaveModal"
      @getActionData="getSaveActionData"
      :formActionConfig="buttonSaveActionConfig"
    ></requestModal>
    <!---编辑接口配置-->
    <requestModal
      v-model="urlSaveModal"
      @getActionData="getEditActionData"
      :formActionConfig="buttonEditActionConfig"
    ></requestModal>
    <!---复制接口配置-->
    <requestModal
      v-model="urlCopyModal"
      @getActionData="getCopyActionData"
      :formActionConfig="buttonCopyActionConfig"
    ></requestModal>
    <!---提交接口配置-->
    <requestModal
      v-model="urlPostModal"
      @getActionData="getPostActionData"
      :formActionConfig="buttonPostActionConfig"
    ></requestModal>
    <!---审核接口配置-->
    <requestModal
      v-model="urlAuditModal"
      @getActionData="getAuditActionData"
      :formActionConfig="buttonAuditActionConfig"
    ></requestModal>
    <!---反审接口配置-->
    <requestModal
      v-model="urlOppositeAuditModal"
      @getActionData="getOppositeAuditData"
      :formActionConfig="buttonOppositeAuditConfig"
    ></requestModal>
    <!---关闭接口配置-->
    <requestModal
      v-model="urlCloseModal"
      @getActionData="getCloseActionData"
      :formActionConfig="buttonCloseActionConfig"
    ></requestModal>
    <!---作废接口配置-->
    <requestModal
      v-model="urlCancelModal"
      @getActionData="getCancelActionData"
      :formActionConfig="buttonCloseActionConfig"
    ></requestModal>
    <!---冲红接口配置-->
    <requestModal
      v-model="urlFlushRedModal"
      @getActionData="getFlushRedActionData"
      :formActionConfig="buttonFlushRedActionConfig"
    ></requestModal>
    <!---下推接口配置-->
    <requestModal
      v-model="urlPushDownModal"
      @getActionData="getPushDownActionData"
      :formActionConfig="buttonPushDownActionConfig"
    ></requestModal>
    <!---删除接口配置-->
    <requestModal
      v-model="urlDelModal"
      @getActionData="getDelActionData"
      :formActionConfig="buttonDelActionConfig"
    ></requestModal>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, defineComponent, onMounted, reactive, ref, toRaw } from 'vue'
import { useRouter } from 'vue-router'
import _ from 'lodash'
import LyyErpToolbar from '../../../Model/other/lyy-erp-toolbar-model'
import lyyErpActionItemModel from '../../../Model/other/lyy-erp-action-item-model'
import { toolBar } from './toolbar'
import { card, genAuditModal } from './comp'
import {
  clickRequest,
  detailRequest,
  setValRequest,
  delRequest,
  goBack,
  broadcastAction,
  openConfirmAction,
} from '../../../components/Modal/modelUtils/actionModel'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
const emit = defineEmits(['update:modelValue', 'ok', 'cancel'])
const formDetailAction = 'getDetail'
const formKey = ref('id')
const urlDetailModal = ref(false)
// 详情请求事件
let formActionConfig = reactive(_.cloneDeep(detailRequest))
const getActionData = (actionConfig) => {
  formActionConfig = Object.assign(formActionConfig, { ...actionConfig })
}
// 新增请求事件
const urlSaveModal = ref(false)
let buttonSaveActionConfig = reactive(_.cloneDeep(clickRequest))
const getSaveActionData = (actionConfig) => {
  buttonSaveActionConfig = Object.assign(buttonSaveActionConfig, {
    ...actionConfig,
  })
}
// 编辑请求事件
const urlEditModal = ref(false)
let buttonEditActionConfig = reactive(_.cloneDeep(clickRequest))
const getEditActionData = (actionConfig) => {
  buttonEditActionConfig = Object.assign(buttonEditActionConfig, {
    ...actionConfig,
  })
}
// 复制请求事件
const urlCopyModal = ref(false)
let buttonCopyActionConfig = reactive(_.cloneDeep(detailRequest))
const getCopyActionData = (actionConfig) => {
  buttonCopyActionConfig = Object.assign(buttonCopyActionConfig, {
    ...actionConfig,
  })
}
// 提交请求事件
const urlPostModal = ref(false)
let buttonPostActionConfig = reactive(_.cloneDeep(clickRequest))
const getPostActionData = (actionConfig) => {
  buttonPostActionConfig = Object.assign(buttonPostActionConfig, {
    ...actionConfig,
  })
}
// 审核请求事件
const urlAuditModal = ref(false)
//审核弹窗
const auditModal = ref(null)
let buttonAuditActionConfig = reactive(_.cloneDeep(clickRequest))
const getAuditActionData = (actionConfig) => {
  buttonAuditActionConfig = Object.assign(buttonAuditActionConfig, {
    ...actionConfig,
  })
}
// 反审请求事件
const urlOppositeAuditModal = ref(false)
let buttonOppositeAuditConfig = reactive(_.cloneDeep(clickRequest))
const getOppositeAuditData = (actionConfig) => {
  buttonOppositeAuditConfig = Object.assign(buttonOppositeAuditConfig, {
    ...actionConfig,
  })
}
// 关闭请求事件
const urlCloseModal = ref(false)
let buttonCloseActionConfig = reactive(_.cloneDeep(clickRequest))
const getCloseActionData = (actionConfig) => {
  buttonCloseActionConfig = Object.assign(buttonCloseActionConfig, {
    ...actionConfig,
  })
}
// 作废请求事件
const urlCancelModal = ref(false)
let buttonCancelActionConfig = reactive(_.cloneDeep(clickRequest))
const getCancelActionData = (actionConfig) => {
  buttonCloseActionConfig = Object.assign(buttonCancelActionConfig, {
    ...actionConfig,
  })
}
// 冲红请求事件
const urlFlushRedModal = ref(false)
let buttonFlushRedActionConfig = reactive(_.cloneDeep(clickRequest))
const getFlushRedActionData = (actionConfig) => {
  buttonFlushRedActionConfig = Object.assign(buttonFlushRedActionConfig, {
    ...actionConfig,
  })
}
// 下推请求事件
const urlPushDownModal = ref(false)
let buttonPushDownActionConfig = reactive(_.cloneDeep(clickRequest))
const getPushDownActionData = (actionConfig) => {
  buttonPushDownActionConfig = Object.assign(buttonPushDownActionConfig, {
    ...actionConfig,
  })
}
// 删除请求事件
const urlDelModal = ref(false)
let buttonDelActionConfig = reactive(_.cloneDeep(delRequest))
const getDelActionData = (actionConfig) => {
  buttonDelActionConfig = Object.assign(buttonDelActionConfig, {
    ...actionConfig,
  })
}
const funcOptions = [
  '新增',
  '编辑',
  '取消',
  '保存',
  '复制',
  '刷新',
  '提交',
  '审核',
  '反审',
  '关闭',
  '作废',
  '冲红',
  '下推',
  '打印',
  '删除',
  '选单',
  '列表',
  // '全屏',
  // '设置',
]
const funcVal = ref(['新增', '编辑', '取消', '保存', '刷新', '删除', '列表'])
const visible = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})
defineComponent({
  name: 'toolbar-modal',
})
const props = defineProps<{
  modelValue: boolean
  types?: any
  templateData?: object
}>()
const routerUrl = toRaw(useRouter()).currentRoute.value.fullPath
let children: object[] = []
let btns: object[] = []
const funcChange = (formId) => {
  children = []
  btns = []
  if (funcVal.value.length > 0)
    for (const item of funcVal.value) {
      const btn = addAliasNameAnduuid(_.cloneDeep(lyyErpActionItemModel))
      btn.prop.text = item != '更多' ? item : ''
      btn.prop.dropdown = []
      switch (item) {
        case '新增':
          btn.prop.icon = 'PlusOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled`,
          }
          btn.actions = [
            {
              event: 'click',
              action: 'resetValue',
              option: {
                targetId: formId,
              },
              thenActions: [
                {
                  event: 'click',
                  action: 'setNewValue',
                  option: {
                    from: {
                      static: '${false}',
                    },
                    to: {
                      type: 'static',
                      dynamic: {
                        nodePath: `${formId}.prop.disabled`,
                      },
                    },
                  },
                  thenActions: [
                    {
                      event: 'click',
                      action: 'setNewValue',
                      option: {
                        from: {
                          static: '${null}',
                        },
                        to: {
                          type: 'static',
                          dynamic: {
                            nodePath: `${formId}.modelValue.${formKey.value}`,
                          },
                        },
                      },
                    },
                  ],
                },
              ],
            },
          ]
          break
        case '取消':
          btn.prop.icon = 'CloseCircleOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||${formId}.prop.disabled`,
          }
          btn.actions = [
            {
              event: 'click',
              action: 'setNewValue',
              option: {
                from: {
                  static: '${true}',
                },
                to: {
                  type: 'static',
                  dynamic: {
                    nodePath: `${formId}.prop.disabled`,
                  },
                },
              },
              thenActions: [
                {
                  event: 'click',
                  action: 'broadcast',
                  option: {
                    targetId: formId,
                    event: formDetailAction,
                  },
                },
              ],
            },
          ]
          break
        case '编辑':
          btn.prop.icon = 'FormOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([0,3],${formId}.modelValue.status))`,
          }
          btn.actions = [
            {
              event: 'click',
              action: 'setNewValue',
              option: {
                from: {
                  static: '${false}',
                },
                to: {
                  type: 'static',
                  dynamic: {
                    nodePath: `${formId}.prop.disabled`,
                  },
                },
              },
            },
          ]
          break
        case '保存':
          btn.prop.icon = 'SaveOutlined'
          btn.prop.forbid = {
            exp: `!formIsChange-${formId}||${formId}.prop.disabled`,
          }
          // 新增保存事件
          buttonSaveActionConfig.option.payloads = {
            type: 'dynamic',
            dynamic: {
              nodePath: formId + '.modelValue',
            },
            higher: [],
            static: '',
          }
          buttonSaveActionConfig.option.responseDataKey = `单据Id-${formId}`
          // eslint-disable-next-line no-case-declarations
          let buttonSaveSetVal = _.cloneDeep(setValRequest)
          buttonSaveSetVal.event = 'click'
          buttonSaveSetVal.option.from.dynamic.nodePath = `单据Id-${formId}`
          buttonSaveSetVal.option.to.dynamic.nodePath = `${formId}.modelValue.${formKey.value}`
          // eslint-disable-next-line no-case-declarations
          // 派发表单mounted事件
          let buttonBroadcastForm = _.cloneDeep(broadcastAction)
          buttonBroadcastForm.event = 'click'
          buttonBroadcastForm.option.targetId = formId
          buttonBroadcastForm.option.event = 'mounted'
          buttonSaveSetVal.thenActions.push(buttonBroadcastForm)
          buttonSaveActionConfig.thenActions.push(buttonSaveSetVal)
          //编辑保存事件
          buttonEditActionConfig.option.payloads = {
            type: 'dynamic',
            dynamic: {
              nodePath: formId + '.modelValue',
            },
            higher: [],
            static: '',
          }
          buttonEditActionConfig.option.method = 'put'
          buttonEditActionConfig.thenActions.push(buttonBroadcastForm)
          btn.actions = [
            {
              event: 'click',
              action: 'validate',
              option: {
                targetId: formId,
              },
              condition: {
                exp: `!${formId}.modelValue.${formKey.value}`,
              },
              thenActions: [buttonSaveActionConfig],
            },
            {
              event: 'click',
              action: 'validate',
              option: {
                targetId: formId,
              },
              condition: {
                exp: `${formId}.modelValue.${formKey.value}`,
              },
              thenActions: [buttonEditActionConfig],
            },
          ]
          break
        case '复制':
          btn.prop.icon = 'CopyOutlined'
          btn.prop.forbid = {
            exp: '!route.query.dataId||!${formId}.prop.disabled',
          }
          buttonCopyActionConfig.event = 'click'
          buttonCopyActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: 'route.query.dataId',
              },
            ],
            static: '',
          }
          // eslint-disable-next-line no-case-declarations
          let setValRequestAction = _.cloneDeep(setValRequest)
          setValRequestAction.event = 'click'
          buttonCopyActionConfig.option.responseDataKey = `copyData-${formId}`
          // 设置值配置
          setValRequestAction.option.to.dynamic.nodePath =
            formId + '.modelValue'
          setValRequestAction.option.from.dynamic.nodePath = `copyData-${formId}`
          buttonCopyActionConfig.thenActions.push(setValRequestAction)
          btn.actions.push(buttonCopyActionConfig)
          break
        case '刷新':
          btn.prop.icon = 'RedoOutlined'
          //新增
          // eslint-disable-next-line no-case-declarations
          let btnOpenConfirm = _.cloneDeep(openConfirmAction)
          btnOpenConfirm.option.content =
            '点击刷新，会将页面数据清空并重置到初始状态！'
          btnOpenConfirm.condition = {
            exp: `!${formId}.modelValue.${formKey.value}`,
          }
          btnOpenConfirm.thenActions.push({
            event: 'click',
            action: 'resetValue',
            option: {
              targetId: formId,
            },
            thenActions: [],
          })
          //编辑
          // eslint-disable-next-line no-case-declarations
          let btnOpenConfirm2 = _.cloneDeep(openConfirmAction)
          btnOpenConfirm2.option.content =
            '点击刷新，会将页面数据清空并重置到初始状态！'
          btnOpenConfirm2.condition = {
            exp: `${formId}.modelValue.${formKey.value}`,
          }
          btnOpenConfirm2.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
            condition: {
              exp: `${formId}.modelValue.${formKey.value}&&!${formId}.prop.disabled`,
            },
          })
          //详情
          // eslint-disable-next-line no-case-declarations
          let btnOpenConfirm3: any = {
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
            condition: {
              exp: `${formId}.modelValue.${formKey.value}&&${formId}.prop.disabled`,
            },
          }
          btn.actions = [btnOpenConfirm, btnOpenConfirm2, btnOpenConfirm3]
          break
        case '提交':
          btn.prop.icon = 'CheckSquareOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([0,3],${formId}.modelValue.status))`,
          }
          buttonPostActionConfig.option.method = 'put'
          buttonPostActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: `${formId}.modelValue.${formKey.value}`,
              },
            ],
            static: '',
          }
          // 提交后刷新页面
          buttonPostActionConfig.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
          })
          btn.actions.push(buttonPostActionConfig)
          break
        case '审核':
          btn.prop.icon = 'AuditOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([1,4],${formId}.modelValue.status))`,
          }
          buttonAuditActionConfig.option.method = 'put'
          //配置弹窗
          auditModal.value = genAuditModal(
            formKey.value,
            formId,
            buttonAuditActionConfig,
          )
          btn.actions.push({
            event: 'click',
            action: 'openModal',
            option: {
              targetId: auditModal.value?.compId,
            },
            thenActions: [],
          })
          break
        case '反审':
          btn.prop.icon = 'FileSyncOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([2],${formId}.modelValue.status))`,
          }
          buttonAuditActionConfig.option.method = 'put'
          buttonAuditActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: `${formId}.modelValue.${formKey.value}`,
              },
            ],
            static: '',
          }
          // 审核后刷新页面
          buttonAuditActionConfig.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
          })
          btn.actions.push(buttonAuditActionConfig)
          break
        case '关单':
          btn.prop.icon = 'CloseCircleOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&ARRAYINCLUDES([5,6],${formId}.modelValue.status))`,
          }
          buttonCloseActionConfig.option.method = 'put'
          buttonCloseActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: `${formId}.modelValue.${formKey.value}`,
              },
            ],
            static: '',
          }
          // 关闭后刷新页面
          buttonCloseActionConfig.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
          })
          btn.actions.push(buttonCloseActionConfig)
          break
        case '作废':
          btn.prop.icon = 'CloseSquareOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([2,4],${formId}.modelValue.status))`,
          }
          buttonCancelActionConfig.option.method = 'put'
          buttonCancelActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: `${formId}.modelValue.${formKey.value}`,
              },
            ],
            static: '',
          }
          // 作废后刷新页面
          buttonCancelActionConfig.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
          })
          btn.actions.push(buttonCancelActionConfig)
          break
        case '冲红':
          btn.prop.icon = 'InteractionOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([2,5],${formId}.modelValue.status))`,
          }
          buttonFlushRedActionConfig.option.method = 'put'
          buttonFlushRedActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: `${formId}.modelValue.${formKey.value}`,
              },
            ],
            static: '',
          }
          // 冲红后刷新页面
          buttonFlushRedActionConfig.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
          })
          btn.actions.push(buttonFlushRedActionConfig)
          break
        case '下推':
          btn.prop.icon = 'RightSquareOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([2],${formId}.modelValue.status))`,
          }
          buttonPushDownActionConfig.option.method = 'put'
          buttonPushDownActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: `${formId}.modelValue.${formKey.value}`,
              },
            ],
            static: '',
          }
          // 下推后刷新页面
          buttonPushDownActionConfig.thenActions.push({
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: formId,
              event: formDetailAction,
            },
          })
          btn.actions.push(buttonPushDownActionConfig)
          break
        case '删除':
          btn.prop.icon = 'DeleteOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled||(${formId}.prop.disabled&&!ARRAYINCLUDES([0,3,6],${formId}.modelValue.status))`,
          }
          buttonPushDownActionConfig.option.method = 'delete'
          buttonDelActionConfig.option.payloads = {
            type: 'higher',
            dynamic: {
              nodePath: '',
            },
            higher: [
              {
                key: formKey.value,
                value: 'route.query.dataId',
              },
            ],
            static: '',
          }
          buttonDelActionConfig.option.responseDataKey = `copyData-${formId}`
          // 删除后返回上一页
          buttonDelActionConfig.thenActions.push(goBack)
          // eslint-disable-next-line no-case-declarations
          let delOpenConfirm = _.cloneDeep(openConfirmAction)
          delOpenConfirm.option.content = '您确定要删除这条数据吗？'
          delOpenConfirm.thenActions.push(buttonDelActionConfig)
          btn.actions.push(delOpenConfirm)
          break
        case '打印':
          btn.prop.icon = 'PrinterOutlined'
          btn.prop.forbid = {
            exp: `!${formId}.modelValue.${formKey.value}||!${formId}.prop.disabled`,
          }
          break
        case '选单':
          btn.prop.icon = 'FileSearchOutlined'
          btn.prop.forbid = {
            exp: `${formId}.modelValue.${formKey.value}`,
          }
          break
        case '列表':
          btn.prop.icon = 'TableOutlined'
          // eslint-disable-next-line no-case-declarations
          let action1 = reactive(_.cloneDeep(goBack))
          action1.condition.exp =
            formId + '.prop.disabled||!formIsChange-' + formId
          // eslint-disable-next-line no-case-declarations
          let listOpenConfirm = _.cloneDeep(openConfirmAction)
          listOpenConfirm.option.content = '您还有未保存的数据，确认返回吗?'
          listOpenConfirm.thenActions = [reactive(_.cloneDeep(goBack))]
          listOpenConfirm.condition.exp =
            !formId + '.prop.disabled&&formIsChange-' + formId
          btn.actions.push(action1, listOpenConfirm)
          break
        case '全屏':
          btn.prop.icon = 'FullscreenOutlined'
          // eslint-disable-next-line no-case-declarations
          let moreOpenConfirm = _.cloneDeep(openConfirmAction)
          moreOpenConfirm.option.content = '暂时没有全屏功能，敬请期待'
          btn.actions = [moreOpenConfirm]
          break
        case '设置':
          btn.prop.icon = 'SettingOutlined'
          btn.actions = [
            {
              event: 'click',
              action: 'openConfirm',
              option: {
                content: '暂时没有设置功能，敬请期待',
              },
            },
          ]
          break
      }
      btns.push(btn)
    }
  if (btns.length > 0) {
    children.push(toolBar(btns), card())
  }
}
const confirm = () => {
  const lyyForm = addAliasNameAnduuid(_.cloneDeep(LyyErpToolbar))
  lyyForm.prop.isSignChange = true
  lyyForm.prop.disabled = false
  if (formActionConfig.option.url) {
    // 详情请求事件配置
    formActionConfig.option.responseDataKey = `详情数据-${lyyForm.compId}`
    formActionConfig.event = formDetailAction
    let setValRequestAction = _.cloneDeep(setValRequest)
    setValRequestAction.event = formDetailAction
    // 请求参数配置
    formActionConfig.option.payloads.higher.push({
      key: formKey.value,
      value: `${lyyForm.compId}.modelValue.${formKey.value}`,
    })
    // 设置值配置
    setValRequestAction.option.to.dynamic.nodePath =
      lyyForm.compId + '.modelValue'
    setValRequestAction.option.from.dynamic.nodePath = `详情数据-${lyyForm.compId}`
    formActionConfig.thenActions.push(setValRequestAction)
    lyyForm.actions = [formActionConfig]
    //设置表单禁用自定义事件
    let setDisabled = _.cloneDeep(setValRequest)
    setDisabled.event = 'mounted'
    setDisabled.option.to.type = 'static'
    setDisabled.option.to.dynamic.nodePath = `${lyyForm.compId}.prop.disabled`
    setDisabled.option.from.static = '${true}'
    // 派发请求详情事件
    let formBroadcastAction = _.cloneDeep(broadcastAction)
    formBroadcastAction.event = 'mounted'
    formBroadcastAction.option.targetId = lyyForm.compId
    formBroadcastAction.option.event = formDetailAction
    formBroadcastAction.condition.exp = `${lyyForm.compId}.modelValue.${formKey.value}`
    formBroadcastAction.thenActions = [setDisabled]
    //将dataId设置到表单里面 如果dataId存在的话
    let formSetValDataIdAction = _.cloneDeep(setValRequest)
    formSetValDataIdAction.event = 'beforeMount'
    formSetValDataIdAction.option.to.dynamic.nodePath = `${lyyForm.compId}.modelValue.${formKey.value}`
    formSetValDataIdAction.option.from.dynamic.nodePath = 'route.query.dataId'
    formSetValDataIdAction.condition = {
      exp: 'route.query.dataId',
    }
    lyyForm.actions.push(formBroadcastAction, formSetValDataIdAction)
  }
  funcChange(lyyForm.compId)
  lyyForm.childrens.push(...children)
  if (auditModal.value) {
    lyyForm.childrens.push(auditModal.value)
  }
  children = []
  emit('ok', _.cloneDeep(lyyForm))
  children = []
}
const cancel = () => {
  emit('cancel')
}
</script>
<style scoped lang="scss">
.crud-modal {
  .ant-modal-body {
    padding: 8px 24px;
  }
}
.btn-box {
  padding-top: 8px;
  color: #40a9ff;
  .btn {
    padding-left: 4px;
  }
}
.padding4px {
  padding: 4px 0;
}
.url-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .setIcon {
    margin-left: 8px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
</style>
