//doc https://leyaoyao.yuque.com/vbutb7/qw48a7/xua13g
import { IActionFormData, Payloads } from './base'

// 弹窗类型
export type Type = 'modal' | 'notification' | 'message'

// 弹窗效果类型
export type ModalType = 'info' | 'success' | 'error' | 'warning' | 'confirm'

export type NotificationType =
  | 'open'
  | 'info'
  | 'success'
  | 'error'
  | 'warning'
  | 'warn'
  | 'loading'
  | 'close'

export type MessageType =
  | 'open'
  | 'info'
  | 'success'
  | 'error'
  | 'warning'
  | 'warn'
  | 'loading'

export type PlacementType =
  | 'topLeft'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomRight'

export type OpenConfirmBaseOption = {
  styleType?: Type
  title?: string
  content?: string
  payloads?: Payloads[]
}
export type OpenConfirmModalOption = {
  type?: ModalType
  okText?: string
  cancelText?: string
  okButtonProps?: object
} & OpenConfirmBaseOption

export type OpenConfirmNotificationOption = {
  type?: NotificationType
  placement?: PlacementType // 通知提醒框出现位置
  duration?: number // 通知提醒框/全局提示 出现时间
} & OpenConfirmBaseOption

export type OpenConfirmMessageOption = {
  type?: MessageType
  duration?: number // 通知提醒框/全局提示 出现时间
} & OpenConfirmBaseOption

export type OpenConfirmOption =
  | OpenConfirmModalOption
  | OpenConfirmNotificationOption
  | OpenConfirmMessageOption

export interface IOpenConfirmFormData
  extends IActionFormData<OpenConfirmOption> {
  condition?: {
    exp?: string
    payloads?: Payloads[]
  }
}
