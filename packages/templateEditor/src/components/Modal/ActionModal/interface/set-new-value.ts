import { IActionFormData } from './base'

type nodePath = string

interface ICustomer {
  key: string
  value: string | nodePath
  id: string
}

export type SetNewValueOption = {
  from: {
    dynamic?: {
      nodePath: string // 资源路径
    }
    higher?: ICustomer[]
    static?: string
  }
  to: {
    type: 'static' | 'dynamic' | 'higher' // 简易设置 高级设置
    dynamic?: {
      nodePath: string // 资源路径
    }
  }
}
export type qtTrackOption = {
  trackerEventCode: string
  eventType?: string
  eventParams: {
    type: string
    higher?: ICustomer[]
    static?: string
  }
}

export type ISetNewValueFormData = IActionFormData<SetNewValueOption>
