// 事件与表单配置的映射表
import { IRequestFormData } from './interface/request'
import { ILinkToFormData } from './interface/link-to'
import { ISetValueFormData } from './interface/set-value'
import { IDownloadFormData } from './interface/download'
import { IBroadcastFormData } from './interface/broadcast'
import { IOpenModalFormData } from './interface/open-modal'
import { ICloseModalFormData } from './interface/close-modal'
import { IOpenConfirmFormData } from './interface/open-confirm'
import { IResetValueFormData } from './interface/reset-value'
import { IValidateFormData } from './interface/validate-form'
import { IUploadFormData } from './interface/upload'
import { IDepActionFormData } from './interface/dep-action'
import { IPrintFormData } from './interface/print'
import { IRequestDatasetOption } from './interface/request-dataset'

// ---- 页面 start ----
// 链接跳转
import ActionLinkTo from './components/action-link-to.vue'
// 新版设置数据
import ActionSetNewValue from './components/action-new-set-value.vue'
import ActionActionCustomJs from './components/action-custom-js.vue'
import ActionCopyToClipboard from './components/action-copy-to-clipboard.vue'
// ---- 页面 end ------

// ---- 弹窗信息 start -
// 打开弹窗
import ActionOpenModal from './components/action-open-modal.vue'
// 关闭弹窗
import ActionCloseModal from './components/action-close-modal.vue'
// 打开抽屉
import ActionOpenDrawer from './components/action-open-drawer.vue'
// 关闭抽屉
import ActionCloseDrawer from './components/action-close-drawer.vue'
// 弹窗信息
import ActionOpenConfirm from './components/action-open-confirm.vue'
// ---- 弹窗信息 end ---

// ---- 网络 start ---
// 请求
import ActionRequest from './components/action-request.vue'
// 数据集
import ActionRequestDataset from './components/action-request-dataset.vue'
// 下载
import ActionDownload from './components/action-download.vue'
// 上传
import ActionUpload from './components/action-upload.vue'
// 打印
import ActionPrint from './components/action-print.vue'
// ---- 网络 end -----
// ---- 埋点设置 start ----
import ActionQtTrack from './components/action-qt-track.vue'
// ---- 表单 start ---
// 重置表单
import ActionResetForm from './components/action-reset-form.vue'
// 表单校验
import ActionValidateForm from './components/action-validate-form.vue'
// ---- 表单 end -----

// ---- 通信 start ---
// 外部通信
import ActionDepAction from './components/action-dep-action.vue'
// 派发事件
import ActionBroadcast from './components/action-broadcast.vue'
import ActionGlobalDepAction from './components/action-global-dep-action.vue'
// ---- 通信 end -----

import type { Component } from 'vue'

export interface IActionConfigActions {
  key: string
  action: string
  text: string
  desc?: string
  docLink?: string
  components: Component
}

export interface IActionConfig {
  key: string
  text: string
  actions: IActionConfigActions[]
}
export interface IActionMenuItem {
  key: string
  label: string
  type?: string
  config?: {
    action: string
    option: object
  }
  children?: IActionMenuItem[]
}

// formLabel
export const labelCol = { style: { width: '110px' } }
export const wrapperCol = { style: { flex: '1' } }

export const methodList: string[] = [
  'get',
  'post',
  'delete',
  'put',
  'patch',
  'head',
]

export const modalType: {
  key: string
  name: string
  typeList: Record<string, string>[]
  placementList: Record<string, string>[]
}[] = [
  {
    key: 'modal',
    name: '提示弹窗',
    typeList: [
      { key: 'info', name: '信息弹窗' },
      { key: 'success', name: '成功弹窗' },
      { key: 'error', name: '错误弹窗' },
      { key: 'warning', name: '警告弹窗' },
      { key: 'confirm', name: '确认弹窗' },
    ],
    placementList: [],
  },
  {
    key: 'notification',
    name: '通知信息',
    typeList: [
      { key: 'open', name: '简单通知' },
      { key: 'info', name: '信息通知' },
      { key: 'success', name: '成功通知' },
      { key: 'error', name: '错误通知' },
      { key: 'warn', name: '警告通知' },
      { key: 'loading', name: '加载中' },
      { key: 'close', name: '关闭通知' },
    ],
    placementList: [
      { key: 'topLeft', name: '上左' },
      { key: 'topRight', name: '上右' },
      { key: 'bottomLeft', name: '下左' },
      { key: 'bottomRight', name: '下右' },
    ],
  },
  {
    key: 'message',
    name: '消息通知',
    typeList: [
      { key: 'open', name: '简单消息' },
      { key: 'info', name: '信息消息' },
      { key: 'success', name: '成功消息' },
      { key: 'error', name: '错误消息' },
      { key: 'warn', name: '警告消息' },
      { key: 'loading', name: '加载中' },
    ],
    placementList: [],
  },
]
export const requestModalType: {
  key: string
  name: string
  typeList: Record<string, string>[]
  placementList: Record<string, string>[]
}[] = [
  {
    key: 'notification',
    name: '通知信息',
    typeList: [
      { key: 'open', name: '简单通知' },
      { key: 'info', name: '信息通知' },
      { key: 'success', name: '成功通知' },
      { key: 'error', name: '错误通知' },
      { key: 'warn', name: '警告通知' },
      { key: 'loading', name: '加载中' },
      { key: 'close', name: '关闭通知' },
    ],
    placementList: [
      { key: 'topLeft', name: '上左' },
      { key: 'topRight', name: '上右' },
      { key: 'bottomLeft', name: '下左' },
      { key: 'bottomRight', name: '下右' },
    ],
  },
  {
    key: 'message',
    name: '消息通知',
    typeList: [
      { key: 'open', name: '简单消息' },
      { key: 'info', name: '信息消息' },
      { key: 'success', name: '成功消息' },
      { key: 'error', name: '错误消息' },
      { key: 'warn', name: '警告消息' },
      { key: 'loading', name: '加载中' },
    ],
    placementList: [],
  },
]
export const typeList: { key: string; name: string }[] = [
  { key: '', name: '值直接覆盖' },
  { key: 'merge', name: '值浅层合并' },
  { key: 'splice', name: '向数组插入元素' },
]
export const setValuetypeList: { key: string; name: string }[] = [
  { key: 'undefined', name: '直接覆盖' },
  { key: 'merge', name: '浅层合并' },
]

export const spliceList: { key: string; name: string }[] = [
  { key: 'start', name: '插入数组开头' },
  { key: 'end', name: '插入数组末尾' },
  { key: 'number', name: '自定义位置' },
]

export const downloadType: Record<string, string> = {
  href: '链接下载',
  '.doc': 'doc',
  '.docx': 'docx',
  '.xls': 'xls',
  '.xlsx': 'xlsx',
  '.ppt': 'ppt',
  '.pptx': 'pptx',
  '.zip': 'zip',
}

export const responseType: Record<string, string> = {
  blob: '文件流',
  '': '其他',
}

const linktoAction = {
  key: 'linkto',
  action: 'linkto',
  text: '跳转链接',
  desc: '跳转到指定链接的页面',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/kcec1t0rx89cuv6y',
  components: ActionLinkTo,
}

const setNewValueAction = {
  key: 'setNewValue',
  action: 'setNewValue',
  text: '设置数据',
  desc: '新的数据值',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/ncb4efc8rv02pe0r',
  components: ActionSetNewValue,
}

const customerJSAction = {
  key: 'customerJS',
  action: 'customerJS',
  text: '自定义JS',
  desc: '通过JavaScript自定义动作逻辑',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/zqv653la0h2dzibp',
  components: ActionActionCustomJs,
}
const copyToClipboardAction = {
  key: 'copyToClipboard',
  action: 'copyToClipboard',
  text: '复制到剪贴板',
  desc: '复制到剪贴板',
  // docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/zqv653la0h2dzibp',
  components: ActionCopyToClipboard,
}
const qtTrackAction = {
  key: 'qtTrack',
  action: 'qtTrack',
  text: '埋点事件',
  desc: '埋点事件',
  // docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/zqv653la0h2dzibp',
  components: ActionQtTrack,
}
const openModalAction = {
  key: 'openModal',
  action: 'openModal',
  text: '打开弹窗',
  // desc: '打开弹窗，弹窗内支持复杂的交互设计',
  desc: '打开某个弹窗组件',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/ruclg1n13k3gx7ei',
  components: ActionOpenModal,
}
const closeModalAction = {
  key: 'closeModal',
  action: 'closeModal',
  text: '关闭弹窗', // 与打开弹窗共用一个（暂时
  desc: '这个事件只针对lyy-modal',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/ruclg1n13k3gx7ei',
  components: ActionCloseModal,
}

const openConfirmAction = {
  key: 'openConfirm',
  action: 'openConfirm',
  text: '弹窗信息',
  desc: '可配置 Modal居中弹窗、notification信息通知弹窗、message消息弹窗',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/hxcf2tb2ettgauqt',
  components: ActionOpenConfirm,
}

const requestAction = {
  key: 'request',
  action: 'request',
  text: '发起请求',
  desc: '发起一些请求',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/yeonbgt8eawt30mg',
  components: ActionRequest,
}

const requestDatasetAction = {
  key: 'requestDataset',
  action: 'requestDataset',
  text: '数据集请求',
  desc: '关联数据集，方便api维护',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/yeonbgt8eawt30mg',
  components: ActionRequestDataset,
}

const downloadAction = {
  key: 'download',
  action: 'download',
  text: '下载',
  desc: '下载数据',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/sr1mg3gxh2byd08g',
  components: ActionDownload,
}

const uploadAction = {
  key: 'upload',
  action: 'upload',
  text: '上传',
  desc: '上传数据到后台',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/gy7zm6vgv8qcpkva',
  components: ActionUpload,
}

const printAction = {
  key: 'print',
  action: 'print',
  text: '打印',
  desc: '生成链接调起浏览器打印',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/gxbcn7',
  components: ActionPrint,
}

const resetValueAction = {
  key: 'resetValue',
  action: 'resetValue',
  text: '重置表单',
  desc: '目前这个事件只针对lyy-form',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/wmi9s4ax4gohf26f',
  components: ActionResetForm,
}

const validateAction = {
  key: 'validate',
  action: 'validate',
  text: '校验表单',
  desc: '目前这个事件只针对lyy-form',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/wmi9s4ax4gohf26f',
  components: ActionValidateForm,
}

const depActionAction = {
  key: 'depAction',
  action: 'depAction',
  text: '组件特性动作',
  desc: '跨父子组件通信',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/qnrn6cyi25xeg1kn',
  components: ActionDepAction,
}

const broadcastAction = {
  key: 'broadcast',
  action: 'broadcast',
  text: '触发事件',
  desc: '子组件向上层组件通信',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/pt343sq8252lrf2g',
  components: ActionBroadcast,
}

const globalDepActionAction = {
  key: 'globalDepAction',
  action: 'globalDepAction',
  text: '广播事件',
  desc: '组件库组件与应用系统中组件通信',
  docLink: 'https://leyaoyao.yuque.com/vbutb7/qw48a7/pt343sq8252lrf2g',
  components: ActionGlobalDepAction,
}
// 事件与组件的映射表
const actionConfig: IActionConfig[] = [
  {
    key: 'page',
    text: '页面',
    actions: [
      linktoAction,
      setNewValueAction,
      customerJSAction,
      copyToClipboardAction,
      qtTrackAction,
    ],
  },
  {
    key: 'dialog',
    text: '弹窗信息',
    actions: [openModalAction, closeModalAction, openConfirmAction],
  },
  {
    key: 'network',
    text: '网络',
    actions: [
      requestAction,
      requestDatasetAction,
      downloadAction,
      uploadAction,
      printAction,
    ],
  },
  {
    key: 'form',
    text: '表单',
    actions: [resetValueAction, validateAction],
  },
  {
    key: 'component',
    text: '组件',
    actions: [depActionAction],
  },
  {
    key: 'communication',
    text: '通信',
    actions: [broadcastAction, globalDepActionAction],
  },
]
export const actionMenu: IActionMenuItem[] = [
  {
    key: 'page',
    label: '页面',
    type: 'group',
    children: [
      {
        key: linktoAction.key,
        label: linktoAction.text,
        config: {
          action: linktoAction.key,
          option: {},
        },
      },
      {
        key: setNewValueAction.key,
        label: setNewValueAction.text,
        config: {
          action: setNewValueAction.key,
          option: {},
        },
      },
      {
        key: customerJSAction.key,
        label: customerJSAction.text,
        config: {
          action: customerJSAction.key,
          option: {},
        },
      },
      {
        key: copyToClipboardAction.key,
        label: copyToClipboardAction.text,
        config: {
          action: copyToClipboardAction.key,
          option: {},
        },
      },
      {
        key: qtTrackAction.key,
        label: qtTrackAction.text,
        config: {
          action: qtTrackAction.key,
          option: {},
        },
      },
    ],
  },
  {
    key: 'dialog',
    label: '弹窗信息',
    type: 'group',
    children: [
      {
        key: openModalAction.key,
        label: openModalAction.text,
        config: {
          action: openModalAction.key,
          option: {},
        },
      },
      {
        key: closeModalAction.key,
        label: closeModalAction.text, // 与打开弹窗共用一个（暂时
        config: {
          action: closeModalAction.key,
          option: {},
        },
      },
      {
        key: openConfirmAction.key,
        label: openConfirmAction.text,
        config: {
          action: openConfirmAction.key,
          option: {},
        },
      },
    ],
  },
  {
    key: 'network',
    label: '网络',
    type: 'group',
    children: [
      {
        key: requestAction.key,
        label: requestAction.text,
        config: {
          action: requestAction.key,
          option: {},
        },
      },
      {
        key: requestDatasetAction.key,
        label: requestDatasetAction.text,
        config: {
          action: requestDatasetAction.key,
          option: {},
        },
      },
      {
        key: downloadAction.key,
        label: downloadAction.text,
        config: {
          action: downloadAction.key,
          option: {},
        },
      },
      {
        key: uploadAction.key,
        label: uploadAction.text,
        config: {
          action: uploadAction.key,
          option: {},
        },
      },
      {
        key: printAction.key,
        label: printAction.text,
        config: {
          action: printAction.key,
          option: {},
        },
      },
    ],
  },
  {
    key: 'form',
    label: '表单',
    type: 'group',
    children: [
      {
        key: resetValueAction.key,
        label: resetValueAction.text,
        config: {
          action: resetValueAction.key,
          option: {},
        },
      },
      {
        key: validateAction.key,
        label: validateAction.text,
        config: {
          action: validateAction.key,
          option: {},
        },
      },
    ],
  },
  {
    key: 'component',
    label: '组件',
    type: 'group',
    children: [
      {
        key: depActionAction.key,
        label: depActionAction.text,
        config: {
          action: depActionAction.key,
          option: {},
        },
      },
    ],
  },
  {
    key: 'communication',
    label: '通信',
    type: 'group',
    children: [
      {
        key: broadcastAction.key,
        label: broadcastAction.text,
        config: {
          action: broadcastAction.key,
          option: {},
        },
      },
      {
        key: globalDepActionAction.key,
        label: globalDepActionAction.text,
        config: {
          action: globalDepActionAction.key,
          option: {},
        },
      },
    ],
  },
]
export default actionConfig
const baseConfigOp = {
  clear: '清空',
  reset: '重置',
  setData: '设置组件值',
}
// 外部通讯的一些特定组件的事件映射
export const depActionConfig: Record<string, Record<string, string>> = {
  'lyy-edit-table': {
    setCellDisable: '禁用某个单元格',
    setColumnDisable: '禁用某列',
    setRowDisable: '禁用某行',
    setCellEnable: '启用某个单元格',
    setColumnEnable: '启用某列',
    setRowEnable: '启用某行',
    validFormEvent: '校验表格',
  },
  // 编辑表格
  'lyy-excel-table': {
    deleteRow: '删除一行或者多行数据',
    addData: '新增一行数据',
    addNewRows: '批量新增多行数据',
    // updateData: '更新数据',
    restData: '重置表格',
    restLogic: '逻辑重置表格',
    logicDelUpdate: '逻辑删除并更新',
    setCheckBoxPicked: '设置勾选一行或者多行',
    setCheckBoxCancel: '取消一行或者多行勾选',
    fetch: '请求数据',
    fullValidEvent: '全校验',
    validEvent: '快速校验',
    updateForm: '数据同步更新到表单',
    asyncUpdateForm: '数据异步更新到表单',
    batchUpdateDataSomeProto: '批量更新数据字段',
    getRemoteOptions: '获取远程下拉选项',
  },
  'lyy-text-input': baseConfigOp,
  'lyy-text-area': baseConfigOp,
  'lyy-checkbox': {
    reLoad: '重新加载',
    ...baseConfigOp,
  },
  'lyy-erp-sku': {
    addData: '新增一行数据',
    prevAddData: '向上插入数据',
    nextAddData: '向下插入数据',
    updateData: '更新某一行数据',
  },
  'lyy-input-range': baseConfigOp,
  'lyy-number-input': baseConfigOp,
  'lyy-date-picker': {
    setInitDate: '初始化默认值',
    ...baseConfigOp,
  },
  'lyy-date-range': baseConfigOp,
  'lyy-radio': baseConfigOp,
  'lyy-select': {
    ...baseConfigOp,
    clearOptions: '清空下拉选项',
    setOptions: '设置下拉选项',
    getRemoteOptions: '获取远程下拉选项',
  },
  'lyy-pagination': {
    reset: '重置',
    setData: '赋值',
  },
  'lyy-transfer': {
    clear: '清空',
    setData: '赋值',
  },
  'lyy-select-prod': {
    clear: '清空',
    reFresh: '刷新',
    setData: '赋值',
  },
  'lyy-switch': {
    setDisable: '禁用',
    reset: '重置',
    setData: '赋值',
  },
  'lyy-input-password': {
    clear: '清空',
  },
  'lyy-form': {
    resetForm: '表单重置',
    validate: '表单校验',
    setData: '表单赋值',
  },
  'lyy-file-uploader': {
    clear: '清空文件',
  },
  'lyy-table': {
    clearSelectRow: '清空全部选中项',
    setAllSelectRows: '设置全部选中',
    setSelectRows: '设置选中某一项',
  },
  'lyy-table-page': {
    find: '查找',
  },
  // 弹窗选择输入框
  'lyy-modal-selector-input': {
    setData: '设置数据',
  },
  // 下拉列表选择
  'lyy-pull-down-selector-input': {
    setData: '设置数据',
  },
}

export type IActionItemConfig =
  | IRequestFormData
  | ILinkToFormData
  | ISetValueFormData
  | IDownloadFormData
  | IBroadcastFormData
  | IOpenModalFormData
  | ICloseModalFormData
  | IOpenConfirmFormData
  | IResetValueFormData
  | IValidateFormData
  | IUploadFormData
  | IDepActionFormData
  | IPrintFormData
  | IRequestDatasetOption
