<template>
  <div class="action-set-value">
    <a-form
      :model="formData"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      ref="formRef"
    >
      <a-form-item
        label="目标组件"
        name="targetId"
        help="选择目标组件"
        :rules="rules.targetId"
      >
        <SelectComponentsId v-model="formData.targetId" />
      </a-form-item>
      <a-form-item label="类型" name="valueType">
        <a-radio-group v-model:value="valueType" @change="changeValueType">
          <a-radio
            v-for="item in valueTypeList"
            :key="item.key"
            :value="item.key"
          >
            {{ item.name }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="赋值属性"
        name="targetKey"
        v-if="valueType === propKey"
        :rules="rules.targetKey"
      >
        <a-input
          v-model:value="formData.targetKey"
          placeholder="配置置到组件的哪个属性上"
          :maxlength="200"
        ></a-input>
      </a-form-item>
      <a-form-item label="赋值类型" name="type">
        <a-radio-group v-model:value="formData.type">
          <a-radio v-for="item in typeList" :key="item.key" :value="item.key">
            {{ item.name }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="插入位置" v-if="isSplice">
        <a-radio-group v-model:value="formData.index">
          <a-radio v-for="item in spliceList" :key="item.key" :value="item.key">
            {{ item.name }}
          </a-radio>
        </a-radio-group>
        <a-form-item-rest v-if="isSpliceNumber">
          <a-input-number v-model:value="indexNum" :min="0" :max="999" />
        </a-form-item-rest>
      </a-form-item>
      <PayloadsComponents
        v-model:modelValue="formData.payloads"
        ref="payloadsRef"
      />
      <a-form-item label="取值属性" name="sourceKey">
        <a-input
          v-model:value="formData.sourceKey"
          placeholder="只需要取值对象的某个属性时需要配置"
          :maxlength="200"
        ></a-input>
      </a-form-item>
      <!-- <TargetsComponents v-model:modelValue="formData.targets" /> -->
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, Component, watchEffect } from 'vue'
import { labelCol, wrapperCol, typeList, spliceList } from '../config'
import { SetValueOption } from '../interface/set-value'
// import TargetsComponents from './common/targets-components.vue'
import PayloadsComponents from './common/payloads-components-two.vue'
import SelectComponentsId from './common/select-components-id.vue'

const props = defineProps<{
  option: SetValueOption
}>()

const splice = 'splice'
const number = 'number'

const valueKey = 1
const propKey = 2

const valueTypeList: { key: number; name: string }[] = [
  { key: valueKey, name: '组件值' },
  { key: propKey, name: '属性值' },
]

const isSplice = computed(() => {
  return formData.value.type === splice
})

const isSpliceNumber = computed(() => {
  return formData.value.index === number
})

const formData = ref<SetValueOption>({
  type: '',
  index: undefined,
  sourceKey: '',
  targetId: null,
  targetKey: '',
  // targets: [],
  payloads: [],
})

const valueType = ref<number>(valueKey)
const indexNum = ref<number>(0)
const payloadsRef = ref<Component>()

const rules = {
  targetId: [{ required: true, message: '请选择组件' }],
  targetKey: [{ required: true, message: '请输入属性值' }],
}

// 修改类型值
const changeValueType = (e: { target: { value: number } }) => {
  switch (e.target.value) {
    case valueKey: // 如果是设置组件值 则把targetKey设为空
      formData.value.targetKey = ''
      break
    case propKey: // 如果是设置属性值，如果targetKey没有值则需要默认填充一个modalValue
      if (!formData.value.targetKey) {
        formData.value.targetKey = 'modelValue'
      }
      break
    default:
      break
  }
}

const setData = (option: SetValueOption) => {
  const { targetKey, index } = option
  const isNumber: boolean = typeof index === number
  formData.value = {
    ...option,
    index: isNumber ? number : index,
  }
  indexNum.value = isNumber ? Number(index) : 0
  // 如果有targetKey则为设置属性值
  if (targetKey) {
    valueType.value = propKey
  }
  try {
    payloadsRef.value?.setData(option.payloads)
  } catch (error) {
    console.log('设置payloads失败 =====>', error)
  }
}

const getData = async (): Promise<SetValueOption> => {
  const data: SetValueOption = {
    ...formData.value,
  }
  if (formData.value.index === 'number') {
    data.index = indexNum.value
  }
  return { ...data }
}

watchEffect(() => {
  setData(props.option)
})

defineExpose({
  getData,
  setData,
})
</script>

<style scoped></style>
