<template>
  <div class="action-dep-action">
    <a-form
      :model="formData"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      ref="formRef"
    >
      <a-form-item label="事件id" name="targetId" :rules="rules.targetId">
        <a-input v-model:value="formData.targetId"></a-input>
      </a-form-item>
      <getValueComponent
        :label="'取值配置'"
        v-model="formData.payloads"
        :staticTxt="staticText"
        :mode="modeArr"
        :showComps="''"
      ></getValueComponent>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, Component, watchEffect, nextTick } from 'vue'
import { FormInstance } from 'ant-design-vue'
import { DepActionOption } from '../interface/dep-action'
import { labelCol, wrapperCol } from '../config'
import { useCompInfoByCompId } from '@/hooks/use-current-comp'
import getValueComponent from './common/get-value-components.vue'

const props = defineProps<{
  option: DepActionOption
}>()

const formData = ref<DepActionOption>({
  targetId: null,
  action: null,
  params: '',
  source: '',
  sourceKey: '',
  payloads: {
    type: '',
    static: '',
    dynamic: {}
  },
})

const formRef = ref<FormInstance>()


const staticTxt = {
  'lyy-table': {
    title: '需要选中的行',
    placeholder: '输入表格rowKey',
  },
}
const staticText = computed(() => {
  const componentInfo = useCompInfoByCompId(formData.value?.targetId)
  return staticTxt[componentInfo?.compName]
})

const modeArr = computed(() => {
  const modeArr = ['dynamic', 'higher', 'static']
  return modeArr
})

const rules = {
  targetId: [{ required: true, message: '请输入事件id' }],
}

const setData = (option: DepActionOption) => {
  formData.value = { ...formData.value, ...option }
}

watchEffect(() => {
  setData(props.option)
})


const getData = async (): Promise<DepActionOption> => {
  await formRef.value?.validate()
  return formData.value
}

defineExpose({
  getData,
  setData,
})
</script>

<style scoped></style>
