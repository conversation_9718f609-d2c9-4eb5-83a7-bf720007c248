<template>
  <div class="action-broadcast">
    <!-- 派发事件 -->
    <a-form
      :model="formData"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      ref="formRef"
    >
      <a-form-item
        label="目标组件"
        :rules="rules.targetId"
        name="targetId"
        help="选择目标组件"
      >
        <SelectComponentsId
          v-model="formData.targetId"
          @change="changeSelectCompId"
        />
      </a-form-item>
      <a-form-item label="触发事件" name="event" :rules="rules.event">
        <a-select v-model:value="formData.event" placeholder="请选择触发事件">
          <a-select-option
            v-for="item in getComponentEventList"
            :key="item.event"
          >
            {{ item.description || item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from 'vue'
import { labelCol, wrapperCol } from '../config'
import { BroadcastOption } from '../interface/broadcast'
import SelectComponentsId from './common/select-components-id.vue'

import componentEventList from '@/Pages/Edit/Right/ActionConfig/EventConfig'
import { useCompInfoByCompId } from '@/hooks/use-current-comp'
import renderItemText from '@/Pages/Edit/Right/ActionConfig/ActionItem/config'
import EventType from '@/enum/event-type'

const props = defineProps<{
  option: BroadcastOption
}>()

const formRef = ref()
const formData = ref<BroadcastOption>({
  targetId: null,
  event: '',
})

watchEffect(() => {
  formData.value = {
    ...formData.value,
    ...props.option,
  }
})

const rules = {
  targetId: [{ required: true, message: '请选择组件' }],
  event: [{ required: true, message: '请选择事件' }],
}

const changeSelectCompId = () => {
  formData.value.event = ''
}

const getComponentEventList = computed(() => {
  if (!formData.value.targetId) {
    return []
  }
  const compInfo = useCompInfoByCompId(formData.value.targetId)
  return compInfo.actions.map((item) => {
    const { event } = item
    const { title, desc } = renderItemText(item)
    return {
      event: event,
      description: [
        EventType[event] ? EventType[event] : `自定义事件${event}`,
        title,
        desc ?? '',
      ]
        .filter((key: string) => !!key)
        .join('/'),
    }
  })
})

const getData = async (): Promise<BroadcastOption> => {
  await formRef.value?.validate()
  return {
    ...formData.value,
  }
}

const setData = (data: BroadcastOption) => {
  formData.value = { ...data }
}

defineExpose({
  getData,
  setData,
})
</script>

<style scoped></style>
