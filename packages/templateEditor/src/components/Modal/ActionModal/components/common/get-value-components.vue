<script lang="ts" setup>
import { defineComponent, computed, defineEmits, ref } from 'vue'
import JsonSelectorModal from '@/components/Modal/JsonSelectorModal/index2.vue'
import HigherGetValueComponent from './higher-get-value-component.vue'
defineComponent({
  name: 'get-value-components',
})
const emit = defineEmits(['update:modelValue', 'update'])
const props = defineProps<{
  label: string
  mode: string[]
  showComps: string[]
  staticTxt?: {
    title?: string
    placeholder?: string
  }
  modelValue: {
    type: 'dynamic' | 'higher' | 'static'
    dynamic: {
      nodePath: string
    }
    higher: {
      key: string
      value: string
      id: string
    }[]
    static: string
  }
}>()
const showComps = computed(() => {
  return props.showComps ?? ['lyy-form']
})
const staticTxt = computed(() => {
  return (
    props.staticTxt ?? {
      title: '模板赋值',
      placeholder: '支持静态值和模板值',
    }
  )
})
const mode = computed(() => {
  return props.mode ?? ['dynamic', 'higher']
})
const payloads = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})
// modal状态控制
const sourceDatavisible = ref(false)
const handleOpen = () => {
  sourceDatavisible.value = true
}
// 弹窗数值回传
const sourceDataChange = (path: string) => {
  if (!payloads.value.dynamic) {
    payloads.value.dynamic = {
      nodePath: '',
    }
  }
  payloads.value.dynamic.nodePath = path
}
</script>

<template>
  <!-- 取值模式 -->
  <a-form-item label="入参模式" name="from.to">
    <a-radio-group v-model:value="payloads.type">
      <a-radio value="dynamic" v-if="mode.includes('dynamic')">
        组件取值
      </a-radio>
      <a-radio value="static" v-if="mode.includes('static')">
        {{ staticTxt.title }}
      </a-radio>
      <a-radio value="higher" v-if="mode.includes('higher')">对象模式</a-radio>
    </a-radio-group>
  </a-form-item>
  <!-- 静态赋值（直接赋值） -->
  <a-form-item v-if="payloads.type === 'static'" :label="staticTxt.title">
    <a-input
      v-model:value="payloads.static"
      :placeholder="staticTxt.placeholder"
    ></a-input>
  </a-form-item>
  <!-- 动态赋值 -->
  <a-form-item
    label="选择组件"
    name="from.nodePath"
    class="flex"
    v-if="payloads.type === 'dynamic' && payloads.dynamic"
  >
    <!-- <a-button class="btn" @click="handleOpen">点击选择</a-button>
    <span class="nodePath-label" v-show="payloads.dynamic?.nodePath">
      组件路径：
    </span> -->
    <a-input v-model:value="payloads.dynamic.nodePath" class="nodePath">
      <template #suffix>
        <JsonSelectorModal @select="sourceDataChange" :showComps="showComps">
          <search-outlined style="color: rgba(0, 0, 0, 0.45)" />
        </JsonSelectorModal>
      </template>
    </a-input>
  </a-form-item>
  <!-- 高级模式 -->
  <HigherGetValueComponent
    v-else-if="payloads.type === 'higher'"
    label="对象模式"
    v-model:modelValue="payloads.higher"
  ></HigherGetValueComponent>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
  :deep(.ant-row) {
    flex: 1;
  }
}
.nodePath-label {
  margin-left: 12px;
}
.nodePath {
  // width: 500px;
}
</style>
