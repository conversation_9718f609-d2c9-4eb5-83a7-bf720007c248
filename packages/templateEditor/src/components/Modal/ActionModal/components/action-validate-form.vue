<template>
  <div class="action-validate-form">
    <a-form
      :model="formData"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      ref="formRef"
    >
      <a-form-item
        label="目标组件"
        :rules="rules.targetId"
        name="targetId"
        help="选择目标组件"
      >
        <SelectComponentsId
          v-model="formData.targetId"
          filter-component-name="lyy-form"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import { OpenModalOption } from '../interface/open-modal'
import { labelCol, wrapperCol } from '../config'
import SelectComponentsId from './common/select-components-id.vue'

const props = defineProps<{
  option: ValidateFormOption
}>()

const formData = ref<OpenModalOption>({
  targetId: null,
})

const formRef = ref()

const rules = {
  targetId: [{ required: true, message: '请输入组件id' }],
}

const setData = (data: OpenModalOption) => {
  formData.value = { ...formData.value, ...data }
}

const getData = async (): Promise<OpenModalOption> => {
  await formRef.value?.validate()
  return {
    ...formData.value,
  }
}

watchEffect(() => {
  setData(props.option)
})

defineExpose({
  getData,
  setData,
})
</script>

<style scoped></style>
