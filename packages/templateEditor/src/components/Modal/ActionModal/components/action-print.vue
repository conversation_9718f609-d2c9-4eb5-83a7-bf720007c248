<template>
  <div class="action-print">
    <a-form
      :model="optionsForm"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      ref="formRef"
    >
      <a-form-item label="请求路径" name="url" :rules="rules.url">
        <div class="url-input">
          <a-input
            :maxlength="200"
            v-model:value="optionsForm.url"
            placeholder="请输入请求路径"
          />
          <setting-outlined class="setIcon" @click="urlSetModalFn" />
        </div>
      </a-form-item>
      <a-form-item label="请求方式" name="method">
        <a-radio-group v-model:value="optionsForm.method">
          <a-radio v-for="key in methodList" :key="key" :value="key">
            {{ key.toLocaleUpperCase() }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="响应类型" name="responseType">
        <a-radio-group v-model:value="optionsForm.responseType">
          <a-radio
            v-for="key in Object.keys(responseType)"
            :key="key"
            :value="key"
          >
            {{ responseType[key] }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <!-- <PayloadsComponents
        label="参数取值配置"
        v-model:modelValue="optionsForm.payloads"
      /> -->
      <!-- <HigherGetValueComponent
        label="参数取值"
        v-model:modelValue="optionsForm.payloads"
      ></HigherGetValueComponent> -->
      <HigherGetValueComponent
        label="请求头配置"
        v-model:modelValue="optionsForm.headerPayloads"
      />
      <GetValueComponent
        label="入参配置"
        v-model:modelValue="optionsForm.payloads"
      ></GetValueComponent>
      <!-- <TargetsComponents v-model:modelValue="optionsForm.targets" />
      <a-form-item
        label="赋值属性"
        name="targetKey"
        help="如果是单个组件赋值可以直接配置这个赋值属性"
      >
        <a-input
          :maxlength="200"
          v-model:value="optionsForm.targetKey"
          placeholder="请输入赋值属性"
        />
      </a-form-item>
      <a-form-item
        label="来源属性"
        name="sourceKey"
        help="从哪个字段取值，如果不配置，则数据取返回的整个data"
      >
        <a-input
          :maxlength="200"
          v-model:value="optionsForm.sourceKey"
          placeholder="请输入赋值属性"
        />
      </a-form-item>
      <a-form-item
        label="赋值组件ID"
        name="targetId"
        help="不配置，则当前触发 actions 的组件为目标组件"
      >
        <a-input
          :maxlength="200"
          v-model:value="optionsForm.targetId"
          placeholder="请输入赋值组件ID"
        />
      </a-form-item> -->
      <!-- <PayloadsComponents
        label="请求头取值配置"
        v-model:modelValue="optionsForm.headerPayloads"
      /> -->
      <a-form-item
        label="出参命名"
        name="responseDataKey"
        help="如果需要从出参取值时需要配置"
      >
        <a-input
          :maxlength="200"
          v-model:value="optionsForm.responseDataKey"
          placeholder="请输入出参命名"
        />
      </a-form-item>
      <a-collapse ghost>
        <a-collapse-panel key="condition" header="自定义配置">
          <RequestCustomOptionComponents
            v-model:modelValue="optionsForm.customOption"
          />
        </a-collapse-panel>
      </a-collapse>
    </a-form>
    <a-modal
      v-model:open="urlSetModal"
      title="高级配置"
      @ok="urlconfirm"
      width="900px"
    >
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="http配置">
          <HttpConfig :requestConfig="requestConfig"></HttpConfig>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watchEffect } from 'vue'
import { PrintOption } from '../interface/upload'
import { methodList, labelCol, wrapperCol, responseType } from '../config'
import type { FormInstance } from 'ant-design-vue'
import GetValueComponent from './common/get-value-components.vue'
import RequestCustomOptionComponents from './common/request-custom-option-components.vue'
import HigherGetValueComponent from './common/higher-get-value-component.vue'
import { clone } from 'lodash'
import HttpConfig from '../../RequestModal/http-config.vue'

const props = defineProps<{
  option: PrintOption
}>()
const formRef = ref<FormInstance>()
const optionsForm = reactive<PrintOption>({
  url: '',
  method: 'post',
  payloads: {
    type: 'dynamic',
    dynamic: {
      nodePath: '',
    },
  },
  // targets: [],
  // targetKey: '',
  // sourceKey: '',
  // targetId: '',
  responseType: 'blob',
  responseDataKey: '',
  customOption: {
    jsonPath: '',
    loading: false,
    successfulFeedback: null,
    failedFeedback: null,
    nextEventsDelay: 0,
    codeValue: '',
    codeKey: '',
    messageKey: '',
    parse: false,
  },
  headerPayloads: [],
  interceptors: {
    requestInterceptor: '',
    responseInterceptor: '',
  },
})

const rules: FormInstance['rules'] = {
  url: [{ required: true, message: '请输入请求路径' }],
}

const filteData = (data) => {
  for (const [key, value] of Object.entries(data)) {
    if (value === null || value === '') {
      delete data[key]
    }
  }
  return data
}

const getData = async (): Promise<UploadOption> => {
  await formRef.value?.validate()
  const { customOption } = optionsForm
  const filteRes = filteData(clone(customOption))
  optionsForm.customOption = filteRes
  return {
    ...optionsForm,
  }
}
// 高级设置
const urlSetModal = ref(false)
const activeKey = ref('1')
const urlSetModalFn = () => {
  urlSetModal.value = true
}
// 临时适配
const requestConfig = {
  option: optionsForm,
}
const urlconfirm = () => {
  console.log(optionsForm, '最终配置')
  urlSetModal.value = false
}
const setData = (data: UploadOption) => {
  Object.assign(optionsForm, reactive({ ...data }))
}

watchEffect(() => {
  setData(props.option)
})

defineExpose({
  getData,
  setData,
})
</script>

<style scoped lang="scss">
.url-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .setIcon {
    margin-left: 8px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
</style>
