<script lang="ts" setup>
import { ref, watchEffect } from 'vue'
import { qtTrackOption } from '../interface/set-new-value'
import { labelCol, wrapperCol } from '../config'
import { uuidv4 } from '@leyaoyao/libs'
import JsonSelectorModal from '@/components/Modal/JsonSelectorModal/index2.vue'
import _ from 'lodash'
import { FormInstance } from 'ant-design-vue'

const props = defineProps<{
  option: qtTrackOption
}>()

const formRef = ref<FormInstance>()
// 基础数据定义
const formData = ref<qtTrackOption>({
  trackerEventCode: '',
  eventType: 'EXP',
  eventParams: {
    type: 'higher',
    higher: [{ key: 'event_description', value: '', id: 'c' + uuidv4() }],
    static: '',
  },
})
///------------------

// modal状态控制
const sourceDatavisible = ref(false)
const higherDatavisible = ref(false)
const targetDataVisible = ref(false)
const currentId = ref(0)
const handleOpenHigherData = (formItem) => {
  currentId.value = formItem.id
  higherDatavisible.value = true
  console.log(formItem)
}
///-------------------

// 弹窗数值回传
const sourceDataChange = (path: string) => {
  if (!formData.value.eventParams.dynamic) {
    return
  }
  formData.value.eventParams.dynamic.nodePath = path
}
///--------------------

/// 高级选择回传
const higherDataChange = (path: string, id: string) => {
  const target = formData.value.eventParams.higher?.find((item) => {
    return item.id === id
  })
  target && (target.value = path)
}
/// 高级添加
const addHigher = () => {
  formData.value.eventParams.higher?.push({
    key: '',
    value: '',
    id: 'c' + uuidv4(),
  })
}
/// 删除高级选项
const deleteHigher = (id: string) => {
  const index = formData.value.eventParams.higher?.findIndex(
    (high) => high.id === id,
  )
  index && formData.value.eventParams.higher?.splice(index, 1)
}
///--------------------

// 外部赋值用
const getData = async () => {
  await formRef.value?.validate()
  return formData.value
}
// 回显用props.option
const setData = (data) => {
  const cloneValue = _.clone(data)
  formData.value = { ...formData.value, ...cloneValue }
}

watchEffect(() => {
  setData(props.option)
})

defineExpose({
  getData,
  setData,
})
</script>

<template>
  <div class="action-qt-track">
    <a-form
      :model="formData"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      ref="formRef"
    >
      <a-form-item
        label="事件代码"
        :rules="[{ required: true, message: '请输入事件代码' }]"
        name="trackerEventCode"
      >
        <a-input
          v-model:value="formData.trackerEventCode"
          required
          placeholder="例如：wwj-free-card"
        ></a-input>
      </a-form-item>
      <!-- 高级模式 -->
      <a-form-item label="事件设置">
        <a-form
          layout="inline"
          class="flex"
          v-for="(formItem, index) in formData.eventParams.higher"
          :key="index"
        >
          <!-- key值 -->
          <a-form-item class="higer-key">
            <a-input placeholder="key" v-model:value="formItem.key">
              {{ formItem.key }}
            </a-input>
          </a-form-item>
          <!-- value值 -->
          <a-form-item class="higer-value">
            <a-input placeholder="value" v-model:value="formItem.value">
              <template #suffix>
                <JsonSelectorModal :id="currentId" @select="higherDataChange">
                  <search-outlined
                    style="color: rgba(0, 0, 0, 0.45)"
                    @click="handleOpenHigherData(formItem)"
                  />
                </JsonSelectorModal>
                <!-- 高级模式 -->
              </template>
            </a-input>
          </a-form-item>
          <!-- 删除 -->
          <delete-outlined @click="deleteHigher(formItem.id)" class="delete" />
        </a-form>
        <a-button block class="add-button" type="primary" @click="addHigher">
          + 增加
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="less" scoped>
.add-button {
  width: 120px;
}
.flex {
  display: flex;
}
.nodePath-label {
  margin-left: 12px;
}
.nodePath {
  // width: 500px;
}
.higer-key {
  width: 163px;
}
.higer-value {
  flex: 1;
}
.static-input {
  width: 679px;
}
.delete {
  color: red;
  margin-top: -16px;
}
</style>
