<template>
  <div class="action-close-modal">
    <a-form
      :model="formData"
      :labelCol="labelCol"
      :wrapperCol="wrapperCol"
      labelAlign="left"
      ref="formRef"
      class="action-form"
    >
      <a-form-item name="targetId" :rules="rules.targetId" label="选择弹窗">
        <SelectComponentsId
          v-model="formData.targetId"
          filter-component-name="lyy-modal"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watchEffect } from 'vue'
import { CloseModalOption } from '../interface/close-modal'
import { labelCol, wrapperCol } from '../config'
import { usePageConfig } from '@/hooks/use-current-comp'
import SelectComponentsId from './common/select-components-id.vue'

const props = defineProps<{
  option: CloseModalOption
}>()

const formData = ref<CloseModalOption>({
  targetId: null,
})

watchEffect(() => {
  formData.value = {
    ...formData.value,
    ...props.option,
  }
})

const formRef = ref({})
const pageConfig = usePageConfig()

const rules = {
  targetId: [{ required: true, message: '这是必填项' }],
}

const setData = (data: CloseModalOption) => {
  formData.value = { ...data }
}

const getData = async (): Promise<CloseModalOption> => {
  await formRef.value?.validate()
  return {
    ...formData.value,
  }
}

defineExpose({
  getData,
  setData,
})
</script>

<style scoped>
.action-form {
  width: 70%;
}
.btn {
  width: 100%;
}
</style>
