<template>
  <a-modal
    v-model:open="visible"
    :maskClosable="false"
    title="表单快速开始-AdvancedForm"
    class=""
    width="800px"
    @ok="confirm"
    @cancel="cancel"
    :destroyOnClose="true"
  >
    <!--功能启用-->
    <section class="func-list">
      <div class="funcTit">启用功能：</div>
      <div class="padding4px">
        <a-checkbox-group v-model:value="funcVal" :options="funcOptions" />
      </div>
    </section>
    <!-- 保存配置 -->
    <div class="section-wrap" v-if="funcVal.length > 0">
      <div class="section-tit">接口配置：</div>
      <div class="config-wrap">
        <section class="config-item" v-if="funcVal.includes('保存')">
          <div class="config-title">保存接口地址：</div>
          <div class="input-wrap">
            <a-input
              v-model:value="buttonActionConfig.option.url"
              placeholder="Http://"
            ></a-input>
            <setting-outlined class="setIcon" @click="urlSaveModal = true" />
          </div>
        </section>
        <!-- 详情请求配置 -->
        <section class="config-item mt-8" v-if="funcVal.includes('详情请求')">
          <div class="config-title">详情接口地址：</div>
          <div class="input-wrap">
            <a-input
              v-model:value="formActionConfig.option.url"
              placeholder="Http://"
            ></a-input>
            <setting-outlined class="setIcon" @click="urlSetModal = true" />
          </div>
        </section>
      </div>
    </div>

    <div class="section-wrap">
      <div class="section-tit">页头配置：</div>
      <div class="config-item">
        <div class="config-title">标题：</div>
        <div class="input-wrap">
          <a-input v-model:value="formLayoutModel.prop.title"></a-input>
        </div>
      </div>
      <AttrButton
        class="mt-8"
        label="操作按钮："
        v-model:value="formLayoutModel.prop.actionButtons"
      />
      <!-- <div class="header-status">
          <div></div>
        </div> -->
    </div>

    <div class="section-wrap">
      <div class="section-tit">内容主体配置：</div>
      <div class="config-item">
        <div class="config-title">表单栅格数：</div>
        <div class="input-wrap">
          <a-select
            v-model:value="formLayoutModel.prop.cols"
            :options="colList"
          ></a-select>
        </div>
      </div>
      <AttrModules v-model:value="formLayoutModel.prop.cardList" />
    </div>
    <!---详情接口配置-->
    <requestModal
      v-model="urlSetModal"
      @getActionData="getActionData"
      :formActionConfig="formActionConfig"
    ></requestModal>
    <!---保存接口配置-->
    <requestModal
      v-model="urlSaveModal"
      @getActionData="getButtonActionData"
      :formActionConfig="buttonActionConfig"
    ></requestModal>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, defineComponent, reactive, ref } from 'vue'
import _ from 'lodash'
import requestModal from '../RequestModal/index.vue'
import LyyNewUIFormModel from '../../../Model/static/lyy-newui-form-model'
import lyyFormLayoutModel from '../../../Model/layoutItem/lyy-form-layout-model'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import AttrButton from './attr-buttons.vue'
import AttrModules from './attr-modules.vue'
import {
  detailRequest,
  setValRequest,
  clickRequest,
} from '../../../components/Modal/modelUtils/actionModel'
defineComponent({
  name: 'basic-form-modal',
})

const props = defineProps<{
  modelValue: boolean
  types?: any
  templateData?: object
}>()
const emit = defineEmits(['update:modelValue', 'ok', 'cancel'])

const colList = [
  {
    label: '一列',
    value: 'large',
  },
  {
    label: '两列',
    value: 'middle',
  },
  {
    label: '三列',
    value: 'normal',
  },
  {
    label: '四列',
    value: 'default',
  },
]
// 详情请求弹窗控制
const urlSetModal = ref(false)
// 保存请求弹窗控制
const urlSaveModal = ref(false)

const genLyyFormLayoutModel = () => {
  const target = addAliasNameAnduuid(_.cloneDeep(lyyFormLayoutModel))
  return {
    ...target,
    prop: {
      ...target.prop,
      actionButtons: [
        { text: '保存', key: '', icon: '', authCode: '', show: '', forbid: '' },
        { text: '取消', key: '', icon: '', authCode: '', show: '', forbid: '' },
      ],
    },
  }
}

let formLayoutModel = reactive(genLyyFormLayoutModel())
// 启用功能
const funcVal = ref([])
const funcOptions = ['详情请求']
const visible = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})

const confirm = () => {
  const lyyForm = addAliasNameAnduuid(_.cloneDeep(LyyNewUIFormModel))
  lyyForm.prop.mainLayout = true
  if (funcVal.value.includes('详情请求') && formActionConfig.option.url) {
    formActionConfig.option.responseDataKey = `AdvancedForm-${lyyForm.compId}`
    let setValRequestAction = _.cloneDeep(setValRequest)
    // 请求参数配置
    formActionConfig.option.payloads.higher.push({
      key: 'id',
      value: 'route.query.id',
    })
    // 设置值配置
    setValRequestAction.option.to.dynamic.nodePath =
      lyyForm.compId + '.modelValue'
    setValRequestAction.option.from.dynamic.nodePath = `AdvancedForm-${lyyForm.compId}`
    formActionConfig.thenActions.push(setValRequestAction)
    lyyForm.actions = [formActionConfig]
  }
  for (const item of formLayoutModel.prop.cardList) {
    delete item.contentType
  }
  lyyForm.childrens.push(formLayoutModel)
  setInit()
  emit('ok', _.cloneDeep(lyyForm))
}
const cancel = () => {
  setInit()
  emit('cancel')
}
// 详情请求事件
let formActionConfig = reactive(_.cloneDeep(detailRequest))
const getActionData = (actionConfig) => {
  formActionConfig = Object.assign(formActionConfig, { ...actionConfig })
}
// 保存请求事件
let buttonActionConfig = reactive(_.cloneDeep(clickRequest))
const getButtonActionData = (actionConfig) => {
  buttonActionConfig = Object.assign(buttonActionConfig, { ...actionConfig })
}

// 重置请求参数
const restActionConfig = () => {
  // 详情请求
  formActionConfig.option.url = ''
  formActionConfig.option.interceptors = {}
  formActionConfig.option.method = 'get'
  formActionConfig.option.payloads = {
    type: 'higher',
    dynamic: {
      nodePath: '',
    },
    higher: [],
    static: '',
  }
  formActionConfig.option.headerPayloads = []
  formActionConfig.thenActions = []
  //保存请求
  buttonActionConfig.option.url = ''
  buttonActionConfig.option.interceptors = {}
  buttonActionConfig.option.method = 'post'
  buttonActionConfig.option.payloads = {
    type: 'higher',
    dynamic: {
      nodePath: '',
    },
    higher: [],
    static: '',
  }
  buttonActionConfig.option.headerPayloads = []
  buttonActionConfig.thenActions = []
}
// 重置表单数据
const setInit = () => {
  // formLayoutModel = reactive({})
  restActionConfig()
  funcVal.value.length = 0
  // funcVal.value.push('保存')
}
</script>
<style scoped lang="scss">
.func-list {
  display: flex;
  height: 40px;
  align-items: center;
}
.funcTit {
  margin-right: 24px;
}
.section-wrap {
  padding: 12px;
  border-radius: 4px;
  background-color: #f7f7f9;
  margin-bottom: 8px;
}
.config-item {
  display: flex;
  align-items: center;
}
.config-title {
  width: 100px;
  text-align: left;
  margin-right: 12px;
}
.input-wrap {
  flex: 1;
  .ant-select {
    width: 100%;
  }
  &.flex-end {
    justify-content: flex-end;
  }
  .add-btn {
    height: auto;
    padding: 0;
  }
}
.section-tit {
  margin-bottom: 8px;
  font-weight: 600;
}
.mt-8 {
  margin-top: 8px;
}

.padding4px {
  padding: 4px 0;
}
.input-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .setIcon {
    margin-left: 8px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
</style>
