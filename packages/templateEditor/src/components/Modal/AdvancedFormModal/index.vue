<template>
  <a-modal
    v-model:open="visible"
    :maskClosable="false"
    title="高级表单快速开始-AdvancedForm"
    class="Toolbar-modal"
    width="800px"
    @ok="confirm"
    @cancel="cancel"
    :destroyOnClose="true"
  >
    <!--功能启用-->
    <section>
      <div class="funcTit padding4px">启用功能</div>
      <div class="padding4px">
        <a-checkbox-group v-model:value="funcVal" :options="funcOptions" />
      </div>
    </section>
    <!-- 保存配置 -->
    <section v-if="funcVal.includes('保存')">
      <div class="funcTit padding4px">保存接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="buttonActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlSaveModal = true" />
      </div>
    </section>
    <!-- 详情请求配置 -->
    <section v-if="funcVal.includes('详情请求')">
      <div class="funcTit padding4px">详情接口地址</div>
      <div class="url-input">
        <a-input
          v-model:value="formActionConfig.option.url"
          placeholder="Http://"
        ></a-input>
        <setting-outlined class="setIcon" @click="urlSetModal = true" />
      </div>
    </section>
    <!--表单模块配置-->
    <section class="formCardList">
      <a-row class="funcTit padding4px">
        <a-col :span="20" style="font-weight: bold">表单模块</a-col>
        <a-col :span="4">
          <a-button
            size="small"
            type="primary"
            style="float: right"
            @click="addCard"
          >
            新增模块
          </a-button>
        </a-col>
      </a-row>
      <template v-for="(card, index) in formCardList" :key="'card' + card.id">
        <div class="model-item">
          <span
            class="del-btn"
            @click="delCard(index)"
            v-if="formCardList.length > 1"
          >
            删除
          </span>
          <a-row class="funcTit padding4px">
            <a-col :span="2" class="tit" :offset="1">标题：</a-col>
            <a-col :span="9">
              <a-input v-model:value="formCardList[index].title"></a-input>
            </a-col>
            <a-col :span="2" class="tit" :offset="1">栅格：</a-col>
            <a-col :span="9">
              <a-select
                v-model:value="formCardList[index].span"
                :options="colList"
                style="width: 100%"
              ></a-select>
            </a-col>
          </a-row>
          <formItemList :ref="setCardRef" :id="card.id" />
        </div>
      </template>
    </section>
    <!---表格-->
    <tableSet
      v-if="funcVal.includes('编辑表格')"
      :types="types"
      ref="TableRef"
      :operatebtns="operatebtns"
    ></tableSet>
    <!---详情接口配置-->
    <requestModal
      v-model="urlSetModal"
      @getActionData="getActionData"
      :formActionConfig="formActionConfig"
    ></requestModal>
    <!---保存接口配置-->
    <requestModal
      v-model="urlSaveModal"
      @getActionData="getButtonActionData"
      :formActionConfig="buttonActionConfig"
    ></requestModal>
  </a-modal>
</template>

<script lang="ts" setup>
import { computed, defineComponent, onMounted, reactive, ref } from 'vue'
import _ from 'lodash'
import formItemList from '../BasicForm/formItemList.vue'
import requestModal from '../RequestModal/index.vue'
import tableSet from '../AdvancedForm/edit-table.vue'
import LyyAdvancedFormModel from '../../../Model/static/lyy-advanced-form-model'
import lyyCardModel from '../../../Model/layoutItem/lyy-card-model'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
import { genModal } from '../CRUD/model/createModel'
import { genGrid } from '../../../components/Modal/modelUtils/genModel'
import { gridSize } from '../../../Pages/Edit/Right/AttrConfig/config/SelectOptions'
import {
  detailRequest,
  setValRequest,
  clickRequest,
  validateRequest,
  closeModalAction,
} from '../../../components/Modal/modelUtils/actionModel'
import LyyCanvas from '@/context/canvas'
defineComponent({
  name: 'basic-form-modal',
})
const emit = defineEmits(['update:modelValue', 'ok', 'cancel'])
const defaultCard = {
  span: 6,
  title: '',
  id: 0,
}
const colList = [
  { value: 24, label: '一列' },
  { value: 12, label: '两列' },
  { value: 8, label: '三列' },
  { value: 6, label: '四列' },
]
const TableRef = ref(null)
//表单模块
const formCardList = ref([])
// 详情请求弹窗控制
const urlSetModal = ref(false)
// 保存请求弹窗控制
const urlSaveModal = ref(false)
let operatebtns: object[] = reactive([])
// 启用功能
const funcVal = ref(['保存'])
const funcOptions = ['保存', '详情请求', '编辑表格']
const visible = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})
const props = defineProps<{
  modelValue: boolean
  types?: any
  templateData?: object
}>()

let children: object[] = []
// 设置弹窗事件
const setModalAction = (modalFormId, modalId) => {
  // 加校验事件
  let validateRequestAction = _.cloneDeep(validateRequest)
  validateRequestAction.event = 'confirm'
  validateRequestAction.option.targetId = modalFormId
  // 加保存事件
  let postRequest = _.cloneDeep(clickRequest)
  postRequest.event = 'confirm'
  postRequest.option.url = ''
  postRequest.option.payloads.dynamic.nodePath = modalFormId + '.modelValue'
  postRequest.option.payloads.type = 'dynamic'
  //加关闭弹窗事件
  let closeModal = _.cloneDeep(closeModalAction)
  closeModal.option.targetId = modalId
  postRequest.thenActions.push(closeModal)
  validateRequestAction.thenActions.push(postRequest)
  return [validateRequestAction]
}
// 获取编辑表格
const genTable = () => {
  if (funcVal.value.includes('编辑表格')) {
    // 获取编辑表格配置
    const tableComp = TableRef.value.tableHandle()
    const lyyCard = addAliasNameAnduuid(_.cloneDeep(lyyCardModel))
    lyyCard.prop.title = '表格'
    lyyCard.childrens.push(tableComp)
    return [lyyCard]
  } else {
    return []
  }
}
// 获取表单模块
const genFormCard = () => {
  let list = []
  for (const [i, e] of formCardList.value.entries()) {
    const lyyCard = addAliasNameAnduuid(_.cloneDeep(lyyCardModel))
    lyyCard.prop.title = e.title
    lyyCard.style = {
      'margin-bottom': '20px',
    }
    const formList = formItemListRefs.value[e.id].listHandle()
    let lyyGrid = genGrid({ col: { span: e.span } })
    lyyGrid.prop.children.push(...formList)
    lyyCard.childrens.push(lyyGrid)
    list.push(lyyCard)
  }
  return list
}
const formItemListRefs = ref({})
const setCardRef = (el: any) => {
  if (el) {
    formItemListRefs.value[el.id] = el
  }
}
const confirm = () => {
  const lyyForm = addAliasNameAnduuid(_.cloneDeep(LyyAdvancedFormModel))
  lyyForm.prop.mainLayout = true
  if (funcVal.value.includes('详情请求') && formActionConfig.option.url) {
    formActionConfig.option.responseDataKey = `AdvancedForm-${lyyForm.compId}`
    let setValRequestAction = _.cloneDeep(setValRequest)
    // 请求参数配置
    formActionConfig.option.payloads.higher.push({
      key: 'id',
      value: 'route.query.id',
    })
    // 设置值配置
    setValRequestAction.option.to.dynamic.nodePath =
      lyyForm.compId + '.modelValue'
    setValRequestAction.option.from.dynamic.nodePath = `AdvancedForm-${lyyForm.compId}`
    formActionConfig.thenActions.push(setValRequestAction)
    lyyForm.actions = [formActionConfig]
  }
  lyyForm.childrens.push(...genFormCard(), ...genTable())
  let lyyModal = genModal({
    title: '弹窗',
  })
  lyyModal.compNameCn = '高级表单弹窗'
  lyyModal.actions = setModalAction(lyyForm.compId, lyyModal.compId)
  lyyForm.childrens.push(...children)
  lyyModal.childrens.push(_.cloneDeep(lyyForm))
  children = []
  setInit()
  emit('ok', _.cloneDeep(lyyModal))
}
const cancel = () => {
  emit('cancel')
}
// 详情请求事件
let formActionConfig = reactive(_.cloneDeep(detailRequest))
const getActionData = (actionConfig) => {
  formActionConfig = Object.assign(formActionConfig, { ...actionConfig })
}
// 保存请求事件
let buttonActionConfig = reactive(_.cloneDeep(clickRequest))
const getButtonActionData = (actionConfig) => {
  buttonActionConfig = Object.assign(buttonActionConfig, { ...actionConfig })
}
const addCard = () => {
  let card = _.cloneDeep(defaultCard)
  card.id = Math.random()
  formCardList.value.push(card)
}
addCard()
const delCard = (i) => {
  formCardList.value.splice(i, 1)
}

// 重置请求参数
const restActionConfig = () => {
  // 详情请求
  formActionConfig.option.url = ''
  formActionConfig.option.interceptors = {}
  formActionConfig.option.method = 'get'
  formActionConfig.option.payloads = {
    type: 'higher',
    dynamic: {
      nodePath: '',
    },
    higher: [],
    static: '',
  }
  formActionConfig.option.headerPayloads = []
  formActionConfig.thenActions = []
  //保存请求
  buttonActionConfig.option.url = ''
  buttonActionConfig.option.interceptors = {}
  buttonActionConfig.option.method = 'post'
  buttonActionConfig.option.payloads = {
    type: 'higher',
    dynamic: {
      nodePath: '',
    },
    higher: [],
    static: '',
  }
  buttonActionConfig.option.headerPayloads = []
  buttonActionConfig.thenActions = []
}
// 重置表单数据
const setInit = () => {
  children.length = 0
  formCardList.value.length = 0
  restActionConfig()
  funcVal.value.length = 0
  funcVal.value.push('保存')
  formItemListRefs.value = {}
  addCard()
}
</script>
<style scoped lang="scss">
.formCardList {
  .model-item {
    padding: 8px;
    border: 1px solid #ebeaea;
    border-radius: 4px;
    margin-bottom: 4px;
    .tit {
      line-height: 32px;
    }
    &:last-child {
      margin-bottom: 0;
    }
    position: relative;
    &:hover {
      .del-btn {
        display: block;
      }
    }
    .del-btn {
      position: absolute;
      right: 0;
      top: 0;
      z-index: 9;
      background: #ff4d4f;
      color: #fff;
      font-size: 12px;
      padding: 0 10px;
      border-radius: 0 0 0 8px;
      display: none;
      cursor: pointer;
    }
  }
}
.crud-modal {
  .ant-modal-body {
    padding: 8px 24px;
  }
}
.btn-box {
  padding-top: 8px;
  color: #40a9ff;
  .btn {
    padding-left: 4px;
  }
}
.padding4px {
  padding: 4px 0;
}
.url-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .setIcon {
    margin-left: 8px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
</style>
