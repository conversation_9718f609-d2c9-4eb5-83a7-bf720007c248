<script lang="ts" setup>
import { computed, defineComponent, reactive, ref } from 'vue'
import InterfaceConfig from './interface-config.vue'
import { IrequestConfig } from './types'
import _ from 'lodash'
import actionCrudDefaultModel from '@/Model/static/action-crud-default-model'
import HttpConfig from './http-config'

defineComponent({
  name: 'request-modal',
})
const props = defineProps<{
  modelValue: boolean
  formActionConfig: object
}>()
const requestConfig = props.formActionConfig
const activeKey = ref('1')
const emit = defineEmits(['update:modelValue', 'getActionData'])
const visible = computed({
  get() {
    return props.modelValue
  },
  set(nVal) {
    emit('update:modelValue', nVal)
  },
})
const actionSet = (requestConfig) => {
  const actionModel = _.cloneDeep(actionCrudDefaultModel)
  actionModel.option.url = requestConfig.url
  actionModel.option.method = requestConfig.method
  actionModel.condition = requestConfig.condition
  emit('getActionData', actionModel)
}
const confirm = () => {
  if (requestConfig?.url) {
    console.log(requestConfig, 8989)
    actionSet(requestConfig)
  }
  emit('update:modelValue', false)
}
</script>

<template>
  <a-modal v-model:open="visible" title="高级配置" @ok="confirm" width="900px">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="接口设置">
        <InterfaceConfig :requestConfig="requestConfig"></InterfaceConfig>
      </a-tab-pane>
      <a-tab-pane key="2" tab="http配置">
        <HttpConfig :requestConfig="requestConfig"></HttpConfig>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
