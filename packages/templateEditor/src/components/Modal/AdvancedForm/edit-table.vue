<template>
  <section>
    <div class="title">编辑表格字段设置</div>
    <div class="table-area">
      <div class="drag-area">
        <Draggable
          :list="lists"
          class="list-group"
          handle=".handle"
          group="people"
          item-key="name"
          @start="dragStart"
          @end="dragEnd"
        >
          <template #item="{ element, index }">
            <div class="list-box">
              <div class="item shrink">
                <a-tooltip>
                  <template #title>拖拽排序</template>
                  <unordered-list-outlined class="handle" />
                </a-tooltip>
              </div>
              <div class="item grow">
                <a-input
                  v-model:value="element.title"
                  placeholder="列标题"
                ></a-input>
              </div>
              <div class="item grow">
                <a-input
                  v-model:value="element.dataIndex"
                  placeholder="绑定字段名"
                ></a-input>
              </div>
              <div class="item grow">
                <a-select
                  v-model:value="element.compType"
                  class="select"
                  placeholder="纯文本"
                  size="middle"
                  :options="options"
                ></a-select>
              </div>
              <close-outlined class="item shrink" @click="removeAt(index)" />
            </div>
          </template>
        </Draggable>
      </div>
      <div class="btn-box">
        <plus-outlined @click="addRow" />
        <a-button type="link" class="btn" @click="addRow">新增一列</a-button>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { computed, defineComponent, reactive, ref } from 'vue'
import Draggable from 'vuedraggable'
import _ from 'lodash'
import { getCurrentTime } from '@leyaoyao/libs/utils/get-date-range'
import lyyEditTableModel from '../../../Model/formItem/lyy-edit-table-model'
import CompFormItem from '../../../Model/formItem'
import { addAliasNameAnduuid } from '@/utils/addAliasName'
const props = defineProps<{
  types?: any
  operatebtns: string[]
}>()
const defaultRow = {
  name: '假数据',
}
const defaultCol = {
  dataIndex: '',
  title: '列信息',
  comp: null,
  compType: null,
  type: 'text',
}

let lists = reactive([_.cloneDeep(defaultCol)])
const removeAt = (idx) => {
  lists.splice(idx, 1)
}
const addRow = () => {
  const col = _.cloneDeep(defaultCol)
  col.dataIndex = ''
  col.title = ''
  lists.push(col)
}
const isDraging = ref(false)
const dragStart = () => {
  isDraging.value = true
}
const dragEnd = () => {
  isDraging.value = false
}
const defaultOptions = [
  {
    value: 'lyy-text-input',
    label: '文本框',
  },
  {
    value: 'lyy-select',
    label: '下拉框',
  },
  {
    value: 'lyy-date-range',
    label: '日期范围',
  },
  {
    value: 'lyy-date-picker',
    label: '日期',
  },
  {
    value: 'lyy-radio',
    label: '单选框',
  },
]
const options = computed(() => {
  return props.types || defaultOptions
})
const tableHandle = () => {
  const table = addAliasNameAnduuid(_.cloneDeep(lyyEditTableModel))
  // table.prop.columns = []
  let hasOperate = false
  for (const item of lists) {
    if (item.type == 'datetime') {
      defaultRow[item.dataIndex] = getCurrentTime()
    } else if (item.type == 'operation') {
      hasOperate = true
      item.childrens = props.operatebtns ?? []
    } else {
    }
    if (item.compType) {
      item.comp = addAliasNameAnduuid(
        _.cloneDeep(CompFormItem(`./${item.compType}-model.ts`).default),
      )
    }
    delete item.compType
  }
  // 如果选择了操作栏删除、查看之类的，则需要加上操作栏
  if (!hasOperate && props.operatebtns?.length) {
    const col = _.cloneDeep(defaultCol)
    col.dataIndex = ''
    col.title = '操作栏'
    col.type = 'operation'
    col.childrens = props.operatebtns ?? []
    lists.push(col)
  }
  table.prop.columns = lists
  table.prop.datasource = [defaultRow]
  return table
}
defineComponent({
  name: 'table-index',
})
defineExpose({
  tableHandle,
  setRestLists: () => {
    lists.length = 1
  },
})
</script>

<style scoped lang="scss">
.table-area {
  padding: 8px;
  border: 1px solid #ebeaea;
  border-radius: 4px;
}
.title {
  padding: 4px 0;
}
.list-box {
  display: flex;
  padding: 4px 0;
  align-items: center;
  justify-content: space-around;
  .item {
    padding-left: 8px;
    &:first-child {
      padding-left: 0;
    }
    .select {
      width: 100%;
    }
  }
  .grow {
    flex-grow: 1;
    width: 30%;
  }
  .shrink {
    flex-shrink: 0;
  }
}
</style>
