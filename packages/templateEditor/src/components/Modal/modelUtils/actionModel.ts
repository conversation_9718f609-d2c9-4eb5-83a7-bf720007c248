/* eslint-disable unicorn/filename-case */
export const detailRequest: any = {
  event: 'mounted',
  action: 'request',
  option: {
    url: '',
    method: 'get',
    payloads: {
      type: 'higher',
      dynamic: {
        nodePath: '',
      },
      higher: [],
      static: '',
    },
    responseDataKey: '',
    customOption: {
      loading: true,
    },
    headerPayloads: [],
    interceptors: {
      // responseInterceptor: () => {},
      // requestInterceptor: () => {},
    },
  },
  condition: {},
  thenActions: [],
}
export const setValRequest: any = {
  event: 'mounted',
  action: 'setNewValue',
  option: {
    to: {
      type: 'dynamic',
      dynamic: {
        nodePath: '',
      },
      static: '',
    },
    from: {
      dynamic: {
        nodePath: '',
      },
    },
  },
  thenActions: [],
}
export const clickRequest: any = {
  event: 'click',
  action: 'request',
  option: {
    url: '',
    method: 'post',
    payloads: {
      type: 'static',
      dynamic: {
        nodePath: '',
      },
      higher: [],
      static: '',
    },
    responseDataKey: '',
    headerPayloads: [],
    interceptors: {
      // responseInterceptor: () => {},
      // requestInterceptor: () => {},
    },
  },
  condition: {},
  thenActions: [],
}
export const delRequest: any = {
  event: 'click',
  action: 'request',
  option: {
    url: '',
    method: 'delete',
    payloads: {
      type: 'static',
      dynamic: {
        nodePath: '',
      },
      higher: [],
      static: '',
    },
    responseDataKey: '',
    headerPayloads: [],
    interceptors: {
      // responseInterceptor: () => {},
      // requestInterceptor: () => {},
    },
  },
  condition: {},
  thenActions: [],
}
export const validateRequest: any = {
  event: 'click',
  action: 'validate',
  option: {
    targetId: '',
  },
  condition: {},
  thenActions: [],
}
export const closeModalAction: any = {
  event: 'confirm',
  action: 'closeModal',
  option: {
    targetId: '',
  },
  condition: {},
  thenActions: [],
}
export const broadcastAction: any = {
  event: 'confirm',
  action: 'broadcast',
  option: {
    targetId: '',
    event: 'mounted',
  },
  condition: {},
  thenActions: [],
}
export const depAction: any = {
  event: 'confirm',
  action: 'depAction',
  option: {
    targetId: '',
    action: 'find',
  },
  delay: '',
  thenActions: [],
}
export const goBack: any = {
  event: 'click',
  action: 'linkto',
  option: {
    url: '',
    go: -1,
    tab: '_self',
    mode: 'query',
    payloads: [],
  },
  condition: {},
  thenActions: [],
}
export const openConfirmAction: any = {
  event: 'click',
  action: 'openConfirm',
  option: {
    title: '温馨提示',
    content: '',
    styleType: 'modal',
    type: 'confirm',
    placement: 'topRight',
    okText: '确定',
    cancelText: '取消',
    duration: 3,
    payloads: [],
  },
  condition: {},
  thenActions: [],
}
