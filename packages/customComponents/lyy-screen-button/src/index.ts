import { defineCustomComponent } from '@leyaoyao/libs'
import myComponent from './index.vue'

export default defineCustomComponent({
  component: myComponent,

  module: {
    compName: 'lyy-screen-button',
    compNameCn: '全屏按钮',
    compId: null,
    prop: {
      domSelect: '',
    },
    modelValue: {},
  },

  propsDictionary: {
    domSelect: {
      name: '容器',
      type: 'text-input',
    },
  },

  emits: ['click'],
})
