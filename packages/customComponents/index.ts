import LyyNewEcharts from './lyy-echarts/src/index.vue'
import LyyCardStatistic from './lyy-card-statistic/src/index.vue'
import LyyDataStatistic from './lyy-data-statistic/src/index.vue'
import LyyLayoutResponsive from './lyy-layout-responsive/src/index.vue'
import LyySortButton from './lyy-sort-button/src/index.vue'
import LyyStatisticItem from './lyy-statistic-item/src/index.vue'
import LyyScreenButton from './lyy-screen-button/src/index.vue'
// import onRulesHoc from '../lyyPcRender/src/hoc/onRules-hoc'
// import disableHoc from '../lyyPcRender/src/hoc/disable-hoc'

const comp = {
  LyyNewEcharts: LyyNewEcharts,
  LyyCardStatistic: LyyCardStatistic,
  LyyDataStatistic: LyyDataStatistic,
  LyyLayoutResponsive: LyyLayoutResponsive,
  LyySortButton: LyySortButton,
  LyyStatisticItem: LyyStatisticItem,
  LyyScreenButton: LyyScreenButton,
}
// for (const key of Object.keys(comp)) {
//   comp[key] = onRulesHoc(disableHoc(comp[key]))
// }

export const customComponents = comp
