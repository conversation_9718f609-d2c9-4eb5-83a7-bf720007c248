<template>
  <div class="lyy-component">
    <template v-if="prop.type === 'float'">
      <div class="float-wrapper">
        <div
          v-for="item in options"
          :key="item.key"
          class="lyy-sort-button"
          :style="floatStyle"
          @click.stop="onToggle(item)"
        >
          <div class="text" :style="textStyle(item)">{{ item.name }}</div>
          <div class="icon-wrapper" v-if="prop.status !== 'orderless'">
            <CaretUpOutlined :style="ascStyle(item)" />
            <CaretDownOutlined :style="descStyle(item)" />
          </div>
        </div>
      </div>
    </template>
    <a-row
      v-else
      :align="prop.align"
      :justify="prop.justify"
      :gutter="[prop.rowGutter, prop.lineGutter]"
    >
      <a-col
        :span="prop.span"
        :flex="prop.flex"
        v-for="item in options"
        :key="item.key"
      >
        <div class="lyy-sort-button" @click.stop="onToggle(item)">
          <div class="text" :style="textStyle(item)">{{ item.name }}</div>
          <div class="icon-wrapper" v-if="prop.status !== 'orderless'">
            <CaretUpOutlined :style="ascStyle(item)" />
            <CaretDownOutlined :style="descStyle(item)" />
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, toRefs, computed } from 'vue'
// 获取主题色
import { theme } from 'ant-design-vue'
import { merge } from 'lodash'
import { ACTION, UPDATE_MODELVALUE } from '@leyaoyao/libs/constants/action-type'
import { CLICK } from '@leyaoyao/libs/constants/event-type'
import { useDep, useTpl } from '@leyaoyao/libs'
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue'
// 获取主题色
const { useToken } = theme
export interface Props {
  prop?: {
    options: string | object
    type: string
    float: string
    margin: string
    align: string
    justify: string
    flex: string | number
    span: string | number
    rowGutter: string
    lineGutter: string
    textStyle: object
    iconStyle: object
    activeStyle: object
    active: string
    status: string
  }
  modelValue: {
    active: string
    status: string
  }
  compId: string
}
const themeColor = useToken()
const { token } = themeColor

const emits = defineEmits([UPDATE_MODELVALUE, ACTION])

const props = withDefaults(defineProps<Props>(), {
  prop: () => ({
    options: '',
    type: 'flex',
    float: 'left',
    margin: '',
    align: 'center',
    justify: 'center',
    flex: '1',
    span: '2',
    rowGutter: '10',
    lineGutter: '10',
    textStyle: {},
    iconStyle: {},
    activeStyle: {},
    active: '',
    status: '',
  }),
  modelValue: () => ({
    active: '',
    status: '',
  }),
  compId: '',
})
/**
 * 浮动
 */
const floatStyle = computed(() => {
  const { float, margin } = prop.value
  return merge({ float }, { [`margin-${float}`]: `${margin}` })
})
/**
 * 弹性
 */
// 转换
const transferTpl = (target: string) => {
  return useTpl(target, {}, 'object')
}
const options = computed(() => {
  if (prop.value.options) {
    const _options =
      typeof prop.value.options === 'object'
        ? JSON.stringify(prop.value.options)
        : prop.value.options
    const data = transferTpl(_options)
    return typeof data === 'object' ? data : eval(`(${data})`)
  }
  return []
})
// 文本样式
const textStyle = (item) => {
  const res =
    selected.value.active === item.key
      ? merge({}, prop.value.textStyle, { color: token.value.colorPrimary })
      : prop.value.textStyle
  return res
}
// 正序
const ascStyle = (item) => {
  return selected.value.active === item.key && props.modelValue.status === 'asc'
    ? merge({}, prop.value.iconStyle, { color: token.value.colorPrimary })
    : prop.value.iconStyle
}
// 反序
const descStyle = (item) => {
  return selected.value.active === item.key &&
    props.modelValue.status === 'desc'
    ? merge({}, prop.value.iconStyle, { color: token.value.colorPrimary })
    : prop.value.iconStyle
}

const { prop } = toRefs(props)
// 选中值
const selected = computed(() => {
  return merge(
    {
      active: prop.value.active,
      status: prop.value.status,
    },
    props.modelValue,
  )
})
// 切换
const onToggle = (item) => {
  if (props.prop.status === 'orderless') {
    const value = {
      status: null,
      active: item.key,
    }
    emits(UPDATE_MODELVALUE, value)
    emits(ACTION, { event: CLICK })
    return
  }
  const value = {
    status:
      props.modelValue.active === item.key && props.modelValue.status === 'asc'
        ? 'desc'
        : 'asc',
    active: item.key,
  }
  emits(UPDATE_MODELVALUE, value)
  emits(ACTION, { event: CLICK })
}
// 抛出的事件
const actionsSet = {
  onToggle,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

defineComponent({
  name: 'lyy-sort-button',
})
</script>

<style lang="less" scoped>
.float-wrapper {
  width: 100%;
  clear: both;
  overflow: hidden;
}
.lyy-sort-button {
  padding: 0 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-right: 28px;
  .icon-wrapper {
    display: flex;
    flex-direction: column;
    font-size: 8px;
    margin-left: 5px;
  }
}
</style>
