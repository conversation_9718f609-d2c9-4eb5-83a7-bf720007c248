import { defineCustomComponent } from '@leyaoyao/libs'
import myComponent from './index.vue'

export default defineCustomComponent({
  component: myComponent,

  module: {
    compName: 'lyy-button-plush',
    compNameCn: '测试按钮',
    compId: null,
    prop: {
      title: '测试按钮',
      radio: false,
      arrr: [],
    },
  },

  propsDictionary: {
    title: {
      name: '标题',
      type: 'text-input',
    },
    radio: {
      name: '标题sss',
      type: 'attr-radio',
    },
  },

  emits: ['click'],
})
