<template>
  <div
    class="lyy-container lyy-card-statistic-box lyy-main-card"
    :style="props?.style"
  >
    <div class="lyy-statistic-top">
      <div class="lyy-top-left">
        <div class="lyy-title">
          <span>{{ statisticNumber.siteName || '未知' }}</span>
          <span class="icon1" v-if="prop.iconShow"></span>
          <span class="icon2" v-if="prop.iconShow"></span>
        </div>
        <div class="lyy-top-address">
          <img src="./img/ad.png" class="top-img-icon" />
          <!-- <EnvironmentOutlined style="font-size: small" /> -->
          <span class="address">{{ statisticNumber.address }}</span>
        </div>
      </div>
      <div class="lyy-top-right" v-if="prop.rightShow">
        <div class="device-num">
          <img src="./img/dv.png" class="top-img-icon" />
          <!-- <LaptopOutlined style="font-size: small" /> -->
          <span class="num">{{ statisticNumber.deviceNum || 0 }}台</span>
        </div>
        <div class="percent-box">
          <span class="title">
            环比 {{ filterPercent(statisticNumber.deviceNumPercent || 0) }}
          </span>
          <img
            class="img-icon"
            v-if="
              statisticNumber?.percent && Number(statisticNumber?.percent) > 0
            "
            src="./img/up.png"
          />
          <img
            class="img-icon"
            v-else-if="
              statisticNumber?.percent && Number(statisticNumber?.percent) < 0
            "
            src="./img/down.png"
          />
          <img v-else class="img-icon" src="./img/ping.png" />
          <!-- <span class="up" v-else>平</span> -->
        </div>
      </div>
    </div>
    <div class="lyy-bottom">
      <div class="lyy-bottom-center">
        <div class="title">{{ prop.title }}</div>
        <div class="total-income">{{ statisticNumber?.total || 0 }}</div>
        <div class="percent-item" v-if="prop.showArrow">
          <span class="percent-item-title">
            {{ subTitleStr
            }}{{ filterPercentText(statisticNumber?.percent || 0) }}
          </span>
          <span class="percent">
            {{ filterPercent(statisticNumber?.percent || 0) }}
          </span>
          <img
            class="img-icon"
            v-if="
              statisticNumber?.percent && Number(statisticNumber?.percent) > 0
            "
            src="./img/up.png"
          />
          <img
            class="img-icon"
            v-else-if="
              statisticNumber?.percent && Number(statisticNumber?.percent) < 0
            "
            src="./img/down.png"
          />
          <img class="img-icon" src="./img/ping.png" v-else />
        </div>
      </div>
      <slot></slot>
      <!-- <pc-render-template :elements="props.childrens" :rowData="props.rowData"
                :dataItem="statisticNumber"></pc-render-template> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useTpl } from '@leyaoyao/libs'
import { computed, defineComponent, toRefs, onMounted, inject } from 'vue'
import { LaptopOutlined, EnvironmentOutlined } from '@ant-design/icons-vue'
import { ACTION } from '@leyaoyao/libs/constants/action-type'
import { BEFORE_MOUNT, MOUNTED } from '@leyaoyao/libs/constants/event-type'

interface ItemListType {
  number: number
  percent: number | string
}

interface StaticNum {
  total: number
  number: number
  percent: number | string
  deviceNum: number
  deviceNumPercent: number | string
  address: string
  groupName: string
}

export interface PropsType {
  prop: {
    title: string
    color: string
    prefix?: string
    showArrow?: boolean
    subTitle: string
    options: object
    statisticNumber: StaticNum
    extraList?: IElement[]
    siteName: string
    address: string
    deviceNum: number
    deviceNumPercent: string
    total: number
    percent: string
    rightShow: boolean
    iconShow: boolean
  }
  // rowData?: any
  style?: object
  childrens: any
}
const props = defineProps<PropsType>()
const rowData = inject('currentItem')
const { prop } = toRefs(props)
const statisticNumber = computed(() => {
  if (rowData) {
    const obj: any = {}
    obj.deviceNum = rowData[props.prop.deviceNum]
    obj.address = rowData[props.prop.address]
    obj.siteName = rowData[props.prop.siteName]
    obj.deviceNumPercent = rowData[props.prop.deviceNumPercent]
    obj.total = rowData[props.prop.total]
    obj.percent = rowData[props.prop.percent]
    return obj
  }
  return props.prop.statisticNumber
})
const filterPercent = (num) => {
  const percentage = Math.abs(Number(num)) * 100
  return Number.isInteger(percentage)
    ? Math.floor(percentage) + '%'
    : percentage.toFixed(2) + '%'
}
const filterPercentText = (num) => {
  if (num && Number(num) > 0) {
    return '「上升」'
  } else if (num && Number(num) < 0) {
    return '「下降」'
  } else {
    return '「持平」'
  }
}
// 描述信息
const subTitleStr = computed(() => {
  return useTpl(props.prop?.subTitle, {}, 'string')
})
const emits = defineEmits([ACTION])
onMounted(() => {
  emits(ACTION, { event: MOUNTED })
})
defineComponent({
  name: 'lyy-card-statistic',
})
</script>

<style lang="less" scoped>
@import './index.less';
</style>
