import { defineCustomComponent } from '@leyaoyao/libs'
import myComponent from './index.vue'

export default defineCustomComponent({
  component: myComponent,

  module: {
    compName: 'lyy-layout-responsive',
    compNameCn: '响应式布局',
    compId: null,
    childrens: [],
    prop: {
      xxxlCol: '6',
      xxlCol: '4',
      xlCol: '3',
      lgCol: '3',
      mdCol: '2',
      smCol: '1',
      xsCol: '1',
      lineGutter:'16',
      rowGutter:'16',
      data:'',
      showArrow:true,
      ghost:true,
      bordered: true,
    },
    style: {},
    modelValue:{
      datasource: [{}]
    }
  },

  propsDictionary: {
    showArrow: {
      name: '是否展示圆角',
      type: 'attr-radio',
    },
    ghost: {
      name: '是否展示背景',
      type: 'attr-radio',
    },
    bordered: {
      name: '是否展示边框',
      type: 'attr-radio',
    },
    data:{
      name: '数据区',
      type: 'text-input',
    },
    xxxlCol: {
      name: '大于等于2000px展示的列数',
      type: 'text-input',
    },
    xxlCol: {
      name: '大于等于1600px展示的列数',
      type: 'text-input',
    },
    xlCol: {
      name: '大于等于1200px展示的列数',
      type: 'text-input',
    },
    lgCol: {
      name: '大于等于992px展示的列数',
      type: 'text-input',
    },
    mdCol: {
      name: '大于等于768px展示的列数',
      type: 'text-input',
    },
    smCol: {
      name: '大于等于576px展示的列数',
      type: 'text-input',
    },
    xsCol: {
      name: '小于576px展示的列数',
      type: 'text-input',
    },
    rowGutter: {
      name: '每列之间的间隔',
      type: 'text-input',
    },
    lineGutter: {
      name: '每行之间的间隔',
      type: 'text-input',
    },
  },

  emits: ['click','mounted'],
})
