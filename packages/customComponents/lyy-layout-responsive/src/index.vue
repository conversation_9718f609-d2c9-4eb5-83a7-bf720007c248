<template>
  <div
    class="lyy-container lyy-layout-responsive"
    :style="props?.style"
    :class="[
      prop.ghost ? 'lyy-layout-responsive-bg' : '',
      prop.showArrow ? 'lyy-layout-responsive-br' : '',
      prop.bordered ? 'lyy-layout-responsive-bordered' : '',
    ]"
  >
    <a-row :wrap="true" :gutter="[row<PERSON><PERSON>, lineGutter]">
      <a-col
        :class="`ant-col-xxxl-${xxxl}`"
        v-for="(currentItem, index) in dataList"
        :key="prop.rowKey ? prop.rowKey : currentItem.lyyKeyNumber"
        :xs="xs"
        :sm="sm"
        :md="md"
        :lg="lg"
        :xl="xl"
        :xxl="xxl"
        :xxxl="xxxl"
      >
        <Item :currentItem="currentItem">
          <slot></slot>
        </Item>
        <!-- <pc-render-template :elements="props.childrens" :rowData="currentItem"></pc-render-template> -->
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, toRefs, onMounted, computed } from 'vue'
import { useTpl } from '@leyaoyao/libs'
import { ACTION } from '@leyaoyao/libs/constants/action-type'
import { BEFORE_MOUNT, MOUNTED } from '@leyaoyao/libs/constants/event-type'
import Item from './item.vue'

// provide('rowData', currentItem);

export interface PropsType {
  prop: {
    xxxlCol: string
    xxlCol: string
    xlCol: string
    lgCol?: string
    mdCol?: string
    smCol: string
    xsCol: string
    lineGutter: string
    rowGutter: string
    data: any
    options: string
    showArrow: boolean
    ghost: boolean
    bordered: boolean
    datasource?: object[]
    rowKey: string
  }
  childrens: any
  style: object
  modelValue?: {
    datasource?: object[]
  }
}

const props = defineProps<PropsType>()

const { prop, modelValue } = toRefs(props)
const dataList = computed(() => {
  if (props.prop.data) {
    const list = useTpl(props.prop.data, {}, 'object')
    const arr =
      list && !prop.value.rowKey
        ? list.map((e, i) => {
            return { ...e, lyyKeyNumber: Math.random() + i }
          })
        : list
    return arr
  }
  if (props.modelValue?.datasource?.length) {
    return props.modelValue.datasource
  }
  return [{}]
})

const xs = computed(() => {
  if (props.prop.xsCol) {
    const xs = 24 / Number(props.prop.xsCol)
    return xs
  }
  return 24
})

const sm = computed(() => {
  if (props.prop.smCol) {
    const sm = 24 / Number(props.prop.smCol)
    return sm
  }
  return 24
})

const md = computed(() => {
  if (props.prop.mdCol) {
    const md = 24 / Number(props.prop.mdCol)
    return md
  }
  return 12
})

const lg = computed(() => {
  if (props.prop.lgCol) {
    const lg = 24 / Number(props.prop.lgCol)
    return lg
  }
  return 8
})

const xl = computed(() => {
  if (props.prop.xlCol) {
    const xl = 24 / Number(props.prop.xlCol)
    return xl
  }
  return 8
})

const xxl = computed(() => {
  if (props.prop.xxlCol) {
    console.log(
      '🚀 ~ file: index.vue:101 ~ xxl ~ props.prop.xsCol:',
      props.prop.xsCol,
    )
    const xxl = 24 / Number(props.prop.xxlCol)
    return xxl
  }
  return 6
})
const xxxl = computed(() => {
  if (props.prop.xxlCol) {
    console.log(
      '🚀 ~ file: index.vue:101 ~ xxl ~ props.prop.xsCol:',
      props.prop.xsCol,
    )
    const xxxl = 24 / Number(props.prop.xxxlCol)
    return xxxl
  }
  return 4
})
const lineGutter = computed(() => {
  // if (props.prop.xsCol) {
  //     const xs = 24 / Number(props.prop.xsCol)
  //     return xs
  // }
  return Number(props.prop.lineGutter)
})
const rowGutter = computed(() => {
  // if (props.prop.xsCol) {
  //     const xs = 24 / Number(props.prop.xsCol)
  //     return xs
  // }
  return Number(props.prop.rowGutter)
})

const emits = defineEmits([ACTION])
onMounted(() => {
  emits(ACTION, { event: MOUNTED })
})
defineComponent({
  name: 'lyy-statistic-item',
})
</script>
<style lang="less" scoped>
@import './index.less';
</style>
