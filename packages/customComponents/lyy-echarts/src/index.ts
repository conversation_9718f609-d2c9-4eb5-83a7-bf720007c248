import { defineCustomComponent } from '@leyaoyao/libs'
import myComponent from './index.vue'

export interface style {
  [key: string]: string | number
}

type DATA = {
  name: string
  value: number
}

export interface Data {
  key?: string
  value: Array<DATA>
}

export default defineCustomComponent({
  component: myComponent,

  module: {
    compName: 'lyy-new-echarts',
    compNameCn: '图表',
    compId: null,
    prop: {
      // 响应式开关
      reactive: false,
      // 自定义样式
      style: {
        width: '300px',
        height: '300px',
      },
      // 图表类型
      chartType: 'bar',
      // 图表类型选项
      chartTypeOptions: [
        { value: 'bar', text: '柱状图' },
        { value: 'line', text: '折线图' },
        { value: 'pie', text: '饼图' },
        { value: 'scatter', text: '散点图' },
        { value: 'custom', text: '自定义' },
      ],
      // 标题
      title: '图表标题',
      // 图表自定义配置
      options: ``,
      // 数据
      data: [],
    },
    modelValue: '',
  },

  propsDictionary: {
    title: {
      name: '标题',
      type: 'text-input',
    },
    reactive: {
      name: '响应式',
      type: 'attr-radio',
    },
    style: {
      name: '样式',
      type: 'attr-object',
    },
    chartType: {
      name: '类型',
      type: 'attr-type',
    },
    options: {
      name: '配置',
      type: 'textarea-input',
    },
    data: {
      name: '数据源',
      type: 'attr-add-data',
    },
  },

  emits: ['beforeMount'],
})
