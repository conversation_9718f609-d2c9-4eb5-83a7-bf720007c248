<template>
  <div class="lyy-component lyy-echarts-container" :style="parentStyle">
    <div class="lyy-components-container" ref="parentDom" :style="style">
      <div class="lyy-echarts" ref="chartDom"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  defineComponent,
  nextTick,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  ref,
  shallowRef,
  toRefs,
  watch,
  inject,
} from 'vue'
import type { EChartsOption } from 'echarts'
import type { style } from './index'
import { bar, line, pie, scatter } from './options'
import { ACTION } from '@leyaoyao/libs/constants/action-type'
import { BEFORE_MOUNT } from '@leyaoyao/libs/constants/event-type'
import { isArray, throttle } from 'lodash'
import { useTpl } from '@leyaoyao/libs'

const isRealDark = inject('isRealDark', ref(false))

console.log('isRealDark~~~~~~~~~~~~~~~~', isRealDark.value)

const chart = shallowRef()
const chartDom = ref()
const parentDom = ref()

export interface Props {
  prop: {
    title?: string
    aliasName?: string
    chartType: string
    reactive: boolean
    style?: style
    options: string
    data: Array<{
      key: string
      value: string
    }>
  }
  modelValue: ''
}

const props = withDefaults(defineProps<Props>(), {
  prop: () => ({
    title: '图表标题',
    style: {},
    reactive: true,
    chartType: 'bar',
    options: '',
    data: [],
  }),
  modelValue: '',
})

const emits = defineEmits([ACTION])
// 自适应样式
const style = computed(() => {
  return prop.value.reactive ? null : prop.value.style
})

// 父级样式
const parentStyle = computed(() => {
  return {
    width: prop.value.reactive ? '100%' : style?.value?.width,
    height: prop.value.reactive ? '100%' : style?.value?.height,
    minWidth: style?.value?.width ?? 'unset',
    minHeight: style?.value?.height ?? 'unset',
  }
})

// 转换
const transferTpl = (target: string) => {
  return useTpl(target, {}, 'object')
}
// 标题
const title = computed(() => {
  try {
    if (prop.value.title) {
      return transferTpl(prop.value.title)
    }
  } catch (error) {
    console.error(error)
  }
  return ''
})

// 数据源
const customData = computed(() => {
  if (props.modelValue) {
    return props.modelValue
  }
  return prop.value.data
    ?.map((item) => {
      const _value =
        item.value && isArray(transferTpl(item.value))
          ? transferTpl(item.value)
          : []
      const flag =
        _value.length > 0 &&
        _value.every((item) => item.name !== '' && item.value !== '')
      const data: [] = flag ? transferTpl(item.value) : []
      return {
        key: item.key,
        value: data,
      }
    })
    .filter((item) => item.value.length > 0)
})

const { prop } = toRefs(props)

defineComponent({
  name: 'lyy-echarts',
})

watch(
  () => props,
  (val) => {
    updateData()
  },
  {
    deep: true,
  },
)
// 更新数据
const updateData = throttle(() => {
  initEchartsData()
}, 500)

// 设置柱状图
const setBarOptions = () => {
  const data =
    customData.value.length > 0 && customData.value[0].value?.length > 0
      ? customData.value
      : bar.data
  const xAxisData = Array.from(
    new Set(
      data
        .map((list) => {
          return (
            list?.value?.map((item) => {
              return item.name
            }) ?? []
          )
        })
        ?.flat(2),
    ),
  )
  const seriesData = data.map((list) => {
    return {
      type: 'bar',
      data:
        list?.value?.map((item) => {
          return item.value
        }) ?? [],
    }
  })
  const options = Object.assign(bar.options, {
    title: {
      text: title.value,
    },
    xAxis: {
      show: true,
      data: xAxisData,
    },
    series: seriesData,
  })
  prop.value.options = JSON.stringify(options)
  chart.value.setOption(options)
}
// 设置扇形图
const setPieOptions = () => {
  const data =
    customData.value.length > 0 && customData.value[0].value?.length > 0
      ? customData.value
      : pie.data
  const seriesData = data.map((list) => {
    return {
      type: 'pie',
      data: list.value,
    }
  })
  const options = Object.assign(pie.options, {
    title: {
      text: title.value,
    },
    xAxis: {
      show: false,
      data: [],
    },
    series: seriesData,
  })
  prop.value.options = JSON.stringify(options)
  chart.value.setOption(options)
}
// 设置折线图
const setLineOptions = () => {
  const data =
    customData.value.length > 0 && customData.value[0].value?.length > 0
      ? customData.value
      : line.data
  const xAxisData = Array.from(
    new Set(
      data
        .map((list) => {
          return (
            list?.value?.map((item) => {
              return item.name
            }) ?? []
          )
        })
        ?.flat(2),
    ),
  )
  const seriesData = data.map((list) => {
    return {
      type: 'line',
      data:
        list?.value?.map((item) => {
          return item.value
        }) ?? [],
    }
  })
  const options = Object.assign(line.options, {
    title: {
      text: title.value,
    },
    xAxis: {
      show: true,
      type: 'category',
      data: xAxisData,
    },
    yAxis: {
      type: 'value',
    },
    series: seriesData,
  })
  prop.value.options = JSON.stringify(options)
  chart.value.setOption(options)
}
// 设置散点图
const setScatterOptions = () => {
  const data =
    customData.value.length > 0 && customData.value[0].value?.length > 0
      ? customData.value
      : scatter.data
  const xAxisData = Array.from(
    new Set(
      data
        .map((list) => {
          return (
            list?.value?.map((item) => {
              return item.name
            }) ?? []
          )
        })
        ?.flat(2),
    ),
  )
  const seriesData = data.map((list) => {
    return {
      type: 'scatter',
      data:
        list?.value?.map((item) => {
          return item.value
        }) ?? [],
    }
  })
  const options = Object.assign(scatter.options, {
    title: {
      text: title.value,
    },
    xAxis: {
      show: true,
      data: xAxisData,
    },
    series: seriesData,
  })
  prop.value.options = JSON.stringify(options)
  chart.value.setOption(options)
}
// 自定义
const setCustomOption = () => {
  try {
    if (prop.value?.options) {
      const _options = transferTpl(prop.value.options)
      const options =
        typeof _options === 'object'
          ? _options
          : eval(`(${transferTpl(prop.value.options)})`)
      prop.value.title = ''
      chart.value.setOption(options, true)
    }
  } catch (error) {
    console.error(error)
  }
}
// 设置数据
const initEchartsData = () => {
  let options: EChartsOption
  switch (prop?.value?.chartType) {
    // 柱状图
    case 'bar': {
      setBarOptions()
      break
    }
    // 饼图
    case 'pie': {
      setPieOptions()
      break
    }
    // 折线图
    case 'line': {
      setLineOptions()
      break
    }
    // 散点图
    case 'scatter': {
      setScatterOptions()
      break
    }
    // 自定义
    case 'custom': {
      setCustomOption()
      break
    }
    default: {
      options = bar.options
      break
    }
  }
}

const resize = () => {
  nextTick(() => {
    chart.value.resize()
  })
}
// 响应式
let resizeOb
function sizeObserver() {
  // 创建监听器
  resizeOb = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (prop.value.reactive) {
        prop.value.style = {}
        prop.value.style.width = `${entry.contentRect.width}px`
        prop.value.style.height = `${entry.contentRect.height}px`
      }
      resize()
    }
  })
  // 监听尺寸变化
  resizeOb.observe(parentDom.value)
}

// 初始化
onBeforeMount(() => {
  emits(ACTION, { event: BEFORE_MOUNT })
})

const initChart = async () => {
  chart.value?.dispose()
  chart.value = null

  const { createEcharts } = await import('./init-echarts')

  chart.value = createEcharts(
    chartDom.value,
    isRealDark.value ? 'dark-pro' : undefined,
  )
  initEchartsData()
}
// 挂载后
onMounted(async () => {
  // window.echarts = echarts
  await initChart()
  sizeObserver()
})
// 销毁前
onBeforeUnmount(() => {
  // 销毁监听
  resizeOb.disconnect()
})

watch(
  isRealDark,
  () => {
    initChart()
  },
  { flush: 'post' },
)
</script>

<style lang="less" scoped>
.lyy-echarts-container {
  width: 100%;
  height: 100%;
}
.lyy-components-container {
  width: 100%;
  height: 100%;
  .lyy-echarts {
    width: 100%;
    height: 100%;
  }
}
</style>
