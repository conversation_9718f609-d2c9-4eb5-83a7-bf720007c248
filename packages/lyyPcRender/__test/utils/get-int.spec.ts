import { getIn } from '@/utils/get-in'
// 对象
const data = {
  a: {
    b: 1
  }
}
// 数组
const data1 = [data]
const data2 = [data, data]

it('测试 get-in 的 a.b 取值方式', () => {
  const output = getIn(data, 'a.b')
  expect(output).toEqual(1)
})

it(`测试 get-in 的 a/b 的取值方式`, () => {
  const output = getIn(data, 'a/b')
  expect(output).toEqual(1)
})

it(`测试 get-in 的 ['a','b']的取值方式`, () => {
  const output = getIn(data, ['a', 'b'])
  expect(output).toEqual(1)
})

it(`测试 get-in 的数组中 a.b 的取值`, () => {
  const output = getIn(data1, 'a.b')
  expect(output).toEqual([1])
})

it(`测试 get-in 的 无输入path 返回数值`, () => {
  const output = getIn(data)
  expect(output).toEqual(output)
})

it(`测试 get-in 的数组，['a','b']取值方式`, () => {
  const output = getIn(data2, ['a', 'b'])
  expect(output).toEqual([1, 1])
})

it(`测试 get-in 的数组，a/b 取值方式`, () => {
  const output6 = getIn(data2, 'a/b')
  expect(output6).toEqual([1, 1])
})
