import { setCompentFormId } from '@/utils/set-component-formId'
const pageConfig1: IElement[] = [
  {
    compId: '111',
    compName: 'lyy-text-input',
    prop:{
      field:'1'
    }
  }
]
const pageConfig2: IElement[] = [
  {
    compId: '112',
    compName: 'lyy-form',
    childrens: [
      {
        compId: '113',
        compName: 'lyy-input-group',
        prop:{
          field:'box'
        },
        childrens: [
          {
            compId: '1231',
            compName: 'lyy-text-input',
            prop:{
              field:'id'
            }
          },
          {
            compId: '1232',
            compName: 'lyy-text-input',
            prop:{
              field:'name'
            }
          }
        ]
      }
    ]
  }
]

const pageConfig3: IElement[] = [
  {
    compId: '113',
    compName: 'lyy-form',
    childrens: [
      {
        compId: '1231',
        compName: 'lyy-text-input',
        prop: {
          field:'name'
        }
      },
      {
        compId: '1232',
        compName: 'lyy-text-input',
        prop: {
          field:'id',
          formId:'f1'
        }
      }
    ]
  }
]

const pageConfig4: IElement[] = [
  {
    compId: '114',
    compName: 'lyy-form',
    childrens: [
      {
        compId: '1141',
        compName: 'lyy-form',
        prop: {
          field:'zz'
        },
        childrens: [
          {
            compId: '12411',
            compName: 'lyy-text-input',
            prop: {
              field:'id'
            }
          },
          {
            compId: '12412',
            compName: 'lyy-text-input',
            prop: {
              field:'name'
            }
          },
          {
            compId: '12413',
            compName: 'lyy-text-input'
          }
        ]
      }
    ]
  }
]
it('测试不需设置formId', () => {
  const output = setCompentFormId(pageConfig1, '')
  output.forEach((e) => {
    expect(e.prop.formId).toBeUndefined()
  })
})
it('测试传值设置formId', () => {
  const output = setCompentFormId(pageConfig1, 'f1')
  expect(output[0].prop.formId).toBe('f1')
})
it('测试深层设置formId', () => {
  const output = setCompentFormId(pageConfig2, '')
  const deepReduce = (list) => {
    list.forEach((e) => {
      if (e.childrens && e.childrens.length) {
        expect(e.prop.formId).toBe('112')
        deepReduce(e.childrens)
      }
    })
  }
  deepReduce(output[0].childrens)
})

it('测试自定义formId', () => {
  const output = setCompentFormId(pageConfig3, '')
  output[0].childrens?.forEach((e, i) => {
    if (i == 0) {
      expect(e.prop.formId).toBe('113')
    }
    if (i == 1) {
      expect(e.prop.formId).toBe('f1')
    }
  })
})
it('测试只取最近一层父级的formId设置', () => {
  const output = setCompentFormId(pageConfig4, '')
  const deepReduce = (list) => {
    list.forEach((e) => {
      if (e.compId == '1141') {
        expect(e.prop.formId).toBe('114')
      }
      if (e.compId == '12411' || e.compId == '12412') {
        expect(e.prop.formId).toBe('1141')
      }
      if (e.compId == '12413') {
        expect(e.prop.formId).toBeUndefined()
      }
      if (e.childrens && e.childrens.length) {
        deepReduce(e.childrens)
      }
    })
  }
  deepReduce(output[0].childrens)
})
