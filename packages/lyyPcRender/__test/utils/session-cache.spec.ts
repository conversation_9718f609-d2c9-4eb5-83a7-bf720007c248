import { sessionCache } from '@/utils/session-cache'
it('测试 SessionCache 实例的set方法存入', () => {
  sessionCache.set('token', 123)
  const output = sessionStorage.getItem('token')
  expect(output).toBe('123')
  const output1 = sessionCache.get('token')
  expect(output1).toBe(123)
})

it('测试 SessionCache 实例的 set和get方法闭环', () => {
  sessionCache.set('token', '我是token')
  const output = sessionCache.get('token')
  expect(output).toBe('我是token')
})

it('测试 SessionCache 实例的 set 对象，get取出也是对象', () => {
  sessionCache.set('token', { a: 1 })
  const output = sessionCache.get('token')
  expect(output.a).toBe(1)
})

it('测试 SessionCache 的remove 方法', () => {
  sessionCache.set('token', { a: 1 })
  sessionCache.remove('token')
  const output = sessionCache.get('token')
  expect(output).toBe(undefined)
})

it('测试 SessionCache 的clear 方法', () => {
  sessionCache.set('token', { a: 1 })
  sessionCache.set('ab', { a: 2 })
  sessionCache.clear()
  const output1 = sessionCache.get('token')
  const output2 = sessionCache.get('ab')
  expect(output1).toBe(undefined)
  expect(output2).toBe(undefined)
})

it('测试 SessionCache set入undefined,取出是字符串undefined', () => {
  sessionCache.set('token', undefined)
  const output = sessionCache.get('token')
  expect(output).toBe('undefined')
})
