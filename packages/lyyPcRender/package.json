{"name": "@leyaoyao/render-template", "version": "1.0.0", "description": "2.0 组件库", "main": "./src/render/index.ts", "scripts": {"test": "jest", "dev": "leyaoyao-vue -m dev", "build:release": "leyaoyao-vue build -m release && cross-env LYY_COMP=release node build.mjs", "build": "leyaoyao-vue build && node build.mjs", "build:dev": "leyaoyao-vue build -m dev && node build.mjs", "build:master": "leyaoyao-vue build -m master && node build.mjs", "dev:vue2mf": "leyaoyao-vue -m dev", "dev:vue3mf": "cross-env LYY_VUE3_MF=1 leyaoyao-vue -m dev", "build:vue3mf:dev": "cross-env LYY_VUE3_MF=1 leyaoyao-vue build -m dev && cross-env LYY_VUE3_MF=1 node build.mjs", "build:vue3mf:master": "cross-env LYY_VUE3_MF=1 leyaoyao-vue build -m master && cross-env LYY_VUE3_MF=1 node build.mjs", "build:gitlab": "cross-env LYY_VUE3_MF=1 leyaoyao-vue build", "build:lang": "node --experimental-modules ./src/locales/generate-language-pack.mjs"}}