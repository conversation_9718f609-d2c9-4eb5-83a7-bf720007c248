<template>
  <div class="detail">
    <h2>Table Page</h2>
    <LyyRender :pageConfig="pageConfig"></LyyRender>
  </div>
</template>

<script setup lang="ts">
import { IComponent } from './export'

const t3data = [
  {
    amount: 238013.93,
    price_amount: 238005,
    sale_send_id: '38416733419679745',
    doc_date: '2022-10-08',
    doc_no: 'XSK20221008144618',
    series_no: 24,
    bpartner_name: '广州家得宝',
    warehouse_name: '222222233312',
    product_value: '5GBOX1.0.0',
    product_name: '5G盒子',
    color_name: '白',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '公斤',
    piece_qty: 34,
    qty: 1935,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '公斤',
    price: 123,
    price_qty: 1935,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 10,
    pay_amount: 4336,
    total_amount: 240811,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 2797.07,
    price_amount: 2796,
    sale_send_id: '38416733419679745',
    doc_date: '2022-10-08',
    doc_no: 'XSK20221008144618',
    series_no: 24,
    bpartner_name: '广州家得宝',
    warehouse_name: 'THE仓库',
    product_value: '5GBOX1.0.0',
    product_name: '5G盒子',
    color_name: '黑',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '公斤',
    piece_qty: 12,
    qty: 233,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '公斤',
    price: 12,
    price_qty: 233,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 10,
    pay_amount: 4336,
    total_amount: 240811,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 238013.93,
    price_amount: 238005,
    sale_send_id: '38416427118047233',
    doc_date: '2022-10-08',
    doc_no: 'XSK20221008144505',
    series_no: 23,
    bpartner_name: '广州家得宝',
    warehouse_name: '222222233312',
    product_value: '5GBOX1.0.0',
    product_name: '5G盒子',
    color_name: '白',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '公斤',
    piece_qty: 34,
    qty: 1935,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '公斤',
    price: 123,
    price_qty: 1935,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 10,
    pay_amount: 4336,
    total_amount: 240811,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 2797.07,
    price_amount: 2796,
    sale_send_id: '38416427118047233',
    doc_date: '2022-10-08',
    doc_no: 'XSK20221008144505',
    series_no: 23,
    bpartner_name: '广州家得宝',
    warehouse_name: 'THE仓库',
    product_value: '5GBOX1.0.0',
    product_name: '5G盒子',
    color_name: '黑',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '公斤',
    piece_qty: 12,
    qty: 233,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '公斤',
    price: 12,
    price_qty: 233,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 10,
    pay_amount: 4336,
    total_amount: 240811,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 605,
    price_amount: 605,
    sale_send_id: '38172317102714881',
    doc_date: '2022-10-07',
    doc_no: 'XSK20221007223505',
    series_no: 14,
    bpartner_name: '1222',
    warehouse_name: '222222233312',
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '公斤',
    piece_qty: 3,
    qty: 55,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '公斤',
    price: 11,
    price_qty: 55,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 605,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '38149346329964545',
    doc_date: '2022-10-07',
    doc_no: 'XSK20221007210348',
    series_no: 22,
    bpartner_name: '广州家得宝',
    warehouse_name: null,
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: null,
    piece_qty: null,
    qty: null,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: null,
    price: null,
    price_qty: null,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: null,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '38059833586954242',
    doc_date: '2022-10-07',
    doc_no: 'XSK20221007150807',
    series_no: 2,
    bpartner_name: '柯利达',
    warehouse_name: '仓库1273',
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 233,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: null,
    price_qty: 233,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: null,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '38059794043056129',
    doc_date: '2022-10-07',
    doc_no: 'XSK20221007150757',
    series_no: 1,
    bpartner_name: '柯利达',
    warehouse_name: '仓库1273',
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 233,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: null,
    price_qty: 233,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: null,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 705,
    price_amount: 705,
    sale_send_id: '35211411978547202',
    doc_date: '2022-09-29',
    doc_no: 'XSK20220929000005',
    series_no: 13,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 235,
    diff_qty_tag: null,
    qty_tag: 235,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 235,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 705,
    total_amount: 705,
    settle_amount: 705,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 705,
    price_amount: 705,
    sale_send_id: '35206854305214466',
    doc_date: '2022-09-29',
    doc_no: 'XSK20220929000004',
    series_no: 12,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 235,
    diff_qty_tag: null,
    qty_tag: 235,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 235,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 705,
    total_amount: 705,
    settle_amount: 705,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 705,
    price_amount: 705,
    sale_send_id: '35186916551065601',
    doc_date: '2022-09-29',
    doc_no: 'XSK20220929000003',
    series_no: 11,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 235,
    diff_qty_tag: null,
    qty_tag: 235,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 235,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 705,
    total_amount: 705,
    settle_amount: 705,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 705,
    price_amount: 705,
    sale_send_id: '35186848217464834',
    doc_date: '2022-09-29',
    doc_no: 'XSK20220929000002',
    series_no: 10,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 235,
    diff_qty_tag: null,
    qty_tag: 235,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 235,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 705,
    total_amount: 705,
    settle_amount: 705,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 705,
    price_amount: 705,
    sale_send_id: '35186746899857409',
    doc_date: '2022-09-29',
    doc_no: 'XSK20220929000001',
    series_no: 9,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 235,
    diff_qty_tag: null,
    qty_tag: 235,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 235,
    description: null,
    status: 5,
    status_label: '已作废',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 705,
    total_amount: 705,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 705,
    price_amount: 705,
    sale_send_id: '34789757492473857',
    doc_date: '2022-09-28',
    doc_no: '销售开单_1222_20220928143400',
    series_no: 8,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 235,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 235,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 9,
    total_amount: 705,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 133,
    price_amount: 133,
    sale_send_id: '31469871185866754',
    doc_date: '2022-09-19',
    doc_no: '销售开单_广州家得宝_20220919104157',
    series_no: 21,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 11,
    qty: 133,
    diff_qty_tag: null,
    qty_tag: 133,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 1,
    price_qty: 133,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 133,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 9,
    price_amount: 9,
    sale_send_id: '29657859720032257',
    doc_date: '2022-09-14',
    doc_no: '销售开单_广州家得宝_20220914104140',
    series_no: 20,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库12',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 3,
    qty: 3,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 3,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 9,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1398,
    price_amount: 1398,
    sale_send_id: '27589407501004801',
    doc_date: '2022-09-08',
    doc_no: '销售开单_广州家得宝_20220908174223',
    series_no: 19,
    bpartner_name: '广州家得宝',
    warehouse_name: '新仓库',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 10,
    qty: 233,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 6,
    price_qty: 233,
    description: '撒额供认不讳',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: null,
    total_amount: 7848,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: 'superadmin',
  },
  {
    amount: 6450,
    price_amount: 6450,
    sale_send_id: '27589407501004801',
    doc_date: '2022-09-08',
    doc_no: '销售开单_广州家得宝_20220908174223',
    series_no: 19,
    bpartner_name: '广州家得宝',
    warehouse_name: '旧仓库',
    product_value: '65',
    product_name: '集成芯片',
    color_name: '黑',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 3,
    qty: 75,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 86,
    price_qty: 75,
    description: '撒额供认不讳',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: null,
    total_amount: 7848,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: 'superadmin',
  },
  {
    amount: 45,
    price_amount: 45,
    sale_send_id: '27571647341473794',
    doc_date: '2022-09-06',
    doc_no: '销售开单_广州家得宝_20220906163148',
    series_no: 13,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库12',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 7,
    qty: 5,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '米',
    price: 9,
    price_qty: 5,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: null,
    total_amount: 45,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '27541927254110209',
    doc_date: '2022-09-08',
    doc_no: '销售开单_广州家得宝_20220908143343',
    series_no: 18,
    bpartner_name: '广州家得宝',
    warehouse_name: null,
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: null,
    piece_qty: null,
    qty: null,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: null,
    price: null,
    price_qty: null,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: null,
    pay_amount: 0,
    total_amount: null,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1476,
    price_amount: 1476,
    sale_send_id: '27501284884557825',
    doc_date: '2022-09-08',
    doc_no: '销售开单_通_20220908115213',
    series_no: 1,
    bpartner_name: '通',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 12,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 123,
    price_qty: 12,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: null,
    pay_amount: null,
    total_amount: 1476,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1386,
    price_amount: 1386,
    sale_send_id: '27488066220929025',
    doc_date: '2022-09-08',
    doc_no: '销售开单_广州家得宝_20220908105941',
    series_no: 17,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 231,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 6,
    price_qty: 231,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 12384,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 10998,
    price_amount: 10998,
    sale_send_id: '27488066220929025',
    doc_date: '2022-09-08',
    doc_no: '销售开单_广州家得宝_20220908105941',
    series_no: 17,
    bpartner_name: '广州家得宝',
    warehouse_name: '旧仓库',
    product_value: '65',
    product_name: '集成芯片',
    color_name: '白坯',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 1222,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 9,
    price_qty: 1222,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 12384,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 332.1,
    price_amount: 324,
    sale_send_id: '27258918592393217',
    doc_date: '2022-09-07',
    doc_no: '销售开单_广州家得宝_20220907194908',
    series_no: 16,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 12,
    qty: 54,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 6,
    price_qty: 54,
    description: '阿俄金融功能机',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 12,
    pay_amount: 12,
    total_amount: 726,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: 'biaomei',
  },
  {
    amount: 393.9,
    price_amount: 390,
    sale_send_id: '27258918592393217',
    doc_date: '2022-09-07',
    doc_no: '销售开单_广州家得宝_20220907194908',
    series_no: 16,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库12',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '灰色',
    color_code: '#333333',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 4,
    qty: 26,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 15,
    price_qty: 26,
    description: '阿俄金融功能机',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 12,
    pay_amount: 12,
    total_amount: 726,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: 'biaomei',
  },
  {
    amount: 378,
    price_amount: 378,
    sale_send_id: '27124305123422209',
    doc_date: '2022-09-07',
    doc_no: '销售开单_广州家得宝_20220907105414',
    series_no: 15,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 5,
    qty: 63,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 6,
    price_qty: 63,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: null,
    pay_amount: 0,
    total_amount: 378,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 366.96,
    price_amount: 366,
    sale_send_id: '27122316218019841',
    doc_date: '2022-09-07',
    doc_no: '销售开单_广州家得宝_20220907104619',
    series_no: 14,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 10,
    qty: 61,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 6,
    price_qty: 61,
    description: 'asdfg',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 8,
    pay_amount: 3974,
    total_amount: 3974,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: 'superadmin',
  },
  {
    amount: 3607.05,
    price_amount: 3600,
    sale_send_id: '27122316218019841',
    doc_date: '2022-09-07',
    doc_no: '销售开单_广州家得宝_20220907104619',
    series_no: 14,
    bpartner_name: '广州家得宝',
    warehouse_name: '新仓库',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '灰色',
    color_code: '#333333',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 4,
    qty: 450,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 8,
    price_qty: 450,
    description: 'asdfg',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 8,
    pay_amount: 3974,
    total_amount: 3974,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: 'superadmin',
  },
  {
    amount: 108,
    price_amount: 108,
    sale_send_id: '27111835726065666',
    doc_date: '2022-09-06',
    doc_no: '销售开单_广州家得宝_20220906100441',
    series_no: 12,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 5,
    qty: 27,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 4,
    price_qty: 27,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 20,
    total_amount: 108,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1,
    price_amount: 1,
    sale_send_id: '26832852744744961',
    doc_date: '2022-09-17',
    doc_no: '销售开单_1222_20220917153606',
    series_no: 7,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 1,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 1,
    price_qty: 1,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 1,
    total_amount: 1,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1230,
    price_amount: 1230,
    sale_send_id: '25459726053859330',
    doc_date: '2022-09-02',
    doc_no: '销售开单_广州家得宝_20220902203947',
    series_no: 11,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 246,
    diff_qty_tag: null,
    qty_tag: 246,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 5,
    price_qty: 246,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 1230,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 2214,
    price_amount: 2214,
    sale_send_id: '25459392514416641',
    doc_date: '2022-09-02',
    doc_no: '销售开单_广州家得宝_20220902203828',
    series_no: 10,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 246,
    diff_qty_tag: null,
    qty_tag: 246,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 9,
    price_qty: 246,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 2272,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 36,
    price_amount: 36,
    sale_send_id: '22883658816368642',
    doc_date: '2022-08-26',
    doc_no: '销售开单_广州家得宝_20220826_180325',
    series_no: 9,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: '12',
    wide: 1,
    weight: 12,
    uom_name: '米',
    piece_qty: 3,
    qty: 6,
    diff_qty_tag: null,
    qty_tag: 6,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 6,
    price_qty: 6,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 36,
    total_amount: 36,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '22822400326971393',
    doc_date: '2022-08-27',
    doc_no: '销售开单_认认真真_20220827_140000',
    series_no: 1,
    bpartner_name: '认认真真',
    warehouse_name: null,
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: null,
    piece_qty: null,
    qty: null,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: null,
    price: null,
    price_qty: null,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: null,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 295.56,
    price_amount: 264,
    sale_send_id: '22571362411950081',
    doc_date: '2022-08-25',
    doc_no: '销售开单_广州家得宝_20220825_212228',
    series_no: 8,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 88,
    diff_qty_tag: null,
    qty_tag: 88,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 88,
    description: '备注220803',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 85,
    pay_amount: 999,
    total_amount: 945,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: 'chenkejian',
  },
  {
    amount: 387.92,
    price_amount: 356,
    sale_send_id: '22571362411950081',
    doc_date: '2022-08-25',
    doc_no: '销售开单_广州家得宝_20220825_212228',
    series_no: 8,
    bpartner_name: '广州家得宝',
    warehouse_name: '新仓库',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 4,
    qty: 89,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 4,
    price_qty: 89,
    description: '备注220803',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 85,
    pay_amount: 999,
    total_amount: 945,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: 'chenkejian',
  },
  {
    amount: 261.52,
    price_amount: 240,
    sale_send_id: '22571362411950081',
    doc_date: '2022-08-25',
    doc_no: '销售开单_广州家得宝_20220825_212228',
    series_no: 8,
    bpartner_name: '广州家得宝',
    warehouse_name: '222222233312',
    product_value: '65',
    product_name: '集成芯片',
    color_name: '黑',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 60,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 4,
    price_qty: 60,
    description: '备注220803',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 85,
    pay_amount: 999,
    total_amount: 945,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: 'chenkejian',
  },
  {
    amount: 48198.08,
    price_amount: 48146,
    sale_send_id: '22567775296368642',
    doc_date: '2022-08-25',
    doc_no: '销售开单_广州家得宝_20220825_210812',
    series_no: 7,
    bpartner_name: '广州家得宝',
    warehouse_name: '旧仓库',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: '45',
    wide: 160,
    weight: 45,
    uom_name: '米',
    piece_qty: 20,
    qty: 905,
    diff_qty_tag: 5,
    qty_tag: 1005,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 53.2,
    price_qty: 905,
    description: '备注220803',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 85,
    pay_amount: 999,
    total_amount: 79083.8,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: 'chenkejian',
  },
  {
    amount: 30616.65,
    price_amount: 30588.8,
    sale_send_id: '22567775296368642',
    doc_date: '2022-08-25',
    doc_no: '销售开单_广州家得宝_20220825_210812',
    series_no: 7,
    bpartner_name: '广州家得宝',
    warehouse_name: '新仓库',
    product_value: '65',
    product_name: '集成芯片',
    color_name: '白',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 20,
    qty: 484,
    diff_qty_tag: 8,
    qty_tag: 644,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 63.2,
    price_qty: 484,
    description: '备注220803',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 85,
    pay_amount: 999,
    total_amount: 79083.8,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: 'chenkejian',
  },
  {
    amount: 269.06,
    price_amount: 264,
    sale_send_id: '22567775296368642',
    doc_date: '2022-08-25',
    doc_no: '销售开单_广州家得宝_20220825_210812',
    series_no: 7,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 88,
    diff_qty_tag: null,
    qty_tag: 88,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 88,
    description: '备注220803',
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 85,
    pay_amount: 999,
    total_amount: 79083.8,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: 'chenkejian',
  },
  {
    amount: 735,
    price_amount: 735,
    sale_send_id: '20741293093539841',
    doc_date: '2022-08-20',
    doc_no: '销售开单_1222_20220820_201025',
    series_no: 6,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 1,
    qty: 2,
    diff_qty_tag: null,
    qty_tag: 245,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '米',
    price: 3,
    price_qty: 245,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 20,
    pay_amount: 0,
    total_amount: 755,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 220,
    price_amount: 220,
    sale_send_id: '20312589232320513',
    doc_date: '2022-08-19',
    doc_no: '销售开单_222_20220819_154654',
    series_no: 6,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库1273',
    product_value: '65',
    product_name: '集成芯片',
    color_name: '白',
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '米',
    piece_qty: 2,
    qty: 44,
    diff_qty_tag: 3,
    qty_tag: 50,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '米',
    price: 5,
    price_qty: 44,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 220,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1170.08,
    price_amount: 1179.075,
    sale_send_id: '16698824649682945',
    doc_date: '2022-08-09',
    doc_no: '销售开单_1222_20220809_162705',
    series_no: 1,
    bpartner_name: '普宁',
    warehouse_name: '普宁仓库',
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '公斤',
    piece_qty: 4,
    qty: 118.5,
    diff_qty_tag: 0.5,
    qty_tag: 120.5,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '公斤',
    price: 9.95,
    price_qty: 118.5,
    description: 'qqq',
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: -9,
    pay_amount: 0.08,
    total_amount: 1170.08,
    settle_amount: 0.08,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 810,
    price_amount: 810,
    sale_send_id: '15611998509805569',
    doc_date: '2022-08-06',
    doc_no: '销售开单_广州乐哈岛_20220806_162826',
    series_no: 24,
    bpartner_name: '广州乐哈岛',
    warehouse_name: '仓库2',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '灰色',
    color_code: '#333333',
    color_no: '33302',
    wide: 160,
    weight: 260,
    uom_name: '个',
    piece_qty: 3,
    qty: 45,
    diff_qty_tag: 0.5,
    qty_tag: 46.5,
    diff_mode: '+',
    diff_qty: null,
    price_uom_name: '个',
    price: 18,
    price_qty: 45,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 30,
    total_amount: 810,
    settle_amount: 0,
    create_user_name: 'admin1',
    sale_user_name: null,
  },
  {
    amount: 61480,
    price_amount: 61480,
    sale_send_id: '15154972927275009',
    doc_date: '2022-08-05',
    doc_no: '销售开单_222_20220805000000',
    series_no: 5,
    bpartner_name: '广州家得宝',
    warehouse_name: '仓库2',
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '个',
    piece_qty: null,
    qty: 212,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '个',
    price: 290,
    price_qty: 212,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 61480,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 1092,
    price_amount: 1092,
    sale_send_id: '15259428788449281',
    doc_date: '2022-08-05',
    doc_no: '销售开单_1222_20220805_170727',
    series_no: 5,
    bpartner_name: '1222',
    warehouse_name: '仓库2',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '个',
    piece_qty: 2,
    qty: 364,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '个',
    price: 3,
    price_qty: 364,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 1626,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 534,
    price_amount: 534,
    sale_send_id: '15259428788449281',
    doc_date: '2022-08-05',
    doc_no: '销售开单_1222_20220805_170727',
    series_no: 5,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '灰色',
    color_code: '#333333',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '个',
    piece_qty: 4,
    qty: 178,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: '个',
    price: 3,
    price_qty: 178,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 1626,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '12787077626802178',
    doc_date: '2022-07-29',
    doc_no: '销售开单_222_20220729000000',
    series_no: 4,
    bpartner_name: '广州家得宝',
    warehouse_name: null,
    product_value: null,
    product_name: null,
    color_name: null,
    color_code: null,
    color_no: null,
    wide: null,
    weight: null,
    uom_name: null,
    piece_qty: null,
    qty: null,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: null,
    price: null,
    price_qty: null,
    description: null,
    status: 1,
    status_label: '新建',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: null,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: 39366,
    price_amount: 39366,
    sale_send_id: '12278798194651138',
    doc_date: '2022-07-28',
    doc_no: '销售开单_1222_20220728000000',
    series_no: 3,
    bpartner_name: '1222',
    warehouse_name: '222222233312',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '灰色',
    color_code: '#333333',
    color_no: '#888888',
    wide: null,
    weight: null,
    uom_name: '个',
    piece_qty: 5,
    qty: 13122,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: null,
    price: 3,
    price_qty: 13122,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 39366,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
  {
    amount: null,
    price_amount: null,
    sale_send_id: '12278798194651138',
    doc_date: '2022-07-28',
    doc_no: '销售开单_1222_20220728000000',
    series_no: 3,
    bpartner_name: '1222',
    warehouse_name: '仓库1273',
    product_value: 'P00001',
    product_name: '4G盒子',
    color_name: '黑色',
    color_code: '#888888',
    color_no: null,
    wide: null,
    weight: null,
    uom_name: '个',
    piece_qty: 2,
    qty: 133,
    diff_qty_tag: null,
    qty_tag: null,
    diff_mode: null,
    diff_qty: null,
    price_uom_name: null,
    price: null,
    price_qty: 133,
    description: null,
    status: 3,
    status_label: '审核通过',
    is_amount_input: 'N',
    fee: 0,
    pay_amount: 0,
    total_amount: 39366,
    settle_amount: 0,
    create_user_name: 'superadmin',
    sale_user_name: null,
  },
]

const pageConfig: IComponent[] = [
  // 搜索
  {
    compId: 'search1',
    compName: 'lyy-form',
    prop: {},
    actions: [
      {
        event: 'mounted',
        action: 'request',
        option: {
          url: '/api/table/get',
          payloads: [{ source: 'search1' }],
          customOption: {
            jsonPath: 'data',
            loading: true,
          },

          // 拦截器
          interceptors: {
            responseInterceptor: (res) => {
              res.data = {
                data: {
                  list: [
                    {
                      key: '1',
                      name: 'Mac',
                      age: 32,
                      address: '西湖区湖底公园1号',
                      url: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/dealer/ds16611661098337013.jpg',
                      amount: 1345,
                      rate: 0.368,
                      date: 'Tue Sep 29 2022 10:56:02 GMT+0800 (中国标准时间)',
                      status: 1,
                      a: '广东',
                      b: '广州',
                      c: '番禺',
                      sale: 200,
                      quantitySold: 100,
                      quantityLeased: 50,
                      quantityShared: 72,
                    },
                    {
                      key: '2',
                      name: 'iPhone',
                      amount: 8,
                      age: 42,
                      address: '西湖区湖底公园1号',
                      url: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/dealer/ds16611661098337013.jpg',
                      status: 0,
                    },
                    {
                      key: '3',
                      name: 'iPad',
                      age: 2,
                      amount: 10,
                      address: '西湖区湖底公园1号',
                      url: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/dealer/ds16611661098337013.jpg',
                      status: 0,
                    },
                    {
                      key: '4',
                      name: 'iPad5',
                      age: 92,
                      // amount: 100,
                      address: '西湖区湖底公园1号',
                      url: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/dealer/ds16611661098337013.jpg',
                      status: 0,
                    },
                  ],
                  img: [
                    {
                      url: 'http://lyy-public.oss-cn-shenzhen.aliyuncs.com/dealer/ds16611661098337013.jpg',
                    },
                  ],
                  total: 100,
                  summary: {
                    rate: 0.9,
                  },
                },
              }
              return res
            },
          },

          // 响应数据赋值：表格、分页
          targets: [
            {
              sourceKey: 'list',
              targetId: 't1',
              targetPath: 'datasource',
            },
            {
              sourceKey: 'summary',
              targetId: 't1',
              targetPath: 'summary',
            },
            {
              sourceKey: 'total',
              targetId: 'p1',
              targetPath: 'prop.total',
            },
          ],
        },
      },

      {
        event: 'mounted',
        action: 'request',
        option: {
          url: '/api/table/get122',
          payloads: [{ source: 'search1' }],
          customOption: {
            jsonPath: 'data',
            loading: true,
          },

          // 拦截器
          interceptors: {
            responseInterceptor: (res) => {
              res.data = {
                data: [
                  {
                    id: '7617360414044162',
                    name: 'iPhone',
                    pid: null,
                    type: 'folder',
                    createdby_name: 'admin',
                    updatedby_name: 'admin',
                    created: '2022-07-15 15:00:36',
                    updated: '2022-07-15 15:00:36',
                    children: [
                      {
                        id: '7617409781002241',
                        name: '仪表板2-2哈哈哈哈',
                        pid: '7617360414044162',
                        type: 'folder',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-07-15 15:00:47',
                        updated: '2022-07-15 15:00:47',
                        children: [
                          {
                            id: '7621689241956354',
                            name: '展板1',
                            pid: '7617409781002241',
                            type: 'dashboard',
                            createdby_name: '梁林海',
                            updatedby_name: 'admin',
                            created: '2022-07-15 15:17:48',
                            updated: '2022-10-08 15:03:13',
                          },
                          {
                            id: '12738157562490882',
                            name: '1',
                            pid: '7617409781002241',
                            type: 'dashboard',
                            createdby_name: 'admin',
                            updatedby_name: 'admin',
                            created: '2022-07-29 18:08:49',
                            updated: '2022-09-25 18:56:54',
                          },
                          {
                            id: '12735000912523266',
                            name: 'ghgh',
                            pid: '7617409781002241',
                            type: 'dashboard',
                            createdby_name: 'admin',
                            updatedby_name: 'admin',
                            created: '2022-07-29 17:56:16',
                            updated: '2022-09-26 20:08:17',
                          },
                        ],
                      },
                      {
                        id: '14077344106606594',
                        name: '喔喔哦',
                        pid: '7617360414044162',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-08-02 10:50:16',
                        updated: '2022-10-09 17:33:22',
                      },
                    ],
                  },
                  {
                    id: '9368232659709954',
                    name: 'Android',
                    pid: null,
                    type: 'folder',
                    createdby_name: '梁林海',
                    updatedby_name: '梁林海',
                    created: '2022-07-20 10:57:56',
                    updated: '2022-07-20 10:57:56',
                    children: [
                      {
                        id: '33654628924715010',
                        name: '交叉表',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-09-25 11:23:24',
                        updated: '2022-10-09 11:20:24',
                      },
                      {
                        id: '34197715501641730',
                        name: '交叉表-测试2',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-09-26 23:21:26',
                        updated: '2022-09-27 19:56:19',
                      },
                      {
                        id: '12747066415181826',
                        name: '交叉表-测试1',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-07-29 18:44:13',
                        updated: '2022-10-08 15:45:47',
                      },
                      {
                        id: '38400288429371393',
                        name: '交叉表-日期分组',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-10-08 13:40:58',
                        updated: '2022-10-09 19:31:51',
                      },
                      {
                        id: '10105778301759490',
                        name: '设备仪表板',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: '梁林海',
                        updatedby_name: 'admin',
                        created: '2022-07-22 11:48:41',
                        updated: '2022-09-30 13:44:16',
                      },
                      {
                        id: '34366970746564610',
                        name: '交叉表-测试3',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-09-27 10:34:00',
                        updated: '2022-09-27 11:30:07',
                      },
                      {
                        id: '9131042608443394',
                        name: 'test1',
                        pid: '9368232659709954',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-07-19 19:15:26',
                        updated: '2022-10-08 09:55:14',
                      },
                    ],
                  },
                  {
                    id: '34099506649427969',
                    name: '报表测试',
                    pid: null,
                    type: 'folder',
                    createdby_name: '郭良棠',
                    updatedby_name: '郭良棠',
                    created: '2022-09-26 16:51:11',
                    updated: '2022-09-26 16:51:11',
                    children: [
                      {
                        id: '34120951232393217',
                        name: '出货及新增上线',
                        pid: '34099506649427969',
                        type: 'dashboard',
                        createdby_name: '郭良棠',
                        updatedby_name: '郭良棠',
                        created: '2022-09-26 18:16:24',
                        updated: '2022-09-29 15:34:03',
                      },
                      {
                        id: '34099720667983873',
                        name: '设备品类汇总',
                        pid: '34099506649427969',
                        type: 'dashboard',
                        createdby_name: '郭良棠',
                        updatedby_name: '郭良棠',
                        created: '2022-09-26 16:52:02',
                        updated: '2022-09-26 18:15:20',
                      },
                      {
                        id: '35548695795265538',
                        name: '明细表1',
                        pid: '34099506649427969',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-09-30 16:49:45',
                        updated: '2022-09-30 22:16:23',
                      },
                    ],
                  },
                  {
                    id: '34102650980724737',
                    name: '报表测试2',
                    pid: null,
                    type: 'folder',
                    createdby_name: 'admin',
                    updatedby_name: 'admin',
                    created: '2022-09-26 17:03:41',
                    updated: '2022-09-26 17:03:41',
                    children: [
                      {
                        id: '34102702818127874',
                        name: '测试',
                        pid: '34102650980724737',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: '陈锐锋',
                        created: '2022-09-26 17:03:53',
                        updated: '2022-09-29 17:22:15',
                      },
                    ],
                  },
                  {
                    id: '34103266129932290',
                    name: 'pmtest',
                    pid: null,
                    type: 'folder',
                    createdby_name: 'admin',
                    updatedby_name: 'admin',
                    created: '2022-09-26 17:06:08',
                    updated: '2022-09-26 17:06:08',
                    children: [
                      {
                        id: '34115398355578882',
                        name: 'jiaocha',
                        pid: '34103266129932290',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: '陈锐锋',
                        created: '2022-09-26 17:54:20',
                        updated: '2022-09-29 17:20:45',
                      },
                    ],
                  },
                  {
                    id: '34788505462435841',
                    name: 'cansee',
                    pid: null,
                    type: 'folder',
                    createdby_name: '陈可健',
                    updatedby_name: '陈可健',
                    created: '2022-09-28 14:29:01',
                    updated: '2022-09-28 14:29:01',
                  },
                  {
                    id: '35163616594030594',
                    name: 'zrd测试',
                    pid: null,
                    type: 'folder',
                    createdby_name: 'admin',
                    updatedby_name: 'admin',
                    created: '2022-09-29 15:19:35',
                    updated: '2022-09-29 15:19:35',
                    children: [
                      {
                        id: '35163659854082050',
                        name: '日期分组',
                        pid: '35163616594030594',
                        type: 'dashboard',
                        createdby_name: 'admin',
                        updatedby_name: 'admin',
                        created: '2022-09-29 15:19:45',
                        updated: '2022-10-09 15:38:52',
                      },
                    ],
                  },
                ],
              }
              return res
            },
          },

          // 响应数据赋值：表格、分页
          // sourceKey: 'list',
          targetId: 't2',
          targetPath: 'datasource',
        },
      },
      {
        event: 'mounted',
        action: 'request',
        option: {
          url: '/api/table/get6667',
          payloads: [{ source: 'search1' }],
          customOption: {
            jsonPath: 'data',
            loading: true,
          },

          // 拦截器
          interceptors: {
            responseInterceptor: (res) => {
              res.data = {
                data: t3data,
              }
              return res
            },
          },

          targetId: 't3',
          targetPath: 'datasource',
        },
      },
    ],
    childrens: [
      {
        compName: 'lyy-text-input',
        prop: {
          label: '公司名称',
          field: 'name',
        },
      },
      {
        compName: 'lyy-text-input',
        prop: {
          label: '金额',
          field: 'amount',
        },
      },

      {
        compId: 'btn001',
        compName: 'lyy-button',
        prop: {
          text: '重置',
        },
        actions: [
          {
            event: 'click',
            action: 'resetValue',
            option: {
              targetId: 'search1',
            },
            thenActions: [
              {
                action: 'broadcast',
                option: {
                  targetId: 'search1',
                  event: 'mounted',
                },
              },
            ],
          },
        ],
      },
      {
        compId: 'btn002',
        compName: 'lyy-button',
        prop: {
          text: '搜索',
          type: 'primary',
        },
        actions: [
          {
            event: 'click',
            action: 'broadcast',
            option: {
              targetId: 'search1',
              event: 'mounted',
            },
          },
        ],
      },
    ],
  },

  // 工具栏
  {
    compName: 'lyy-toolbar',
    prop: {
      title: '表格数据',
    },
    childrens: [
      {
        compName: 'lyy-button',
        prop: {
          text: '删除',
          danger: true,
        },
      },
      {
        compName: 'lyy-button',
        prop: {
          text: '上架',
        },
      },
      {
        compName: 'lyy-button',
        prop: {
          text: '下架',
        },
      },
      {
        compName: 'lyy-button',
        prop: {
          text: '新增',
          type: 'primary',
        },
      },
    ],
  },

  // 表格
  {
    compId: 't1',
    compName: 'lyy-table',
    prop: {
      summary: {
        placement: 'table', // top bottom table
        summarys: [
          {
            // label: '合计: ',
            // value: 'sum',
            // field: 'sum',
            // field: '_no',
            value: '合计',
          },
          {
            label: '年龄: ',
            field: 'age',
          },
          {
            label: '数字: ',
            field: 'amount',
            pipes: ['decimal'],
          },
          {
            label: '利率: ',
            field: 'rate',
            pipes: ['percent'],
          },
        ],

        // 来源一
        payloads: [{ source: 't1', sourceKey: 'summary', decompose: true }],

        // 来源二
        sumFields: ['amount'],
        averageFields: ['age'],

        // 来源三
        // 请求，赋值到 summary
      },

      showIndex: true,
      // showSelection: false,
      rowSelection: {
        // columnWidth: 40,
        // checkStrictly: true,
        fixed: false,
        getCheckboxProps: (record) => ({
          disabled: record.status === 0,
        }),
      },
      // disabledRow: {
      //   exp: 'status === 0',
      //   background: '#fff2f0',
      // },
      // backgroundRows: [
      //   {
      //     exp: 'status === 1',
      //     background: 'red',
      //   },
      //   {
      //     exp: 'status === 0',
      //     background: '#ccc',
      //   },
      // ],
      rowKey: 'name',
      columns: [
        {
          title: '图标',
          dataIndex: 'url',
          type: 'image',
          prop: {},
        },
        {
          title: '名称',
          dataIndex: 'name',
          type: 'link',
          actions: [
            {
              event: 'click',
              action: 'linkto',
              option: {
                url: '/detail',
                payloads: [
                  {
                    key: 'name',
                    source: 't1',
                    sourceKey: 'currentRow.name',
                  },
                ],
              },
            },
          ],
        },
        // {
        //   title: '姓名',
        //   dataIndex: 'name',
        //   // sorter: (a: any, b: any) => a.name.length - b.name.length,
        //   sorter: true,
        //   filters: ['iPhone', 'Macbook'],
        //   // filters: [
        //   //   { text: 'iPhone', value: 'iPhone' },
        //   //   { text: 'Jim', value: 'Jim' },
        //   // ],
        // },
        {
          title: '年龄',
          dataIndex: 'age',
          type: 'number',
          sorter: true,
        },
        {
          title: '数字',
          dataIndex: 'amount',
          pipes: ['decimal'],
          // pipes: [
          // {
          //   pipe: 'decimal',
          //   // option: { fixed: 2, thousands: true }
          // },
          // {
          //   pipe: 'textmap',
          //   option: {
          //     textMap: [{ origin: '1,345.00', result: '1000' }],
          //   },
          // },
          // {
          //   pipe: 'currency',
          //   option: { currency: 'CNY' },
          // },
          // ],
        },
        {
          title: '百分比',
          dataIndex: 'rate',
          pipes: [
            {
              pipe: 'percent',
              // option: { fixed: 2, thousands: true }
            },
          ],
        },
        {
          title: '日期时间',
          dataIndex: 'date',
          pipes: [
            {
              pipe: 'date',
              option: { format: 'YYYY-MM-DD HH:mm:ss' },
            },
          ],
        },
        {
          title: '映射',
          dataIndex: 'status',
          pipes: [
            {
              pipe: 'textmap',
              option: {
                textMap: [
                  { origin: 1, result: '在线' },
                  { origin: 0, result: '离线' },
                ],
              },
            },
          ],
          type: 'tags',
          colors: [
            {
              exp: 'status === 1',
              color: 'green',
            },
            {
              exp: 'status === 0',
              color: 'red',
            },
          ],
        },
        {
          title: '住址',
          dataIndex: 'address',
          headComp: {
            compName: 'lyy-icon',
            prop: {
              iconName: 'QuestionCircleOutlined',
            },
          },
        },
        {
          title: '详细地址',
          dataIndex: 'address',
          joins: ['a', 'b', 'c'],
          separator: ' ',
          pipes: [
            {
              pipe: 'textmap',
              option: {
                textMap: [
                  { origin: '广东', result: '广东省' },
                  { origin: '番禺', result: '番禺区' },
                ],
              },
            },
          ],
        },
        {
          title: '已售出',
          dataIndex: 'sale',
          type: 'panel',
          option: {
            panelColumns: [
              {
                title: '业务模式',
                dataIndex: ['售卖', '租赁', '分成'],
                type: 'text',
              },
              {
                title: '售出数量',
                dataIndex: ['quantitySold', 'quantityLeased', 'quantityShared'],
                style: {
                  'text-align': 'right',
                },
                pipes: ['decimal'],
              },
            ],
          },
        },
        {
          title: '商品分类',
          dataIndex: 'sale',
          type: 'panel',
          option: {
            fetch: {
              url: '/commodity/category/all',
              method: 'get',
              payloads: [
                {
                  key: 'classifyId',
                  source: 't1',
                  sourceKey: 'currentRow.status',
                },
              ],
              interceptors: {
                responseInterceptor: (res) => {
                  res.data = {
                    code: '200',
                    message: '成功',
                    data: '娱乐类/娃娃机',
                  }
                  return res
                },
              },
            },
          },
        },
        {
          title: '操作',
          dataIndex: '',
          width: 150,
          type: 'operation',
          childrens: [
            {
              compName: 'lyy-button',
              prop: {
                text: '详情',
              },
              actions: [
                {
                  event: 'click',
                  action: 'linkto',
                  option: {
                    url: '/detail',
                    payloads: [
                      {
                        key: 'name',
                        source: 't1',
                        sourceKey: 'currentRow.name',
                      },
                    ],
                  },
                },
              ],
            },
            {
              compName: 'lyy-button',
              prop: {
                text: '编辑',
                show: {
                  exp: 'status === 1',
                },
              },
            },
            {
              compName: 'lyy-button',
              prop: {
                text: '删除',
                danger: true,
              },
            },
            {
              compName: 'lyy-button',
              prop: {
                text: '上架',
              },
            },
            {
              compName: 'lyy-button',
              prop: {
                text: '下架',
                forbid: {
                  exp: 'status === 0',
                },
              },
              actions: [
                {
                  event: 'click',
                  action: 'linkto',
                  option: {
                    url: '/detail',
                    payloads: [
                      {
                        key: 'age',
                        source: 't1',
                        sourceKey: 'currentRow.age',
                      },
                      {
                        key: 'rate',
                        source: 't1',
                        sourceKey: 'currentRow.rate',
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    actions: [],
  },
  // 分页
  {
    compId: 'p1',
    compName: 'lyy-pagination',
    prop: {
      fields: ['current', 'pageSize'],
    },
    actions: [
      {
        event: 'update',
        action: 'broadcast',
        option: {
          targetId: 'search1',
          event: 'mounted',
        },
      },
    ],
  },

  {
    compName: 'lyy-button',
    prop: {
      text: '提交',
    },
    actions: [
      {
        event: 'click',
        action: 'request',
        option: {
          url: '/yhysadf/awe',
          payloads: [{ key: 'id', value: '38416733419679745' }],
        },
        thenActions: [
          {
            belong: 'error',
            action: 'setValue',
            option: {
              targetId: 't2',
              targetPath: 'expandedRowKeys',
              sourceKey: 'ids',
              payloads: [
                { key: 'ids', source: 'tree1', sourceKey: 'selectedPaths' },
              ],
            },
          },
        ],
      },
    ],
  },
  {
    compId: 'tree1',
    compName: 'lyy-tree',
    prop: {
      fieldNames: {
        key: 'id',
        title: 'name',
        children: 'children',
      },
    },
  },
  {
    compId: 't2',
    compName: 'lyy-table',
    prop: {
      rowKey: 'id',
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          type: 'tree',
          option: {
            expandedExp: 'type === "folder"',
            dynamicTypes: [
              {
                type: 'link',
                typeExp: 'type === "dashboard"',
              },
            ],
          },
          actions: [
            {
              event: 'click',
              action: 'linkto',
              option: {
                url: '/detail',
                payloads: [
                  {
                    key: 'name',
                    source: 't1',
                    sourceKey: 'currentRow.name',
                  },
                ],
              },
            },
          ],

          prevComps: [
            {
              compName: 'lyy-icon',
              style: { paddingRight: '4px' },
              prop: {
                iconName: 'FolderOpenOutlined',
                iconProps: {
                  style: {
                    color: '#ffa05c',
                  },
                },
                show: {
                  exp: 'type === "folder"',
                },
              },
            },
            {
              compName: 'lyy-icon',
              style: { paddingRight: '4px' },
              prop: {
                iconName: 'ProfileOutlined',
                iconProps: {
                  style: {
                    color: '#2cb554',
                  },
                },
                show: {
                  exp: 'type === "dashboard"',
                },
              },
            },
          ],
        },
        {
          title: '创建人',
          dataIndex: 'createdby_name',
          sorter: true,
        },
        {
          title: '最后修改人',
          dataIndex: 'updatedby_name',
          sorter: true,
        },
        {
          title: '最后修改时间',
          dataIndex: 'updated',
          type: 'datetime',
          sorter: true,
        },
      ],
    },
  },

  // 表格合并单元格
  {
    compId: 't3',
    compName: 'lyy-table',
    prop: {
      rowKey: 'sale_send_id',
      mergeUniqueField: ['sale_send_id'],
      mergeField: [
        'doc_date',
        'doc_no',
        'series_no',
        'bpartner_name',
        'status',
        'fee',
        'sale_user_name',
        'create_user_name',
        'settle_amount',
        'total_amount',
        'pay_amount',
        'qty_tag',
        'diff_qty_tag',
      ],

      columns: [
        {
          title: '日期',
          dataIndex: 'doc_date',
          width: 200,
          fixed: 'left',
          // customCell: (record, rowIndex, column) => {
          //   // 行合并
          //   return {
          //     rowSpan: 3,
          //     // colSpan: 1,
          //   }
          // },
        },
        {
          title: '单号',
          dataIndex: 'doc_no',
          width: 150,
          fixed: 'left',
        },
        {
          title: '流水号',
          dataIndex: 'series_no',
          width: 90,
        },
        {
          title: '客户',
          dataIndex: 'bpartner_name',
          width: 90,
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 80,
          pipes: [
            {
              pipe: 'textmap',
              option: {
                textMap: [
                  { result: '新建', origin: 1 },
                  { result: '审核中', origin: 2 },
                  { result: '审核通过', origin: 3 },
                  { result: '审核不通过', origin: 4 },
                  { result: '已作废', origin: 5 },
                ],
              },
            },
          ],
          type: 'tags',
          colors: [
            {
              exp: 'status === 1',
              color: 'blue',
            },
            {
              exp: 'status === 2',
              color: 'grey',
            },
            {
              exp: 'status === 3',
              color: 'green',
            },
            {
              exp: 'status === 4',
              color: 'red',
            },
            {
              exp: 'status === 5',
              color: '#ccc',
            },
          ],
        },
        {
          title: '仓库',
          dataIndex: 'warehouse_name',
          width: 90,
        },
        {
          title: '产品编号',
          dataIndex: 'product_value',
          width: 90,
        },
        {
          title: '产品名称',
          dataIndex: 'product_name',

          width: 90,
        },
        {
          title: '颜色',
          dataIndex: 'color_name',
          width: 90,
        },
        {
          title: '色号',
          dataIndex: 'color_code',
          width: 90,
        },
        {
          title: '单位',
          dataIndex: 'uom_name',
          width: 90,
        },
        {
          title: '设备数',
          dataIndex: 'piece_qty',
          width: 90,
        },
        {
          title: '累加和',
          dataIndex: 'qty',
          width: 90,
        },
        {
          title: '计价单位',
          dataIndex: 'price_uom_name',
          width: 90,
        },
        {
          title: '计价数量',
          dataIndex: 'price_qty',
          width: 90,
        },
        {
          title: '单价',
          dataIndex: 'price',
          width: 90,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '金额',
          dataIndex: 'price_amount',
          width: 120,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '实收金额',
          dataIndex: 'amount',
          width: 120,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '费用',
          dataIndex: 'fee',
          width: 90,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '付款金额',
          dataIndex: 'pay_amount',
          width: 90,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '总金额',
          dataIndex: 'total_amount',
          width: 120,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '已核销金额',
          dataIndex: 'settle_amount',
          width: 90,
          pipes: [
            {
              pipe: 'currency',
              option: {
                currency: 'CNY',
              },
            },
          ],
        },
        {
          title: '备注',
          dataIndex: 'description',
          width: 200,
        },
        {
          title: '经手人',
          dataIndex: 'create_user_name',
          width: 90,
        },
        {
          title: '业务人',
          dataIndex: 'sale_user_name',
          width: 90,
        },
        {
          title: '操作列',
          type: 'operation',
          fixed: 'right',
          childrens: [
            {
              compName: 'lyy-button',
              prop: {
                text: '查看',
              },
              actions: [
                {
                  event: 'click',
                  action: 'linkto',
                  option: {
                    url: '/sale/saleSendDetail',
                    tab: '_self',
                    mode: 'query',
                    payloads: [
                      {
                        key: 'sale_send_id',
                        source: 'data_table',
                        sourceKey: 'currentRow.sale_send_id',
                      },
                    ],
                  },
                },
              ],
            },
            {
              compName: 'lyy-button',
              prop: {
                text: '编辑',
                forbid: {
                  exp: 'status !== 1',
                },
              },
              actions: [
                {
                  event: 'click',
                  action: 'linkto',
                  option: {
                    url: '/sale/saleSendAdd',
                    tab: '_self',
                    mode: 'query',
                    payloads: [
                      {
                        key: 'sale_send_id',
                        source: 'data_table',
                        sourceKey: 'currentRow.sale_send_id',
                      },
                    ],
                  },
                },
              ],
            },
            {
              compName: 'lyy-popconfirm',
              prop: {
                title: '您确定要删除吗',
                text: '删除',
                okText: '删除',
                cancelText: '取消',
                danger: true,
                forbid: {
                  exp: 'status !== 1',
                },
              },
              actions: [
                {
                  event: 'click',
                  action: 'request',
                  option: {
                    url: '/api/form/delete/saleSend',
                    method: 'post',
                    payloads: [
                      {
                        key: 'sale_send_id',
                        source: 'data_table',
                        sourceKey: 'currentRow.sale_send_id',
                      },
                    ],
                  },
                  thenActions: [
                    {
                      action: 'broadcast',
                      option: {
                        targetId: 'search_comp',
                        event: 'mounted',
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  },
]
</script>
