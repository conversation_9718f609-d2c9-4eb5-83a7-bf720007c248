import qs from 'query-string'
import axios from 'axios'
import type { AxiosInstance } from 'axios'
import type { RequestConfig, Interceptors } from './types'
import { Progress } from './progress'
import { getIn, actionHoc as LYY } from '@leyaoyao/libs'

const streamType = new Set(['blob', 'arraybuffer'])
export class Request {
  instance: AxiosInstance
  interceptors?: Interceptors
  pendingRequest?: Map<string, AbortController>
  progress?: Progress

  constructor(config: RequestConfig) {
    this.instance = axios.create(config)
    this.interceptors = config.interceptors
    this.pendingRequest = new Map()
    this.progress = new Progress()

    this.setupInterceptor()
  }

  /**
   * 拦截器执行顺序
   * 1. 单个请求的 (请求) 拦截器
   * 2. 对应实例的 (请求) 拦截器
   * 3. 类的 (请求) 拦截器
   * 4. 类的 (响应) 拦截器
   * 5. 对应实例的 (响应) 拦截器
   * 6. 单个请求的 (响应) 拦截器
   */
  setupInterceptor(): void {
    // 类的 (请求) 拦截器
    this.instance.interceptors.request.use((config: RequestConfig) => {
      const abortController = new AbortController()
      config.signal = abortController.signal
      config.controller = abortController

      this.cancelDupliRequest(config)
      this.recordRequest(config)
      if (config.customOption?.loading) {
        this.progress?.loading(config.customOption?.loading)
      }
      return config
    })

    // 类的 (响应) 拦截器
    this.instance.interceptors.response.use(
      (res) => {
        if ((res?.config as RequestConfig)?.customOption?.loading) {
          this.progress?.unloading()
        }
        this.removePendingRequest(res.config)
        return res
      },
      (err) => {
        if (err?.config?.customOption?.loading) {
          this.progress?.unloading()
        }
        this.removePendingRequest(err.config ?? {})
        return Promise.reject(err)
      },
    )

    // 对应实例的 (请求) 拦截器
    this.instance.interceptors.request.use(
      this.interceptors?.requestInterceptor,
      this.interceptors?.requestInterceptorCatch,
    )

    // 对应实例的 (响应) 拦截器
    this.instance.interceptors.response.use(
      this.interceptors?.responseInterceptor,
      this.interceptors?.responseInterceptorCatch,
    )
  }

  request<T = any>(config: RequestConfig): Promise<T> {
    return new Promise((resolve, reject) => {
      // 单个请求的 (请求) 拦截器
      if (config.interceptors?.requestInterceptor) {
        config =
          typeof config.interceptors.requestInterceptor === 'function'
            ? config.interceptors.requestInterceptor(config)
            : eval(config.interceptors.requestInterceptor)(config, LYY)
      }

      this.instance
        .request({
          ...config,
          transformRequest: [
            function (data, headers) {
              if (
                headers &&
                (headers['Content-Type'] === 'multipart/form-data' ||
                  headers['Content-Type'] ===
                    'application/x-www-form-urlencoded') &&
                !(data instanceof FormData)
              ) {
                const formData = new FormData()
                for (const key of Object.keys(data))
                  formData.append(key, data[key])

                return formData
              }
              if (!headers['Content-Type']) {
                headers['Content-Type'] = 'application/json'
              }
              return data instanceof FormData ? data : JSON.stringify(data)
            },
          ],
        })
        .then((res) => {
          // 单个请求的 (响应) 拦截器
          if (config.interceptors?.responseInterceptor) {
            res =
              typeof config.interceptors.responseInterceptor === 'function'
                ? config.interceptors.responseInterceptor(res)
                : eval(config.interceptors.responseInterceptor)(res, LYY)
          }

          if (res?.data?.fault) resolve(res.data)
          // @ts-ignore
          if (streamType.has(res.config?.responseType)) resolve(res)
          if (config?.customOption?.jsonPath === '$' ||
            config?.customOption?.stopFailFeedback ||
            config?.customOption?.unhandled
          ) {
            resolve(res?.data ?? res)
          }
          const result = getIn(res?.data ?? res, config?.customOption?.jsonPath)
          if (config?.customOption?.parse) resolve(JSON.parse(result))
          resolve(result)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  get<T = any>(config: RequestConfig): Promise<T> {
    return this.request({ ...config, method: 'GET' })
  }

  post<T = any>(config: RequestConfig): Promise<T> {
    return this.request({ ...config, method: 'POST' })
  }

  delete<T = any>(config: RequestConfig): Promise<T> {
    return this.request({ ...config, method: 'DELETE' })
  }

  patch<T = any>(config: RequestConfig): Promise<T> {
    return this.request({ ...config, method: 'PATCH' })
  }

  head<T = any>(config: RequestConfig): Promise<T> {
    return this.request({ ...config, method: 'HEAD' })
  }

  put<T = any>(config: RequestConfig): Promise<T> {
    return this.request({ ...config, method: 'PUT' })
  }

  // 记录当前请求
  private recordRequest(config: RequestConfig) {
    const key = generateRequestKey(config)
    if (!this.pendingRequest?.has(key)) {
      this.pendingRequest?.set(key, config.controller!)
    }
  }

  // 移除 pendingRequest 中的当前请求
  private removePendingRequest(config: RequestConfig) {
    const key = generateRequestKey(config)
    if (this.pendingRequest?.has(key)) {
      this.pendingRequest.delete(key)
    }
  }

  // 取消重复请求
  private cancelDupliRequest(config: RequestConfig) {
    const key = generateRequestKey(config)
    if (this.pendingRequest?.has(key)) {
      // 如果是重复的请求，则执行对应的 abort方法
      // const controller = this.pendingRequest.get(key)
      // controller && controller.abort()
      // // 将前一次重复的请求移除
      // this.pendingRequest.delete(key)
    }
  }

  // 取消所有请求
  cancelAllRequest() {
    if (this.pendingRequest)
      for (const controller of this.pendingRequest) controller.abort()
    this.pendingRequest?.clear()
  }
}

/**
 * 为每次请求生成唯一的key (2种情况)
 * 不全等情况: 唯一key = 请求方式(method) + 请求地址(url)
 * 全等情况: 唯一key = 请求方式(method) + 请求地址(url) + 请求参数(params data)
 */
const generateRequestKey = (config: RequestConfig) => {
  const { method, url, params, data, customOption } = config
  const { dupliRequestStrictEquality } = customOption || {}
  let requestKey = method + '@' + url
  if (dupliRequestStrictEquality) {
    requestKey += '@' + qs.stringify(params) + '@' + qs.stringify(data)
  }
  return requestKey
}
