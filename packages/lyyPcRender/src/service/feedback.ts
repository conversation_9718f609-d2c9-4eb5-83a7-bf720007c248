import { message, notification } from 'ant-design-vue'
import { IFeedback } from './types'

export function feedback(option: IFeedback, feedText?: string) {
  const {
    type,
    duration,
    placement,
    status = 'info',
    title = '提示信息',
    message: msgText,
  } = option
  if (type === 'notification') {
    notification[status]({
      message: title,
      description: msgText ? msgText : feedText,
      placement: placement ? placement : 'topRight',
      duration: duration,
    })
    return
  }

  message[status](msgText ? msgText : feedText, duration)
}
