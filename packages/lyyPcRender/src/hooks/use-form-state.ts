import {
  inject,
  ref,
  watch,
  getCurrentInstance,
  toRefs,
  Ref,
  computed,
  nextTick,
} from 'vue'
import { UPDATE_MODELVALUE } from '../constants/action-type'
import { useStore } from '@leyaoyao/libs'

export const useFormState = (
  props,
  isUpdateFormState?: boolean,
  isObjValue?: boolean,
) => {
  const store = useStore()

  const { field, fields } = toRefs(props.prop)
  const formState = inject<any>('formState') ?? ref({})
  const isCustomerForm = inject<any>('isCustomerForm')
  const { emit } = getCurrentInstance()
  const isEdit = inject<Ref<boolean>>('isEdit')
  const rowData = inject<Ref<boolean>>('rowData')
  const formDisabled = inject<Ref<boolean>>('formDisabled')
  const formId = inject<string>('formId')
  const disabled = computed(() => {
    return formDisabled?.value || props.prop.disabled
  })

  const setVal = (_filed, newVal) => {
    if (rowData) {
      rowData[_filed] = newVal
    } else {
      formState.value[_filed] = newVal
    }
  }
  const getVal = (_filed, source) => {
    return rowData ? rowData[_filed] : source[_filed]
  }
  const setIsFormChange = () => {
    store.setValue('formIsChange-' + formId, true)
  }

  const updateFormState = (newValue) => {
    if (formState.value && field?.value) {
      setVal(field.value, newValue)
      setIsFormChange()
      return
    }
    if (formState.value && fields?.value) {
      const _newValue = newValue ?? []
      for (const [index, fd] of fields.value.entries()) {
        setVal(fd, _newValue[index])
      }
      setIsFormChange()
      return
    }
  }

  const handleFormStateChange = (newFormState) => {
    if (!newFormState) return
    if (field?.value) {
      const targetValue = getVal(field.value, newFormState)
      if (!isObjValue && targetValue !== props.modelValue && !rowData) {
        emit(UPDATE_MODELVALUE, getVal(field.value, newFormState))
        return
      }
      if (isObjValue && targetValue !== props.modelValue?.datasource) {
        emit(
          UPDATE_MODELVALUE,
          Object.assign(props.modelValue ?? {}, {
            datasource: targetValue,
          }),
        )
        return
      }
    }
    if (fields?.value !== undefined && !rowData) {
      const fieldsFilter = fields?.value?.filter((item) => !!item)
      const modelValue = props.modelValue ?? []
      const res = fieldsFilter?.some((fd, index) => {
        return formState.value[fd] !== modelValue[index]
      })
      if (res) {
        const newValue = fieldsFilter?.reduce((prev, cur) => {
          prev.push(formState.value[cur])
          return prev
        }, [])
        emit(UPDATE_MODELVALUE, newValue)
      }
      return
    }
    return
  }

  watch(
    formState.value,
    (newFormState) => {
      !isCustomerForm && handleFormStateChange(newFormState)
    },
    {
      // form有默认值时可以直接同步到子组件
      immediate: true,
    },
  )
  isUpdateFormState &&
    watch(
      () => props.modelValue,
      (newValue) => {
        if (!formId) return
        if (field?.value) {
          const oldValue = getVal(field.value, formState.value)
          if (!isObjValue && newValue !== oldValue) {
            setVal(field.value, newValue)
            return
          }
          if (isObjValue && newValue.datasource !== oldValue) {
            setVal(field.value, newValue.datasource)
            return
          }
        }
        if (fields?.value !== undefined) {
          const res = fields.value.some((fd, index) => {
            return formState.value[fd] !== newValue[index]
          })
          if (res) {
            for (const [index, fd] of fields.value.entries()) {
              setVal(fd, newValue[index])
            }
          }
          return
        }
        return
      },
    )

  return {
    formState,
    isEdit,
    formId,
    disabled,
    updateFormState,
  }
}
