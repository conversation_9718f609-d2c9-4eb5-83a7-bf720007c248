import { createApp, h } from 'vue'
import Antd from 'ant-design-vue'

import { components } from '../components'
import PcRenderTemplate from '../render/pc-render-template.vue'
import LyyPcRender from '../render/lyy-render.vue'
import version from '../../config/version.mjs'
import Watcher from '@leyaoyao/libs/utils/Watcher'
import { setObject, setDataSetList } from '@leyaoyao/libs/store/jsobject'
import { customComponents } from '../../../customComponents'
import VueI18n from '../locales'
import VXETable from 'vxe-table'
import VxeUI from 'vxe-pc-ui'

const componentsResult = Object.assign(customComponents, components)
export const vue3ToVue2 = (
  WrapperComponent,
  wrapperId,
  router,
  notKeepAlive,
) => {
  let vm
  return {
    mounted() {
      this.init()
    },
    methods: {
      async init() {
        await import('xe-utils')
        await import('vxe-table/lib/style.css')
        await import('vxe-pc-ui/lib/style.css')
        vm = createApp({
          render: () => {
            return h(WrapperComponent, {
              ...this.$props,
            })
          },
        })
        vm.use(VueI18n)
        vm.use(VxeUI)
        vm.use(VXETable)
        vm.use(router)
        window.$router = router
        window.$route = router.app.$route
        vm.use(Antd).use({
          install() {
            let aliasName = window.__aliasName__ || ''
            if (aliasName) {
              aliasName += version.verVue2
            }
            for (const [name, comp] of Object.entries(componentsResult)) {
              vm.component(name + aliasName, comp)
            }
            vm.component('pc-render-template', PcRenderTemplate)
            vm.component('lyy-pc-render', LyyPcRender)
          },
        })
        vm.mount(`#${wrapperId}`)
      },
    },
    props: WrapperComponent.props,
    render() {
      vm && vm.$forceUpdate && vm.$forceUpdate()
    },
    beforeDestroy() {
      Watcher.$clear()
      notKeepAlive && vm.unmount()
    },
  }
}

export const vue3ToAny = (route) => {
  let vm
  const router = route
  return {
    init: async (
      wrapperId,
      pageConfig = [],
      JsObject = [],
      dataSetList = [],
    ) => {
      // await import('ant-design-vue/dist/antd.css')
      setObject(JsObject)
      setDataSetList(dataSetList)
      vm = createApp({
        render: () => {
          return h(LyyPcRender, {
            pageConfig,
          })
        },
      })
      if (router.currentRoute) {
        vm.use(router)
      }
      window.$router = router
      window.$route = router.currentRoute || router.location
      await import('xe-utils')
      await import('vxe-table/lib/style.css')
      await import('vxe-pc-ui/lib/style.css')
      vm.use(VueI18n)
      vm.use(VxeUI)
      vm.use(VXETable)
      vm.use(Antd).use({
        install() {
          let aliasName = window.__aliasName__ || ''
          if (aliasName) {
            aliasName += version.verVue2
          }
          for (const [name, comp] of Object.entries(componentsResult)) {
            vm.component(name + aliasName, comp)
          }
          vm.component('pc-render-template', PcRenderTemplate)
          vm.component('lyy-pc-render', LyyPcRender)
        },
      })
      vm.mount(`#${wrapperId}`)
      return vm
    },
    beforeDestroy() {
      Watcher.$clear()
      vm.unmount()
    },
    getInstance() {
      return vm
    },
  }
}
