<script setup lang="ts">
import { PropType } from './type'
import { computed, ref } from 'vue'
import { useShow } from '@leyaoyao/libs'
const props = defineProps<{
  prop: PropType
}>()
const { prop } = props
const { titles = [], col = 4, showChildLine = 2 } = props.prop
const showAllChildrens = ref(false)
let showChildNum = 8
const elements = computed(() => {
  return props.prop.children
    ? props.prop.children?.filter((child) => {
        return useShow(child)
      })
    : []
})
if (elements.value) {
  let spanNumber = 0
  let childNum = 0
  for (const e of elements.value) {
    if (spanNumber < showChildLine * 24) {
      let itemSpan = 24 / col
      // if (e.compName === 'lyy-text-area') {
      //   itemSpan = 24
      // }
      if (e.prop.span) {
        itemSpan = e.prop.span
      }
      spanNumber = spanNumber + itemSpan
      childNum = spanNumber > showChildLine * 24 ? childNum : childNum + 1
    }
  }
  showChildNum = childNum
}
const getSpan = function (child) {
  let span = 24 / col
  if (child.prop.span) {
    span = child.prop.span
  }
  return span
}
const showChildrensFn = () => {
  showAllChildrens.value = !showAllChildrens.value
}
</script>

<template>
  <div class="lyy-erp-fold-box lyy-container" :class="prop.class">
    <a-row :gutter="8">
      <template v-if="elements.length > 0">
        <template v-for="(child, i) in elements" :key="child.compId">
          <a-col
            :span="col ? getSpan(child) : 24"
            v-show="i < (showAllChildrens ? elements.length : showChildNum)"
          >
            <pc-render-template :elements="[child]"></pc-render-template>
          </a-col>
        </template>
      </template>
      <pc-render-template :elements="elements" v-else></pc-render-template>
    </a-row>
    <a-button
      class="default-btn"
      type="link"
      @click="showChildrensFn"
      :style="prop.buttonStyle"
      v-if="showChildNum < elements.length"
    >
      <template #icon>
        <UpSquareOutlined v-if="showAllChildrens" />
        <DownSquareOutlined v-if="!showAllChildrens" />
      </template>
      <template v-if="!showAllChildrens">
        {{ titles[0] ? titles[0] : '展开' }}
      </template>
      <template v-else>
        {{ titles[1] ? titles[1] : '收起' }}
      </template>
    </a-button>
  </div>
</template>
<style lang="scss" scoped>
.lyy-erp-fold-box {
  position: relative;
  overflow: hidden;
  .default-btn {
    color: #0987f5;
    line-height: 20px;
    height: 20px;
    float: right;
    margin-top: -16px;
    padding: 0;
    .anticon {
      margin-right: 5px;
    }
  }
}
</style>
