<script setup lang="ts">
import { computed, toRefs, ref, inject, watch, onUnmounted } from 'vue'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { UPDATE } from '../../constants/event-type'
import { ModelValueType, PropType } from './type'
import md5 from 'js-md5'
import _ from 'lodash'
import { dynamicRules, onRules } from '@leyaoyao/libs/hooks/use-rules'
import { useDep, useStore, removeDep } from '@leyaoyao/libs'
const props = defineProps<{
  compId: string
  modelValue?: ModelValueType
  prop: PropType
}>()

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const store = useStore()

// const value = computed({
//   get: () => props.modelValue,
//   set: (newValue) => {
//     emit(UPDATE_MODELVALUE, newValue)
//     emit(ACTION, { event: UPDATE })
//   }
// })

const formState = inject<any>('formState')
const formId = inject<any>('formId')

const formDisabled = inject<Ref<boolean>>('formDisabled')

const disabled = computed(() => {
  return formDisabled?.value || props.prop.disabled
})

const rules = computed(() => {
  const rules = props.prop.rules || []
  let dynamicRulesSet = []
  let onrulesSet = []
  if (props.prop?.dynamicRules) {
    dynamicRulesSet = dynamicRules(props.prop, props.prop?.dynamicRules)
  }
  if (props.prop?.onrules) {
    onrulesSet = onRules(props.prop?.onrules)
  }
  const res = [...rules, ...dynamicRulesSet, ...onrulesSet]
  return res
})
const {
  field,
  label,
  type,
  placeholder,
  allowClear,
  showCount,
  maxlength,
  addonAfter,
  suffix,
  size,
  slotLabel,
  icon,
  visibilityToggle,
  isMd5,
} = toRefs(props.prop)

const value = ref('')
const handleUpdate = (e) => {
  let targetValue = e.target.value
  const needMd5 = isMd5?.value ?? true
  if (needMd5 === true) {
    // 由于空字符串经过MD5加密之后就非空了，故需要做此处理
    targetValue = e.target.value === '' ? '' : md5(e.target.value)
  }
  setDataBaseFn(targetValue)
}

// 表单重置时处理
watch(formState.value, (newFormState) => {
  if (!newFormState[field.value]) {
    value.value = ''
  }
})

const setDataBaseFn = (data) => {
  if (formState.value && field?.value) {
    formState.value[field.value] = data
    store.setValue('formIsChange-' + formId, true)
    return
  }
  emit(UPDATE_MODELVALUE, data)
  emit(ACTION, { event: UPDATE })
}
// 清空
const clear = () => {
  setDataBaseFn('')
  value.value = ''
}
const actionsSet = {
  clear,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>

<template>
  <div class="lyy-component">
    <a-form-item :name="field" :rules="rules" :colon="prop.colon">
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
      </template>
      <a-input-password
        v-model:value="value"
        :type="prop.type"
        :placeholder="prop.placeholder || `请输入${prop.label ?? ''}`"
        :allow-clear="prop.allowClear"
        :disabled="disabled"
        :show-count="prop.showCount"
        :maxlength="prop.maxlength"
        :addon-after="prop.addonAfter"
        :suffix="prop.suffix"
        :size="prop.size"
        :visibilityToggle="prop.visibilityToggle"
        @change="handleUpdate"
      />
    </a-form-item>
  </div>
</template>
