<script lang="ts">
import {
  defineComponent,
  computed,
  defineProps,
  onBeforeMount,
  onMounted,
  ref,
  watch,
  toRefs,
  onBeforeUnmount,
} from 'vue'
import LyyIcon from '../lyy-icon/index.vue'

export default defineComponent({
  name: 'lyy-tree',
  components: {
    LyyIcon,
  },
})
</script>

<script lang="ts" setup>
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { BEFORE_MOUNT, MOUNTED, UPDATE } from '../../constants/event-type'
import { useStore } from '@leyaoyao/libs/store'
import _ from 'lodash'
import { ITreeProp } from './type'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import { useFormState } from '../../hooks'
const props = defineProps<{
  id: string
  prop: ITreeProp
  modelValue?: string[]
}>()
const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

useFormState(props, true)
// 展开
const expandedKeys = ref<string[]>([])
const labelCol = computed(() => {
  return Object.keys(props.prop.labelCol?.style ?? []).length > 0
    ? props.prop.labelCol
    : null
})
// 选中的键值 (多选)
const checkedKeys = computed({
  get() {
    if (
      props.modelValue &&
      props.modelValue.length > 0 &&
      !props.prop?.checkedNodes?.length &&
      noChecked.value
    ) {
      getCheckedNodes(treeData.value, props.modelValue)
    }
    return props.prop.checkStrictly
      ? { checked: props.modelValue || [], halfChecked: [] }
      : props.modelValue || []
  },
  set(newValue) {
    if (!(props.prop.checkStrictly && props.prop.checkable)) {
      let value = props.prop.checkStrictly ? newValue.checked : newValue
      if (props.prop.onlySaveLeaves) {
        value = value.filter((item) => {
          if (!parentNodes.includes(item)) return item
        })
      }
      emit(UPDATE_MODELVALUE, value)
      emit(ACTION, { event: UPDATE })
    }
  },
})

// 选中的键值 (单选)
const store = useStore()
const selectedKeys = computed<string[]>({
  get: () => [store.getValue(props.id, 'selectedKeys')],
  set: (newValue) => {
    selectedPaths(newValue[0])
    store.setValue(props.id, newValue[0], 'selectedKeys')
  },
})
// 选中的键路径 (单选)
const selectedPaths = (selectId: string) => {
  if (selectId) {
    let paths = []
    treeData.value?.some((item) => {
      getPathById(item, selectId, (res) => {
        paths = res
      })
      if (paths.length > 0) return true
    })
    store.setValue(props.id, paths, 'selectedPaths')
  }
}

function getPathById(tree, id, callback) {
  //定义变量保存当前结果路径
  const temppath: string[] = []
  try {
    const getNodePath = (node) => {
      temppath.push(node[fieldNames.value.key])

      //找到符合条件的节点，通过throw终止掉递归
      if (node[fieldNames.value.key] === id) {
        // console.log(66, id, temppath)
        throw '找到啦'
      }
      if (node.children && node.children.length > 0) {
        for (let i = 0; i < node.children.length; i++) {
          getNodePath(node.children[i])
        }
        //当前节点的子节点遍历完依旧没找到，则删除路径中的该节点
        temppath.pop()
      } else {
        //找到叶子节点时，删除路径当中的该叶子节点
        temppath.pop()
      }
    }
    getNodePath(tree)
  } catch {
    const result = temppath
    callback(result)
  }
}

// 树节点数据
const treeData = computed(() => {
  return props.prop.options
})
// 树节点的数据映射
const fieldNames = computed(() => {
  if (props.prop.fieldNames) return props.prop.fieldNames
  return {
    key: 'key',
    title: 'title',
    children: 'children',
  }
})
watch(treeData, () => {
  proxyData.setProxyData(`${props.id}.prop.checkedNodes`, [])
  checkedKeys.value = getDefaultCheckedKeys(treeData.value, [])
})
const noChecked = ref(true)
const getCheckedNodes = (treeData, checkedKeys) => {
  if (treeData)
    for (const item of treeData) {
      if (checkedKeys.includes(item[props.prop.fieldNames.key])) {
        props.prop?.checkedNodes?.push(item)
      }
      if (item.children) getCheckedNodes(item.children, checkedKeys)
    }
  proxyData.setProxyData(
    `${props.id}.prop.checkedNodes`,
    props.prop.checkedNodes ?? [],
  )
  emit(ACTION, { event: 'check' })
}
const getDefaultCheckedKeys = (treeData, checked) => {
  if (treeData)
    for (const item of treeData) {
      if (item[props.prop.fieldNames.checked])
        checked.push(item[props.prop.fieldNames.key])
      if (item[props.prop.fieldNames.children])
        getDefaultCheckedKeys(item[props.prop.fieldNames.children], checked)
    }
  return checked
}
const treeDom = ref(null)
onBeforeMount(() => {
  emit(ACTION, { event: BEFORE_MOUNT })
})
onMounted(() => {
  emit(ACTION, { event: MOUNTED })
  props.prop.checkedNodes = []
  proxyData.setProxyData(`${props.id}.prop.checkedNodes`, [])
})
onBeforeUnmount(() => {
  // eslint-disable-next-line vue/no-mutating-props
  props.prop.checkedNodes = []
})
const checkedNodes = ref([])
const { slotLabel, icon, prefixIcons } = toRefs<any>(props.prop)
const selectable = computed(() => {
  return props.prop.selectable === undefined ? true : props.prop.selectable
})
const check = (selectedKeys, e) => {
  noChecked.value = false
  if (props.prop.checkStrictly) {
    checkedNodes.value = e.checkedNodes
    let list = _.cloneDeep(selectedKeys.checked)
    const nodes = _.cloneDeep(e.checkedNodes)
    if (e.checked) {
      list = setCheckChildren(e.node, list)
      list = setCheckParent(e.node, list)
    } else {
      list = delCheck(e.node, list, [])
    }
    // eslint-disable-next-line vue/no-mutating-props
    proxyData.setProxyData(
      `${props.id}.prop.checkedNodes`,
      _.cloneDeep(checkedNodes.value),
    )
    emit(UPDATE_MODELVALUE, list)
    emit(ACTION, { event: UPDATE })
  } else {
    // eslint-disable-next-line vue/no-mutating-props
    props.prop.checkedNodes = e.checkedNodes
    // eslint-disable-next-line vue/no-mutating-props
    props.prop.halfCheckedKeys = e.halfCheckedKeys
    proxyData.setProxyData(`${props.id}.prop.checkedNodes`, e.checkedNodes)
    proxyData.setProxyData(
      `${props.id}.prop.halfCheckedKeys`,
      e.halfCheckedKeys,
    )
  }
  emit(ACTION, { event: 'check' })
}
const select = (selectedKeys, e) => {
  // eslint-disable-next-line vue/no-mutating-props
  props.prop.selectedNodes = e.selectedNodes
  // eslint-disable-next-line vue/no-mutating-props
  props.prop.clickNode = e.node.dataRef
}
const delCheck = (item, list) => {
  const { children, key } = props?.prop?.fieldNames
  if (item[children]) {
    for (const e of item[children]) {
      list = list.filter((item) => item != e[key])
      for (const [i, node] of checkedNodes.value.entries()) {
        if (!list.includes(node[key])) {
          checkedNodes.value.splice(i, 1)
        }
      }
      if (e[children]) {
        list = delCheck(e, list)
      }
    }
  }
  return list
}
const setCheckChildren = (item, list) => {
  const { children, key } = props?.prop?.fieldNames
  if (item[children]) {
    for (const e of item[children]) {
      if (!list.includes(e[key])) {
        list.push(e[key])
        checkedNodes.value.push(e)
      }
      if (e[children]) {
        list = setCheckChildren(e, list)
      }
    }
  }
  return list
}
const setCheckParent = (item, list) => {
  const { children, key } = props?.prop?.fieldNames
  if (item.parent) {
    if (!list.includes(item.parent.key)) {
      list.push(item.parent.key)
      checkedNodes.value.push(item.parent.node)
    }
    list = setCheckParent(item.parent, list)
  }
  return list
}
// 仅保存叶子结点
const onlySaveLeaves = computed(() => prop.onlySaveLeaves ?? false)
const getParentNodes = (tree, arr = []) => {
  if (tree)
    for (const item of tree) {
      const children = fieldNames.value.children
      if (item[children as string]) {
        const key = fieldNames.value.key
        if (!arr.includes(item[key])) arr.push(item[key])
        getParentNodes(item[children as string], arr)
      }
    }
  return arr
}
console.log(props.prop)
const parentNodes: string[] = getParentNodes(treeData.value)
</script>

<template>
  <div class="lyy-component">
    <a-form-item
      :labelCol="labelCol"
      :name="prop.field"
      :id="props.id"
      :rules="prop.rules"
    >
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
      </template>
      <!-- 由于expandedKeys和defaultExpandAll互斥 所以作以下处理 -->
      <a-tree
        v-model:checkedKeys="checkedKeys"
        v-model:selectedKeys="selectedKeys"
        :tree-data="treeData"
        :checkable="prop.checkable"
        :selectable="selectable"
        :disabled="prop.disabled"
        :fieldNames="fieldNames"
        :multiple="prop.multiple"
        :show-icon="prop.showIcon ?? true"
        :height="prop.height"
        :defaultExpandAll="true"
        :checkStrictly="prop.checkStrictly"
        v-if="prop.defaultExpandAll && treeData?.length > 0"
        @check="check"
        @select="select"
        ref="treeDom"
      >
        <!-- 节点前缀图标 -->
        <template #icon="data">
          <lyy-icon v-if="icon" :prop="icon"></lyy-icon>
          <template v-else>
            <template v-for="item in prefixIcons">
              <template
                v-if="
                  data[item?.customOption?.key] === item?.customOption?.value
                "
              >
                <lyy-icon
                  :key="item?.customOption?.key"
                  :prop="item"
                ></lyy-icon>
              </template>
            </template>
          </template>
        </template>
      </a-tree>
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:checkedKeys="checkedKeys"
        v-model:selectedKeys="selectedKeys"
        :tree-data="treeData"
        :checkable="prop.checkable"
        :disabled="prop.disabled"
        :fieldNames="fieldNames"
        :multiple="prop.multiple"
        :show-icon="prop.showIcon ?? true"
        :height="prop.height"
        :checkStrictly="prop.checkStrictly"
        @check="check"
        @select="select"
        ref="treeDom"
        v-else
      >
        <!-- 节点前缀图标 -->
        <template v-if="icon" #icon="data">
          <lyy-icon :prop="icon"></lyy-icon>
        </template>
      </a-tree>
    </a-form-item>
  </div>
</template>
