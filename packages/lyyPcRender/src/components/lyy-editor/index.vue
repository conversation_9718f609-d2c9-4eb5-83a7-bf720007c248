<script lang="ts">
export enum Language {
  JAVASCRIPT = 'javascript',
  TYPESCRIPT = 'typescript',
  JSON = 'json',
  SQL = 'sql',
  MARKDOWN = 'markdown',
  XML = 'xml',
  YML = 'yml',
  TXT = 'txt',
  YAML = 'yaml',
}
</script>

<script lang="ts" setup>
import './initialize'
import {
  computed,
  defineComponent,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
  withDefaults,
} from 'vue'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { UPDATE } from '../../constants/event-type'
import { PropType, ModelValueType } from './type'
import _ from 'lodash'
import { ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons-vue'
import { marked } from 'marked'

import { useFormState } from '../../hooks'
import type { editor } from 'monaco-editor'

defineComponent({
  name: 'lyy-editor',
})

interface Props {
  modelValue: ModelValueType
  prop: PropType
}
const openFormatDocument = computed(() => {
  return props.prop.isOpenFormatDocument
})
const props = withDefaults(defineProps<Props>(), {})

const restSwitch = ref(true)
const language = computed(() => {
  return props.prop.language ?? 'JSON'
})

/** 容器 */
const container = ref<HTMLElement>()

// 编辑器最大化处理
const isLarge = ref(true)

const maxEditor = () => {
  isLarge.value = !isLarge.value
  container.value?.classList.add('editor-fullscreen')
  editorInst?.layout({
    height: document.body.clientHeight,
    width: document.body.clientWidth,
  })
}

const minEditor = () => {
  isLarge.value = !isLarge.value
  container.value?.classList.remove('editor-fullscreen')
  editorInst?.layout({
    height: document.querySelector('#editorBox')?.clientHeight,
    width: document.querySelector('#editorBox')?.clientWidth,
  })
}

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const { updateFormState } = useFormState(props, true)

// ----------------------------------------------------------

// 编辑器实例
let editorInst: ReturnType<typeof editor.create> | undefined

// 大文本，特别是这种代码编辑器 不要包装成双向绑定，性能估计慢得一皮，不过你可以试下
defineExpose({
  /** 获取代码 */
  getCode() {
    return editorInst?.getValue() ?? ''
  },

  /** 设置代码 */
  setCode(code: string) {
    return editorInst?.setValue(code)
  },
})
const value = computed(() => {
  return props.modelValue
})
const htmlResult = computed(() => {
  if (
    props.prop.readOnly &&
    props.prop.language &&
    props.prop.language.toUpperCase() === 'MARKDOWN'
  ) {
    return marked(props.modelValue)
  }
})
// 改变表单值的方法
const changeFormValue = () => {
  const newValue = editorInst?.getValue()
  updateFormState(newValue)
  emit(UPDATE_MODELVALUE, newValue)
  emit(ACTION, { event: UPDATE })
}

watch(
  () => props.prop.language,
  async () => {
    const { editor } = await import('monaco-editor')

    const model = editorInst?.getModel()
    model &&
      props.prop.language &&
      editor.setModelLanguage(model, props.prop.language.toLowerCase())
  },
)

watch(
  value,
  (newValue, oldValue) => {
    // !newValue 是为了处理表单重置时，需要清空值
    if (!newValue || !oldValue || restSwitch.value) {
      restSwitch.value = false
      editorInst?.setValue(newValue ?? '')
      nextTick(() => {
        const { lineNumber = 0 } = editorInst?.getPosition() || {}
        editorInst?.setPosition({
          lineNumber,
          column: newValue ? newValue.length + 1 : 1,
        })
      })
    }
  },
  { immediate: true },
)

onMounted(async () => {
  const { default: hljs } = await import('./init-highlight')

  marked.setOptions({
    highlight: function (code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext'
      return hljs.highlight(code, { language }).value
    },
  })
  if (
    (props.prop.readOnly &&
      props.prop.language &&
      props.prop.language.toUpperCase() === 'MARKDOWN') ||
    !container.value
  ) {
    return
  }

  const { editor } = await import('monaco-editor')

  editorInst = editor.create(container.value, {
    value: props.modelValue,
    language: Language[language.value]
      ? Language[language.value]
      : Language.JSON,
    minimap: { enabled: false },
    tabSize: props.prop.tabSize ?? 2,
    lineNumbers: props.prop.lineNumbers ?? 'on',
    lineNumbersMinChars: 4,
    lineDecorationsWidth: 1,
    readOnly: props.prop.readOnly,
  })
  if (openFormatDocument.value) {
    setTimeout(() => {
      // 需要再次异步格式化一次不然不会生效
      editorInst.getAction(['editor.action.formatDocument'])._run()
    }, 120)
    // // 强制刷新一次
    editorInst.getAction(['editor.action.formatDocument'])._run()
  }
  changeFormValue()
  editorInst.onDidChangeModelContent(
    // 防抖优化性能
    _.debounce(() => {
      changeFormValue()
    }, 100),
  )
})

onUnmounted(() => {
  editorInst?.dispose()
  restSwitch.value = true
})
</script>

<template>
  <!-- language 无用属性，只是为了执行computed计算，以达到切换语言的效果 -->
  <div class="lyy-component">
    <a-form-item
      :name="prop.field"
      :rules="prop.rules"
      :colon="prop?.colon"
      :language="language"
    >
      <div class="preview-content-box" v-if="htmlResult" ref="container">
        <arrows-alt-outlined
          v-if="isLarge"
          class="position"
          @click="maxEditor"
        ></arrows-alt-outlined>
        <shrink-outlined
          v-else
          class="position"
          @click="minEditor"
        ></shrink-outlined>
        <div
          class="preview-content"
          :style="props.prop.style"
          v-html="htmlResult"
        ></div>
      </div>

      <div id="editorBox" v-else :style="props.prop.style">
        <div class="container" ref="container" :value="props.modelValue">
          <arrows-alt-outlined
            v-if="isLarge"
            class="position"
            @click="maxEditor"
          ></arrows-alt-outlined>
          <shrink-outlined
            v-else
            class="position"
            @click="minEditor"
          ></shrink-outlined>
          <!-- <div>{{ language }}</div> -->
        </div>
      </div>
    </a-form-item>
  </div>
</template>
<style lang="less" scoped>
.preview-content-box {
  position: relative;
  .position {
    font-size: 16px;
    position: absolute;
    top: 0;
    right: 20px;
    z-index: 1000;
    cursor: pointer;
  }
}
.preview-content {
  width: 100%;
  height: 250px;
  border: 1px solid #ccc;
  background-color: #fff;
  overflow-y: auto;
  padding: 8px;
  background-color: #f8f8f8;
  color: #333;
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  padding: 10px;
  border-radius: 3px;
}
#editorBox {
  width: 100%;
  height: 250px;
  border: 1px solid #ccc;
  .container {
    position: relative;
    height: 100%;
    width: 100%;
    .position {
      font-size: 16px;
      position: absolute;
      top: 0;
      right: 20px;
      z-index: 1000;
      cursor: pointer;
    }
  }
}

.editor-fullscreen {
  position: fixed !important;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100% !important;
  z-index: 9999;
  .preview-content {
    height: 100%;
  }
}
</style>
