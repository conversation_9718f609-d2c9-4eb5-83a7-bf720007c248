import type { Rule } from 'ant-design-vue/es/form'
import { IElement } from '../../global'
import { PropType as iconPropType } from '../lyy-icon/type'

export type ModelValueType = string
export interface LyyInputGroup extends IElement {
  compName: 'lyy-input-group'
  modelValue?: ModelValueType
  prop: PropType
}

export interface PropType {
  /**
   * 绑定的表单
   */
  formId?: string

  /**
   * 标签名称
   */
  label?: string

  /**
   * 校验规则
   */
  rules?: Rule

  /**
   * 输入框大小
   */
  size?: 'large' | 'default' | 'small'
  /**
   * 是否紧凑
   */
  compact?: boolean
  /**
   * 非紧凑型时栅格布局配置
   */
  row?: object
  cols?: Array<object>
  children: IElement[]
  labelCol: object
}
