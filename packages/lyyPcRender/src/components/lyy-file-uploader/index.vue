<script lang="ts" setup>
import { message, Upload } from 'ant-design-vue'
import type { UploadProps, UploadFile } from 'ant-design-vue'
import {
  isString,
  isObject,
  isUndefined,
  isNull,
  cloneDeep,
  chain,
} from 'lodash'
import { useDep, removeDep, useStore } from '@leyaoyao/libs'
import { fileUpload } from '@leyaoyao/libs/utils/file-upload'
import { fileOssUpload } from '@leyaoyao/libs/utils/file-oss-upload'
import {
  toRefs,
  computed,
  ref,
  inject,
  Ref,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
} from 'vue'
import { PropType, ModelValueType } from './type'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import {
  PlusOutlined,
  DownloadOutlined,
  EyeOutlined,
  DragOutlined,
} from '@ant-design/icons-vue'
import Draggable from 'vuedraggable'
import { IElement } from '@/global.d'

const props = defineProps<{
  compId: string
  prop: PropType
  element: IElement
  isInTable?: boolean
  modelValue?: ModelValueType
  actions: object[]
  rowData?: any
}>()

const { prop } = toRefs(props)
const {
  uploadType,
  accept,
  fileSize,
  auto,
  fieldName,
  maxCount,
  showTip,
  tip,
  triggerStyle,
  data,
  formId,
  download,
  templateUrl,
  fieldNames,
  showCover,
  multiple,
  field,
  uploadStyle,
  showDownloadIcon,
  showRemoveIcon,
  ossRadio,
  ossTokenUrl,
  onlyOssFileName,
  ossPlatform,
  showDragArea,
  uploadImageListSortable,
  labelSuffixIcon,
} = toRefs(prop.value)

const progress: UploadProps['progress'] = {
  strokeColor: {
    '0%': '#108ee9',
    '100%': '#87d068',
  },
  strokeWidth: 3,
  format: (percent) => `${Number.parseFloat(percent?.toFixed(2) ?? '0')}%`,
  class: 'test',
}
const listType = computed(() => {
  return uploadType.value === 'image' ? 'picture-card' : 'text'
})

const uploadRef = ref(null)
const dropZone = ref(null)

const dropClass = ref('')

let isReturnObject = false

const fileListForDraggable = ref([])
const isSortingDrag = ref(false)
const currentDraggingSortEl = ref(null)
const currentDraggingSourceEl = ref(null)

onMounted(() => {
  if (dropZone.value) {
    dropZone.value.addEventListener('dragover', (e) => {
      if (isDraggingFile(e)) return

      e.preventDefault()
      if (!dropClass.value.includes('drop-over')) {
        dropClass.value = 'drop-over'
      }
    })
    dropZone.value.addEventListener('dragleave', (e) => {
      if (isDraggingFile(e)) return

      e.preventDefault()
      if (!dropZone?.value?.contains(e.relatedTarget)) {
        dropClass.value = ''
      }
    })
    dropZone.value.addEventListener('drop', (e) => {
      if (isDraggingFile(e)) return

      e.preventDefault()
      dropClass.value = ''
    })
  }

  function isDraggingFile(evt: DragEvent) {
    return disabled.value || (uploadImageListSortable?.value && !evt.dataTransfer?.types.includes('Files'))
  }
})

const triggerText = computed(() => {
  return prop.value.triggerText ?? '选择'
})

const previewImg = ref<string>('')
const visible = ref<boolean>(false)
const setVisible = (value: boolean): void => {
  visible.value = value
}

const emit = defineEmits(['update:fileList', UPDATE_MODELVALUE, ACTION])
const isOssTokenUrl = computed(() => ossRadio?.value && ossTokenUrl?.value)
// 注入formState, 监听formState、modelValue，互相更新
// const { disabled, updateFormState } = useFormState(props, !isOssTokenUrl.value)

const formDisabled = inject<Ref<boolean>>('formDisabled')
const formState = inject<any>('formState') ?? ref({})
const disabled = computed(() => {
  return formDisabled?.value || props.prop.disabled
})

const emitFn = (value) => {
  if (props.rowData && field.value) {
    props.rowData[field.value] = value
  } else {
    emit(UPDATE_MODELVALUE, value)
  }
}
const store = useStore()

const updateFormState = (newValue) => {
  if (props.rowData && field?.value) {
    props.rowData[field.value] = newValue
  } else if (formState.value && field?.value) {
    formState.value[field.value] = newValue
    store.setValue('formIsChange-' + formId.value, true)
    return
  }
}

field.value &&
  watch(
    () => formState.value[field.value],
    (newVal) => {
      if (
        JSON.stringify(newVal) !== JSON.stringify(props.modelValue) &&
        !props.isInTable
      ) {
        emitFn(newVal)
      }
    },
    {
      // form有默认值时可以直接同步到子组件
      immediate: true,
    },
  )

const fileList = computed({
  get() {
    const compValue = props.rowData?.[field.value] ?? props.modelValue
    if (isUndefined(compValue) || isNull(compValue)) {
      return []
    }
    if (Array.isArray(compValue)) {
      return compValue.map((el, index) => {
        const res = transToFile(el, index) as UploadFile
        return res
      })
    } else {
      const file = transToFile(compValue)
      return [file as UploadFile]
    }
  },
  set(newVal) {
    // oss直传不需要选择完文件立马回写表单，只有上传成功后再回写
    if (!isOssTokenUrl.value) {
      updateFormState(newVal)
    }

    if (uploadImageListSortable?.value) {
      fileListForDraggable.value = cloneDeep(newVal)
    }
    emitFn(newVal)
  },
})
let tipDefault = ''
if (accept?.value) {
  tipDefault += `只允许上传${accept.value}类型文件`
}
if (fileSize?.value) {
  tipDefault += `${tipDefault.length > 0 ? '，' : ''}大小不超过${
    fileSize.value
  }M`
}
const tipMsg = computed(() => tip?.value || tipDefault)
const formItem = ref()

const transToFile = (el, index?) => {
  if (isString(el)) {
    isReturnObject = false
    return {
      url: el,
      uid: `${Date.now()}${index ?? ''}`,
      name: extname(el).fileName,
      status: 'done',
    }
  }
  if (isObject(el)) {
    isReturnObject = true
    if (!el.status && !el.originFileObj) {
      const { name = 'name', url = 'url' } = fieldNames.value ?? {}
      return {
        url: el[url],
        uid: `${Date.now()}${index ?? ''}`,
        name: el[name],
        status: 'done',
      }
    } else {
      return el
    }
  }
}
const extname = (url = '') => {
  const temp = url.split('?')
  const filename = temp[0]
  const filenameWithoutSuffix = filename.split(/#|\?/)[0]
  const extname = (/\.[^./\\]*$/.exec(filenameWithoutSuffix) || [''])[0]
  return {
    fileName: filenameWithoutSuffix,
    extname,
  }
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  if (accept?.value) {
    const types = accept.value.split(',').map((item) => item.replace('.', ''))
    const fileNameItem = file.name.split('.')
    const fileSuffix = fileNameItem.at(-1)
    if (
      types.length > 0 &&
      !types.includes(fileSuffix.toUpperCase()) &&
      !types.includes(fileSuffix.toLowerCase())
    ) {
      message.error(`请选择${accept.value}类型的文件`)
      return Upload.LIST_IGNORE
    }
  }
  if (fileSize?.value) {
    const isLt2M = file.size / 1024 / 1024 < fileSize.value
    if (!isLt2M) {
      message.error(`请选择小于${fileSize.value}M的文件`)
      return Upload.LIST_IGNORE
    }
  }
  if (!auto?.value) {
    return false
  }
}

// 选择图片后上传
const autoUpload: UploadProps['customRequest'] = async (options) => {
  if (auto?.value) {
    setTimeout(() => {
      // 如果是表格或其他类似组件内
      if (props.actions || props.rowData) {
        let upload = null
        if (props.actions) {
          upload = props.actions.find((e) => e.event == 'upload')
        } else {
          upload = props.element.actions.find((e) => e.event == 'upload')
        }
        if (upload) {
          fileUpload(
            upload.option,
            upload.thenActions,
            props,
            fileList.value,
            (res) => {
              emitFn((props.prop.maxCount as number) > 1 ? res : res[0])
            },
          )
        }
      } else if (isOssTokenUrl.value) {
        const payloads = {}
        let __ossTokenUrl__ = ossTokenUrl?.value as string
        if (!!__ossTokenUrl__ && __ossTokenUrl__.trim() !== '') {
          const match = __ossTokenUrl__.match(/\${([^}]+)}/)
          if (match && !!match[1]) {
            __ossTokenUrl__ = match[1]
          }
        }
        fileOssUpload(
          {
            url: __ossTokenUrl__,
            method: 'get',
            payloads: payloads,
          },
          [],
          props.element,
          fileList.value,
          (res: File[]) => {
            const val = (props.prop.maxCount as number) > 1 ? res : res[0]
            const successFiles = res.filter((item) => item.status === 'done')
            const field = onlyOssFileName?.value ? 'name' : 'url'

            emitFn(val)
            if (
              (props.prop.maxCount as number) > 1 &&
              successFiles.length > 0
            ) {
              const successVal = successFiles.map((item) => item[field])
              updateFormState(successVal)
            } else if (
              (props.prop.maxCount as number) === 1 &&
              successFiles.length > 0
            ) {
              const successVal = successFiles[0][field]
              updateFormState(successVal)
            }
          },
        )
      } else {
        emit(ACTION, { event: 'upload' })
      }
    }, 100)
  } else {
    return
  }
}
const handleChange: UploadProps['onChange'] = async ({ file, fileList }) => {
  // 此处不会立马校验必填项，需等校验之后再去按需清空校验结果
  if (fileList.length > 0) {
    nextTick(() => {
      // 遍历文件列表状态，更新可拖拽内容类名
      if (uploadImageListSortable?.value && dropZone.value) {
        const dragChildren = dropZone.value.querySelectorAll('.ant-upload-sortlist--item')

        fileList.forEach((file, index) => {
          dragChildren[index].classList.add(
            'ant-upload-sortlist--item__draggable',
          )
        })
      }
    })

    setTimeout(() => {
      formItem.value.clearValidate()
    }, 0)
  }

  emit(ACTION, { event: 'change' })
}

interface RemovedFile {
  url: string
  uid: string
  name: string
  status: string
}
const handleRemove: UploadProps['onRemove'] = (file: RemovedFile) => {
  const isDone = file.status === 'done'
  const index = fileList.value.findIndex((item) =>
    isDone ? item.url === file.url : item.uid === file.uid,
  )
  fileList.value.splice(index, 1)
  const { name = 'name', url = 'url' } = prop.value.fieldNames || {}
  const newFileList = fileList.value.map((item) => {
    if (isReturnObject) {
      return item.status === 'done'
        ? {
            [name]: item.name,
            [url]: item.url,
          }
        : item
    } else {
      return item.status === 'done' ? item.url : item
    }
  })
  emit(ACTION, { event: 'remove' })
  updateFormState(newFileList)
  emitFn(newFileList)
}

// 图片类型：非立即上传的图片需要转成base64进行预览；立即上传的图片可直接通过url进行预览
// 其他文件类型：非立即上传不做处理；立即上传的点击下载
const handlePreview: UploadProps['onPreview'] = async (file) => {
  if (file?.type?.includes('image/')) {
    file.preview =
      file.preview ?? ((await getBase64(file.originFileObj!)) as string)
    previewImg.value = (file.url || file.preview) as string
    setVisible(true)
  } else if (
    ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'].includes(
      extname(file.url).extname.toLowerCase(),
    )
  ) {
    previewImg.value = file.url
    setVisible(true)
  } else {
    file.url && handleDownload(file.url)
  }
}

const getBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.addEventListener('load', () => resolve(reader.result))
    reader.addEventListener('error', () => reject(reader.error))
  })
}
const fileDownload = (file) => {
  handleDownload(file.url)
}

const handlePaste = (event) => {
  if (disabled.value) return
  if (fileList.value.length >= maxCount?.value) return
  const { items } = event.clipboardData || window.clipboardData
  for (const item of items) {
    if (item.kind === 'file' && fileList.value.length < maxCount?.value) {
      const file = item.getAsFile()
      if (file) {
        file.originFileObj = new File([file], file.name, {
          type: file.type,
          lastModified: file.lastModified,
        })
        if (auto?.value) {
          file.status = 'uploading'
        }
        const res = beforeUpload(file, fileList.value)
        if (res) {
          return
        }
        fileList.value.push(file)
        updateFormState(fileList.value)
        autoUpload({})
      }
    }
  }
}
const handleDownload = (url) => {
  const elink = document.createElement('a')
  elink.href = url ?? templateUrl?.value
  elink.target = '_blank'
  elink.download = '模板'
  elink.click()
  elink.remove()
}
const clear = () => {
  fileList.value.length = 0
  emitFn(fileList.value)
}
const actionsSet = {
  clear,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)
const labelCol = computed(() => {
  return Object.keys(props.prop.labelCol?.style ?? []).length > 0
    ? props.prop.labelCol
    : null
})

const onSortStart = (index, e) => {
  if (!uploadImageListSortable?.value) return

  if (dropZone.value) {
    const source = (dropZone.value as HTMLDivElement).querySelectorAll(
      '.ant-upload-list-item',
    )[index]
    const eventTarget = e.target.closest('.ant-upload-sortlist--item')
    const eventTargetContent = eventTarget.querySelector('.ant-upload-sortlist--item__content')

    const isItemDraggable = eventTarget.classList.contains('ant-upload-sortlist--item__draggable')
    if (!isItemDraggable) {
      return
    }

    const cloneNode = source.cloneNode(true)

    eventTargetContent.replaceChildren(cloneNode)
    // 由于 draggable 拖拽时原元素还在那个位置所以通过降低透明度来减少“加重”效果
    // 可以把这个注释掉看看"加重"效果
    source.style.opacity = '0.3'

    isSortingDrag.value = true

    currentDraggingSortEl.value = eventTarget
    currentDraggingSourceEl.value = source
  }
}

const onSortChanged = (e) => {
  if (!uploadImageListSortable?.value) return

  // 如果交换了顺序
  if (e.moved) {
    const { newIndex, oldIndex } = e.moved
    fileList.value = chain(fileList.value)
      .pullAt(oldIndex)
      .thru((removed) => {
        fileList.value.splice(newIndex, 0, removed[0])

        return fileList.value
      })
      .value()
  }
}

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})

function disableSortingDrag() {
  if (isSortingDrag.value) {
    isSortingDrag.value = false

    currentDraggingSortEl.value &&
      currentDraggingSortEl.value
        .closest('.ant-upload-sortlist--item__content')
        .replaceChildren()

    currentDraggingSourceEl.value &&
      (currentDraggingSourceEl.value.style.opacity = '')

    currentDraggingSortEl.value = null
  }
}
</script>

<template>
  <div
    class="lyy-component lyy-file-uploader-wrap"
    @paste="handlePaste"
    ref="dropZone"
  >
    <a-form-item
      :name="field"
      ref="formItem"
      :rules="prop.rules"
      :colon="prop.colon"
      :labelCol="labelCol"
      :autoLink="false"
    >
      <template #label>
        <template
          v-if="labelSuffixIcon && Object.keys(labelSuffixIcon).length > 0"
        >
          <span class="form-item-label-text">{{ prop.label }}</span>
          <a-tooltip placement="topLeft" :title="labelSuffixIcon.tooltip">
            <lyy-icon
              class="label-suffix-icon"
              v-if="labelSuffixIcon.icon"
              :prop="{
                iconName: labelSuffixIcon.icon,
              }"
            />
          </a-tooltip>
        </template>
        <a-tooltip v-else>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
      </template>
      <div
        class="lyy-file-uploader"
        :class="[
          dropClass,
          {
            'file-uploader-disabled': disabled,
            'other-type-style': uploadType !== 'image',
            'drop-area': showDragArea,
            'file-uploader-inner': !!rowData,
          },
        ]"
      >
        <a-upload
          v-if="!dropClass"
          ref="uploadRef"
          :class="`${
            showCover && uploadType === 'image' ? 'lyy-file-cover' : ''
          }  ${prop.imgSize ?? 'large'}`"
          :list-type="listType"
          :maxCount="maxCount"
          v-model:file-list="fileList"
          :before-upload="beforeUpload"
          :custom-request="autoUpload"
          :disabled="disabled"
          :style="uploadStyle"
          :multiple="multiple"
          @change="handleChange"
          @remove="handleRemove"
          @preview="handlePreview"
          @download="fileDownload"
          :progress="progress"
          :openFileDialogOnClick="true"
          :show-upload-list="{
            showDownloadIcon: showDownloadIcon,
            showPreviewIcon: true,
            showRemoveIcon: showRemoveIcon,
          }"
        >
          <!-- 上传类型为图片 -->
          <div
            v-if="
              fileList && fileList.length < maxCount && uploadType === 'image'
            "
          >
            <plus-outlined />
            <div class="ant-upload-text">{{ triggerText }}</div>
          </div>
          <!-- 上传类型为其他 -->
          <div v-if="uploadType === 'other' && fileList.length < maxCount">
            <a-button v-bind="prop" :style="triggerStyle">
              {{ triggerText }}
            </a-button>
          </div>
          <div v-if="uploadType === 'excel' && fileList.length < maxCount">
            <div class="upload-box">
              <upload-outlined />
              {{ triggerText }}
            </div>
          </div>
          <template #downloadIcon>
            <download-outlined />
          </template>
          <template #previewIcon>
            <eye-outlined />
          </template>
        </a-upload>
        <div class="drop-wrapper" v-else>
          <a-upload
            v-model:file-list="fileList"
            :before-upload="beforeUpload"
            :custom-request="autoUpload"
            :maxCount="maxCount"
            :disabled="disabled"
            :style="uploadStyle"
            :multiple="multiple"
            :progress="progress"
            @change="handleChange"
            @remove="handleRemove"
            @preview="handlePreview"
            @download="fileDownload"
            :openFileDialogOnClick="false"
          >
            <div>拖拽图片、文件至此</div>
          </a-upload>
        </div>
        <div
          v-if="!dropClass && showDragArea"
          style="
            color: rgba(0, 0, 0, 0.25);
            text-align: left;
            margin-top: 8px;
            line-height: 20px;
          "
        >
          拖拽/ctrl+v粘贴图片至框内，单张{{ fileSize }}M内
        </div>
        <a-button
          type="link"
          v-if="download && uploadType !== 'image'"
          @click="handleDownload(undefined)"
        >
          下载模板
        </a-button>

        <!--    拖拽排序    -->
        <draggable
          v-if="uploadImageListSortable && fileListForDraggable.length > 0"
          v-model="fileListForDraggable"
          class="ant-upload-sortlist"
          handle=".ant-upload-sortlist--item__handle"
          @change="onSortChanged"
          @end="disableSortingDrag"
        >
          <template #item="{ index }">
            <div
              :key="index"
              class="ant-upload-sortlist--item"
            >
              <div class="ant-upload-sortlist--item__content" />

              <DragOutlined
                v-if="uploadImageListSortable"
                class="ant-upload-sortlist--item__handle"
                @mousedown="e => onSortStart(index, e)"
              />
            </div>
          </template>
        </draggable>
      </div>

      <div v-if="showTip && !dropClass" class="lyy-file-uploader-tip">
        {{ tipMsg }}
      </div>

      <!-- 图片预览 -->
      <a-image
        :width="200"
        :style="{ display: 'none' }"
        v-if="uploadType === 'image' && visible"
        :preview="{
          visible,
          onVisibleChange: setVisible,
        }"
        :src="previewImg"
        alt="avatar"
      />
    </a-form-item>
  </div>
</template>

<style lang="scss" scoped>
.lyy-file-uploader-wrap {
  :deep(.ant-form-item-control-input-content) {
    flex: inherit;
  }
}
.label-suffix-icon {
  margin-left: 4px;
  cursor: pointer;
}
.lyy-file-uploader-tip {
  font-size: 12px;
  color: #909199;
  margin-top: 6px;
}
.lyy-file-uploader {
  position: relative;

  &.file-uploader-disabled {
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.04);
  }
  &.other-type-style {
    :deep(.ant-upload-wrapper) {
      .ant-motion-collapse {
        height: 40px !important;
        .ant-upload-list-item-progress {
          width: 97%;
        }
      }
    }
  }
  :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper) {
    padding: 12px;
    border-radius: 4px;
    border: 1px dashed rgba(0, 0, 0, 0.08);
    min-width: 324px;
    .ant-upload-list.ant-upload-list-picture-card {
      .ant-upload-list-item-container {
        width: 72px;
        height: 72px;
      }
    }

    .ant-upload.ant-upload-select {
      width: 72px;
      height: 72px;
      margin-bottom: 0;
    }
  }
  &.file-uploader-inner {
    :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper) {
      min-width: 0;
    }
    :deep(.ant-upload) {
      margin-right: 0;
    }
  }

  &.drop-area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 8px;
    border: 1px dashed rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    :deep(.ant-upload-wrapper.ant-upload-picture-card-wrapper) {
      padding: 0;
      border: none;
    }
    // min-height: 116px;
    &.other-type-style {
      :deep(.ant-upload-wrapper) {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &.drop-over {
    width: 100%;
    height: 176px;
    background: #fff;
    justify-content: center;
    border-radius: 8px;
    padding: 8px;
    box-sizing: border-box;
    border: none;
    .drop-wrapper {
      width: 100%;
      height: 100%;
      border: 1px dashed #ccc;
      border-radius: 8px;
    }

    :deep(.ant-upload-wrapper) {
      width: 100%;
      height: 100%;
      .ant-upload {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
      .ant-upload-list {
        display: none;
      }
    }
  }

  .link {
    text-decoration: none;
    color: #1890ff;
    margin-left: 8px;
  }
  :deep(.ant-upload-picture-card-wrapper) {
    .ant-upload-list-item {
      height: 72px;
      width: 72px;
    }
  }

  .ant-upload-sortlist {
    position: absolute;
    display: flex;
    inset: 0;
    padding: 12px;
    pointer-events: none;

    .ant-upload-sortlist--item {
      position: relative;
      display: inline-block;
      width: 72px;
      height: 72px;
      margin-block: 0 8px;
      margin-inline: 0 8px;
      vertical-align: top;

      &__content {
        width: 100%;
        height: 100%;
        pointer-events: auto;
      }

      &__handle {
        position: absolute;
        top: -4px;
        left: -4px;
        padding: 4px;
        width: 20px;
        height: 20px;
        font-size: 16px;
        color: #a7a7a7;
        background-color: rgba(0, 0, 0, 0.1);
        cursor: move;
        pointer-events: auto;
        border-radius: 4px;
        box-sizing: border-box;
      }

      :deep(.ant-upload-list-item) {
        position: relative;
        display: flex;
        align-items: center;
        width: 72px;
        height: 72px;
        padding: 8px;
        box-sizing: border-box;
        border: 1px dashed transparent;

        .ant-upload-list-item-thumbnail {
          position: static;
          display: block;
          width: 100%;
          height: 100%;
          object-fit: contain;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 60px;
          text-align: center;
          flex: none;

          img {
            position: static;
            display: block;
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .ant-upload-list-item-name,
        .ant-upload-list-item-actions {
          display: none;
        }

        .ant-upload-list-item-progress {
          position: absolute;
          bottom: 32px;
          width: calc(100% - 16px);
          padding-inline-start: 0;
          margin-top: 0;
          font-size: 14px;
          line-height: 0;
          pointer-events: none;

          .ant-progress-line {
            position: relative;
            width: 100%;
            font-size: 14px;
            margin-inline-end: 8px;
            box-sizing: border-box;
            margin: 0;
            padding: 0;

            .ant-progress-outer {
              display: inline-block;
            }
          }
        }
      }
    }
  }
}

.lyy-file-uploader {
  .middle {
    :deep(.ant-upload-list) {
      .ant-upload-list-picture-card-container,
      .ant-upload,
      .ant-upload-list-item {
        width: 72px;
        height: 72px;
      }
      .ant-upload-list-item {
        padding: 4px;
      }
    }
  }
  .mini {
    :deep(.ant-upload-list) {
      .ant-upload-list-picture-card-container,
      .ant-upload,
      .ant-upload-list-item {
        width: 32px;
        height: 32px;
      }
      div.ant-upload-list-item-thumbnail {
        display: none;
      }
      .ant-upload-list-item {
        padding: 2px;
      }
      .anticon {
        margin-top: 8px;
      }
    }
  }
}
.lyy-file-cover {
  :deep(.ant-upload-list-picture-card) {
    .ant-upload-list-picture-card-container {
      position: relative;
      &:first-child {
        &:after {
          content: '封面';
          position: absolute;
          top: 0;
          right: 0;
          width: 40px;
          height: 22px;
          text-align: center;
          line-height: 22px;
          color: #fff;
          font-size: 12px;
          background: rgba(24, 144, 255, 0.8);
          border: 1px solid rgba(24, 144, 255, 0.35);
          border-radius: 0 4px 0 8px;
        }
      }
    }
  }
}
:deep(.ant-upload-select-picture-card i) {
  font-size: 32px;
  color: #999;
}
:deep(.ant-upload-select-picture-card .ant-upload-text) {
  color: rgba(0, 0, 0, 0.85);
}

.upload-item-wrapper {
  position: relative;
  cursor: move;
  height: 100%;

  &:hover {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
    }
  }
}
</style>
