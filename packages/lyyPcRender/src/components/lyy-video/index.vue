<template>
  <div class="lyy-component video-box">
    <video
      :src="srcVideo"
      :style="style"
      :autoplay="autoplay"
      :controls="isControls"
      :height="height"
      :width="width"
      :loop="loop"
      :muted="muted"
      :poster="poster"
    ></video>
  </div>
</template>

<script lang="ts" setup>
import { ModelValueType, PropType } from './type'
import { UPDATE_MODELVALUE } from '../../constants/action-type'
import { useFormState } from '../../hooks'
import {
  computed,
  toRefs,
  defineComponent,
  withDefaults,
  StyleValue,
} from 'vue'

defineComponent({
  name: 'lyy-video',
})

interface Props {
  modelValue: ModelValueType
  style: StyleValue
  prop: PropType
}
const emit = defineEmits([UPDATE_MODELVALUE])
const props = withDefaults(defineProps<Props>(), {})
const { prop } = toRefs(props)
const { src, autoplay, controls, height, width, loop, muted, poster } = toRefs(
  prop.value,
)

// 注入formState,并监听formState的变化，更新modelValue的值
useFormState(props)

const srcVideo = computed(() => {
  return props.modelValue || src.value
})
const isControls = computed(() => {
  return controls?.value || true
})
</script>

<style lang="less" scoped>
.edit-status {
  video {
    pointer-events: none;
  }
}
</style>
