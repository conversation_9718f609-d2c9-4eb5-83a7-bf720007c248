<script setup lang="ts">
import {
  onBeforeMount,
  onMounted,
  computed,
  toRefs,
  defineComponent,
  StyleValue,
  onUnmounted,
} from 'vue'
import { BEFORE_MOUNT, MOUNTED, UPDATE } from '../../constants/event-type'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { ModelValueType, PropType } from './type'
import LyyIcon from '../lyy-icon/index.vue'
import { formula, useDep, removeDep, usePayload } from '@leyaoyao/libs'
import { dynamicRules, onRules } from '@leyaoyao/libs/hooks/use-rules'
import { useFormState } from '../../hooks'
defineComponent({
  name: 'lyy-radio',
})

const props = defineProps<{
  modelValue?: ModelValueType
  compId: string
  prop: PropType
  style: StyleValue
}>()

const {
  label,
  field,
  fieldNames,
  optionType,
  size,
  dbuttonStyle,
  slotLabel,
  icon,
  defaultValue,
} = toRefs(props.prop)

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const { disabled, updateFormState } = useFormState(props, true)

const value = computed({
  get: () => props.modelValue,
  set: (newValue) => {
    setDataBaseFn(newValue)
  },
})

onBeforeMount(() => {
  emit(ACTION, { event: BEFORE_MOUNT })
})

const options = computed(() => {
  const temp = props.prop.options?.map((item) => {
    const { forbid } = item
    if (forbid) {
      const { payloads, exp } = forbid
      const payload = usePayload(payloads)
      item.disabled = formula(exp)(payload)
    }
    return item
  })
  return temp
})

onMounted(() => {
  if (defaultValue?.value) {
    value.value = defaultValue.value
  }
  emit(ACTION, { event: MOUNTED })
})
const rules = computed(() => {
  const rules = props.prop.rules || []
  let dynamicRulesSet = []
  let onrulesSet = []
  if (props.prop?.dynamicRules) {
    dynamicRulesSet = dynamicRules(props.prop, props.prop?.dynamicRules)
  }
  if (props.prop?.onrules) {
    onrulesSet = onRules(props.prop?.onrules)
  }
  const res = [...rules, ...dynamicRulesSet, ...onrulesSet]
  return res
})
const labelKey = fieldNames?.value?.label || 'label'
const valKey = fieldNames?.value?.value || 'value'
const labelCol = computed(() => {
  return Object.keys(props.prop.labelCol?.style ?? []).length > 0
    ? props.prop.labelCol
    : null
})

const setDataBaseFn = (data) => {
  updateFormState(data)
  emit(UPDATE_MODELVALUE, data)
  emit(ACTION, { event: UPDATE })
}
// 清空
const clear = () => {
  setDataBaseFn('')
}
// 重置
const reset = () => {
  setDataBaseFn(defaultValue?.value)
}
// 赋值
const setData = (data) => {
  setDataBaseFn(data)
}
// 重新加载
const reLoad = () => {
  emit(ACTION, { event: MOUNTED })
}
const actionsSet = {
  clear,
  reset,
  setData,
  reLoad,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>

<template>
  <div class="lyy-component">
    <a-form-item
      :name="field"
      :rules="rules"
      :colon="prop.colon"
      :style="style"
      :labelCol="labelCol"
    >
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
        <lyy-icon v-if="icon" :prop="icon"></lyy-icon>
      </template>
      <a-radio-group
        v-model:value="value"
        :optionType="optionType"
        :disabled="disabled"
        :size="size"
        :buttonStyle="dbuttonStyle"
      >
        <template v-if="optionType === 'button'">
          <a-radio-button
            v-for="option in options"
            :key="option[valKey]"
            :value="option[valKey]"
            :disabled="option.disabled"
          >
            {{ option[labelKey] }}
          </a-radio-button>
        </template>
        <template v-else>
          <a-radio
            v-for="option in options"
            :key="option[valKey]"
            :value="option[valKey]"
            :disabled="option.disabled"
          >
            {{ option[labelKey] }}
          </a-radio>
        </template>
      </a-radio-group>
    </a-form-item>
  </div>
</template>
