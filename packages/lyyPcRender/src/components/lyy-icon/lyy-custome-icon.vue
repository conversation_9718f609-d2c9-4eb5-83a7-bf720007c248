<template>
  <iconFont class="icon" :type="props.name" v-bind="props.iconProps"></iconFont>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue'
import { AntdIconProps } from '@ant-design/icons-vue/lib/components/AntdIcon'
defineComponent({
  name: 'lyy-custome-icon',
})
const props = defineProps<{
  name: string
  iconProps: AntdIconProps | undefined
}>()
import { createFromIconfontCN } from '@ant-design/icons-vue'
const iconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3964711_uh8t8c3dlfc.js',
  
})
</script>

<style lang="scss">
.icon {
  // font-size: 20px !important;
  font-size: inherit;
  // vertical-align: -0.2em;
}
</style>
