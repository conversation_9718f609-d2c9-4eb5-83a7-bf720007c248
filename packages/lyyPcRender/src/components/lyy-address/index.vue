<template>
  <div class="lyy-component" :style="style">
    <a-form-item
      :name="prop.addressField"
      :rules="prop.rules"
      :colon="prop.colon"
      :labelCol="labelCol"
      :style="style"
    >
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
      </template>

      <Select
        show-search
        allow-clear
        :placeholder="prop.placeholder"
        :disabled="disabled"
        :filter-option="false"
        :not-found-content="null"
        :options="options"
        :show-arrow="false"
        :value="formState[props.prop.addressField]"
        @search="onSearch"
        @select="onSelect"
      >
        <template #clearIcon><CloseCircleFilled @click="onClear" /></template>
      </Select>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import {
  defineComponent,
  onMounted,
  StyleValue,
  ref,
  shallowRef,
  computed,
  watchEffect,
} from 'vue'
import { PropType } from './type'
import { initAMap } from '../../utils/init-amap'
import { useFormState } from '../../hooks'
import { debounce } from 'lodash'
import { Select } from 'ant-design-vue'
import { CloseCircleFilled } from '@ant-design/icons-vue'
import { useTpl } from '@leyaoyao/libs'
import { ACTION } from '../../constants/action-type'
import { UPDATE, MOUNTED } from '../../constants/event-type'

defineComponent({
  name: 'lyy-address',
})

const props = defineProps<{
  compId: string
  prop: PropType
  style: StyleValue
}>()

const emit = defineEmits([ACTION, UPDATE, MOUNTED])

const autoComplete = shallowRef<any>(null)
const { formState, disabled } = useFormState(props, true)

const options = ref<any[]>([])

const labelCol = computed(() => {
  return Object.keys(props.prop.labelCol?.style ?? []).length > 0
    ? props.prop.labelCol
    : null
})

const citylimit = computed(() => {
  if (!props.prop.citylimit) {
    return '全国'
  }
  return useTpl(props.prop.citylimit)
})

watchEffect(() => {
  autoComplete.value?.setCity(citylimit.value)
})

const onSearch = debounce(async (value: string) => {
  if (!value) {
    return
  }

  const result = await new Promise<any>((resolve, reject) => {
    autoComplete.value.search(value, (status: any, result: any) => {
      status == 'complete' ? resolve(result) : reject(result)
    })
  })

  options.value = result.tips.map((item: any) => {
    let address = item.address + item.name

    if (props.prop.needDistrict) {
      address = item.district + address
    }

    return {
      value: item.id,
      label: address,
      lng: item.location.lng,
      lat: item.location.lat,
    }
  })
}, 300)

const onSelect = (value: string, item: any) => {
  const { addressField, addressIdField, addressLngLatField } = props.prop
  formState.value[addressField] = item.label
  formState.value[addressIdField] = item.value
  formState.value[addressLngLatField] = {
    lat: item.lat,
    lng: item.lng,
  }
  emit(ACTION, { event: UPDATE, data: item.label })
}

const onClear = () => {
  const { addressField, addressIdField, addressLngLatField } = props.prop
  formState.value[addressField] = ''
  formState.value[addressIdField] = ''
  formState.value[addressLngLatField] = {
    lat: null,
    lng: null,
  }
  emit(ACTION, { event: UPDATE, data: '' })
}

onMounted(async () => {
  emit(ACTION, { event: MOUNTED })
  await initAMap()
  autoComplete.value = new AMap.AutoComplete({
    city: citylimit.value,
    citylimit: true,
  })
})
</script>

<style lang="scss" scoped></style>
