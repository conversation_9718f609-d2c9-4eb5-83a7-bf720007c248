export interface PropType {
  /**
   * card 的标题
   */
  title?: string

  /**
   * 扩展的按钮
   */
  extraList?: IElement[]

  /**
   * 是否是主页列表容器
   */
  mainLayout?: boolean

  /**
   * 内部卡片[需要关闭ant-card的两侧padding]
   */
  innerCard?: boolean

  /**
   * 是否有边框
   */
  bordered?: boolean

  /**
   * 是否展示卡片头部
   */
  showHeader?: boolean
  bodyStyle?: object
  headStyle?: object
  radius?: string
}

// export interface LyyCard extends IElement {
//   compName: 'lyy-card'
//   prop: PropType
// }
