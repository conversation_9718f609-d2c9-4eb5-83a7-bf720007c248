<script lang="ts" setup>
import { computed, toRefs } from 'vue'
import { PropType } from './type'
import { useTpl } from '@leyaoyao/libs'
import { theme } from 'ant-design-vue'
const props = defineProps<{
  style: Object
  prop: PropType
  childrens: IComponent
  compId: string
  rowData?: object
}>()

const { useToken } = theme
const result = useToken()
const { token } = result

const colorPrimary = computed(() => token.value.colorPrimary)

const { extraList, tip, showHeader } = toRefs(props.prop)

const extraButtons = computed(() => {
  return extraList?.value?.map((item) => {
    return {
      ...item,
      prop: {
        ...item.prop,
        type: 'link',
      },
    }
  })
})

const marginBottom = computed(() => {
  const childLen = props.prop.childList.length
  const lastChild = props.prop.childList[childLen - 1]
  return lastChild.compName === 'lyy-grid' ? '8px' : '24px'
})

const title = computed(() => {
  return useTpl(props.prop.title ?? '', props.rowData)
})
</script>

<template>
  <div class="lyy-container lyy-form-card" :style="style">
    <div class="form-card-header-wrap" v-if="showHeader">
      <div class="form-card-header">
        <div class="form-card-title">
          <span class="title-text">{{ title }}</span>
        </div>
        <div class="form-card-right-extra">
          <pc-render-template :elements="extraButtons"></pc-render-template>
        </div>
      </div>
      <div class="form-card-tip" v-if="tip">{{ tip }}</div>
    </div>
    <div class="form-card-body">
      <pc-render-template :elements="prop.childList"></pc-render-template>
    </div>
  </div>
</template>
<style lang="less" scoped>
.lyy-form-card {
  margin-bottom: v-bind(marginBottom);
  .form-card-header-wrap {
    margin-bottom: 16px;
    .form-card-header {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        width: 100%;
        border-bottom: 2px solid v-bind(colorPrimary);
        opacity: 0.2;
        bottom: 0;
        left: 0;
      }
      .form-card-title {
        flex: 1;
        height: 100%;
        color: v-bind(colorPrimary);
        font-weight: 500;
        .title-text {
          position: relative;
          height: 100%;
          display: inline-flex;
          align-items: center;
          &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0px;
            width: 100%;
            height: 4px;
            background-color: v-bind(colorPrimary);
            border-radius: 2px 2px 0 0;
          }
        }
      }
      .form-card-right-extra {
        display: flex;
      }
    }
    .form-card-tip {
      color: rgba(0, 0, 0, 0.45);
      margin-top: 8px;
    }
  }
}
</style>
