<script lang="ts" setup>
import { defineComponent, StyleValue, toRefs, inject, Ref, ref } from 'vue'
import { CLICK } from '../../constants/event-type'
import { ACTION } from '../../constants/action-type'
import { PropType } from './type'

defineComponent({
  name: 'lyy-page-header',
})
const props = defineProps<{
  prop: PropType
  style: StyleValue | undefined
}>()

const { prop } = toRefs(props)

const isEdit = inject<Ref<boolean>>('isEdit')
const emit = defineEmits([ACTION])

const handleClick = (e: MouseEvent) => {
  emit(ACTION, { event: CLICK })
  e.stopPropagation()
}
</script>

<template>
  <div class="lyy-header lyy-container" :class="{ view: !isEdit }">
    <a-page-header :title="prop.title" @back="handleClick">
      <template #extra>
        <pc-render-template :elements="prop.extraList"></pc-render-template>
      </template>
    </a-page-header>
  </div>
</template>

<style lang="less" scoped>
.lyy-header {
  background-color: #fff;
  border-bottom: 1px solid rgb(235, 237, 240);
  margin-bottom: 8px;
  font-size: 16px;
  .ant-page-header {
    padding: 8px 16px;
    ::v-deep .ant-page-header-heading-title {
      font-size: 16px;
    }
  }
}
</style>
