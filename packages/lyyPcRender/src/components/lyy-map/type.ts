import { IElement } from '../../global'
import type { Rule } from 'ant-design-vue/es/form'

export interface LyyMap extends IElement {
  compName: 'lyy-map'
  prop: PropType
}

export interface PropType {
  /**
   * 绑定的表单
   */
  formId?: string
  label: string
  field: string
  /**
   * 校验规则
   */
  rules?: Rule
  /**
   * 提示占位符
   */
  placeholder?: string
  /**
   * 是否显示清除按钮
   */
  allowClear?: boolean
  /**
   * 是否禁用，默认 false
   */
  disabled?: boolean
  colon?: boolean
  labelCol?: object
  /**
   * 地点搜索服务：关键字搜索、周边搜索、范围内搜索
   */
  placeSearch?: boolean
  placeSearchOptions?: {
    pageSize?: number
    pageIndex?: number
    city?: string
    /**
     * 是否强制限制在设置的城市内搜索
     */
    citylimit?: boolean
    /**
     * 结果列表展示的容器id
     */
    panel?: 'string'
    /**
     * 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
     */
    autoFitView?: boolean
  }
  /**
   * 搜索关键字
   */
  searchKey?: string
  /**
   * 关键字搜索提示
   */
  autoComplete?: boolean
  autoCompleteOptions?: {
    /**
     * 关键字输入框的id
     */
    input: string
  }
}
