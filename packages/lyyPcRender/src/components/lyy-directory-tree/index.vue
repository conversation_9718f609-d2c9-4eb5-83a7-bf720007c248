<template>
  <div class="tree lyy-container">
    <div class="tree-container">
      <!-- 标题 -->
      <h4 class="tree-title">
        {{ title }}
        <!-- 新增按钮组 -->
        <span class="operation-wrap">
          <pc-render-template :elements="operationButtons"></pc-render-template>
        </span>
      </h4>

      <!-- 搜索输入框 -->
      <a-input
        class="search-input"
        v-if="showSearch"
        v-model:value="searchValue"
        :placeholder="placeholder"
      >
        <template #suffix>
          <search-outlined />
        </template>
      </a-input>

      <div
        class="tree-body"
        ref="treeRef"
        :style="{ height: `${treeBodyHeight}px` }"
      >
        <!-- 树 -->
        <a-directory-tree
          v-if="showData.length > 0"
          :tree-data="showData"
          :fieldNames="fieldNames"
          :selected-keys="selectedKeys"
          :expanded-keys="expandedKeys"
          :expand-action="expandAction"
          :show-icon="showIcon"
          :show-line="showLine"
          :default-expand-all="defaultExpandAll"
          :auto-expand-parent="prop.autoExpandParent ?? autoExpandParent"
          :selectable="selectable"
          :block-node="blockNode"
          :draggable="draggable"
          @select="handleSelect"
          @expand="handleExpand"
          @drop="handleDrop"
        >
          <!-- 节点前缀图标 -->
          <template #icon="data">
            <template v-if="prop?.prefixIcon">
              <LyyIcon :prop="prop?.prefixIcon"></LyyIcon>
            </template>
            <template v-else>
              <template v-for="item in prefixIcons">
                <template
                  v-if="
                    data[item?.customOption?.key] === item?.customOption?.value
                  "
                >
                  <LyyIcon
                    :key="item?.customOption?.key"
                    :prop="item"
                  ></LyyIcon>
                </template>
              </template>
            </template>
          </template>
          <!-- 节点名称 -->
          <template #title="data">
            <span
              class="node-title"
              :title="data[fieldNames.title]"
              :style="nodeStyle"
            >
              {{ data[fieldNames.title] }}
            </span>
            <!-- 操作按钮图标 -->
            <span
              class="operation"
              :class="{
                'operation-normal': isEdit || prop.operationMode === 'normal',
                'operation-hover':
                  !prop.operationMode || prop.operationMode === 'hover',
              }"
            >
              <template v-for="icon in operationIcons" :key="icon">
                <!-- 单个 icon -->
                <pc-render-template
                  class="operation-button"
                  :elements="[icon]"
                  v-if="isEdit"
                ></pc-render-template>
                <template v-else>
                  <span
                    class="operation-button"
                    v-if="
                      (!icon.prop?.customOption?.hideAtRoot || !data?.isRoot) &&
                      data[icon.prop?.customOption?.key] ===
                        icon.prop?.customOption?.value &&
                      (!icon?.prop?.customOption?.menuList ||
                        !icon?.prop?.customOption?.menuList?.length)
                    "
                    @click.capture="handleOperationButtonClick(icon, data)"
                  >
                    <pc-render-template
                      class="operation-button"
                      :elements="[icon]"
                    ></pc-render-template>
                  </span>
                  <!-- icon 下拉菜单 -->
                  <a-dropdown
                    v-if="
                      (!icon.prop?.customOption?.hideAtRoot || !data?.isRoot) &&
                      data[icon.prop?.customOption?.key] ===
                        icon.prop?.customOption?.value &&
                      icon?.prop?.customOption?.menuList &&
                      icon?.prop?.customOption?.menuList?.length
                    "
                  >
                    <span class="operation-button">
                      <pc-render-template
                        :elements="[icon]"
                      ></pc-render-template>
                    </span>
                    <template #overlay>
                      <a-menu>
                        <template
                          v-for="button in icon?.prop?.customOption?.menuList"
                        >
                          <a-menu-item
                            v-if="
                              data[button?.prop?.customOption?.key] ===
                              button?.prop?.customOption?.value
                            "
                            :key="button"
                            :style="button?.style || {}"
                            class="tree-menu-item"
                            @click.stop
                          >
                            <span
                              @click.capture="
                                handleOperationButtonClick(button, data)
                              "
                            >
                              <pc-render-template
                                :elements="[button]"
                              ></pc-render-template>
                            </span>
                          </a-menu-item>
                        </template>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>
              </template>
            </span>
          </template>
        </a-directory-tree>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { SearchOutlined } from '@ant-design/icons-vue'
import { cloneDeep } from 'lodash'
import LyyIcon from '../lyy-icon/index.vue'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import { useRoute } from 'vue-router'
import {
  defineComponent,
  ref,
  toRefs,
  onMounted,
  computed,
  watch,
  onBeforeUnmount,
  onActivated,
  inject,
  Ref,
  getCurrentInstance,
} from 'vue'

export default defineComponent({
  components: {
    SearchOutlined,
    LyyIcon,
  },
})
</script>

<script lang="ts" setup>
import { PropType } from './type'
import { useStore } from '@leyaoyao/libs/store'
import { ACTION } from '../../constants/action-type'
import {
  MOUNTED,
  ACTIVATED,
  DROP,
  ROUTE_PARAMS_CHANGE,
} from '../../constants/event-type'

const props = defineProps<{
  compId: string
  prop: PropType
  modelValue: any
}>()

const emit = defineEmits([ACTION])

const isEdit = inject<Ref<boolean>>('isEdit')

const internalInstance = getCurrentInstance()

const { $route } = internalInstance?.appContext.config.globalProperties || {}

const {
  title,
  placeholder = '搜索',
  showSearch = true,
  showIcon = true,
  showLine = false,
  defaultExpandAll,
  selectable = true,
  blockNode = true,
  draggable,
  dragRules,
  operationButtons,
} = toRefs(props.prop)

const fieldNames = computed(() => {
  return Object.assign(
    {
      key: 'key',
      title: 'title',
      children: 'children',
      parentId: 'parentId',
      index: 'index',
    },
    props.prop?.fieldNames || {},
  )
})
// 查找父级id
function findParentIds(tree, targetId) {
  const result = []
  if (
    !tree ||
    typeof tree !== 'object' ||
    !tree[fieldNames.value.key] ||
    !tree[fieldNames.value.children] ||
    !Array.isArray(tree[fieldNames.value.children])
  ) {
    // 输入参数格式不正确，返回空列表
    return result
  }
  if (tree[fieldNames.value.key] === targetId) {
    // 当前节点即为目标节点，返回空列表（因为没有父节点）
    return result
  }
  for (let i = 0; i < tree[fieldNames.value.children].length; i++) {
    const child = tree[fieldNames.value.children][i]
    if (child[fieldNames.value.key] === targetId) {
      // 目标节点在当前节点的子树中，将当前节点id添加到结果列表中，并返回
      result.push(tree[fieldNames.value.key])
      return result
    } else {
      // 继续在子树中查找
      const childResult = findParentIds(child, targetId)
      if (childResult.length > 0) {
        // 如果找到目标节点，将当前节点id添加到结果列表中，并返回
        result.push(tree[fieldNames.value.key], ...childResult)
        return result
      }
    }
  }
  // 遍历完子节点仍未找到目标节点，返回空列表
  return result
}
const root = computed(() => {
  return Object.assign(
    {
      show: false,
      [fieldNames.value.key]: '', // id 值
      [fieldNames.value.title]: '全部', // 节点名称
      [fieldNames.value.children]: [], // children 值
    },
    props.prop?.root || {},
  )
})

// 节点前缀图标
const prefixIcons = computed(() => props.prop?.prefixIcons ?? [])

// 搜索关键字
const searchValue = ref<string>('')

const operationIcons = computed(() => props.prop?.operationIcons || [])

const store = useStore()

// 选中节点
const isDefaultSelect = ref(props.prop?.isDefaultSelect)
// 选中末级子节点
const isDefaultSelectChild = ref(props.prop?.isDefaultSelectChild)
const selectKey = computed({
  get: () => {
    return props.modelValue?.selectKey ?? ''
  },
  set: (newValue) => {
    store.setValue(props.compId, newValue, 'modelValue.selectKey')
    proxyData.setProxyData(`${props.compId}.modelValue.selectKey`, newValue)
  },
})
const selectedKeys = computed(() => [selectKey.value])

watch(selectKey, (newValue) => {
  // 设置当前选中节点的祖先节点 id 列表
  const parentIdList = getParentIdList(props.prop.treeData, newValue)
  store.setValue(
    props.compId,
    parentIdList,
    'modelValue.parentIdsOfSelectedNode',
  )
  proxyData.setProxyData(
    `${props.compId}.modelValue.parentIdsOfSelectedNode`,
    parentIdList,
  )
})

const expandedKeys = computed({
  get: () => {
    // return (
    //   props.modelValue?.expandedKeys ??
    //   (root.value.show ? [root.value[fieldNames.value.key]] : [])
    // )
    return props.modelValue?.expandedKeys
  },
  set: (newValue) => {
    store.setValue(props.compId, newValue, 'modelValue.expandedKeys')
    proxyData.setProxyData(`${props.compId}.modelValue.expandedKeys`, newValue)
  },
})
const autoExpandParent = ref<boolean>(false)
const expandAction = computed(() => {
  return props.prop.expandAction ?? 'click'
})

const nodeStyle = ref(
  Object.assign(
    {
      width: 'auto',
    },
    props.prop?.nodeStyle || {},
  ),
)

// 备份数据
let dataSource: any = []
// 展示数据
const showData = ref([])
// 数据
const treeData = computed(() => {
  let data: any = []
  data = root.value.show
    ? [
        {
          isRoot: true,
          ...root.value,
          [fieldNames.value.children]: props.prop.treeData || [],
        },
      ]
    : props.prop.treeData || []
  const defaultItem = {}
  defaultItem[fieldNames.value.title] = '测试数据'
  defaultItem[fieldNames.value.key] = 1

  return data.length > 0 ? data : isEdit?.value ? [defaultItem] : data
})
const getFirstLast = (item) => {
  return item &&
    item[fieldNames.value.children || 'children'] &&
    item[fieldNames.value.children || 'children'].length > 0
    ? getFirstLast(item[fieldNames.value.children || 'children'][0])
    : item
}
watch(
  treeData,
  (value) => {
    dataSource = cloneDeep(value)
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    showData.value = cloneDeep(value)
    //树数据更新的时候 清空选择项数据
    // store.setValue(props.compId, null, 'modelValue.select')
    // 暂时转移到proxyData
    // proxyData.setProxyData(`${props.compId}.modelValue.select`, null)
    // proxyData.setProxyData(`${props.compId}.modelValue.expandedKeys`, [])
    // selectKey.value = ''
    if (
      isDefaultSelect.value &&
      (selectKey.value === undefined || selectKey.value == '') &&
      dataSource.length > 0
    ) {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      selectKey.value = value?.[0]?.[fieldNames.value.key]
      proxyData.setProxyData(
        `${props.compId}.modelValue.selectKey`,
        selectKey.value,
      )
      store.setValue(props.compId, selectKey.value, 'modelValue.selectKey')
      store.setValue(props.compId, value[0], 'modelValue.select')
      // 暂时转移到proxyData
      proxyData.setProxyData(`${props.compId}.modelValue.select`, value[0])
      emit(ACTION, { event: 'select' })
    }
    if (
      isDefaultSelectChild.value &&
      (selectKey.value === undefined || selectKey.value == '') &&
      value?.[0]
    ) {
      const firstLast = getFirstLast(value?.[0])
      selectKey.value = firstLast[fieldNames.value.key]
      proxyData.setProxyData(
        `${props.compId}.modelValue.selectKey`,
        selectKey.value,
      )
      store.setValue(props.compId, selectKey.value, 'modelValue.selectKey')
      store.setValue(props.compId, firstLast, 'modelValue.select')
      // 暂时转移到proxyData
      proxyData.setProxyData(`${props.compId}.modelValue.select`, firstLast)
      proxyData.setProxyData(`${props.compId}.modelValue.expandedKeys`, [
        selectKey.value,
      ])
      emit(ACTION, { event: 'select' })
    }
  },
  { deep: true, immediate: true },
)

watch(searchValue, (value) => {
  const dataSourceBackup = cloneDeep(dataSource)
  showData.value =
    value === '' ? dataSourceBackup : filterNodes(value, dataSourceBackup)
  searchValue.value = value
})

// const route = useRoute()
watch(
  () => $route.params,
  (value) => {
    emit(ACTION, {
      event: ROUTE_PARAMS_CHANGE,
    })
  },
  {
    deep: true,
    immediate: true,
  },
)

const treeRef = ref<any>(null)
const treeBodyHeight = ref<string | number>('auto')

const calTreeBodyHeight = () => {
  const { top } = treeRef.value.getBoundingClientRect()
  let siblingHeight = 0
  const nextSibling = treeRef.value?.nextElementSibling
  if (nextSibling) {
    siblingHeight = nextSibling.clientHeight
  }
  treeBodyHeight.value =
    document.body.clientHeight - top - siblingHeight - 16 - 8
}

onMounted(() => {
  calTreeBodyHeight()
  emit(ACTION, {
    event: MOUNTED,
  })
})
onActivated(() => {
  emit(ACTION, { event: ACTIVATED })
})
onBeforeUnmount(() => {
  store.deleteValue(props.compId, 'select')
  store.deleteValue(props.compId, 'operation')
  // 暂时转移到proxyData
  // proxyData.setProxyData(`${props.compId}.modelValue.operation`, null)
})

/**
 * @description 操作按钮（编辑、移动、删除）点击，分发事件处理
 */
function handleOperationButtonClick(iconConfig, data): void {
  store.setValue(props.compId, data.data, 'modelValue.operation')
  // 暂时转移到proxyData
  proxyData.setProxyData(`${props.compId}.modelValue.operation`, data.data)
}

/**
 * @description 选中节点，把数据存到全局store
 */
function handleSelect(selectedKeys, e) {
  if (selectedKeys.length === 0 || selectKey.value === selectedKeys[0]) return
  if (selectedKeys.length === 0) {
    store.setValue(props.compId, {}, 'modelValue.select')
    // 暂时转移到proxyData
    proxyData.setProxyData(`${props.compId}.modelValue.select`, {})
    emit(ACTION, { event: 'select' })
    return
  }

  store.setValue(props.compId, e.node.dataRef, 'modelValue.select')
  // 暂时转移到proxyData
  proxyData.setProxyData(`${props.compId}.modelValue.select`, e.node.dataRef)
  if (props.prop?.onextraevent) {
    emit(ACTION, { event: props.prop?.onextraevent })
  }
  selectKey.value = selectedKeys[0]
  emit(ACTION, { event: 'select' })
}

/**
 * @description 展开节点
 */
function handleExpand(keys: string[]) {
  expandedKeys.value = keys
  autoExpandParent.value = false
}

/**
 * @description 递归树节点，找到目标节点，做对应 callback
 */
function loop(data: any[], key: string | number, callback: any) {
  data.some((item, index) => {
    if (item[fieldNames.value.key] === key) {
      callback(item, index, data)
      return true
    }
    if (item[fieldNames.value.children]) {
      return loop(item[fieldNames.value.children], key, callback)
    }
  })
}

/**
 * @description 拖拽节点结束
 */
function handleDrop(info) {
  const dropKey = info.node.key
  const dragKey = info.dragNode.key
  const dropPos = info.node.pos.split('-')
  /* dropPosition = 0  移动到 dropNode 内部
   * dropPosition = -1 移动到 dropNode 前面
   * dropPosition = 1  移动到 dropNode 后面
   */
  const dropPosition = info.dropPosition - Number(dropPos.at(-1))

  // 拖到某个节点内部
  if (info.dropToGap) {
    // 同级内拖拽
    if (
      info.dragNode.dataRef[fieldNames.value.parentId] ===
      info.node.dataRef[fieldNames.value.parentId]
    ) {
      let newIndex // 节点拖拽后的新索引
      // 删除原来拖拽的节点
      loop(showData.value, dragKey, (item, index: number, arr) => {
        arr.splice(index, 1)
      })
      // 插入拖拽后落下的节点
      loop(showData.value, dropKey, (item, index: number, arr) => {
        newIndex = dropPosition === -1 ? index : index + 1
        arr.splice(newIndex, 0, cloneDeep(info.dragNode.dataRef))
      })
      // 如果是根级节点
      if (!info.node.dataRef[fieldNames.value.parentId]) {
        store.setValue(
          props.compId,
          {
            [fieldNames.value.index]: newIndex, // 节点拖拽后的新序号
            data: cloneDeep(info.dragNode.dataRef), // 节点数据
            parentData: {
              [fieldNames.value.key]: null,
              [fieldNames.value.children]: cloneDeep(showData.value),
            }, // 父节点数据
          },
          'modelValue.drop',
        )
        // 暂时转移到proxyData
        proxyData.setProxyData(`${props.compId}.modelValue.drop`, {
          [fieldNames.value.index]: newIndex, // 节点拖拽后的新序号
          data: cloneDeep(info.dragNode.dataRef), // 节点数据
          parentData: {
            [fieldNames.value.key]: null,
            [fieldNames.value.children]: cloneDeep(showData.value),
          }, // 父节点数据
        })
        emit(ACTION, { event: DROP })
        return
      }
      // 非根级节点
      loop(
        showData.value,
        info.node.dataRef[fieldNames.value.parentId],
        (item, index: number, arr) => {
          store.setValue(
            props.compId,
            {
              [fieldNames.value.index]: newIndex, // 节点拖拽后的新序号
              data: cloneDeep(info.dragNode.dataRef), // 节点数据
              parentData: cloneDeep(item), // 父节点数据
            },
            'modelValue.drop',
          )
          // 暂时转移到proxyData
          proxyData.setProxyData(`${props.compId}.modelValue.drop`, {
            [fieldNames.value.index]: newIndex, // 节点拖拽后的新序号
            data: cloneDeep(info.dragNode.dataRef), // 节点数据
            parentData: cloneDeep(item), // 父节点数据
          })
          emit(ACTION, { event: DROP })
        },
      )
    }
    // 跨级拖拽
    else {
      let newIndex // 节点拖拽后的新索引
      let newParent // 节点拖拽后的新父节点
      // 删除原来拖拽的节点
      loop(showData.value, dragKey, (item, index: number, arr) => {
        arr.splice(index, 1)
      })
      // 插入拖拽后落下的节点
      loop(showData.value, dropKey, (item, index: number, arr) => {
        newIndex = dropPosition === -1 ? index : index + 1
        arr.splice(newIndex, 0, cloneDeep(info.dragNode.dataRef))
      })
      // 寻找新的父节点
      // 如果拖拽到根级别
      if (!info.node.dataRef[fieldNames.value.parentId]) {
        store.setValue(
          props.compId,
          {
            [fieldNames.value.index]: newIndex, // 节点拖拽后的新序号
            data: cloneDeep(info.dragNode.dataRef), // 节点数据
            parentData: {
              [fieldNames.value.key]: null,
              [fieldNames.value.children]: cloneDeep(showData.value),
            }, // 父节点数据
          },
          'modelValue.drop',
        )
        // 暂时转移到proxyData
        proxyData.setProxyData(`${props.compId}.modelValue.drop`, {
          [fieldNames.value.index]: newIndex, // 节点拖拽后的新序号
          data: cloneDeep(info.dragNode.dataRef), // 节点数据
          parentData: {
            [fieldNames.value.key]: null,
            [fieldNames.value.children]: cloneDeep(showData.value),
          }, // 父节点数据
        })
        emit(ACTION, { event: DROP })
        return
      }
      // 拖拽非根级
      loop(
        showData.value,
        info.node.dataRef[fieldNames.value.parentId],
        (item, index: number, arr) => {
          newParent = cloneDeep(item)
        },
      )
      // 判断节点是否可移入
      const rules =
        dragRules?.value?.filter((rule) => {
          return newParent[rule.key] === rule.value
        }) || []
      let isRejected = false
      if (rules.length > 0) {
        rules.every((rule) => {
          rule.rejectedChildren.some((v) => {
            if (info.dragNode.dataRef[v.key] === v.value) {
              isRejected = true
            }
            return info.dragNode.dataRef[v.key] === v.value
          })
        })
      }
      if (isRejected) return
      store.setValue(
        props.compId,
        {
          [fieldNames.value.index]: newIndex,
          data: cloneDeep(info.dragNode.dataRef),
          parentData: newParent,
        },
        'modelValue.drop',
      )
      // 暂时转移到proxyData
      proxyData.setProxyData(`${props.compId}.modelValue.drop`, {
        [fieldNames.value.index]: newIndex,
        data: cloneDeep(info.dragNode.dataRef),
        parentData: newParent,
      })
      emit(ACTION, { event: DROP })
    }
  } else {
    // 判断节点是否可移入
    const rules =
      dragRules?.value?.filter((rule) => {
        return info.node.dataRef[rule.key] === rule.value
      }) || []
    let isRejected = false
    if (rules.length > 0) {
      rules.every((rule) => {
        rule.rejectedChildren.some((v) => {
          if (info.dragNode.dataRef[v.key] === v.value) {
            isRejected = true
          }
          return info.dragNode.dataRef[v.key] === v.value
        })
      })
    }
    if (isRejected) return

    // 删除原来拖拽的节点
    loop(showData.value, dragKey, (item, index: number, arr) => {
      arr.splice(index, 1)
    })
    // 插入拖拽后落下的节点
    loop(showData.value, dropKey, (item) => {
      item.children = item.children || []
      item.children.unshift(cloneDeep(info.dragNode.dataRef))
      store.setValue(
        props.compId,
        {
          [fieldNames.value.index]: 1, // 节点拖拽后的新序号
          data: cloneDeep(info.dragNode.dataRef), // 节点数据
          parentData: cloneDeep(item), // 父节点数据
        },
        'modelValue.drop',
      )
      // 暂时转移到proxyData
      proxyData.setProxyData(`${props.compId}.modelValue.drop`, {
        [fieldNames.value.index]: 1, // 节点拖拽后的新序号
        data: cloneDeep(info.dragNode.dataRef), // 节点数据
        parentData: cloneDeep(item), // 父节点数据
      })
      emit(ACTION, { event: DROP })
    })
  }
}

/**
 * @description 树过滤，保留父节点
 */
function filterNodes(keyword: string, treeList: any[]) {
  if (!Array.isArray(treeList)) {
    return []
  }
  return treeList.filter((node) => {
    node.children = filterNodes(keyword, node.children)
    if (node[props.prop?.fieldNames?.title || 'title'].includes(keyword)) {
      return true
    } else if (node.children.length === 0) {
      return false
    }
    return true
  })
}

/**
 * @description 获取当前选中节点的祖先节点id
 */
function getParentIdList(treeList, id) {
  if (!treeList || treeList.length === 0) return
  for (const node of treeList) {
    if (node[fieldNames.value?.key] === id) {
      return []
    } else {
      if (node.children?.length) {
        const ids = getParentIdList(node[fieldNames.value?.children], id)
        if (ids !== undefined) {
          return ids.concat(node[fieldNames.value?.key]).reverse()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree {
  width: 100%;
  height: 100%;
  padding: 8px;
  background: #fff;
  :deep(.ant-tree .ant-tree-treenode) {
    height: 36px;
    align-items: center;
  }

  &-container {
    display: flex;
    width: 100%;
    height: 100%;
    max-height: 100%;
    flex-direction: column;
    .search-input {
      margin-bottom: 8px;
    }

    .operation-wrap {
      display: inline-flex;
      justify-content: flex-end;
      :deep(.ant-btn) {
        margin-left: 8px;
      }
    }

    .tree-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .tree-body {
      // flex: 1;
      overflow-y: auto;
    }
    .tree-body::-webkit-scrollbar-track {
      background-color: transparent; /* 修改为你想要的轨道颜色 */
      border-radius: 5px; /* 修改为你想要的滑块圆角 */
    }
    .tree-body::-webkit-scrollbar-thumb {
      background-color: transparent; /* 修改为你想要的滑块颜色 */
      border-radius: 5px; /* 修改为你想要的滑块圆角 */
    }
    .tree-body:hover::-webkit-scrollbar-thumb {
      background-color: #888; /* 修改为你想要的悬停颜色 */
    }
    .tree-body::-webkit-scrollbar {
      width: 8px; /* 修改为你想要的滚动条宽度 */
      height: 8px; /* 修改为你想要的滚动条高度 */
      border-radius: 5px;
    }
  }
  :deep(.ant-tree-switcher) {
    width: 16px;
    line-height: 33px;
  }
  :deep(.ant-tree-switcher-icon) {
    font-size: 12px;
    color: #7c88b1;
  }
  ::v-deep .ant-tree-node-content-wrapper {
    white-space: nowrap;
    height: 36px;
    line-height: 36px;
    .ant-tree-iconEle {
      vertical-align: middle;
      margin-top: -2px;
    }
    .node-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .draggable-wrap {
      height: auto;
      display: inline;
    }
    .operation-hover {
      visibility: hidden;
    }
    .operation-normal {
      display: inline-block;
      visibility: visible;
    }
    // &:hover {
    //   .operation-hover {
    //     visibility: visible;
    //   }
    // }
    // 节点 selected 时显示操作按钮
    &.ant-tree-node-selected {
      .operation-hover {
        visibility: visible;
      }
    }
  }

  ::v-deep {
    .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
      background: #e6f2fd;
    }
    .ant-tree.ant-tree-directory
      .ant-tree-treenode
      .ant-tree-node-content-wrapper.ant-tree-node-selected {
      color: #000000d9;
    }
    .ant-tree.ant-tree-directory
      .ant-tree-treenode-selected
      .ant-tree-switcher {
      color: inherit;
    }
  }

  .button-box {
    text-align: center;
  }
}
.operation-button {
  margin-left: 10px;
}
</style>

<style lang="scss">
.tree-menu-item {
  button {
    height: auto !important;
    padding: 0 !important;
    line-height: 1 !important;
  }
}
</style>
