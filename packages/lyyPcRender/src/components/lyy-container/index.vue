<script setup lang="ts">
import { defineComponent, StyleValue, computed, inject } from 'vue'
import { PropType } from './type'

defineComponent({
  name: 'lyy-container',
})
const props = defineProps<{
  prop?: PropType
  childrens?: IElement[]
  style: StyleValue | undefined
}>()

const mainLayout = inject('mainLayout')

const isPadding = computed(() => {
  return props.prop?.isPadding ?? true
})

const style = computed(() => {
  // let newStyle = props.prop?.backgroundImg
  //   ? {
  //       backgroundImage: `url(${props.prop?.backgroundImg})`,
  //     }
  //   : {}
  let newStyle = {
    backgroundImage: props.prop?.backgroundImg
      ? `url(${props.prop?.backgroundImg})`
      : 'none',
    padding: isPadding.value ? '12px' : '0px',
  }
  if (props.style) {
    newStyle = Object.assign({}, newStyle, props.style)
    return newStyle
  }
  return newStyle
})
</script>

<template>
  <div
    class="lyy-container content-container"
    :class="{ mainLayout: mainLayout }"
    :style="style"
  >
    <!--    <slot></slot>-->
    <pc-render-template :elements="props.childrens"></pc-render-template>
  </div>
</template>
<style lang="scss" scoped>
.content-container {
  padding: 12px;
  height: inherit;
}
.lyy-container {
  position: relative;
  &.mainLayout {
    height: 100%;
    padding: 0;
  }
}
</style>
<style lang="scss">
.ant-select-dropdown.lyy-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  font-weight: 500;
}
.ant-form.ant-form-vertical {
  .ant-form-row {
    // display: flex;
    flex-direction: column;
  }
  .ant-form-item {
    .ant-form-item-control {
      flex: 1;
    }
  }
}
.ant-form-item {
  .ant-form-item-control {
    .ant-input-affix-wrapper-disabled,
    .ant-input-number-disabled,
    .ant-input-disabled,
    .file-uploader-disabled,
    .ant-picker-disabled {
      border-color: transparent !important;
      cursor: pointer;
      &:hover {
        cursor: pointer;
      }
    }
    .ant-input-affix-wrapper-disabled {
      border-color: transparent;
      .ant-input-suffix {
        .anticon {
          display: none;
        }
      }
    }

    .ant-select-disabled {
      .ant-select-selector {
        border-color: transparent;
      }
      .ant-select-arrow {
        display: none;
      }
      & + .anticon {
        display: none;
      }
    }
    .ant-picker-disabled {
      border-color: transparent;
      .ant-picker-suffix {
        display: none;
      }
      .ant-picker-input > input[disabled] {
        cursor: pointer;
      }
    }
    .ant-upload-disabled {
      display: none;
    }
  }
}
.ant-form-item {
  margin-bottom: 16px !important;
  .ant-form-item-label {
    & > label {
      color: rgba(0, 0, 0, 0.65) !important;
    }
    .form-item-label-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(0, 0, 0, 0.65);
    }
    .form-item-label-desc {
      font-size: 12px;
      color: #999999;
      margin-left: 4px;
      font-style: normal;
    }
  }
}

@media (min-width: 2000px) {
  .ant-col-xxxl-24 {
    display: block;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .ant-col-xxxl-12 {
    display: block;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .ant-col-xxxl-8 {
    display: block;
    flex: 0 0 33.33%;
    max-width: 33.33%;
  }
  .ant-col-xxxl-6 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .ant-col-xxxl-4 {
    display: block;
    flex: 0 0 16.67%;
    max-width: 16.67%;
  }
  .ant-col-xxxl-3 {
    display: block;
    flex: 0 0 12.5%;
    max-width: 12.5%;
  }
  .ant-col-xxxl-2 {
    display: block;
    flex: 0 0 8.33%;
    max-width: 8.33%;
  }
}
</style>
