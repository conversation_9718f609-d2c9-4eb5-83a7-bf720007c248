<script setup lang="ts">
import { computed, toRefs, watch, inject, Ref } from 'vue'
import { PropType } from './type'

const props = defineProps<{
  prop: PropType
  // eslint-disable-next-line no-undef
  childrens: IComponent[]
}>()
const { childrens, prop } = toRefs(props)

const isEdit = inject<Ref<boolean>>('isEdit')

// eslint-disable-next-line unicorn/consistent-destructuring
const buttonGap = computed(() => prop?.value?.buttonGap ?? '12px')
</script>

<template>
  <div class="lyy-footer lyy-container" :class="isEdit ? '' : 'isNoEdit'">
    <template v-if="prop.extraList.length > 0">
      <div
        class="align-left"
        v-if="prop.extraList.length > 0"
        :style="prop?.leftStyle"
      >
        <pc-render-template :elements="prop.extraList"></pc-render-template>
      </div>
      <div class="align-right" :style="prop?.rightStyle">
        <pc-render-template :elements="props.childrens"></pc-render-template>
      </div>
    </template>
    <pc-render-template v-else :elements="props.childrens"></pc-render-template>
  </div>
</template>
<style lang="scss" scoped>
.lyy-footer {
  position: absolute !important;
  height: 48px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-end;
  left: 0px;
  right: 0;
  bottom: 0px;
  background-color: #ffffff;
  padding-left: 24px;
  padding-right: 24px;
  box-shadow: 0px -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  &.isNoEdit {
    gap: v-bind(buttonGap);
  }
  & > .draggable-wrap {
    display: inherit;
    gap: v-bind(buttonGap);
    height: auto !important;
  }
  ::v-deep .ant-form-item {
    margin-bottom: 0;
  }
  .align-left {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    gap: v-bind(buttonGap);
    & > .draggable-wrap {
      display: inherit;
      gap: v-bind(buttonGap);
    }
  }
  .align-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: v-bind(buttonGap);
    & > .draggable-wrap {
      display: inherit;
      gap: v-bind(buttonGap);
    }
  }
}
</style>
