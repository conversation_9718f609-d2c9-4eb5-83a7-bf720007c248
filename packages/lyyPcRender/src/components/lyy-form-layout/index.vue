<script lang="ts" setup>
import {
  defineComponent,
  defineEmits,
  computed,
  inject,
  ref,
  Ref,
  onMounted,
  onBeforeMount,
  provide,
  toRefs,
  reactive,
  onActivated,
  onDeactivated,
} from 'vue'
import uniqueId from 'lodash/uniqueId'
import isNil from 'lodash/isNil'
import throttle from 'lodash/throttle'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { useTpl, getAuthCodeList, formula } from '@leyaoyao/libs'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import LyyIcon from '../lyy-icon/index.vue'
import LyyButton from '../lyy-button/index.vue'
import { PropType, ButtonItem, TagMap } from './type'

defineComponent({
  name: 'lyy-form-layout',
})

const props = defineProps<{
  compId: string
  prop: PropType
  modelValue?: string | undefined
}>()

const emit = defineEmits([ACTION, UPDATE_MODELVALUE])

const isEdit = inject<Ref<boolean>>('isEdit')
const authCodeList = ref(getAuthCodeList())
const formState = inject('formState')
const { cacheScroll = true } = props.prop
const { cols, responsive, billChanger } = toRefs(props.prop)
const contentRef = ref(null)

provide('cols', cols)
provide('responsive', responsive)

const overflowHidden = computed(() => {
  return isEdit?.value ? 'visible' : 'hidden'
})
const overflowAuto = computed(() => {
  return isEdit?.value ? 'visible' : 'auto'
})

const formTitle = computed(() => {
  return props.prop.title
    ? useTpl(props.prop.title, formState?.value ?? {})
    : ''
})

const billIds = computed(() => {
  if (!billChanger?.value) return []
  const { billIds } = billChanger.value
  const _data = billIds
    ? formula(billIds)(proxyData.getProxyData('billIds'))
    : []
  if (typeof _data === 'string' && _data !== '') {
    return _data.split(',')
  }
  return Array.isArray(_data) ? _data : []
})

const currentId = billChanger?.value?.currentId
  ? formula(billChanger.value?.currentId)(proxyData.getProxyData('billId'))
  : ''

const currentIndex = ref(currentId ? billIds.value.indexOf(currentId) : -1)

const billChangerDisplay = computed(() => {
  if (!billChanger?.value) return false
  if (!billChanger.value.show) return true
  return formula(billChanger.value.show)(proxyData.getProxyData('billIds'))
})

const total = computed(() => {
  return billIds.value.length
})

const nextDisabled = computed(() => {
  if (currentIndex.value === billIds.value.length - 1) {
    return true
  }
  return false
})

const prevDisabled = computed(() => {
  if (currentIndex.value <= 0) {
    return true
  }
  return false
})

const handleNext = () => {
  if (nextDisabled.value) return
  currentIndex.value = currentIndex.value + 1
  emit(UPDATE_MODELVALUE, billIds.value[currentIndex.value])
  emit(ACTION, { event: 'next' })
  emit(ACTION, { event: '下一张' })
}

const handlePrev = () => {
  if (prevDisabled.value) return
  currentIndex.value = currentIndex.value - 1
  emit(UPDATE_MODELVALUE, billIds.value[currentIndex.value])
  emit(ACTION, { event: 'prev' })
  emit(ACTION, { event: '上一张' })
}

const tags = computed(() => {
  const _tags = props.prop.tags
    .map((tag) => {
      const { field, textMap = [] } = tag
      const value = formState?.value[field]
      const res = getTagrget(value, textMap)
      return res
    })
    .filter((item) => !!item)
  return _tags
})

const getTagrget = (value, textMap): TagMap | undefined => {
  if (value === undefined) return value
  const _res = textMap.find((item) => {
    return item.origin == value
  })
  return _res
}
const actionButtons = computed(() => {
  if (isEdit?.value) {
    const _buttons = props.prop.actionButtons.map((item) => {
      return {
        ...item,
        type: 'text',
      }
    })
    return _buttons
  }
  const res = props.prop.actionButtons
    .filter((item) => {
      const { show, authCode } = item
      const display = formula(show)(proxyData.getProxyData())
      const isAuth =
        authCode && authCodeList.value.length > 0
          ? authCodeList.value.includes(authCode)
          : true
      return display && isAuth
    })
    .map((item) => {
      const { forbid } = item
      const _disabled = forbid
        ? formula(forbid)(proxyData.getProxyData())
        : false
      delete item.authCode
      return {
        ...item,
        type: 'text',
        disabled: _disabled,
      }
    })
  return res
})

const getButtonWidth = (buttonItem) => {
  const { text, icon } = buttonItem
  if (text.length <= 4) {
    const width = text.length * 14 + 12 + (icon ? 18 : 0)
    if (width < 70) {
      return 70
    }
    return width
  }
  if (text.length > 4) {
    const width = text.length * 14 + 12 + (icon ? 18 : 0)
    if (width < 100) {
      return 100
    }
    return width
  }
}

let observer = null
const buttonWrapRef = ref(null)

const avaliableButtons = computed(() => {
  if (!buttonWrapRef.value) return { visible: [], overflow: [] }
  const availableWidth = buttonWrapRef.value?.offsetWidth - 16
  let usedWidth = 0

  const visible: ButtonItem[] = []
  const overflow: ButtonItem[] = []

  for (let index = 0; index < actionButtons.value.length; index++) {
    const button = actionButtons.value[index]
    const buttonWidth = getButtonWidth(button) + 8
    if (usedWidth + buttonWidth <= availableWidth) {
      visible.push(button)
      usedWidth += buttonWidth
    } else {
      overflow.push(...actionButtons.value.slice(index))
      break
    }
  }
  return reactive({
    visible,
    overflow,
  })
})

const adjustButtons = () => {
  if (!buttonWrapRef.value) return
  const availableWidth = buttonWrapRef.value?.offsetWidth - 16
  let usedWidth = 0

  const visible: ButtonItem[] = []
  const overflow: ButtonItem[] = []

  for (let index = 0; index < actionButtons.value.length; index++) {
    const button = actionButtons.value[index]
    const buttonWidth = getButtonWidth(button) + 8
    if (usedWidth + buttonWidth <= availableWidth) {
      visible.push(button)
      usedWidth += buttonWidth
    } else {
      overflow.push(...actionButtons.value.slice(index))
      break
    }
  }
  avaliableButtons.value.visible.length = 0
  avaliableButtons.value.visible.push(...visible)
  avaliableButtons.value.overflow.length = 0
  avaliableButtons.value.overflow.push(...overflow)
}

const handleMenuClick = ({ key, disabled }) => {
  if (isEdit?.value) return
  if (disabled) return
  emit(ACTION, { event: 'actionClick' }, key)
  emit(ACTION, { event: key })
}

const FormLayoutScrollTopKey = uniqueId(`${props.compId}-FormLayoutScrollTop`)

const scrollTopRef = ref(0)
const recordScrollFn = getRecordScrollTopFn()

onActivated(() => {
  // 默认开启
  // 因为新增属性对于已经在使用的组件来说 model 中不存在该属性，所以使用 false 判断而不是 falsy 值
  // 关闭该功能则需要代码配置 cacheScroll 字段
  if (!cacheScroll) {
    return
  }

  const storageScrollTop = sessionStorage.getItem(FormLayoutScrollTopKey)
  if (!isNil(storageScrollTop) && contentRef.value) {
    contentRef.value.scrollTo(0, Number(storageScrollTop))
  }
})

onDeactivated(() => {
  if (contentRef.value && cacheScroll) {
    sessionStorage.setItem(
      FormLayoutScrollTopKey,
      scrollTopRef.value.toString(),
    )
  }
})

onMounted(() => {
  if (isEdit?.value) return
  observer = new ResizeObserver(() => {
    adjustButtons()
  })
  observer.observe(buttonWrapRef.value) // 监听容器宽度变化

  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', recordScrollFn)
  }
})

onBeforeMount(() => {
  observer && observer.disconnect()

  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', recordScrollFn)
  }
})

function getRecordScrollTopFn() {
  return throttle(function recordScrollTopFn(e) {
    if (contentRef.value) {
      scrollTopRef.value = Number(contentRef.value.scrollTop)
    }
  }, 300)
}
</script>

<template>
  <!-- 外层容器 -->
  <div
    class="lyy-form-layout lyy-container"
    :class="{ 'lyy-form-layout-edit': isEdit }"
  >
    <div class="form-header">
      <div class="header-content-wrap">
        <div class="form-title-wrap">
          <div class="form-title">{{ formTitle }}</div>
          <div class="form-tags">
            <div
              v-for="(item, index) in tags"
              :key="index"
              class="form-tag-item"
            >
              <a-tag :color="item.color">{{ item.result }}</a-tag>
            </div>
          </div>
        </div>
        <div class="form-action-bar-wrap">
          <div ref="buttonWrapRef" class="form-action-bar">
            <div class="action-buttons-wrap">
              <template v-if="isEdit">
                <LyyButton
                  class="button"
                  :prop="item"
                  v-for="item in actionButtons"
                  :key="item.key"
                  @click="handleMenuClick(item)"
                />
              </template>
              <template v-else>
                <LyyButton
                  class="button"
                  :prop="item"
                  v-for="item in avaliableButtons.visible"
                  :key="item.key"
                  @click="handleMenuClick(item)"
                />
              </template>
            </div>
            <div
              class="action-buttons-more"
              v-if="avaliableButtons.overflow.length > 0 && !isEdit"
            >
              <a-dropdown
                placement="bottomLeft"
                :trigger="['hover']"
                overlayClassName="table-footer-buttons-dropdown"
              >
                <span>
                  <MoreOutlined />
                  更多
                </span>

                <template #overlay>
                  <a-menu @click="handleMenuClick">
                    <a-menu-item
                      v-for="item in avaliableButtons.overflow"
                      :disabled="item.disabled"
                      :key="item.key"
                    >
                      <LyyIcon :prop="{ iconName: item.icon }" />
                      {{ item.text }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
      </div>
      <div
        class="short-key-box"
        v-if="(billChangerDisplay && billIds.length > 0) || isEdit"
      >
        <div class="short-key-wrap">
          <LyyIcon
            class="prev-btn page-btn"
            :class="{ 'btn-disabled': prevDisabled }"
            :prop="{ iconName: 'icon-zuo_line' }"
            @click="handlePrev"
          />
          <div class="page-input-wrap">
            <span class="cur-page">{{ currentIndex + 1 }}</span>
            /
            <span class="page-count">{{ total }}</span>
          </div>
          <LyyIcon
            class="next-btn page-btn"
            :class="{ 'btn-disabled': nextDisabled }"
            :prop="{ iconName: 'icon-you_line' }"
            @click="handleNext"
          />
        </div>
      </div>
    </div>

    <div class="form-content-wrap">
      <div class="form-content" ref="contentRef">
        <pc-render-template
          :elements="props.prop.cardList"
        ></pc-render-template>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.lyy-form-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: #f5f5f5;
  .form-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    box-sizing: border-box;
    background-color: #fff;
    .header-content-wrap {
      display: flex;
      flex: 1;
      justify-content: space-between;
    }

    @media (max-width: 1379px) {
      .header-content-wrap {
        gap: 32px;
      }
      .form-title-wrap {
        width: 40%;
      }
    }
    @media (min-width: 1038px) and (max-width: 1599px) {
      .header-content-wrap {
        gap: 140px;
      }
    }
    @media (min-width: 1600px) {
      .header-content-wrap {
        gap: 185px;
      }
    }
    .form-title-wrap {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      flex-shrink: 0;
      gap: 8px;
      .form-title {
        font-weight: 500;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
      }
      .form-tags {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-top: 3px;
        .form-tag-item {
          .ant-tag {
            margin: 0;
          }
        }
      }
    }
    .form-action-bar-wrap {
      flex: 1;
    }
    .form-action-bar {
      display: flex;
      flex: 1;
      align-items: center;
      gap: 8px;
      .action-buttons-wrap {
        flex: 1;
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-end;
        gap: 8px;
        :deep(.lyy-icon) {
          display: inline-flex;
        }
      }
      .action-buttons-more {
        flex-shrink: 0;
        cursor: pointer;
      }
    }
    .short-key-box {
      height: 100%;
    }
    .short-key-wrap {
      position: relative;
      flex-shrink: 0;
      display: flex;
      height: 32px;
      align-items: center;
      justify-content: flex-end;
      width: 121px;
      box-sizing: border-box;
      padding-right: 8px;
      gap: 8px;
      .lyy-icon {
        width: 16px;
        height: 16px;
        display: inline-flex;
        justify-content: center;
        cursor: pointer;
        :deep(.icon) {
          font-size: 16px !important;
        }
        border-radius: 2px;
        &:hover {
          background-color: rgba(0, 0, 0, 0.06);
        }
        &:active {
          background-color: rgba(0, 0, 0, 0.15);
        }
        &.btn-disabled {
          cursor: not-allowed;
          color: rgba(0, 0, 0, 0.25);
          &:hover {
            background-color: transparent;
          }
        }
      }
      &::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 7px;
        width: 0;
        height: 18px;
        border-right: 1px solid rgba(0, 0, 0, 0.15);
      }
    }
  }
  .form-content-wrap {
    background: #f5f5f5;
    flex: 1;
    overflow: v-bind(overflowHidden);
    padding: 12px;
    .form-content {
      height: 100%;
      overflow-y: v-bind(overflowAuto);
      background: #fff;
      padding: 8px 16px 12px;
      border-radius: 4px 4px 0px 0px;
    }
  }
  &.lyy-form-layout-edit {
    overflow: visible !important;
    .form-header {
      .form-action-bar {
        .action-buttons-wrap {
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>
