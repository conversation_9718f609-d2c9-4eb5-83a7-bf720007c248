<template>
  <a-input-group compact class="input-group">
    <a-input-number
      class="open-interval"
      v-model:value="value.openValue"
      v-bind="$attrs"
      style="width: 40%"
      @change="handleUpdate"
    />
    <a-input class="separator" placeholder="~" />
    <a-input-number
      class="close-interval"
      v-model:value="value.closeValue"
      v-bind="$attrs"
      style="width: 40%"
      @change="handleUpdate"
    />
  </a-input-group>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { UPDATE_MODELVALUE } from '../../../../constants/action-type'
import type { InputProps } from 'ant-design-vue'

interface InputType extends InputProps {
  compId?: string
  compName?: string
}

defineExpose({
  name: 'ad-number-input',
})

const emit = defineEmits([UPDATE_MODELVALUE])
const props = defineProps<{
  value?: any
  // prop: PropType
}>()
const value = computed(() => {
  const [v1, v2] = props.value || []
  return { openValue: v1, closeValue: v2 }
})
const handleUpdate = () => {
  const { openValue, closeValue } = value.value
  emit(UPDATE_MODELVALUE, [openValue, closeValue])
}
</script>
<style lang="scss">
.separator {
  width: 50px !important;
  border-left: none !important;
  pointer-events: none;
  text-align: center !important;
}
.input-group {
  .open-interval {
    border-right: none;
  }

  .close-interval {
    border-left: none;
  }
}
</style>
