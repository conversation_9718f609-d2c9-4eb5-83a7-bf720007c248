<template>
  <a-config-provider :locale="locale">
    <component
      :is="componentMap[props.type || props.compName || 'AdInput']"
      v-model:value="value"
      v-bind="$attrs"
      @update:modelValue="update($event)"
    ></component>
  </a-config-provider>
</template>
<script lang="ts">
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN'
</script>
<script lang="ts" setup>
import { computed, ref } from 'vue'
import type { Component } from 'vue'
import { UPDATE_MODELVALUE } from '../../../constants/action-type'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import {
  AdDatePicker,
  AdInput,
  AdInputRange,
  AdNumberInput,
  AdDateRange,
  AdSelect,
  AdSwitch,
  AdTimePicker,
  AdTimeRange,
  AdCascader,
} from './components'
defineExpose({
  name: 'field',
})
type CompType =
  | 'select'
  | 'input'
  | 'datePicker'
  | 'inputRange'
  | 'numberInput'
  | 'dateRange'
  | 'switch'
  | 'timePicker'
  | 'timeRange'
  | 'cascader'
type FiledType = {
  value?: any
  type?: CompType
  compName?: CompType
  compId?: string
}
type ComponentMapType = {
  [key in CompType]: Component
}
const props = defineProps<FiledType>()
const emit = defineEmits([UPDATE_MODELVALUE, 'change'])
const locale = ref(zhCN)
const componentMap: ComponentMapType = {
  select: AdSelect,
  input: AdInput,
  datePicker: AdDatePicker,
  inputRange: AdInputRange,
  numberInput: AdNumberInput,
  dateRange: AdDateRange,
  switch: AdSwitch,
  timePicker: AdTimePicker,
  timeRange: AdTimeRange,
  cascader: AdCascader,
}
const value = computed(() => props.value)
const update = (val) => {
  emit(UPDATE_MODELVALUE, val)
  emit('change', val)
}
</script>

<style lang="scss"></style>
