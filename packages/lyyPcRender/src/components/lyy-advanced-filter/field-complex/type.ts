// import { buildProps } from '../../utils/props'
// import type { OptionsType } from '../../hooks/use-options'
import type { ExtractPropTypes, PropType } from 'vue'

export type FileType = 'select' | 'input'

// import type { Action } from '../../hooks/useAction'

export interface CheckValueTypeItemType {
  searchKey: string // 'options' // 根据此key获取 item的options
  actionType: string //  'valFilter' // 根据此类型执行那种操作
  valueKey: string // 'type' // 根据此key 获取哪个值来判断
}

export interface CheckValueTypeI {
  selfItemConfig?: CheckValueTypeItemType
  targetItemConfig?: CheckValueTypeItemType
}

export interface FieldComplexItemPropsType {
  options?: any
  span?: number
  [key: string]: any // 其他 属性 根据ant-desgin-vue配置
}

export interface FieldComplexItemType {
  compId: string // 组件id
  compName: string // 组件名
  field?: string // 表单key
  prop: FieldComplexItemPropsType
  valueKey?: string
  selectOptions: IObject
}

export interface DataPropsType {
  gutter: number
  form?: object
  style?: object
  selectOptions: IObject
  selectFieldNames: {
    label?: 'label' | string
    value?: 'value' | string
  }
}

export const fieldComplexProps = {
  compId: String, // 组件id
  compName: String, // 组件名
  field: String, // 表单key
  formId: String,
  selectOptions: Object,
  advanProp: Object,
  prop: {
    type: Object as PropType<DataPropsType>,
    default: () => ({ gutter: 24 }),
  },
  childrens: {
    type: Array as PropType<FieldComplexItemType[]>,
  },
  value: {
    type: Array as PropType<any>,
    default: () => [],
  },
}

export type FieldComplexProps = ExtractPropTypes<typeof fieldComplexProps>
