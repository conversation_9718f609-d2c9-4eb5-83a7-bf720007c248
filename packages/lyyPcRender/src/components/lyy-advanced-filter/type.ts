import type { DefaultOptionType } from 'ant-design-vue/es/select'
export interface LyyAdvancedFilter extends IElement {
  compName: 'lyy-advanced-filter'
  prop: PropType
  modelValue?: ModelValueType
}
export type ModelValueType = object
export interface PropType {
  /**
   * 绑定的表单
   */
  formId?: string
  /**
   * 表单字段
   */
  field?: string
  typeConfiguration: {
    type?: 'modal' | 'box'
    option: {
      btnText?: string // 筛选按钮文本
      title?: string // 弹窗标题
      width?: string | number // 弹窗宽度
      okText?: string // 提交按钮文本
      cancelText?: string // 取消按钮文本
      closable?: boolean // 是否显示右上角关闭
      mask?: boolean // 是否显示遮罩层
      maskClosable?: boolean // 点击蒙层是否关闭
    }
  }

  /**
   * 条件组数量配置 默认1
   */
  groupMax?: number

  /**
   * 条件数量设置 默认3
   */
  conditionMax?: number

  /**
   * 条件组判断 与或非
   *
   */
  groupOptions?: groupOption[]

  /**
   * 字段选择列表
   */
  fieldOptions?: fieldOption[]

  /**
   * 默认选中字段
   */
  checkFieldValue?: string
  /**
   * 枚举的子数据
   */
  selectOptions?: IObject
  /**
   * 操作符数组
   */
  operatorOptions: operatorOption[]
  conditionOptions?: groupOption[] //条件与还是或选择列表
  selectFieldNames?: object
}
export interface groupOption {
  label: string
  value: string | number
}
export interface fieldOption {
  /**
   * 名字
   */
  label: string
  /**
   * 值
   */
  value: string
  /**
   * 字段类型
   */
  type?:
    | 'string'
    | 'number'
    | 'date'
    | 'datetime'
    | 'time'
    | 'select'
    | 'cascader'
    | 'boolean'
  /**
   * 下拉框的选项列表
   */
  optionList?: groupOption[]
}
export interface operatorOption extends groupOption {
  /**
   * 哪些类型包含当前 不传代码全部都包含这个
   */
  allow?: any
  /**
   * 是否显示第三个值绑定框 默认是
   */
  showData?: boolean
  /**
   * 是否显示范围组件
   */
  isRenge?: boolean
}
export interface AdFilterValueType {
  /**
   * 条件组
   */
  conditions: ConditionGroupType[] // 条件组
  /**
   * 组的条件 0-并且 1-或
   */
  groupCondition: number | string
}
export interface ConditionGroupType {
  /**
   * 满足条件 0 - 满足全部条件 1 - 满足任意条件
   */
  triggle_condition: number
  /**
   * 条件列表
   */
  condition: ConditionType[]
}
export interface ConditionType {
  left_type?: number // 字段类型 0：字段/指标 1：固定值 此组件都是字段 0
  left_field_name: string | undefined // left输入框的值
  operate?: number //操作符 0：等于 1：大于 2：小于 3：大于等于 4：小于等于 5：不等于 6：包含 7：不包含
  right_type?: number //字段类型 0：字段/指标 1：固定值
  right_field_name: string | undefined // 右边输入框的值
  left_field_type?:
    | 'string'
    | 'number'
    | 'date'
    | 'datetime'
    | 'time'
    | 'select'
    | 'cascader'
    | 'boolean' //选择的字段类型
}

// 字段类型=============
export const STRING = 'string'
export const SELECT = 'select'
export const BOOLEAN = 'boolean'
export const NUMBER = 'number'
export const DATE = 'date'
export const DATETIME = 'datetime'
export const TIME = 'time'
// 字段类型=============
