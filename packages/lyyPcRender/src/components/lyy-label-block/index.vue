<script setup lang="ts">
import { inject, ref, Ref, StyleValue, computed, toRefs } from 'vue'
import isNumber from 'lodash/isNumber'

import { IComponent } from '@/export'
import { PropType } from './type'

const props = defineProps<{
  prop: PropType
  style?: StyleValue
  childrens: IComponent[]
}>()
const { prop, childrens } = props

const formLayout = inject<Ref<string>>('layout', ref('horizontal'))

const {
  label,
  prefixIcon,
  labelWidth = 100,
  contentStyle,
  required,
  labelTextAlign = 'right',
} = toRefs(props.prop)

const defaultLabelWidth = isNumber(labelWidth?.value)
  ? `${labelWidth?.value}px`
  : labelWidth?.value

const isFormHorizontal = computed(() => formLayout.value === 'horizontal')

const textAlign = computed(() =>
  isFormHorizontal.value ? 'left' : labelTextAlign,
)
</script>

<template>
  <div
    class="lyy-label-block lyy-container"
    :class="`form-${formLayout}`"
    :style="style"
  >
    <div :style="{ width: defaultLabelWidth, textAlign }">
      <label class="lyy-label">
        {{ prop.label }}
        <template v-if="prefixIcon">
          <a-tooltip placement="topLeft" :title="prefixIcon.toolTip">
            <component
              :style="prefixIcon.style"
              :is="prefixIcon.icon"
            ></component>
          </a-tooltip>
        </template>
      </label>
    </div>

    <div class="lyy-block" :style="contentStyle">
      <pc-render-template :elements="childrens"></pc-render-template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.lyy-label-block {
  display: flex;
  align-items: center;

  &.form-horizontal {
    flex-direction: column;
    align-items: stretch;
  }
  .label-box{
    overflow-x: hiden;
  }
}
.lyy-label {
  flex-shrink: 0;
  text-align: right;
  &::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
  &::after {
    content: ':';
    position: relative;
    top: -0.5px;
    margin: 0 8px 0 2px;
  }
}
.lyy-block {
  //margin-bottom: 16px;
  flex: 1;
}
.svg-icon {
  margin-left: 2px;
  cursor: pointer;
  &:focus {
    outline: none;
  }
}
</style>
