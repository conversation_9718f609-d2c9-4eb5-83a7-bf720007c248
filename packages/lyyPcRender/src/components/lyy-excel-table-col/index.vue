<template>
  <vxe-column
    class-name="col-excel"
    :className="
      !(isOndisabled && (colSlot === 'edit' || editRender))
        ? 'editDisable col-excel'
        : 'editEnable col-excel'
    "
    :show-overflow="showOverflow"
    :title="title"
    :type="type"
    :width="width"
    :fixed="fixed"
    :align="align"
    :params="params"
    :sortable="sortable"
    :min-width="minWidth"
    :field="field"
    :exp="exp"
    :edit-render="resultEditRenderCol"
  >
    <template #header v-if="isEdit">
      <span
        class="lyy-component-prototype"
        :data="JSON.stringify(props.colConfig)"
        :parentId="parentCompId"
        :field="field"
        prototypeName="field"
      >
        <vxe-checkbox
          v-if="type === 'checkbox' && !field && !title"
          transfer
        ></vxe-checkbox>
        <span v-else>{{ title }}</span>
      </span>
    </template>
    <template #edit="{ row }" v-if="isOndisabled">
      <LyyPulldownContainer
        v-if="
          pullDown && Object.keys(pullDown).length > 0 && type === 'pullDown'
        "
        :xTable="xTable"
        :configPull="pullDown"
        :value="row[field]"
        :field="field"
        :updateData="updateData"
        :parentElement="parentElement"
        @clearRowContent="clearRowContent(row, field)"
        @action="handleAction"
        @click="selectClick"
      ></LyyPulldownContainer>
      <vxe-select
        v-model="row[field]"
        ref="xDown"
        transfer
        filterable
        v-else-if="type === 'select' && optionsCommon"
        :multiple="multiple"
        @change="selectChange"
        @click="selectClick"
        :allow-create="allowCreate"
      >
        <vxe-option
          v-for="item in optionsCommon ?? []"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
      <LyyExcelTableCascader
        v-else-if="type === 'cascader'"
        :option="cascaderOptionComputed"
        :value="row[field]"
        :options="optionsCommon"
        :field="field"
        @update:modelValue="handleUpdate($event, field, row)"
      ></LyyExcelTableCascader>
      <lyyExcelTableSubtable
        v-else-if="type === 'subtable'"
        :option="option"
        :value="row[field]"
        :datasource="row[subtableField]"
        :isNumber="isNumber"
        :field="field"
        :subtableField="subtableField"
        @update:modelValue="handleUpdate($event, field, row)"
      ></lyyExcelTableSubtable>
      <vxe-switch
        v-model="row[field]"
        v-else-if="type === 'switch'"
        :open-value="option?.checkedValue ?? true"
        :close-value="option?.unCheckedValue ?? false"
        transfer
      ></vxe-switch>
      <!-- <vxe-checkbox
        v-model="row[field]"
        v-else-if="type === 'checkbox'"
        :checked-value="option?.checkedValue ?? true"
        :unchecked-value="option?.unCheckedValue ?? false"
        transfer
      ></vxe-checkbox> -->
      <component
        :is="getCompName('lyy-file-uploader')"
        :modelValue="row[field]"
        v-else-if="type === 'file-uploader'"
        :prop="setProp('file-uploader')"
        :actions="uploaderAction"
        @update:modelValue="handleUpdate($event, field)"
        :comp-id="prop.compId"
        :id="prop.compId"
        :rowData="row"
      ></component>
      <component
        :is="getCompName('lyy-tree-select')"
        :modelValue="row[field]"
        v-else-if="type === 'tree-select'"
        :prop="setProp('tree-select')"
        :actions="treeSelectAction"
        @update:modelValue="handleUpdate($event, field)"
        :comp-id="prop.compId"
        :id="prop.compId"
      ></component>
      <vxe-input
        v-model="row[field]"
        :placeholder="editRender && editRender.placeholder"
        :type="type"
        :digits="decimalPoint"
        @blur="handleBlur($event, row)"
        transfer
        v-else
      ></vxe-input>
    </template>
    <template #edit="{ row }" v-else-if="exp">
      <span>{{ expFn(row, field) }}</span>
    </template>
    <template #edit="{ row }" v-else>
      <span>{{ pipeData(row[field], field, row) }}</span>
    </template>
    <template #default="{ row }" v-if="type === 'operation'">
      <div @click.capture="clickFn(row)" @capture="clickFn(row)">
        <table-operation
          :childrens="childrens"
          :isEdit="isEdit"
          :isPreview="isPreview"
          :rowData="row"
        ></table-operation>
      </div>
    </template>
    <template #default="{ row }" v-else-if="exp">
      <span>{{ expFn(row, field) }}</span>
    </template>
    <template #default="{ row }" v-else-if="type === 'subtable'">
      <a-popover v-if="row[field]">
        <template #content>
          <div class="subtable-default">
            <span
              class="subtable-item"
              v-for="(num, i) in row[subtableField]"
              :key="i"
            >
              {{ num }}
            </span>
          </div>
        </template>
        <div class="subtable-cont">{{ row[field] }}</div>
      </a-popover>
    </template>
    <template
      #default="{ row }"
      v-else-if="type === 'image' || type === 'file-uploader'"
    >
      <a-image
        class="img-table"
        v-if="row[field]"
        :src="row[field]"
        :alt="field"
        :style="imgStyle"
      />
    </template>
    <template #radio="{ row, checked }" v-else-if="type === 'radio'">
      <span
        class="custom-radio"
        :class="{ disabled: props.disabled }"
        @click.stop="setRadioRow(row)"
      >
        <i class="checked" v-if="checked"></i>
        <i class="vxe-icon-radio-unchecked" v-else></i>
      </span>
    </template>
    <template #default="{ row }" v-else-if="type === 'switch'">
      <vxe-switch
        :modelValue="row[field]"
        :open-value="option?.checkedValue ?? true"
        :close-value="option?.unCheckedValue ?? false"
        transfer
        v-if="type === 'switch'"
      ></vxe-switch>
      <!-- <vxe-checkbox
        :modelValue="row[field]"
        :checked-value="option?.checkedValue ?? true"
        :unchecked-value="option?.unCheckedValue ?? false"
        transfer
        v-if="type === 'checkbox'"
      ></vxe-checkbox> -->
    </template>
    <template #default="{ row, rowIndex }" v-else-if="type == 'seq'">
      <span :class="hasAddLine && !props.disabled ? 'seq' : ''">
        {{ row[field] ?? rowIndex + 1 }}
      </span>
      <PlusCircleFilled
        class="add-line-icon"
        @click="(e) => addLineClick(e, rowIndex)"
        v-if="hasAddLine && !props.disabled"
      />
    </template>
    <template #default="{ row }" v-else-if="rule">
      <a-tooltip
        v-if="!evaluateExp(row, rule.exp)"
        :trigger="rule.option?.tooltip ? 'hover' : 'null'"
      >
        <template #title>
          <span>{{ rule.option?.text }}</span>
        </template>
        <span :style="{ color: rule?.option?.color || 'red' }">
          {{ row[field] }}
        </span>
      </a-tooltip>
      <span v-else>{{ row[field] }}</span>
    </template>
    <!-- <template #default="{ row }" v-else-if="type == 'select'">
      <vxe-select
        v-model="row[field]"
        class-name="readonly"
        @click="selectFocus"
      >
        <vxe-option
          v-for="item in optionsCommon ?? []"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
      <close-circle-filled
        v-if="row[field] && !(disabled || props.disabled || !cellEdit(row))"
        class="clear_close"
        style="color: #c0c4cc"
        @click.stop="clearRowContent(row, field)"
      />
    </template> -->
    <template #default="{ row }" v-else-if="type === 'tree-select'">
      <div class="excel-table-tree-disable">
        <component
          :is="getCompName('lyy-tree-select')"
          :modelValue="row[field]"
          :prop="setProp('tree-select', '  ')"
          :actions="[]"
          @update:modelValue="handleTreeUpdate($event, field, row)"
          :comp-id="prop.compId"
          :id="prop.compId"
        ></component>
      </div>
      <close-circle-filled
        v-if="row[field] && !(disabled || props.disabled || !cellEdit(row))"
        class="clear_close"
        style="color: #c0c4cc"
        @click.stop="clearRowContent(row, field)"
      />
    </template>
    <template #default="{ row }" v-else>
      <span>{{ pipeData(row[field], field, row) }}</span>
      <close-circle-filled
        v-if="row[field] && !(disabled || props.disabled || !cellEdit(row))"
        class="clear_close"
        style="color: #c0c4cc"
        @click.stop="clearRowContent(row, field)"
      />
    </template>
  </vxe-column>
</template>

<script lang="ts" setup>
import { useStore } from '@leyaoyao/libs/store'
import { useAction, useRequest, _useOnValue, formula } from '@leyaoyao/libs'
import {
  defineComponent,
  ref,
  computed,
  toRefs,
  onMounted,
  watch,
  nextTick,
} from 'vue'
import LyyExcelTableCascader from '../lyy-excel-table-cascader/index.vue'
import LyyPulldownContainer from '../lyy-pulldown-container/index.vue'
import lyyExcelTableSubtable from '../lyy-excel-table-subtable/index.vue'
import { VXETable } from 'vxe-table'
import { PlusCircleOutlined, PlusCircleFilled } from '@ant-design/icons-vue'
import formatNumber, { numToFixed } from '@leyaoyao/libs/utils/format-number'
import _ from 'lodash'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import version from '../../../config/version.mjs'
import tableOperation from './table-operation.vue'
import {datapipe, joinPipe} from '@leyaoyao/libs/utils/pipe'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import { MOUNTED } from '../../constants/event-type'
defineComponent({
  name: 'lyy-excel-table-col',
})
const emit = defineEmits([UPDATE_MODELVALUE, ACTION])
const props = defineProps<{
  colConfig?: any
  xTable?: any
  disabled?: boolean
  isEdit?: boolean
  isPreview?: boolean
  isClickEdit?: boolean
  parentElement?: IElement
  parentCompId: string
  updateData?: any
  logicDelete?: any
}>()
const { parentElement, updateData, logicDelete } = props
const { colConfig } = toRefs(props)
const {
  type = ref('text'),
  field,
  subtableField,
  isNumber,
  title,
  width,
  minWidth,
  fixed,
  align,
  showOverflow = ref('ellipsis'),
  sortable,
  filters,
  params,
  disabled = ref(false),
  editRender,
  formatterOption, // 非编辑状态下的映射配置
  pullDown,
  imgStyle,
  colSlot,
  multiple,
  allowCreate = ref(false),
  condition,
  options,
  defaultOptions,
  forbid = ref({}),
  exp,
  pipes = ref([]),
  hasAddLine,
  decimalPoint, // 保存小数点位数
  isThousands, // 是否展示千分位
  option,
  rule,
  isEmptyShow = ref(false),
  cascaderOption = ref({}),
  prop = ref({}),
  actions = ref([]),
  fieldNames = ref({}), //字段映射
  childrens = ref([]),
  isSummary = ref(false),//是否开启合计
} = toRefs(colConfig.value)
const store = useStore()
const currentPageName = store.getRoute().name
const getCompName = (name) => {
  let aliasName = window.__aliasName__ ?? ''
  if (aliasName) {
    aliasName += version.verVue2
  }
  return name.includes(aliasName) ? name : name + aliasName
}
const xDown = ref(null)
const mapOptions = new Map()
const handleAction = ({ event, value, data }) => {
  if (event === 'blur') {
    handleBlur({ value }, undefined)
  } else if (event === 'select') {
    emit(ACTION, 'select')
  } else if (event === 'focus') {
    // 下拉选择点击聚焦说明点击了表格处于编辑状态
    emit(ACTION, 'edited')
  }
}
const resultEditRenderCol = computed(() => {
  if (props.isEdit) {
    return editRender?.value || {}
  }
  // if (editRender?.value && editRender.value.autofocus) {
  //   editRender.value.autofocus = 'true'
  // }
  return type.value === 'operation' || disabled.value ? null : editRender
})
const selectFocus = () => {
  setTimeout(() => {
    xDown.value?.showPanel()
  })
}
const cascaderOptionComputed = computed(() => {
  const {
    showCheckedStrategy = 'SHOW_PARENT',
    emitPath = false,
    valuefield = '',
  } = cascaderOption.value
  return {
    showCheckedStrategy: showCheckedStrategy,
    emitPath: emitPath,
    valuefield: valuefield,
  }
})
// 设置组件属性
const setProp = (type) => {
  let defaultProp = {}
  if (type == 'file-uploader') {
    defaultProp = {
      auto: false,
      showTip: false,
      uploadType: 'excel',
    }
  } else if (type == 'tree-select') {
    defaultProp = {
      options: optionsCommon.value,
    }
  }
  return Object.assign(defaultProp, prop.value)
}
// 是否禁用 forbid
const isEditFn = (forbid) => {
  return () => {
    const { exp } = forbid.value || {}
    let result = true
    if ((typeof exp === 'boolean' && !exp) || disabled.value) return false
    if (exp) {
      const proxyDataStatic = proxyData.getProxyData()
      try {
        const $table = props?.xTable
        const row = $table?.getCurrentRecord() || {}
        result = formula(exp)({ ...proxyDataStatic, ...row })
      } catch {
        result = false
      }
    }
    return result
  }
}
const setRadioRow = (row) => {
  if (props.disabled) return
  const $table = props?.xTable
  if (row && row[field.value]) {
    row[field.value] = false
    $table?.clearRadioRow()
  } else {
    const currRow = $table.getRadioRecord()
    if (currRow) {
      currRow[field.value] = false
    }
    row[field.value] = true
    $table.setRadioRow(row)
  }
}
const handleTreeUpdate = (event, field, row) => {
  if (field) {
    row[field] = event
  }
}
const handleUpdate = async (data, fieldName, row?) => {
  const item = {}
  if (type.value == 'cascader') {
    if (fieldName && row) row[fieldName] = data[fieldName]
    props.updateData(data, 'add')
  }
  if (type.value == 'subtable') {
    if (fieldName && row) row[fieldName] = data[fieldName]
    props.updateData(data, 'add')
  } else if (type.value === 'file-uploader') {
    if (prop.value && prop.value.fieldNames) {
      const { name = 'name', url = 'url' } = prop.value.fieldNames
      item[fieldName] = data[url] || data
      props.updateData(item, 'add')
    }
  } else {
    if (data && data.length > 0) {
      item[fieldName] = _.isString(data) ? data : data[0]
      props.updateData(item, 'add')
    } else {
      item[fieldName] = null
      props.updateData(item, 'add')
    }
  }
}
const submit = async (fileList) => {
  if (fileList?.length === 0) {
    // 删除情况下也需要更新form
    return []
  }
  // 只提交没有上传过的图片
  const toUploadFiles = fileList?.filter((item) => item.status !== 'done')
  if (!toUploadFiles || toUploadFiles.length === 0) {
    return []
  }
  const useFetchArr = []

  for (const file of toUploadFiles) {
    const formData = new FormData()
    const originFile = file instanceof File ? file : file.originFileObj
    formData.append(prop.value.fieldName || 'file', originFile as any)
    if (prop.value.data) {
      for (const [key, value] of Object.entries(prop.value.data)) {
        formData.append(key, value)
      }
    }
    const submitFetch = _.cloneDeep(prop.value.fetch)
    submitFetch.data = formData
    useFetchArr.push(useRequest(submitFetch))
  }

  return new Promise((resolve, reject) => {
    Promise.all(useFetchArr)
      .then((res) => {
        resolve(res)
      })
      .catch((error) => {
        reject(error)
      })
  })
}
// 计算表达式的值
const evaluateExp = (row, exp) => {
  const result = formula(exp)({ ...store.state[currentPageName], ...row })
  return result
}
const cascaderOptions = ref([])
// 预请求
const getCascaderOptions = async () => {
  if (cascaderOption.value.fetch) {
    const res = await useRequest(cascaderOption.value.fetch)
    cascaderOptions.value = res
  } else {
    cascaderOptions.value = cascaderOption.value.optionList
  }
}
if (cascaderOption.value) {
  getCascaderOptions()
}
// 是否能编辑
const isOndisabled = computed(isEditFn(forbid))
// 某个单元格表达式执行
const cellEdit = (row) => {
  const { exp } = forbid.value || {}
  let result = true
  if (exp) {
    try {
      result = formula(exp)({ ...row })
    } catch {
      result = false
    }
  }
  return result
}
// 树选择事件
const treeSelectAction = actions.value.filter(
  (item) => !(item.event === 'mounted' || item.event === 'beforeMount'),
)
// 上传时事件
const uploaderAction = actions.value.filter((item) => item.event === 'upload')
// 下拉改变值
const selectChange = async ({ value }) => {
  const selectChangeAction = actions.value.filter((v) => v.event === 'update')
  selectChangeAction.length > 0 && useAction(selectChangeAction, parentElement)
  if (colConfig.value?.isSelectChangeUpdate) {
    emit(ACTION, 'updateForm', undefined)
  }
}
// 格式化
const pipeData = (val, field, row) => {
  let value = val
  if (pipes.value.length > 0) {
    return datapipe(value, pipes.value)
  } else if (
    type.value === 'select' ||
    type.value == 'cascader' ||
    type.value == 'tree-select'
  ) {
    let res = Array.isArray(value) ? [] : value
    if (Array.isArray(value)) {
      for (const v of value) {
        const labelValue = mapOptions.get(v)
        if (labelValue) {
          res.push(labelValue)
        } else {
          const label = fieldNames.value?.label
          const value = fieldNames.value?.value
          res.push({
            [label]: v,
            [value]: v,
          })
        }
      }
    } else {
      const labelValue = mapOptions.get(value)
      res = labelValue ? labelValue['label'] : ''
    }
    return Array.isArray(res) && res.length > 0 ? joinPipe(res, {
      separator: ',',
      filterField: fieldNames.value?.label || 'label'
    }) : res
  } else if (value && !isNaN(Number(value))) {
    if (decimalPoint?.value || decimalPoint?.value === 0) {
      value = numToFixed(Number(value), decimalPoint.value)
      if (row && field) {
        row[field] = value
      }
    }
    if (isThousands?.value) {
      value = formatNumber(value, { format: 'thousands' })
    }
  }
  return value
}
// 1. 添加防抖控制器
const expDebounceMap = new Map()

const expFn = (row, field) => {
  const currentRow = props.xTable?.getCurrentRecord() || {}
  // 如果不需要计算表达式，直接返回当前值
  if (
    exp.value &&
    (!props.isClickEdit || currentRow._X_ROW_KEY === row._X_ROW_KEY)
  ) {
    try {
      const proxyDataStatic = proxyData.getProxyData()
      const scope = { ...proxyDataStatic, ...row }
      let result = _useOnValue(scope, exp?.value)
      // if (!result && editRender?.value) {
      //   return row[field]
      // }
      if (!result && !isEmptyShow.value) {
        if (field) row[field] = undefined
        return undefined
      } else if (!result && isEmptyShow.value) {
        return result
      }
      if (decimalPoint?.value || decimalPoint?.value === 0) {
        result = numToFixed(Number(result), decimalPoint.value)
      }
      if (field) row[field] = result
      return isThousands?.value
        ? formatNumber(result || row[field], { format: 'thousands' })
        : result || row[field]
    } catch {
      // console.log(e)
    }
  } else {
    let result = row[field] ?? ''
    if (decimalPoint?.value || decimalPoint?.value === 0) {
      result = numToFixed(Number(result), decimalPoint.value)
    }
    return isThousands?.value
      ? formatNumber(result || row[field], { format: 'thousands' })
      : result || row[field]
  }
}
const clickFn = (row) => {
  emit(UPDATE_MODELVALUE, {
    currentRow: row,
  })
}
const removeEvent = async (row: any, btnParams: any) => {
  const type = await VXETable.modal.confirm(btnParams.prop?.confirmTxt)
  if (type === 'confirm') {
    if (!updateData) {
      const $table = props?.xTable
      $table.remove(row)
    } else if (logicDelete && row['_reallyData']) {
      updateData && updateData(row, 'logicDelete')
    } else {
      updateData && updateData(row, 'del')
    }
  }
}
const addLineClick = (e, rowIndex) => {
  // 1. 使用防抖，避免频繁触发
  if (addLineClick.timer) {
    clearTimeout(addLineClick.timer)
  }

  addLineClick.timer = setTimeout(() => {
    // 2. 批量更新
    window.requestAnimationFrame(() => {
      if (updateData) {
        // 3. 预处理新行数据
        const newRow = {}  // 可以预先准备新行的默认值
        updateData(newRow, 'addLine', rowIndex)
      }
    })
  }, 100)
}
const transferSelectOption = (optios) => {
  const select = []
  const formatter = []
  for (const v of optios) {
    const temp = {
      label: v[fieldNames.value['label']],
      value: v[fieldNames.value['value']],
    }
    select.push(temp)
    formatter.push(temp)
    mapOptions.set(temp.value, temp)
    if (v[fieldNames.value['children']]) {
      temp['children'] = transferSelectOption(v[fieldNames.value['children']])
    }
  }
  return select
}
const optionsCommon = computed(() => {
  if (options.value.length > 0 && fieldNames.value) {
    return transferSelectOption(options.value)
  } else if (defaultOptions?.value.length > 0 && fieldNames.value) {
    return transferSelectOption(defaultOptions.value)
  }
  return transferSelectOption(props.colConfig.options)
})
watch(
  () => [props.colConfig.options, props.colConfig.defaultOptions],
  (newVal) => {
    const [newVal1, newVal2] = newVal
    if (newVal1 && newVal1.length > 0) {
      transferSelectOption(newVal1)
    } else if (newVal2 && newVal2.length > 0) {
      transferSelectOption(newVal2)
    }
  },
  {
    immediate: true,
    flush: 'post',
  },
)
// 预请求
const selectPreFetchFn = async () => {
  const selectMountedAction = actions.value.filter((v) => v.event === 'mounted')
  selectMountedAction.length > 0 &&
    useAction(selectMountedAction, parentElement)
}
selectPreFetchFn()
const selectClick = () => {
  const clickActions = actions.value.filter((v) => v.event === 'click')
  clickActions.length > 0 && useAction(clickActions, parentElement)
  if (colConfig.value?.loadDataEvent === 'click') {
    selectPreFetchFn()
    if (cascaderOption.value) {
      getCascaderOptions()
    }
  }
}
const handleBlur = ({ value }, row) => {
  const $table = props?.xTable
  if (!row) {
    row = $table?.getCurrentRecord() || {}
    if (field.value && value !== undefined) {
      row[field.value] = value
    }
  }
  if (isSummary.value) {
    $table?.updateFooter()
    emit(ACTION, 'saveSummaryData')
  }
  const blurEvent = actions.value.filter((v) => v.event === 'blur')
  blurEvent.length > 0 && useAction(blurEvent, parentElement)
}
// 清除单元的内容
const clearRowContent = (row, field) => {
  const $table = props?.xTable
  row[field] = ''
  const currentRow = $table?.getCurrentRecord()
  if (currentRow !== row) {
    $table?.setCurrentRow(row)
    emit(UPDATE_MODELVALUE, {
      currentRow: row,
    })
  }
  emit(ACTION, 'updateForm', undefined)
  const changeAction = actions.value.filter((v) => v.event === 'update')
  changeAction.length > 0 && useAction(changeAction, parentElement)
}
onMounted(() => {
  const mountedAction = actions.value.filter((item) => item.event === 'mounted')
  mountedAction.length > 0 && useAction(mountedAction, parentElement)
})
</script>
<style lang="scss">
.col-excel {
  position: relative;
  .excel-table-tree-disable {
    height: 30px;
    overflow-y: hidden;
    .ant-select-arrow {
      display: none !important;
    }
    .ant-select-selector {
      background-color: #fff !important;
      border: none !important;
      .ant-select-selection-item {
        color: #606266 !important;
      }
    }
  }
  &:hover {
    .clear_close {
      display: inline-block;
    }
  }
  .readonly {
    .vxe-input--suffix {
      display: none;
    }
  }
  .clear_close {
    display: none;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 8px;
  }
  .custom-radio {
    position: relative;
    width: 20px;
    height: 20px;
    cursor: pointer;
    i {
      //border-radius: 50%;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 16px;
      height: 16px;
      margin: auto;
      border: 1px solid #bbb;
      border-radius: 100%;
      &:after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 6px;
        height: 6px;
        margin: auto;
        border-radius: 100%;
        background: #fff;
        -webkit-transform: scale(0);
        transform: scale(0);
        -webkit-transition: all 0.3s;
        transition: all 0.3s;
      }
    }
    .checked {
      border-color: #1db0fc;
      background: #1db0fc;
      &:after {
        -webkit-transform: scale(1);
        transform: scale(1);
      }
    }
    &.disabled {
      .checked {
        border-color: #bbbbbb;
        background: #bbbbbb;
      }
    }
    .vxe-icon-radio-unchecked {
      border: 1px solid #dcdfe6;
    }
  }
  .lyy-file-uploader .ant-upload-picture-card-wrapper {
    .ant-upload-list-picture-card-container,
    .ant-upload-list-item,
    .ant-upload-select-picture-card,
    .ant-upload,
    .ant-upload-span,
    .ant-upload-list-item-info {
      width: 30px;
      height: 30px;
      padding: 0;
      border-radius: 2px;
      transition: none !important;
      margin: 0 auto;
    }
    .ant-upload-list-picture-card {
      //height: 38px;
      .ant-upload-list-item-info::before {
        transition: none !important;
      }
    }
    .ant-upload-list-item-image {
      border-radius: 2px;
    }
    div.ant-upload-list-item-thumbnail {
      line-height: 16px;
      font-size: 12px;
      padding-top: 4px;
    }
    .ant-upload-list-item-actions {
      padding-left: 5px;
      a,
      button {
        span {
          padding: 0;
          margin: 0 1px;
          font-size: 13px;
        }
      }
    }
  }
  .lyy-file-uploader-tip,
  .ant-upload-text {
    display: none;
  }
  .ant-form-item-label {
    width: 0 !important;
  }
}
</style>
<style lang="scss">
.img-table {
  display: block;
  height: 32px;
  width: 32px;
  object-fit: cover;
  border-radius: 4px;
  margin: 2px;
}
.lyy-colum {
  .vxe-input--inner {
    border: none;
  }
}
.subtable-default {
  padding: 10px;
  &::after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .subtable-item {
    float: left;
    height: 30px;
    line-height: 30px;
    width: 80px;
    border: 1px solid #eee;
    border-radius: 2px;
    text-align: center;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
.subtable-cont {
  text-indent: 6px;
}
.vxe-cell {
  .ant-form-item {
    margin-bottom: 0 !important;
    height: 30px;
    .lyy-file-uploader {
      width: 100%;
      background: #fff;
      height: 30px;
      > span {
        display: block;
        width: 100%;
      }
      span {
        padding: 0 !important;
      }
      .upload-box {
        width: 100%;
        display: block;
      }
    }
  }
}
.vxe-body--column {
  &:hover {
    cursor: pointer;
    .seq {
      display: none;
    }
    .add-line-icon {
      display: block;
    }
  }
  .add-line-icon {
    display: none;
    color: #42a4fd;
    font-size: 18px;
  }
}
</style>
