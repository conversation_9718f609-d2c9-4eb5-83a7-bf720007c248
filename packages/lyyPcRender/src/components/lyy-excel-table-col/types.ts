import { isNumber } from 'lodash';
type RuleObj = {
  exp: Array<string>
  option?: {
    tooltip?: boolean
    text?: string
    color?: string
  }
}
export interface columnConfig {
  type?:
    | string
    | 'pullDown'
    | 'select'
    | 'cascader'
    | 'switch'
    | 'checkbox'
    | 'file-uploader'
    | 'operation'
    | 'text'
    | 'search'
    | 'number'
    | 'float'
    | 'password'
    | 'date'
    | 'time'
    | 'datetime'
    | 'week'
    | 'month'
    | 'year'
    | 'image'
  colType?: string
  field?: string
  // 子表字段
  subtableField?: string
  //是否是数字  暂时只用于子表配置
  isNumber?:boolean
  title?: string
  width?: number | string
  minWidth?: number | string
  fixed?: string
  align?: string
  showOverflow?: string
  className?: string
  sortable?: boolean
  isImg?: boolean
  filters?: any[]
  params?: any
  editRender?: any
  formatterOption?: any
  xTable?: any
  pullDown?: any
  updateData?: any
  imgStyle?: any
  colSlot?: string
  multiple?: boolean
  disabledEdit?: boolean | string
  selectList?: any
  exp?: string
  expDataSourceKey?: string
  parentCompId?: string
  buttonList?: [any]
  /*
   * 是否在序号列添加新增行操作
   */
  hasAddLine?: boolean
  /**
   * 单元格校验标红
   */
  rule: RuleObj
}
