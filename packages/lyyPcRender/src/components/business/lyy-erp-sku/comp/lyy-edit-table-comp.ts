export const lyyEditTableComp: any = {
  compName: 'lyy-edit-table',
  prop: {
    field: '',
    label: '',
    labelCol: {
      style: {
        width: '100px',
      },
    },
    rules: [],
    columns: [],
    showAddRow: true,
    showInsertForward: true,
    showInsertBackwards: true,
    rowInsertForwardText: '向上添加',
    rowInsertBackwardsText: '向下添加',
    maxRows: '',
    datasource: [
      {
        name: '假数据',
      },
    ],
  },
  style: {},
  modelValue: {},
  actions: [],
  childrens: [],
}
