<script lang="ts" setup>
import { defineComponent, computed } from 'vue'
import { UPDATE } from '../../../constants/event-type'
import { ACTION, UPDATE_MODELVALUE } from '../../../constants/action-type'

defineComponent({
  name: 'lyy-charter',
})

const props = defineProps<{
  compId: string
  prop: any
  modelValue: any
}>()

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const [f1, f2, f3] = props.prop.fields ?? []
const [l1, l2, l3] = props.prop.label ?? []
const [r1, r2, r3] = props.prop.rules ?? []
const value = computed(() => {
  const [v1, v2, v3] = props.modelValue || []
  return { [f1]: v1, [f2]: v2, [f3]: v3 }
})

const handleChange = () => {
  const v1 = value.value[f1]
  const v2 = value.value[f2]
  const v3 = value.value[f3]
  emit(UPDATE_MODELVALUE, [v1, v2, v3])
  emit(ACTION, { event: UPDATE })
}

const max = computed(() => {
  const type = value.value[f1]
  if (type === 'year') return 20
  if (type === 'month') return 240
  if (type === 'week') return 1040
  return 20
})
</script>

<template>
  <div class="charter">
    <a-form-item
      :name="f1"
      :rules="r1"
      :validate-trigger="prop.validateTrigger"
    >
      <template #label>
        <span>{{ l1 }}</span>
        <a-popover
          placement="topLeft"
          arrow-point-at-center
          class="icon-popover"
        >
          <template #content>
            <p>分期上限20年</p>
            <p>按周，最多可填1040期</p>
            <p>按月，最多可填240期</p>
            <p>按年，最多可填20期</p>
          </template>
          <question-circle-outlined />
        </a-popover>
      </template>

      <a-radio-group
        v-model:value="value[f1]"
        button-style="solid"
        @change="handleChange"
      >
        <a-radio-button value="week">按周</a-radio-button>
        <a-radio-button value="month">按月</a-radio-button>
        <a-radio-button value="year">按年</a-radio-button>
      </a-radio-group>
    </a-form-item>

    <a-form-item
      class="period"
      :name="f2"
      :label="l2"
      :rules="r2"
      :validate-trigger="prop.validateTrigger"
    >
      <a-input-number
        v-model:value="value[f2]"
        :max="max"
        :controls="false"
        :precision="0"
        :style="{ width: '70px' }"
        @input="handleChange"
      >
        <template #suffix>
          <span style="color: #ccc">期</span>
        </template>
      </a-input-number>
    </a-form-item>

    <a-form-item
      class="rate"
      :name="f3"
      :label="l3"
      :rules="r3"
      :validate-trigger="prop.validateTrigger"
      :style="{ width: '60px' }"
    >
      <a-input
        v-model:value="value[f3]"
        :style="{ width: '90px' }"
        @input="handleChange"
      >
        <template #suffix>
          <span style="color: #ccc">%</span>
        </template>
      </a-input>
    </a-form-item>
  </div>
</template>

<style lang="scss" scoped>
.charter {
  display: flex;

  .icon-popover {
    margin-left: 2px;
  }
}

.period {
  ::v-deep .ant-form-item-label {
    width: 115px !important;
  }
}

.period,
.rate {
  ::v-deep .ant-form-item-label {
    &
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      content: '';
    }
  }
}

.rate {
  ::v-deep .ant-form-item-explain-error {
    display: block;
    width: 90px;
  }
}
</style>
