import { PropType as iconPropType } from '../../lyy-icon/type'
import { IElement } from '../../../global'

export type TagRecordsItem = {
  /**
   * tag 颜色
   */
  color?: string

  /*
   * 分类名
   * */
  name?: string

  /**
   * 父id
   */
  parentId?: number | null

  /*
   * 序号值
   * */
  serialNum?: number | null

  /*
   * 日期
   * */
  date?: string
}

export interface ModelValueType extends TagRecordsItem {
  [x: string]: any

  children?: TagRecordsItem[]
}

export interface PropType {
  /**
   * 绑定的表单
   */
  formId?: string
  /**
   * 字段
   */
  field: string
  /**
   * 值的深层路径
   */
  path?: string | string[]

  /**
   * tag 颜色
   */
  color?: string

  /**
   * tag 图标
   */
  icon?: string | iconPropType

  /**
   * 标签是否可以关闭
   */
  closable?: boolean
  /**
   * 默认值
   */
  defaultValue?: ModelValueType[]
}

export interface LyyCrmTag extends IElement {
  compName: 'lyy-crm-tag'
  modelValue?: ModelValueType
  prop?: PropType
}
