<template>
  <div class="lyy-component" :style="style">
    <a-form-item
      :name="field"
      :rules="rules"
      :colon="prop.colon"
      :labelCol="labelCol"
      :style="style"
    >
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
        <lyy-icon v-if="icon" :prop="icon"></lyy-icon>
      </template>
      <a-tree-select
        v-model:value="value"
        :disabled="disabled"
        :tree-data="options"
        :size="prop.size"
        :placeholder="prop.placeholder || `请选择${prop.label || ''}`"
        :allow-clear="allowClear"
        :showSearch="showSearch"
        :multiple="prop.multiple"
        :treeCheckable="prop.treeCheckable"
        :getPopupContainer="
          prop.isMountToBody === undefined || prop.isMountToBody === true
            ? null
            : (triggerNode) => {
                return triggerNode.parentNode
              }
        "
        @click="handleClick"
        @select="handleClickSelect"
        :fieldNames="fieldNames"
        :filterTreeNode="prop.filterTreeNode"
        :treeNodeFilterProp="
          prop.treeNodeFilterProp ? prop.treeNodeFilterProp : fieldNames.label
        "
      ></a-tree-select>
    </a-form-item>
  </div>
</template>
<script setup lang="ts">
import {
  computed,
  toRefs,
  onBeforeMount,
  onMounted,
  StyleValue,
  watch,
  onUnmounted,
} from 'vue'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import {
  BEFORE_MOUNT,
  MOUNTED,
  UPDATE,
  CLICK,
} from '../../constants/event-type'
import { ModelValueType, PropType } from './type'
import LyyIcon from '../lyy-icon/index.vue'
import { useDep, removeDep } from '@leyaoyao/libs'
import { useStore } from '@leyaoyao/libs/store'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import { dynamicRules, onRules } from '@leyaoyao/libs/hooks/use-rules'
import _ from 'lodash'
import { useFormState } from '../../hooks'

const props = defineProps<{
  modelValue?: ModelValueType
  compId: string
  prop: PropType
  style: StyleValue
}>()

const store = useStore()

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const { disabled, updateFormState } = useFormState(props, true)

onBeforeMount(() => {
  emit(ACTION, { event: BEFORE_MOUNT })
})

onMounted(() => {
  emit(ACTION, { event: MOUNTED })
})

let lock = false
const value = computed({
  get: () => {
    if (!lock && props.prop.immediate && props.modelValue !== undefined) {
      // 在需要默认赋值触发时只触发一次
      emit(ACTION, { event: UPDATE })
      lock = true
    }
    return (
      props.modelValue ??
      (props.prop.multiple || props.prop.treeCheckable ? [] : undefined)
    )
  },
  set: (newValue) => {
    updateFormState(newValue)
    emit(UPDATE_MODELVALUE, newValue)
    emit(ACTION, { event: UPDATE })
  },
})
const handleClickSelect = (value, node) => {
  store.setValue(props.compId, node, 'prop.selectedOption')
  proxyData.setProxyData(`${props.compId}.prop.selectedOption`, node)
  emit(ACTION, { event: UPDATE })
}
const rules = computed(() => {
  const rules = props.prop.rules || []
  let dynamicRulesSet = []
  let onrulesSet = []
  if (props.prop?.dynamicRules) {
    dynamicRulesSet = dynamicRules(props.prop, props.prop?.dynamicRules)
  }
  if (props.prop?.onrules) {
    onrulesSet = onRules(props.prop?.onrules)
  }
  const res = [...rules, ...dynamicRulesSet, ...onrulesSet]
  return res
})
const {
  field,
  allowClear = true,
  icon,
  // disabled,
  showSearch = false,
} = toRefs(props.prop)
const fieldNames = Object.assign(
  {
    label: 'label',
    value: 'value',
    children: 'children',
  },
  props.prop?.fieldNames ?? {},
)

const options = computed(() => {
  const childrenKey = fieldNames?.value?.children || 'children'
  return formattedTree(
    childrenKey,
    props.prop.selectOnlyLeafNodes
      ? initOptions(props.prop.options ?? [])
      : props.prop.options ?? [],
  )
})
const formattedTree = (key, arr) => {
  const data = _.cloneDeep(arr)
  for (const e of data) {
    if (e[key] && !_.isArray(e[key])) {
      e[key] = null
    }
  }
  return data
}
const initOptions = (arr) => {
  const list = _.cloneDeep(arr)
  const children = fieldNames?.value?.children || 'children'
  for (const e of list) {
    if (e[children] && e[children].length > 0) {
      e.selectable = false
      e.disabled = true
      e[children] = initOptions(e[children])
    }
  }
  return list
}
const labelCol = computed(() => {
  return Object.keys(props.prop.labelCol?.style ?? []).length > 0
    ? props.prop.labelCol
    : null
})

function handleClick(e: MouseEvent) {
  emit(ACTION, { event: CLICK })
  e.stopPropagation()
}
const setDataBaseFn = (newValue) => {
  const option = options.value.find(
    (option) => option[fieldNames.value] == newValue,
  )
  store.setValue(props.compId, option, 'prop.selectedOption')
  proxyData.setProxyData(`${props.compId}.prop.selectedOption`, option)
  updateFormState(newValue)
  emit(UPDATE_MODELVALUE, newValue)
  emit(ACTION, { event: UPDATE })
}
// 清空下拉选项
const clearOptions = () => {
  if (props.prop.options) {
    props.prop.options.length = 0
  }
  options.value.length = 0
}
// 设置下拉选项
const setOptions = (data = []) => {
  if (props.prop.options) {
    props.prop.options.length = 0
    props.prop.options.push(...data)
  } else {
    options.value.length = 0
    options.value.push(...data)
  }
}
// 清空
const clear = () => {
  setDataBaseFn(['multiple', 'tags'].includes(mode.value) ? [] : undefined)
}
// 重置
const reset = () => {
  setDataBaseFn(['multiple', 'tags'].includes(mode.value) ? [] : undefined)
}
// 赋值
const setData = (data) => {
  setDataBaseFn(data)
}
const actionsSet = {
  clear,
  reset,
  setData,
  clearOptions,
  setOptions,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>
