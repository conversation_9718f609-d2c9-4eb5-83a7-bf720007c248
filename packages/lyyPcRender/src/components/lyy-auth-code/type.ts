import type { Rule } from 'ant-design-vue/es/form'
import { PropType as iconPropType } from '../lyy-icon/type'

export type ModelValueType = string

export interface LyyAuthCode extends IElement {
  compName: 'lyy-auth-code'
  modelValue?: ModelValueType
  prop?: PropType
}

export interface PropType {
  /**
   * 绑定的表单
   */
  formId?: string

  /**
   * 绑定的表单字段
   */
  field: string
  /**
   * 获取验证码的字段
   */
  linkField: string
  /**
   * 标签名称
   */
  label?: string

  /**
   * 验证码类型
   *
   * 默认值：'phone'
   *
   * 可选值：'email'
   */
  type?: 'phone' | 'email'

  /**
   * 校验规则
   */
  rules?: Rule

  /**
   * 提示占位符
   */
  placeholder?: string

  /**
   * 是否显示清除按钮
   */
  allowClear?: boolean

  /**
   * 是否禁用，默认 false
   */
  disabled?: boolean

  /**
   * 功能与 label 一样，但是它们互斥
   */
  slotLabel?: string

  /**
   * icon
   */
  icon?: string | iconPropType

  /**
   * 重新获取验证码倒计时（单位：秒）
   *
   * 默认值：60
   */
  seconds?: number

  /**
   * 输入框样式
   */
  inputStyle?: {
    width?: string
  }

  /**
   * 按钮样式
   */
  buttonStyle?: {
    width?: string
  }

  /**
   * 显示输入最大字数
   */
  showCount?: boolean

  /**
   * 最大长度
   */
  maxlength?: number
  /**
   * 正则
   */
  pattern?: string | RegExp
}
