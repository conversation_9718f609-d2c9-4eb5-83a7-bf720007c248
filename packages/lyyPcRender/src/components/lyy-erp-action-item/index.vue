<script setup lang="ts">
import { ACTION } from '../../constants/action-type'
import { CLICK } from '../../constants/event-type'
import { DownOutlined } from '@ant-design/icons-vue'
import { toRefs, computed, ref, inject, Ref } from 'vue'
import { formula, usePayload } from '@leyaoyao/libs'
import LyyIcon from '../lyy-icon/index.vue'
import _ from 'lodash'
import { PropType } from './type'
const props = defineProps<{
  prop: PropType
  isPreview?: boolean
}>()
const emit = defineEmits([ACTION])

const isEdit = inject<Ref<boolean>>('isEdit')

const authCodeList = ref([])
const getCurrentPageName = function () {
  const arr = location.hash.replace('#').split('/')
  return arr[arr.length - 1].split('?')[0]
}
try {
  authCodeList.value =
    JSON.parse(sessionStorage.getItem('service-authCodeList'))[
      getCurrentPageName()
    ]?.buttonAuthCode || []
} catch {
  authCodeList.value = []
}
const disabled = computed(() => props.prop.disabled)
const handleClick = _.debounce((e: MouseEvent) => {
  if (disabled.value) {
    return
  }
  emit(ACTION, { event: CLICK })
  e.stopPropagation()
}, 300)
// 下拉菜单点击
const handleMenuClick = (e) => {
  emit(ACTION, { event: 'actionItem' + e.key })
}
const dropdown = computed(() => {
  if (props?.prop?.dropdown) {
    const temp = props.prop.dropdown?.map((item) => {
      const { forbid } = item
      if (forbid) {
        const { payloads, exp } = forbid || {}
        const payload = usePayload(payloads)
        item.disabled = formula(exp)(payload)
      }
      return item
    })
    return temp
  } else {
    return []
  }
})
const { text, icon } = toRefs(props.prop)
</script>

<template>
  <div
    :class="`lyy-component lyy-erp-action-item ${disabled ? 'disabled' : ''}`"
    v-if="
      isEdit ||
      isPreview ||
      !props.prop.authCode ||
      (props.prop.authCode && authCodeList.includes(props.prop.authCode))
    "
  >
    <div
      class="action-item"
      v-if="!dropdown || dropdown.length === 0"
      v-bind="prop"
      @click="handleClick"
    >
      <lyy-icon
        class="action-icon"
        v-if="prop.icon"
        :prop="prop.icon"
        :isStopPropagation="false"
      ></lyy-icon>
      <!-- <component
        :is="prop.icon"
        class="action-icon"
        v-if="prop.icon"
      ></component> -->
      <span class="action-name" v-if="text">{{ text }}</span>
    </div>
    <a-dropdown class="action-dropdown" v-else :disabled="disabled">
      <span class="ant-dropdown-link" v-bind="prop" :class="`action-item`">
        <component :is="icon" v-if="icon" class="action-icon"></component>
        <span class="action-name" v-if="prop.text">{{ prop.text }}</span>
        <DownOutlined class="dropdown" />
      </span>
      <template #overlay>
        <a-menu>
          <a-menu-item
            v-for="(menu, i) in prop.dropdown"
            :key="i"
            :disabled="menu.prop.disabled"
          >
            <pc-render-template :elements="[menu]"></pc-render-template>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<style lang="scss" scoped>
.lyy-erp-action-item {
  cursor: pointer;
  &.disabled {
    cursor: not-allowed;
    .action-icon,
    .anticon-down,
    .action-name {
      color: #aaa;
    }
  }
  .action-icon {
    color: #1890ff;
    margin-right: 2px;
  }
  .dropdown {
    margin-left: 3px;
  }
}
</style>
