<script setup lang="ts">
import {
  onBeforeMount,
  computed,
  toRefs,
  defineComponent,
  watch,
  watchEffect,
  onUnmounted,
} from 'vue'
import { UPDATE } from '../../constants/event-type'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { PropType, ModelValueType } from './type'
import { useDep, removeDep } from '@leyaoyao/libs'
import { useFormState } from '../../hooks'

defineComponent({
  name: 'lyy-pagination',
})

const props = defineProps<{
  modelValue?: ModelValueType
  compId: string
  prop: PropType
}>()

const {
  hideOnSinglePage,
  showQuickJumper,
  simple,
  size,
  showSizeChanger = true,
  pageSizeOptions,
} = toRefs(props.prop)

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const fields = computed(() => {
  return props.prop.fields ?? ['current', 'size']
})

const current = computed(() => {
  return fields.value[0] ?? 'current'
})

const pageSize = computed(() => {
  return fields.value[1] ?? 'size'
})

const { formState } = useFormState(props, true)

// 编辑状态所见即所得
watchEffect(() => {
  formState.value[current.value] =
    formState.value[current.value] ?? (props.prop.defaultCurrent || 1)
  formState.value[pageSize.value] =
    formState.value[pageSize.value] ?? (props.prop.defaultPageSize || 10)
})

const updatePage = (currentPage: number, pageSize: number) => {
  emit(UPDATE_MODELVALUE, [currentPage, pageSize])
}

const handleChange = (currentPage: number, pageSize: number) => {
  updatePage(currentPage < 1 ? 1 : currentPage, pageSize)
  emit(ACTION, { event: UPDATE })
}
const handlePiganitionChange = () => {
  emit(ACTION, { event: UPDATE })
}
watch(
  () => props.prop.total,
  (n, o) => {
    // 因为后端返回的total可能是字符串类0
    if (
      n &&
      Number(n) &&
      (formState.value[current.value] - 1) * formState.value[pageSize.value] ==
        n
    ) {
      handleChange(1, formState.value[pageSize.value])
    }
  },
  {
    deep: true,
  },
)

// 重置
const reset = () => {
  handleChange(1, formState.value[pageSize.value])
}
// 赋值
const setData = (page) => {
  if (!Array.isArray(page)) {
    page = [page]
  }
  handleChange(...page)
}
const actionsSet = {
  reset,
  setData,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>
<!-- @change="handleChange" -->
<template>
  <div class="lyy-pagination lyy-component">
    <a-pagination
      v-model:current="formState[current]"
      v-model:pageSize="formState[pageSize]"
      :total="prop.total"
      :show-total="(total) => `共 ${total} 条`"
      :hide-on-single-page="hideOnSinglePage"
      :show-quick-jumper="showQuickJumper"
      :showSizeChanger="showSizeChanger"
      :simple="simple"
      :size="size"
      :pageSizeOptions="pageSizeOptions"
      @change="handlePiganitionChange"
    />
  </div>
</template>
<style lang="scss" scoped>
.lyy-pagination {
  margin-top: 12px;
  text-align: right;
}
</style>
