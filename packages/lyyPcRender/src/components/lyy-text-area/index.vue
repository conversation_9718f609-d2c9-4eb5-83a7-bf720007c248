<template>
  <div class="lyy-component">
    <a-form-item :name="prop.field" :rules="rules" :colon="prop.colon">
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}{{ labelDesc }}</template>
          <span class="form-item-label-text">
            {{ prop.label }}
            <i v-if="labelDesc" class="form-item-label-desc">{{ labelDesc }}</i>
          </span>
        </a-tooltip>
        <lyy-icon v-if="icon" :prop="icon"></lyy-icon>
      </template>

      <a-textarea
        class="lyy-textarea"
        v-model:value="value"
        :disabled="disabled"
        :placeholder="prop.placeholder ?? `请输入${prop.label || ''}`"
        :allow-clear="prop.allowClear"
        :show-count="prop.showCount"
        :maxlength="prop.maxlength"
        :rows="prop.rows"
        :autoSize="prop.autosize"
      />
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, onUnmounted } from 'vue'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { UPDATE } from '../../constants/event-type'
import { ModelValueType, PropType } from './type'
import LyyIcon from '../lyy-icon/index.vue'
import { formula, useDep, usePayload, removeDep } from '@leyaoyao/libs'
import { dynamicRules, onRules } from '@leyaoyao/libs/hooks/use-rules'
import { useFormState } from '../../hooks'
const props = defineProps<{
  compId: string
  modelValue?: ModelValueType
  rowData?: any
  prop: PropType
}>()

const {
  field,
  label,
  placeholder,
  allowClear,
  showCount,
  maxlength,
  size,
  slotLabel,
  icon,
  labelDesc,
} = toRefs(props.prop)

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const { disabled, updateFormState } = useFormState(props, true)

const value = computed({
  get: () => props.rowData ? props.rowData[field.value] : props.modelValue,
  set: (newValue) => {
    setDataBaseFn(newValue)
  },
})
const rules = computed(() => {
  const rules = props.prop.rules || []
  let dynamicRulesSet = []
  let onrulesSet = []
  if (props.prop?.dynamicRules) {
    dynamicRulesSet = dynamicRules(props.prop, props.prop?.dynamicRules)
  }
  if (props.prop?.onrules) {
    onrulesSet = onRules(props.prop?.onrules)
  }
  const res = [...rules, ...dynamicRulesSet, ...onrulesSet]
  return res
})

const setDataBaseFn = (newValue) => {
  if (props?.rowData) {
    props.rowData[field.value] = newValue
  } else {
    updateFormState(newValue)
    emit(UPDATE_MODELVALUE, newValue)
  }
  emit(ACTION, { event: UPDATE })
}
// 清空
const clear = () => {
  setDataBaseFn('')
}
// 重置
const reset = () => {
  setDataBaseFn(props.prop.defaultValue)
}
// 赋值
const setData = (data) => {
  setDataBaseFn(data)
}
const actionsSet = {
  clear,
  reset,
  setData,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>
<style lang="scss" scoped>
.lyy-textarea {
  :deep(textarea.ant-input) {
    padding: 4px 12px;
  }
}
</style>
