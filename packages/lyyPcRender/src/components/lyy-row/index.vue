<template>
  <div class="lyy-container" v-if="isEdit">
    <pc-render-template
      :elements="childrens"
      tag="a-row"
      :align="align"
      :gutter="props.prop.gutter"
      :justify="justify"
      :wrap="wrap"
      :rowData="rowData"
    ></pc-render-template>
  </div>
  <a-row
    v-else
    type="flex"
    class="lyy-container"
    :align="align"
    :gutter="
      prop.responsive ? [props.prop.lineGutter, props.prop.rowGutter] : gutter
    "
    :justify="justify"
    :wrap="wrap"
    :style="style"
  >
    <!--    <slot></slot>-->
    <pc-render-template
      :elements="childrens"
      :row-data="rowData"
    ></pc-render-template>
  </a-row>
</template>

<script lang="ts" setup>
import { toRefs, watch, inject, Ref, ref, computed, provide } from 'vue'
import { PropType } from './type'

const props = defineProps<{
  prop: PropType
  childrens: IElement[]
  rowData: any
  style: object
  compId: string
}>()

const { align, gutter, justify, wrap } = toRefs(props.prop)

const isEdit = inject<Ref<boolean>>('isEdit')

const responsiveConfig = computed(() => {
  const res = {
    responsive: props.prop.responsive,
    xxxlCol: props.prop.xxxlCol,
    xxlCol: props.prop.xxlCol,
    xlCol: props.prop.xlCol,
    lgCol: props.prop.lgCol,
    mdCol: props.prop.mdCol,
    smCol: props.prop.smCol,
    xsCol: props.prop.xsCol,
  }
  return res
})

provide('responsiveConfig', responsiveConfig)

// const store = useStore()

// const pageConfig = store.getValue(PAGE_CONFIG)

// // 循环页面配置，根据 compId 寻找目标，处理对应 callback
// function loop(pageConfig, compId, callback) {
//   pageConfig.some((compConfig, index) => {
//     if (compConfig.compId === compId) {
//       console.log(
//         '🚀 ~ file: index.vue:37 ~ pageConfig.some ~ compId:',
//         compConfig,
//       )
//       callback(compConfig.childrens, compId, index)
//       return true
//     }
//     if (compConfig?.childrens?.length) {
//       loop(compConfig.childrens, compId, callback)
//     }
//   })
// }

// watch(props.prop, () => {
//   console.log('🚀 ~ file: index.vue:41 ~ watch ~ props.prop:', props)
//   // 插入到页面配置
//   function cb(configList, compId, index) {
//     // 插入位置
//     configList.map((item) => {
//       item.prop.responsive = props.prop.responsive
//       item.prop.xxxlCol = props.prop.xxxlCol
//       item.prop.xxlCol = props.prop.xxlCol
//       item.prop.xlCol = props.prop.xlCol
//       item.prop.lgCol = props.prop.lgCol
//       item.prop.mdCol = props.prop.mdCol
//       item.prop.smCol = props.prop.smCol
//       item.prop.xsCol = props.prop.xsCol
//     })
//   }
//   loop(pageConfig, props.compId, cb)
// })
</script>
