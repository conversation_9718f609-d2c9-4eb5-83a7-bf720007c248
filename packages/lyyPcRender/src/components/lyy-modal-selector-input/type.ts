import { Rule } from 'ant-design-vue/es/form/interface'
import type { ButtonType } from 'ant-design-vue/es/button'

interface IModalSelectorBase {
  /**
   * @description formId
   */
  formId?: string
  /**
   * 字段名
   */
  field: string

  /**
   * @description 模态框标题
   */
  title: string

  /**
   * @description 确认按钮文本
   */
  okText?: string
  /**
   * @description 确认按钮类型
   */
  okType?: ButtonType
  /**
   * @description 取消按钮文本
   */
  cancelText?: string
  /**
   * @description 是否多选
   */
  multiple?: boolean
  /**
   * @description 是否展示右上角关闭按钮
   */
  closable?: boolean
  /**
   * @description 是否展示遮罩
   */
  mask?: boolean
  /**
   * @description 点击蒙层是否允许关闭
   */
  maskClosable?: boolean
  /**
   * @description 是否垂直居中展示模态框
   */
  centered?: boolean
  /**
   * @description 关闭时是否销毁子元素
   */
  destroyOnClose?: boolean
  /**
   * @description 关联的table的compId
   */
  tableCompId: string
}
export interface PropType extends IModalSelectorBase {
  /**
   * @description label 标签文本
   */
  label?: string
  /**
   * @description 表单验证规则 object | array
   */
  rules?: Rule
  /**
   * @description 是否禁用
   */
  disabled?: boolean
  /**
   * @description 配合 label 属性使用，表示是否显示 label 后面的冒号
   */
  colon?: boolean
  /**
   * @description 设置字段校验的时机
   */
  validateTrigger?: string | string[]
  /**
   * @description 输入框提示文本
   */
  placeholder?: string
  /**
   * 输入框展示对应的字段
   */
  labelField: string
  /**
   * 字段映射
   */
  fieldNames: {
    [key: string]: string
  }
  /**
   * 弹窗大小
   */
  modalSize?: 'small' | 'middle' | 'large'
  /**
   * 禁用
   */
  forbid?: IForbid
  /**
   * 弹窗样式
   */
  bodyStyle?: object
}

export interface LyyModalSelectorInput extends IElement {
  compName: 'lyy-modal-selector-input'
  prop: PropType
}
