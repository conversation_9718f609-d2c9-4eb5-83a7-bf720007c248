<script setup lang="ts">
import { defineComponent, computed, toRefs, ref, reactive, inject } from 'vue'
import LyyIcon from '../lyy-icon/index.vue'
import { CLICK } from '../../constants/event-type'
import { ACTION } from '../../constants/action-type'
import { PropType } from './type'
import _ from 'lodash'
import { useTpl } from '@leyaoyao/libs'

defineComponent({
  name: 'lyy-button',
})

const props = defineProps<{
  prop: PropType
  rowData?: any
  isPreview?: boolean
  element?: object
}>()

const isEdit = inject<boolean>('isEdit')
const { prop } = toRefs(props)

// 样式规范（废弃2025-01-03）
const btnWidth = computed(() => {
  const { type } = props.prop
  if (['link'].includes(type ?? '')) {
    return 'auto'
  }
  return '62px'
})
const authCodeList = ref([])
const getCurrentPageName = function () {
  const arr = location.hash.replace('#').split('/')
  return arr.at(-1).split('?')[0]
}
try {
  authCodeList.value =
    JSON.parse(sessionStorage.getItem('service-authCodeList'))[
      getCurrentPageName()
    ]?.buttonAuthCode || []
} catch {
  authCodeList.value = []
}const emit = defineEmits([ACTION])


const handleClick = _.debounce((e: MouseEvent) => {
  emit(ACTION, { event: CLICK })
  e.stopPropagation()
}, 300)
const { text, type, size, icon, danger, ghost, disabled } = toRefs(
  reactive(prop.value),
)
/*
 *
 * 编辑状态div模拟button
 * */
const sizeClass = reactive({
  small: 'ant-btn-sm',
  middle: 'ant-btn',
  large: 'ant-btn-lg',
})
const classDivBtn = computed(() => {
  const classSet = [`ant-btn-${type.value}`]
  if (size.value) {
    classSet.push(sizeClass[size.value])
  }
  if (['text', 'link'].includes(type.value)) {
    classSet.push('non-padding')
  }
  if (danger.value) {
    classSet.push('ant-btn-dangerous')
  }
  if (ghost.value) {
    classSet.push('ant-btn-background-ghost')
  }
  return classSet
})
</script>

<template>
  <template
    v-if="
      isEdit ||
      isPreview ||
      !props.prop.authCode ||
      (props.prop.authCode && authCodeList.includes(props.prop.authCode))
    "
  >
    <a-button
      class="lyy-component lyy-button"
      :class="{
        'non-padding': ['text', 'link'].includes(prop.type ?? ''),
        'middle-btn': prop.size === undefined || 'middle',
      }"
      :type="prop.type"
      :size="prop.size"
      :ghost="props.prop.ghost"
      :danger="props.prop.danger"
      :disabled="isEdit ? false : props.prop.disabled"
      @click="handleClick"
    >
      <template #icon>
        <lyy-icon
          v-if="prop.icon"
          :prop="prop.icon"
          :isStopPropagation="false"
        ></lyy-icon>
      </template>
      {{ useTpl(text, props.rowData, 'string') }}
    </a-button>
  </template>
</template>

<style lang="scss" scoped>
.non-padding {
  padding: 0;
}
.middle-btn {
  min-width: v-bind(btnWidth);
  padding: 0px 8px;
  border-radius: 4px;
  box-shadow: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  &.ant-btn-primary,
  &.ant-btn-text {
    // background-color: #1677ff;
    border: none;
  }
  &.ant-btn-link {
    padding: 0;
    height: auto;
  }
}
.lyy-icon {
  display: inline-flex;
  padding-right: 4px;
  font-size: 16px;
}
</style>
