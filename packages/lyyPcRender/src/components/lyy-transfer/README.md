功能：
- formId 绑定的表单id
- field 绑定的表单字段
- fields 绑定的表单字段数组 如果未选中也需要提交时 用数组绑定提交字段 和field互斥
- dataList 数据列表存放字段
- defaultData 回显值不是从表单取时 取值字段
- label 标签
- showSearch 是否显示搜索
- titles 标题集合，顺序从左至右
- rules 表单校验规则
- locale 配置项文案
- fieldNames 字段关联配置
- locale 配置项文案
- fieldNames 字段关联配置
- listStyle 两个穿梭框的自定义样式

备注：
  1. 动态禁用、动态校验规则：在（render-template 或 高价组件）处理
  2. 显示/隐藏：在（render-template 或 高价组件）处理
  3. 输入框失去焦点后的事件：在（use-action）处理：
  4. 请求数据并回填
  5. 中文 在（render-template 或 高价组件）处理
  6. 原来的未选中字段unSelectedfield 提交改成 fields 里面第二个字段提交 第一个字段为选中值提交字段 如果不需要这样操作的话 可以只配置field 提交选中值
   


