<script setup lang="ts">
import {
  onBeforeMount,
  computed,
  toRefs,
  defineComponent,
  StyleValue,
  watch,
  inject,
  Ref,
  onUnmounted,
} from 'vue'
import { formatTime } from '@leyaoyao/libs/utils/get-date-range'
import { UPDATE } from '../../constants/event-type'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { ModelValueType, PropType } from './type'
import dayjs from 'dayjs'
import LyyIcon from '../lyy-icon/index.vue'
import { useDep, removeDep } from '@leyaoyao/libs'
import { useFormState } from '../../hooks'
defineComponent({
  name: 'lyy-date-picker',
})

const props = defineProps<{
  compId: string
  modelValue?: ModelValueType
  prop: PropType
  style: StyleValue
}>()
const emit = defineEmits([UPDATE_MODELVALUE, ACTION])
const { disabled, updateFormState } = useFormState(props, true)

const value = computed({
  get: () => (props.modelValue ? props.modelValue : null),
  set: (newValue) => {
    updateFormState(newValue)
    setDataBaseFn(newValue)
  },
})

const {
  field,
  rules,

  picker,
  format,
  valueFormat,
  allowClear = true,
  showTime,
  showNow,
  showToday,
  defaultValue,
  icon,
  disabledOption,
} = toRefs(props.prop)

onBeforeMount(() => {
  if (defaultValue?.value) {
    value.value = setDefaultValue()
  }
})
const labelCol = computed(() => {
  return Object.keys(props.prop.labelCol?.style ?? []).length > 0
    ? props.prop.labelCol
    : null
})

watch(defaultValue, () => {
  value.value = setDefaultValue()
})

const setDefaultValue = () => {
  switch (defaultValue?.value) {
    case '今天': {
      return formatTime({
        datetime: dayjs().startOf('day'),
        format: valueFormat?.value,
      })
    }
    case '明天': {
      return formatTime({
        datetime: dayjs().add(1, 'days').startOf('day'),
        format: valueFormat?.value,
      })
    }
    case '昨天': {
      return formatTime({
        datetime: dayjs().subtract(1, 'days').startOf('day'),
        format: valueFormat?.value,
      })
    }
    case '月初': {
      return formatTime({
        datetime: dayjs().startOf('month'),
        format: valueFormat?.value,
      })
    }
    case '月末': {
      return formatTime({
        datetime: dayjs().endOf('month'),
        format: valueFormat?.value,
      })
    }
    case '年初': {
      return formatTime({
        datetime: dayjs().startOf('year'),
        format: valueFormat?.value,
      })
    }
    case '年末': {
      return formatTime({
        datetime: dayjs().endOf('year'),
        format: valueFormat?.value,
      })
    }
    default: {
      break
    }
  }
}

const handleChange = () => {
  emit(ACTION, { event: UPDATE })
}
const disabledDate = (current) => {
  if (disabledOption?.value) {
    const { unit, duration, type } = disabledOption.value
    if (!unit && !duration) return false
    return type === 'after'
      ? current &&
          current >
            (duration > 0
              ? dayjs().add(duration, unit).startOf(unit)
              : dayjs().subtract(-duration, unit).startOf(unit))
      : current &&
          current <
            (duration > 0
              ? dayjs().add(duration, unit).startOf(unit)
              : dayjs().subtract(-duration, unit).startOf(unit))
  } else {
    return false
  }
}
// 组件特性暴露
const setDataBaseFn = (data) => {
  emit(UPDATE_MODELVALUE, data)
  emit(ACTION, { event: UPDATE })
}
// 组件特性暴露
const setInitDate = () => {
  setTimeout(() => {
    value.value = setDefaultValue()
  }, 120)
}
// 清空
const clear = () => {
  setDataBaseFn('')
}
// 重置
const reset = () => {
  setDataBaseFn(setDefaultValue())
}
// 赋值
const setData = (data) => {
  setDataBaseFn(formatTime({ datetime: data }))
}
const actionsSet = {
  clear,
  reset,
  setData,
  setInitDate,
}
// 使用联动模式
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>

<template>
  <div class="lyy-component" :style="style">
    <a-form-item
      :name="field"
      :rules="rules"
      :colon="prop?.colon"
      :labelCol="labelCol"
      :style="style"
    >
      <template #label>
        <a-tooltip>
          <template #title>{{ prop.label }}</template>
          <span class="form-item-label-text">{{ prop.label }}</span>
        </a-tooltip>
        <lyy-icon v-if="icon" :prop="prop.icon"></lyy-icon>
      </template>

      <a-date-picker
        v-model:value="value"
        :picker="picker"
        :format="format"
        :value-format="valueFormat"
        :placeholder="prop.placeholder"
        :bordered="prop.bordered"
        :allow-clear="allowClear"
        :show-time="showTime"
        :show-now="showNow"
        :show-today="showToday"
        :size="prop.size"
        :disabled="disabled"
        :disabledDate="disabledDate"
        @change="handleChange"
      ></a-date-picker>
    </a-form-item>
  </div>
</template>

<style lang="scss" scoped>
::v-deep .ant-picker {
  width: 100%;
}
</style>
