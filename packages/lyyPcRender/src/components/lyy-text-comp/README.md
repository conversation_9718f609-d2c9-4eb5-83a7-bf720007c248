# LyyTextComp 文本组件

`lyy-text-comp` 是一个用于展示文本内容的组件，支持自定义样式、文本对齐方式和背景设置。该组件主要用于在页面中展示静态或动态文本内容，可以根据需要调整文本的样式和布局。

## 功能演示

### 基础用法

```vue
<template>
  <lyy-text-comp
    :prop="{
      text: '这是一段示例文本',
      width: '200px',
      height: '40px',
      specificSize: '16px',
      specificColor: '#333333',
      textAlign: 'left',
      isBackgroud: false
    }"
  />
</template>
```

### 不同对齐方式

```vue
<template>
  <div>
    <lyy-text-comp
      :prop="{
        text: '左对齐文本',
        width: '200px',
        textAlign: 'left'
      }"
    />

    <lyy-text-comp
      :prop="{
        text: '居中对齐文本',
        width: '200px',
        textAlign: 'center'
      }"
    />

    <lyy-text-comp
      :prop="{
        text: '右对齐文本',
        width: '200px',
        textAlign: 'right'
      }"
    />
  </div>
</template>
```

### 自定义样式

```vue
<template>
  <lyy-text-comp
    :prop="{
      text: '自定义样式文本',
      width: '300px',
      height: '60px',
      specificSize: '20px',
      specificColor: '#ff5500',
      textAlign: 'center',
      isBackgroud: true
    }"
  />
</template>
```

### 动态文本内容

```vue
<template>
  <lyy-text-comp
    :prop="{
      text: '${user.name}，欢迎您！',
      width: '300px',
      textAlign: 'center'
    }"
    :rowData="{ user: { name: '张三' } }"
  />
</template>
```

## 属性介绍

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| modelValue | string | - | 组件的值，可通过 v-model 绑定 |
| compId | string | - | 组件唯一标识 |
| prop | object | - | 组件属性配置对象 |
| style | object | - | 组件样式对象，会与 prop 中的样式合并 |
| rowData | object | - | 行数据对象，用于模板字符串解析 |

### prop 属性详情

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| text | string | '' | 文本内容，支持模板字符串语法 `${...}` |
| width | string | '' | 组件宽度 |
| height | string | '' | 组件高度 |
| specificSize | string | '' | 文本字体大小 |
| specificColor | string | '' | 文本颜色 |
| textAlign | string | '' | 文本对齐方式，可选值：'left'、'center'、'right' |
| isBackgroud | boolean | false | 是否显示背景，true 时背景为白色 |
| formId | string | - | 关联的表单 ID |
| field | string | - | 关联的表单字段名 |

## 事件介绍

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| update:modelValue | 当组件值变化时触发 | (value: string) |

## 插槽介绍

该组件不提供插槽。

## 使用注意事项

1. 文本内容可以通过 `prop.text` 属性设置，也可以通过 `v-model` 或 `modelValue` 属性绑定。

2. 当同时设置 `modelValue` 和 `prop.text` 时，优先使用 `modelValue` 的值。

3. 文本内容支持模板字符串语法，可以通过 `${...}` 引用 `rowData` 中的数据。例如：
   ```
   text: '${user.name}，您好！'
   rowData: { user: { name: '张三' } }
   ```
   最终显示为：`张三，您好！`

4. 组件样式可以通过 `prop` 中的样式属性设置，也可以通过 `style` 属性设置。当两者有冲突时，`style` 属性的优先级更高。

## 代码示例

### 在表单中使用

```vue
<template>
  <lyy-form :form="formData">
    <lyy-text-comp
      compId="userNameText"
      :prop="{
        formId: 'userForm',
        field: 'userName',
        text: '${userName}',
        width: '200px',
        specificSize: '16px',
        specificColor: '#333333'
      }"
    />
  </lyy-form>
</template>

<script setup>
import { ref } from 'vue'

const formData = ref({
  userName: '张三'
})
</script>
```

### 在列表中使用

```vue
<template>
  <div>
    <div v-for="(item, index) in userList" :key="index">
      <lyy-text-comp
        :prop="{
          text: '用户名：${user.name}，年龄：${user.age}',
          width: '300px',
          specificSize: '14px'
        }"
        :rowData="{ user: item }"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const userList = ref([
  { name: '张三', age: 25 },
  { name: '李四', age: 30 },
  { name: '王五', age: 28 }
])
</script>
```
