<script setup lang="ts">
import {
  ref,
  computed,
  onBeforeMount,
  onMounted,
  onBeforeUnmount,
  defineComponent,
  defineEmits,
  inject,
  Ref,
  watch,
} from 'vue'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import {
  BEFORE_MOUNT,
  BEFORE_UNMOUNT,
  MOUNTED,
  SEARCH,
} from '../../constants/event-type'
import { formula, useDep, removeDep, useRequest } from '@leyaoyao/libs'
import { Empty } from 'ant-design-vue'

defineComponent({
  name: 'lyy-permission-table',
})
// 表格数据类型
interface ModelValueType {
  datasource: any[]
  currentRow: any
  currentIndex: number
  selectedRows: any[]
  selectedRowKeys: string[]
}
// 表格属性类型
interface PropType {
  columns: any[]
  rowKey?: string
  bordered?: boolean
  sticky?: boolean
  mergeUniqueField?: string[]
  colspanConfig?: {
    fields: string[]
    exp?: string
  }[]
  datasource?: any[]
}
const props = defineProps<{
  compId: string
  prop: PropType
  modelValue?: ModelValueType
  isPreview?: boolean
}>()

const tableAntRef = ref(null)
const tableContainerRef = ref(null)
const columnLink = ref<string[]>([])
const isEdit = inject<Ref<boolean>>('isEdit')

const initModelValue = () => {
  const modelValue = props.modelValue ?? {}
  if (!modelValue.datasource) {
    modelValue.datasource = []
  }
  if (!modelValue.currentRow) {
    modelValue.currentRow = {}
  }
  if (!modelValue.selectedRows) {
    modelValue.selectedRows = []
  }
  emit(UPDATE_MODELVALUE, modelValue)
}

const emit = defineEmits([UPDATE_MODELVALUE, ACTION, SEARCH])

const selectedRows = computed({
  get: () => {
    return props.modelValue?.selectedRows || []
  },
  set: (newValue) => {
    emit(
      UPDATE_MODELVALUE,
      Object.assign(props.modelValue ?? {}, {
        selectedRows: newValue,
        selectedRowKeys: newValue.map((item) => item[rowKey.value!]),
      }),
    )
  },
})
const datasource = computed({
  get: () => {
    console.log('datasource', 'get')
    if (props.modelValue?.datasource?.length) {
      return props.modelValue.datasource
    }
    if (props.prop?.datasource?.length) {
      return props.prop?.datasource
    }
    return isEdit?.value ? [{}] : []
  },
  set: (newValue) => {
    console.log('datasource', 'set')
    return emit(
      UPDATE_MODELVALUE,
      Object.assign(props.modelValue ?? {}, {
        datasource: newValue,
      }),
    )
  },
})
// 点击单元格
const cellClick = (record, index, column?: any) => {
  emit(
    UPDATE_MODELVALUE,
    Object.assign(props.modelValue ?? {}, {
      currentRow: record,
      currentIndex: index,
    }),
  )
}

const rowKey = computed(() => props.prop.rowKey || 'id')
const sticky = computed(() => props.prop.sticky ?? true)

const bordered = computed(() => props.prop.bordered)

const tableData = computed(() => {
  return datasource.value
})
const customRow = (record, index) => {
  return {
    onClick: (event) => {
      emit(
        UPDATE_MODELVALUE,
        Object.assign(props.modelValue ?? {}, { currentRow: record }),
      )
      emit(ACTION, { event: 'customRowClick' })
    }, // 点击行
    onDblclick: (event) => {
      emit(ACTION, { event: 'customRowDblclick' })
    },
    onContextmenu: (event) => {
      emit(ACTION, { event: 'customRowContextmenu' })
    },
    onMouseenter: (event) => {
      emit(ACTION, { event: 'customRowMouseenter' })
    }, // 鼠标移入行
    onMouseleave: (event) => {
      emit(ACTION, { event: 'customRowMouseleave' })
    },
  }
}

/*********************
 * 合并单元格代码
 *********************/
// @ts-ignore
// eslint-disable-next-line vue/no-setup-props-destructure
const mergeField = computed(() => {
  return props.prop.columns.map((item) => item.dataIndex)
})
// eslint-disable-next-line vue/no-setup-props-destructure
const mergeUniqueField = props.prop.mergeUniqueField
const colspanConfig = props.prop.colspanConfig
// 初始化合并字段的rowSpan
const mergeFieldRowSapn = ref({})
// 初始化合并字段的colSpan 合并列可以配置多个 但是不能同个字段在多个合并列内
const mergeFieldColSpan = ref({})
// 初始化合并字段的colSpan
const mergeFieldCol: string[] = []
// 初始化合并字段的rowSpan
const initMergeFieldRowSapn = () => {
  mergeFieldRowSapn.value = {}
  mergeFieldColSpan.value = {}
  if (mergeField.value) {
    for (const e of mergeField.value) {
      mergeFieldRowSapn.value[e] = []
    }
  }
  if (colspanConfig && colspanConfig.length > 0) {
    for (const configItem of colspanConfig) {
      for (const key of configItem.fields) {
        mergeFieldColSpan.value[key] = []
        mergeFieldCol.push(key)
      }
    }
  }
}
// 获取合并列信息数组
const getColSpanArr = (data) => {
  //找出需要合并的列
  for (const [i, row] of data.entries()) {
    for (const configItem of colspanConfig || []) {
      for (const [idx, key] of configItem.fields.entries()) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        if (configItem.exp && formula(configItem.exp)(row)) {
          mergeFieldColSpan.value[key][i] =
            idx == 0 ? configItem.fields.length : 0
        } else {
          if (mergeFieldColSpan.value[key][i] == undefined) {
            mergeFieldColSpan.value[key][i] = 1
          }
        }
      }
    }
  }
}
// 获取合并单元格信息数组
const getRowSpanArr = (data) => {
  // 合并单元格位置对象
  const mergeFieldPos = {}
  // 合并单元格字段数组
  let compareField = []
  if (!mergeUniqueField && rowKey.value) {
    compareField = [rowKey.value]
  } else if (mergeUniqueField) {
    compareField = mergeUniqueField
  }
  //找出需要合并单元格的字段
  for (let i = 0; i < data.length; i++) {
    const row = data[i]
    if (i === 0) {
      //第一行, 初始化所有需要需要合并的列为1
      for (const key of mergeField.value) {
        mergeFieldRowSapn.value[key].push(1)
        mergeFieldPos[key] = 0
      }
    } else {
      //获取上一行
      const preRow = data[i - 1]
      //通过多个字段判断是否需要合并
      const isMerge = !compareField.some(
        (field) => row[field] !== preRow[field],
      )
      if (isMerge) {
        //比较需要合并的列是否相同，相同则合并
        for (const key of mergeField.value) {
          if (row[key] === preRow[key]) {
            const pos = mergeFieldPos[key]
            mergeFieldRowSapn.value[key][pos] += 1
            mergeFieldRowSapn.value[key].push(0)
          } else {
            mergeFieldRowSapn.value[key].push(1)
            mergeFieldPos[key] = i
          }
        }
      } else {
        for (const key of mergeField.value) {
          mergeFieldRowSapn.value[key].push(1)
          mergeFieldPos[key] = i
        }
      }
    }
  }
}
//执行合并行单元格 和合并列
const customCell = (row, index, column) => {
  if (
    mergeFieldRowSapn.value[column.dataIndex] ||
    mergeFieldColSpan.value[column.dataIndex]
  ) {
    return {
      rowSpan: mergeFieldRowSapn.value[column.dataIndex]
        ? mergeFieldRowSapn.value[column.dataIndex][index]
        : 1,
      colSpan: mergeFieldColSpan.value[column.dataIndex]
        ? mergeFieldColSpan.value[column.dataIndex][index]
        : 1,
    }
  }
}
// 合并行和列
const mergeRowColHandler = () => {
  // 如果配置了合并单元格字段 获取合并行数组
  initMergeFieldRowSapn && initMergeFieldRowSapn()
  if (mergeField.value && mergeField.value.length > 0) {
    getRowSpanArr && getRowSpanArr(tableData.value ?? [])
  }
  if (colspanConfig && colspanConfig.length > 0) {
    getColSpanArr && getColSpanArr(tableData.value ?? [])
  }
}
/**
 * 初始化选择处理器函数
 * 该函数用于根据数据源生成一个包含所有已选择项的数组
 * @param {Array} datasource - 数据源，包含可能被选择的项
 * @returns {Array} - 包含所有已选择项的数组，每个项包含id和type属性
 */
const initSelectHandler = (datasource = []) => {
  // 定义一个数组，用于存储所有已选择的项
  const selectedArr: any = []
  // 定义一个哈希映射，用于记录已选择的项的id，以避免重复选择
  const selectHashMap = {}
  // 遍历数据源中的每一项
  for (const e of datasource) {
    // 获取当前项的列链接，并初始化长度变量
    let len = columnLink.value.length - 1

    // 逆向遍历列链接，以确保从最深层的链接开始检查选择状态
    while (len--) {
      // 检查当前项在当前列链接下的选择状态
      if (
        !selectHashMap[e[columnLink.value[len] + 'Id']] &&
        (e[columnLink.value[len] + 'Selected'] ||
          e[columnLink.value[len] + 'HalfSelected'])
      ) {
        // 如果当前项未被选择且处于选中或半选中状态，则将其添加到已选择项数组中
        selectedArr.push({
          id: e[columnLink.value[len] + 'Id'],
          type: e?.type,
        })
        // 在哈希映射中记录当前项的id，以避免重复选择
        selectHashMap[e[columnLink.value[len] + 'Id']] = true
      }
    }

    // 如果当前项包含子功能项，则进一步检查这些子功能项的选择状态
    if (e?.functions) {
      for (const item of e.functions) {
        // 如果子功能项被选中且未在哈希映射中记录，则将其添加到已选择项数组中
        if (item.selected && !selectHashMap[item.id]) {
          selectedArr.push({
            id: item.id,
            type: item?.type,
          })
          // 在哈希映射中记录子功能项的id，以避免重复选择
          selectHashMap[item.id] = true
        }
      }
    }
  }
  // 返回包含所有已选择项的数组
  return selectedArr
}

if (
  (mergeField.value && mergeField.value.length > 0) ||
  (colspanConfig && colspanConfig.length > 0)
) {
  watch(
    () => datasource.value,
    (newVal, oldVal) => {
      console.log('datasource', 'watch')
      if (oldVal && newVal && newVal.length != oldVal.length) {
        mergeRowColHandler()
        // 初始化已选择项
        const selectedArr = initSelectHandler(newVal)
        selectedRows.value = selectedArr
      }
    },
  )
}

/*********************
 * 合并单元格代码
 *********************/
// 通过拽调整列宽度
const handleResizeColumn = (w: number, col) => {
  col.width = w
}
// 所有表头字段列表
const columns = computed(() => {
  const cols = props.prop.columns.map((item) => {
    item.customCell = customCell
    columnLink.value.push(item.dataIndex)
    return item
  })
  return cols
})
/**
 * 根据给定的步骤和记录查找特定层级的数据
 * 此函数旨在从表格数据中筛选出与给定记录相关的一级数据以及该级中选定的行
 *
 * @param {number} step - 当前处理的步骤，用于确定使用哪一级的数据索引
 * @param {Object} record - 当前记录对象，用于比较并找出匹配的行
 * @returns {Array[]} - 返回一个包含两个数组的数组：
 *                      第一个数组包含所有匹配的行，
 *                      第二个数组包含在当前步骤中被选定的行
 */
const findSomeLevel = (step, record) => {
  // 初始化用于存储匹配行和选定行的数组
  const rows: any[] = []
  const rowsSelected: any[] = []
  const rowsHalfSelected: any[] = []

  // 获取前一级和当前级的数据索引，用于后续的数据匹配和筛选
  const prevDataIndex = columnLink.value[step - 1]
  const curDataIndex = columnLink.value[step]

  // 遍历表格数据，寻找匹配的行
  for (let i = 0; i < tableData.value.length; i++) {
    // 如果当前行与给定记录在前一级索引上匹配，则将其添加到匹配行数组中
    if (
      tableData.value[i][prevDataIndex + 'Id'] === record[prevDataIndex + 'Id']
    ) {
      rows.push(tableData.value[i])
      // 如果当前行在当前级索引上被选定，则也将其添加到选定行数组中
      tableData.value[i][curDataIndex + 'Selected'] &&
        rowsSelected.push(tableData.value[i])
      // 如果当前行处于半选状态，则也将其添加到选定行数组中
      tableData.value[i][curDataIndex + 'HalfSelected'] &&
        rowsHalfSelected.push(tableData.value[i])
    }
  }

  // 返回匹配行和选定行的数组
  return [rows, rowsSelected, rowsHalfSelected]
}
/**
 * 遍历选择前一行的函数
 * 该函数用于在给定记录中，从当前行索引向前遍历，并根据已选中状态更新相关行的选中状态
 * 主要用于处理树形结构数据的行选中逻辑
 *
 * @param {Object} record - 当前行的记录对象，包含行数据及选中状态
 * @param {number} curIdx - 当前行的索引，用于确定从哪里开始向前遍历
 * @param {boolean} selected - 当前行的选中状态，true表示选中，false表示未选中
 */
const traverseSelectPrev = (record, curIdx, selected) => {
  let step = curIdx
  while (step >= 0) {
    const [rows, someLevel, rowsHalfSelected] = findSomeLevel(step, record)
    // 没有找到行数据，或者某个子节点选中后再往上查找的时候发现该节点的其他兄弟没有全选则终止循环无需再往上查找
    if (rows.length === 0) {
      break
    }
    for (const item of rows) {
      // 往上查找，如果子节点都是选中的则将自己也勾选
      if (
        columnLink.value[step - 1] &&
        !(selected && rows.length != someLevel.length)
      ) {
        // 如果子节点都是选中的则将自己也勾选
        item[columnLink.value[step - 1] + 'Selected'] = selected
      }
      /***
       * 设置半选状态
       * 如果处于选择状态，那半选状态一定是false
       * 如果有子节点存在半选状态，则该节点半选状态为true rowsHalfSelected.length > 0
       * 如果自身处于非选中状态，但有子节点存在选中状态，则该节点半选状态为true someLevel.length > 0
       * **/
      item[columnLink.value[step - 1] + 'HalfSelected'] =
        !item[columnLink.value[step - 1] + 'Selected'] &&
        (rowsHalfSelected.length > 0 || someLevel.length > 0)
    }
    step--
  }
}
/**
 * 遍历选择后一行的函数
 * 该函数用于在给定记录中，从当前行索引向前遍历，并根据已选中状态更新相关行的选中状态
 * 主要用于处理树形结构数据的行选中逻辑
 *
 * @param {Object} record - 当前行的记录对象，包含行数据及选中状态
 * @param {number} curIdx - 当前行的索引，用于确定从哪里开始向前遍历
 * @param {boolean} selected - 当前行的选中状态，true表示选中，false表示未选中
 */
const traverseSelectNext = (record, curIdx, selected) => {
  let step = curIdx
  const len = columnLink.value.length
  const rows: any[] = []
  const curDataIndex = columnLink.value[step]
  // 找出当前列的所有子节点
  for (let i = 0; i < tableData.value.length; i++) {
    if (
      tableData.value[i][curDataIndex + 'Id'] === record[curDataIndex + 'Id']
    ) {
      rows.push(tableData.value[i])
    }
  }
  // 遍历所有子节点
  for (const item of rows) {
    step = curIdx
    while (step < len) {
      // 如果不是最后一列，则设置当前列的选中状态
      if (step < len - 1) {
        // 设置每一个列的选中状态
        item[columnLink.value[step] + 'Selected'] = selected
        // 设置半选状态
        item[columnLink.value[step] + 'HalfSelected'] = false
      } else {
        // 最后一列，则设置权限列表的选中状态
        const funs = item.functions
        for (const item of funs) {
          item.selected = selected
        }
      }
      step++
    }
  }
}
/**
 * 处理复选框变化的函数
 * @param {Object} record - 当前操作的记录项，包含functions属性表示功能列表
 * @param {Object} column - 当前操作的列信息，包含dataIndex属性用于标识列
 */
const onCheckChange = (record, column, item) => {
  // 是否有展示的权限
  const funs = record.functions || []
  // 找出点击第几列
  const idx = columnLink.value?.findIndex((item) => item === column.dataIndex)
  // 如果不是有效的列，则不进行处理
  if (idx < 0) return
  // 如果是最后一列且存在功能项，则计算是否全部选中
  if (idx === columnLink.value.length - 1 && funs.length > 0) {
    // 过滤出选中的功能项
    const selecteds = funs.filter((item) => item.selected)
    // 如果权限列全选，则设置倒数第二列的选中状态
    const selected = selecteds.length === funs.length
    // 有选择并且没有全选，则设置倒数第二列的半选状态
    const HalfSelectedd =
      selecteds.length > 0 && selecteds.length !== funs.length
    // 设置倒数第二列的选中状态
    record[columnLink.value.at(-2) + 'Selected'] = selected
    // 设置倒数第二列的半选状态
    record[columnLink.value.at(-2) + 'HalfSelected'] = HalfSelectedd
    // 遍历父级节点并设置选中状态
    traverseSelectPrev(record, idx - 1, selected)
  } else {
    const curIdx = record[column.dataIndex + 'Id']
    // 找出当前列的所有子节点
    const rows = tableData.value.filter(
      (item) => item[column.dataIndex + 'Id'] === curIdx,
    )
    // 设置当前列的选中状态
    for (const item of rows) {
      // 设置当前列的选中状态
      item[column.dataIndex + 'Selected'] =
        record[column.dataIndex + 'Selected']
      // 设置半选状态
      item[column.dataIndex + 'HalfSelected'] = false
    }
    // 遍历父级节点并设置选中状态
    traverseSelectPrev(record, idx, record[column.dataIndex + 'Selected'])
    // 遍历下一级节点并设置选中状态
    traverseSelectNext(record, idx, record[column.dataIndex + 'Selected'])
  }
  // 更新选中状态
  const selectedArr = initSelectHandler(datasource.value)
  selectedRows.value = [...selectedArr]
}
initModelValue()
onBeforeMount(() => {
  emit(ACTION, { event: BEFORE_MOUNT })
})
onMounted(() => {
  emit(ACTION, { event: MOUNTED })
  if (tableData.value.length > 0) {
    mergeRowColHandler()
    // 初始化已选择项
    const selectedArr = initSelectHandler(tableData.value)
    selectedRows.value = selectedArr
  }
})

onBeforeUnmount(() => {
  emit(ACTION, { event: BEFORE_UNMOUNT })
})
</script>

<template>
  <div
    ref="tableContainerRef"
    class="lyy-tree-table lyy-container"
    :class="{
      isEdit: isEdit,
      isEmpty: datasource.length <= 0,
    }"
  >
    <div class="lyy-tree-table-content">
      <div class="lyy-tree-table-body">
        <a-table
          ref="tableAntRef"
          class="ant-table-striped animate-scroll"
          :dataSource="tableData"
          :customRow="customRow"
          :columns="columns"
          :pagination="false"
          :rowKey="rowKey"
          :sticky="sticky"
          :bordered="bordered"
          table-layout="fixed"
          @resizeColumn="handleResizeColumn"
        >
          <template #emptyText>
            <a-empty
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
              :description="prop.emptyText ?? '暂无数据'"
            />
          </template>
          <!-- 表格头插槽start -->
          <template #headerCell="{ column }">
            <div
              class="lyy-component-prototype"
              :data="JSON.stringify(column)"
              :parentId="props.compId"
              :field="column?.dataIndex"
              prototypeName="dataIndex"
            >
              <a-tooltip placement="topLeft" :mouseEnterDelay="1">
                <template #title v-if="column?.title.length > 5">
                  <span>{{ column.title }}</span>
                </template>
                <div class="table-head-cell">
                  {{ column.title }}
                </div>
              </a-tooltip>
            </div>
          </template>
          <!-- 表格头插槽end -->
          <!-- 表格单元格插槽 -->
          <template #bodyCell="{ column, record, index }">
            <template v-if="column?.dataIndex === '_no'">
              <span>{{ index + 1 }}</span>
            </template>
            <!-- 单元格 -->
            <span
              v-else
              class="table-cell"
              @click.capture="cellClick(record, index, column)"
            >
              <span
                v-if="
                  record.functions && column['dataIndex'] === columnLink.at(-1)
                "
              >
                <a-checkbox
                  :key="item[rowKey]"
                  v-for="item in record.functions"
                  v-model:checked="item.selected"
                  @change="onCheckChange(record, column, item)"
                >
                  {{ item.name }}
                </a-checkbox>
              </span>
              <span v-else>
                <a-checkbox
                  v-model:checked="record[column?.dataIndex + 'Selected']"
                  :indeterminate="record[column?.dataIndex + 'HalfSelected']"
                  @change="onCheckChange(record, column)"
                >
                  {{ record[column?.dataIndex] }}
                </a-checkbox>
              </span>
            </span>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
