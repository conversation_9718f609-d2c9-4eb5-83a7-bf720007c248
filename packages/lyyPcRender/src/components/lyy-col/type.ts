export interface PropType {
  /**
   * 栅格占位格数，为 0 时相当于 display: none
   */
  span?: number

  /**
   * flex 布局填充
   */
  flex?: string | number

  /**
   * 栅格左侧的间隔格数，间隔内不可以有栅格
   */
  offset?: number

  /**
   * 栅格顺序，flex 布局模式下有效
   */
  order?: number

  /**
   * 栅格向左移动格数
   */
  pull?: number

  /**
   * 栅格向右移动格数
   */
  push?: number

  /**
   * 垂直对齐方式
   *
   * 默认值：'top'
   */
  align?: 'flex-start' | 'flex-end ' | 'center' | 'baseline' | 'stretch'

  /**
   * 水平排列方式
   *
   * 默认值：'flex-start'
   */
  justify?:
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-around'
    | 'space-between'

  /**
   * 元素之间的间隔
   */
  gap?: number
  /**
   * 响应式布局
   */
  responsive?: boolean
  xxxlCol?: number
  xxlCol?: number
  xlCol?: number
  lgCol?: number
  mdCol?: number
  smCol?: number
  xsCol?: number
  lineGutter: number,
  rowGutter: number,
}

export interface LyyCol extends IElement {
  compName: 'lyy-col'
  prop: PropType
}
