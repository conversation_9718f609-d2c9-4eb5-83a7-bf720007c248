<template>
  <a-col
    v-if="responsiveConfig?.responsive"
    class="lyy-container"
    :id="compId"
    :span="span"
    :xs="col.xsCol"
    :sm="col.smCol"
    :md="col.mdCol"
    :lg="col.lgCol"
    :xl="col.xlCol"
    :xxl="col.xxlCol"
    :xxxl="col.xxxlCol"
    :style="style"
    :offset="offset"
    :order="order"
    :pull="pull"
    :push="push"
    :compId="compId"
  >
    <div class="lyy-col lyy-container">
      <pc-render-template
        :elements="childrens"
        :row-data="rowData"
      ></pc-render-template>
      <!--      <slot />-->
    </div>
  </a-col>
  <a-col
    v-else
    :style="style"
    class="lyy-container"
    :span="span"
    :offset="offset"
    :order="order"
    :pull="pull"
    :push="push"
    :flex="flex"
    :id="compId"
    :compId="compId"
  >
    <div class="lyy-col">
      <!--      <slot />-->
      <pc-render-template
        :elements="childrens"
        :row-data="rowData"
      ></pc-render-template>
    </div>
  </a-col>
</template>

<script lang="ts" setup>
import { toRefs, ref, computed, watch, inject, Ref } from 'vue'
import { PropType } from './type'

const props = defineProps<{
  prop: PropType
  childrens: IElement[]
  rowData: any
  compId: string
  id: string
  style: object
}>()

const {
  span,
  offset,
  order,
  pull,
  push,
  align = 'left',
  flex,
  // justify = 'flex-start',
  gap = ref(0),
} = toRefs(props.prop)
const gapValue = computed(() => {
  return gap.value + 'px'
})

const responsiveConfig = inject<Ref<object>>('responsiveConfig')

const col = computed(() => {
  console.log('responsiveConfig', responsiveConfig?.value)
  if (responsiveConfig?.value) {
    const { xxxlCol, xxlCol, xlCol, lgCol, mdCol, smCol, xsCol } =
      responsiveConfig.value
    const colObj = {
      xxxlCol: Math.floor(24 / Number(xxxlCol)),
      xxlCol: Math.floor(24 / Number(xxlCol)),
      xlCol: Math.floor(24 / Number(xlCol)),
      lgCol: Math.floor(24 / Number(lgCol)),
      mdCol: Math.floor(24 / Number(mdCol)),
      smCol: Math.floor(24 / Number(smCol)),
      xsCol: Math.floor(24 / Number(xsCol)),
    }
    return colObj
  }
  return {
    xxxlCol: 6,
    xxlCol: 6,
    xlCol: 6,
    lgCol: 6,
    mdCol: 3,
    smCol: 3,
    xsCol: 3,
  }
})
</script>

<style lang="less" scoped>
.lyy-col {
  // display: flex;
  // align-items: v-bind(align);
  // justify-content: v-bind(justify);
  text-align: v-bind(align);
  gap: v-bind(gapValue);
  height: inherit;
}
</style>
