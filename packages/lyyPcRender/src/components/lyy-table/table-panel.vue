<script setup lang="ts">
import { ref, computed } from 'vue'
import { formula, getIn, useRequest } from '@leyaoyao/libs'
import { datapipe } from '@leyaoyao/libs/utils/pipe'

const props = defineProps<{
  value: string | number
  rowData: object
  column: any
  rawValue?: any // 未经过数据管道处理的原始值
}>()

const panelColumns = computed(() => props.column?.option?.panelColumns)

const getValue = (value, pipes) => {
  // 处理: 空值
  if (value === undefined || value === null || value === '') return '-'
  // 处理: 数字、日期、映射
  if (!pipes) return value
  return datapipe(value, pipes)
}

const tooltip = ref<any>('')
const handleClick = async () => {
  const { fetch } = props.column?.option ?? {}
  tooltip.value = await useRequest(fetch)
}
const isText = computed(() => {
  const { textExp } = props.column?.option ?? {}
  return textExp ? formula(textExp)(props.rowData) : false
})
</script>

<template>
  <div class="table-panel">
    <span v-if="isText">{{ props.value }}</span>

    <a-dropdown v-else-if="props.column?.option?.fetch">
      <a class="ant-dropdown-link" @click="handleClick">
        {{ value }}
      </a>
      <template #overlay>
        <a-menu style="padding: 8px 16px">
          {{ tooltip }}
        </a-menu>
      </template>
    </a-dropdown>
    <a-popover v-else-if="panelColumns && panelColumns.length > 0" placement="bottom">
      <template #content>
        <div class="panel-column">
          <div v-for="item in panelColumns" :key="item.title">
            <div>{{ item.title }}</div>
            <div v-for="one in item.dataIndex" :key="one" :style="item.style">
              <template v-if="item.type === 'text'">{{ one }}</template>
              <template v-else>
                {{ getValue(getIn(props.rowData, one), one.pipes) }}
              </template>
            </div>
          </div>
        </div>
        <!-- <div class="no-data">无数据</div> -->
      </template>
      <a class="ant-dropdown-link">
        {{ props.value }}
      </a>
    </a-popover>

    <a-popover v-else-if="Array.isArray(props.rawValue)" placement="bottomLeft">
      <template #content>
        <template v-for="value in props.rawValue" :key="value">
          <p>{{ value }}</p>
        </template>
      </template>
      <a>
        {{ props.value }}
      </a>
    </a-popover>
    <a-popover v-else-if="props.column.option&&props.column.option.type == 'textArray'" placement="bottomLeft">
      <template #content>
        <template v-for="value in props.rowData[props.column.option.dataIndex]" :key="value">
          <p>{{ value }}</p>
        </template>
      </template>
      <a>
        {{ props.value }}
      </a>
    </a-popover>
    <a v-else>{{ props.value }}</a>
  </div>
</template>

<style scoped lang="scss">
.table-panel {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: block;
}

.panel-column {
  display: flex;
  gap: 16px;
}
</style>
