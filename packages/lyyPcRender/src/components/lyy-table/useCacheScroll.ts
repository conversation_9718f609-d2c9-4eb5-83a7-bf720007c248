import throttle from 'lodash/throttle'
import isNil from 'lodash/isNil'
import uniqueId from 'lodash/uniqueId'
import {
  onActivated,
  onDeactivated,
  computed,
  Ref,
  onUnmounted,
  ref,
} from 'vue'

interface IUseCacheScroll {
  cache: Ref<boolean | {
    x: boolean,
    y: boolean,
  }>,
  salt?: string,
  elRef: Ref<HTMLElement | null>,
}

export default function useCacheScroll({
  cache,
  salt,
  elRef,
}: IUseCacheScroll) {

  const scrollTopRef = ref(0)
  const scrollLeftRef = ref(0)
  const TablePageScrollXKey = keyGenerator('x')
  const TablePageScrollYKey = keyGenerator('y')

  const recordScrollFn = getRecordScrollTopFn()

  const noCache = computed(() => cache.value === false || (
    typeof cache.value === 'object' &&
      cache.value.x &&
      cache.value.y
  ))

  onActivated(() => {
    // 默认开启
    // 因为新增属性对于已经在使用的组件来说 model 中不存在该属性，所以使用 false 判断而不是 falsy 值
    // 关闭该功能则需要代码配置 cacheScrollX | cacheScrollY 字段
    if (noCache.value) {
      return
    }

    const storageScrollX = sessionStorage.getItem(TablePageScrollXKey)
    const storageScrollY = sessionStorage.getItem(TablePageScrollYKey)

    if (elRef.value) {
      elRef.value.scrollTo(
        Number(storageScrollX ?? 0),
        Number(storageScrollY ?? 0)
      )
    }
  })

  onDeactivated(() => {
    if (noCache.value) return

    if (elRef.value) {
      const scrollTop = scrollTopRef.value
      const scrollLeft = scrollLeftRef.value

      if (!isNil(scrollLeft)) {
        sessionStorage.setItem(TablePageScrollXKey, Number(scrollLeft).toString())
      }

      if (!isNil(scrollTop)) {
        sessionStorage.setItem(TablePageScrollYKey, Number(scrollTop).toString())
      }
    }
  })

  onUnmounted(() => {
    sessionStorage.removeItem(TablePageScrollXKey)
    sessionStorage.removeItem(TablePageScrollYKey)
  })

  return {
    attachListeners,
    unattachListeners,
  }

  function keyGenerator (dir: 'x' | 'y') {
    return uniqueId(`${salt}${salt ? '-' : ''}TablePageScroll${dir.toUpperCase()}${dir.toLowerCase()}`)
  }

  function attachListeners() {
    if (elRef.value) {
      elRef.value.addEventListener('scroll', recordScrollFn)
    }
  }

  function unattachListeners() {
    if (elRef.value) {
      elRef.value.removeEventListener('scroll', recordScrollFn)
    }
  }

  function getRecordScrollTopFn() {
    return throttle(function recordScrollTopFn(e) {
      if (elRef.value) {
        scrollTopRef.value = Number(elRef.value.scrollTop)
        scrollLeftRef.value = Number(elRef.value.scrollLeft)
      }
    }, 300)
  }
}
