<template>
  <div class="search-box" ref="searchRef">
    <a-row
      class="row-out list-scheme"
      align="top"
      :gutter="8"
      v-if="filterRecord.length > 0"
    >
      <a-col class="label">我的方案</a-col>
      <a-col class="list">
        <a-button
          class="record-filter-btn"
          :class="item.checked ? 'ant-btn-primary' : ''"
          v-for="(item, idx) in filterRecord"
          :key="idx"
          :disabled="isEdit"
          @click="clickFilter(item)"
        >
          {{ item.name }}
        </a-button>
      </a-col>
      <a-col class="edit" v-if="filterRecord.length > 0">
        <form-outlined @click="handleOpenEditModal" />
      </a-col>
    </a-row>
    <a-row class="row-out" align="top" :gutter="8">
      <a-col class="label">快捷过滤</a-col>
      <a-col class="options">
        <template v-for="(item, index) in filters" :key="index">
          <TableSearchFilterItem
            v-model:filter="filters[index]"
            :tableColumns="tableColumns"
            :isEdit="isEdit"
          >
            <minus-circle-outlined class="opIcon" @click="minusOption(index)" />
            <plus-circle-outlined
              class="opIcon"
              v-if="index === filters.length - 1"
              @click="addOption"
            />
          </TableSearchFilterItem>
        </template>
      </a-col>
      <a-col class="buttons">
        <a-button type="link" @click="search" :disabled="isEdit">搜索</a-button>
        <a-button type="link" @click="save" :disabled="isEdit">保存</a-button>
        <a-button type="link" @click="reset" :disabled="isEdit">重置</a-button>
      </a-col>
    </a-row>
    <TableSearchSchameEdit
      v-if="showEditModal"
      v-model:open="showEditModal"
      :schames="filterRecord"
      :tableColumns="tableColumns"
      :isEdit="isEdit"
      @save="handleOk"
      @deleteSchame="delScheme"
      @tempSaveSchame="(schame) => clickFilter(schame, true)"
    ></TableSearchSchameEdit>
    <TableSearchSettingSaveModal
      v-model:open="visible"
      :filters="filters"
      @save="handleOk"
    />
  </div>
</template>

<script setup lang="ts">
import { PropType } from '../lyy-grid/type'
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  watch,
  onUnmounted,
} from 'vue'
import { getCurrentPageName, useRequest } from '@leyaoyao/libs'
import TableSearchFilterItem from './table-search-filter-item.vue'
import TableSearchSchameEdit from './table-search-schame-edit.vue'
import TableSearchSettingSaveModal from './table-search-setting-save-modal.vue'
const emit = defineEmits([
  'upDateFilter',
  'save',
  'search',
  'upDateSort',
  'reset',
])
const props = defineProps<{
  style: Object
  prop: PropType
  tableCompId: string
  isEdit: boolean
  childrens: IComponent
  compId: string
  columns: IObject[]
  enableEnterShortcut?: boolean
}>()
const curSchameId = ref('')
const visible = ref(false)
const schemeEdit = ref(false)
const showEditModal = ref(false)
const isEdit = computed(() => {
  return props.isEdit
})
const curFilter = ref<null | any[]>(null)
const getPageConfigureId = () => {
  const pageName = getCurrentPageName()
  const regex = /^\d+/
  const match = pageName.match(regex)
  const result = match ? match[0] : null
  return result
}

const filters = reactive([
  {
    field: '',
    op: '',
    value: '',
  },
])

const defaultFilters = []

const tableColumns = computed(() => {
  return props.columns
    .filter((item) => item.isFilter)
    .map((item) => {
      return Object.assign(item, {
        align: 'left',
        fixed: '',
      })
    })
})
const setDefaultFilterOprions = (cols = []) => {
  const defaultlFilter = cols.slice(0, 2) || []
  if (defaultlFilter.length > 0) {
    filters.length = 0
    defaultFilters.length = 0
    for (const item of defaultlFilter) {
      const filter = {
        field: item.dataIndex,
        op: item.type === 'text' ? 'like' : 'eq',
        value: '',
      }
      filters.push(filter)
      defaultFilters.push(filter)
    }
  }
}

watch(
  tableColumns,
  (newVal) => {
    setDefaultFilterOprions(newVal)
  },
  {
    immediate: true,
  },
)

// const watchFn = watch(
//   tableColumns,
//   (newVal) => {
//     setDefaultFilterOprions(newVal)
//     watchFn()
//   },
//   {
//     immediate: true,
//   },
// )

const filterRecord = reactive([])
const delScheme = (id) => {
  const idx = filterRecord.findIndex((val) => val.pagePreferenceId == id)
  filterRecord.splice(idx, 1)
  if (filterRecord.length === 0) {
    showEditModal.value = false
  }
}

const getPagePreference = async () => {
  const higher = [
    {
      key: 'type',
      value: 'custom_search',
    },
    {
      key: 'pageConfigureId',
      value: getPageConfigureId(),
    },
  ]
  const res = await useRequest({
    url: '/page-preference',
    method: 'get',
    baseURL: '/gw/ram-service',
    payloads: {
      type: 'higher',
      static: '',
      higher,
    },
  })
  if (res) {
    filterRecord.length = 0
    filterRecord.push(...res)
    for (const item of filterRecord) {
      if (curSchameId.value && item.pagePreferenceId === curSchameId.value) {
        item.checked = true
      }
    }
  }
}
const handleOk = async () => {
  getPagePreference()
  emit('save')
}
const handleOpenEditModal = () => {
  showEditModal.value = true
}
const clickFilter = (item, isFromSave = false) => {
  if (item.jsonstr && !schemeEdit.value) {
    let _filters = [],
      sort = []
    if (item.checked && !isFromSave) {
      item.checked = false
      curSchameId.value = ''
      _filters = [...defaultFilters]
    } else {
      // 编辑弹窗会改变item的引用， 故需要在filterRecord中找到对应的方案，并高亮
      for (const el of filterRecord) {
        el.checked =
          el.pagePreferenceId === item.pagePreferenceId ? true : false
      }
      curSchameId.value = item.pagePreferenceId
      const jsonstr = JSON.parse(item.jsonstr) || {}
      _filters = jsonstr.filters || []
      sort = jsonstr.sort
    }
    curFilter.value = _filters
    filters.length = 0
    filters.push(..._filters)
    emit('upDateFilter', [...filters])
    emit('upDateSort', sort)
    emit('search')
  }
}

const minusOption = (idx) => {
  filters.splice(idx, 1)
}
const addOption = () => {
  if (isEdit.value) return
  filters.push({
    field: '',
    op: '',
    value: '',
  })
}
const search = () => {
  // const fil = curFilter.value ? [...filters, ...curFilter.value] : filters
  emit('upDateFilter', filters)
  emit('search', filters)
}
const save = () => {
  visible.value = true
  emit('upDateFilter', filters)
  // emit('save', filters)
}
const reset = () => {
  filters.length = 0
  setDefaultFilterOprions(tableColumns.value)
  // filters.push({
  //   field: '',
  //   op: '',
  //   value: '',
  // })
  for (const item of filterRecord) item.checked = false
  curFilter.value = null
  curSchameId.value = ''
  emit('upDateFilter', [])
  emit('upDateSort', [])
  emit('reset')
}

const searchRef = ref(null)
const handleEnter = (event) => {
  if (event.keyCode === 13) {
    const activeElement = document.activeElement
    if (searchRef.value?.contains(activeElement)) {
      // 由于日期选择器在聚焦的状态下，按下enter键，日历弹窗会弹出，故需将当前激活的元素失焦
      activeElement && activeElement.blur()
      search()
    } else if (activeElement === document.body) {
      search()
    }
  }
}

onMounted(() => {
  !isEdit.value && getPagePreference()
  if (props.enableEnterShortcut || props.enableEnterShortcut == undefined) {
    document.addEventListener('keydown', handleEnter)
  }
})

onUnmounted(() => {
  if (props.enableEnterShortcut || props.enableEnterShortcut == undefined) {
    document.removeEventListener('keydown', handleEnter)
  }
})
defineComponent({
  name: 'table-search-setting',
})
</script>

<style scoped lang="less">
.record-filter-btn {
  margin-right: 8px;
  position: relative;
  .red {
    color: red;
    position: absolute;
    top: -7px;
    right: -2px;
  }
}
.form-scheme {
  :deep(.ant-form-item-label) {
    width: 86px;
  }
}
.list-scheme {
  margin-bottom: 8px;
  align-items: center;
}
.row-out {
  //width: 1120px;
  //padding-bottom: 8px;
  flex-wrap: nowrap;
  .width180 {
    width: 180px;
  }
  .label {
    height: 32px;
    line-height: 32px;
    flex-shrink: 0;
  }
  .options {
    display: flex;
    //flex-grow: 1;
    flex-wrap: wrap;
    margin-bottom: 4px;
    .row-option {
      //flex-grow: 1;
      align-items: center;
      margin-bottom: 8px;
      margin-right: 8px;
    }
    .select {
      width: 155px;
    }
    .op {
      width: 100px;
      margin: 0 5px;
    }
  }
  .buttons {
    flex-shrink: 0;
    button {
      padding: 0 8px 0 0;
    }
  }
  .opIcon {
    margin-left: 8px;
  }
}
</style>
