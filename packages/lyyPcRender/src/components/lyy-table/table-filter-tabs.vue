<script lang="ts" setup>
import {
  inject,
  defineComponent,
  defineEmits,
  Ref,
  onMounted,
  ref,
  onUnmounted,
  computed,
  defineExpose,
  nextTick,
} from 'vue'

import LyyIcon from '../lyy-icon/index.vue'
import { getCurrentPageName, useRequest } from '@leyaoyao/libs'
import Draggable from 'vuedraggable'

defineComponent({
  name: 'table-toolbar',
})

const props = defineProps<{
  activeKey: string
  commonFilter: boolean // 常规筛选
  schames: object[]
  filter: boolean // 快捷筛选
}>()

const emit = defineEmits(['update:activeKey', 'change', 'edit', 'expand'])

const schameList = computed(() => {
  return props.schames
})

let containerWidth = 0
let contentWidth = 0

const currentOffset = ref(0)
const highlightLeft = ref(0)
const highlightWidth = ref(0)
const tabWrapRef = ref(null)
const tabListRef = ref(null)
const tabRefs = ref([])
const tabCommonRef = ref(null)
const tabCustomRef = ref(null)
const tabRightRef = ref(null)

const isExpand = ref(true)

const showLeftBtn = ref(false)

const showRightBtn = ref(false)

const activeKey = computed({
  get() {
    if (schameList.value.length === 0 && props.activeKey === 'custom') {
      doAnimateTocustom()
    } else {
      const index = schameList.value.findIndex(
        (item) => item.pagePreferenceId === props.activeKey,
      )
      if (index > -1) {
        handleAnimate(index)
      }
    }
    return props.activeKey
  },
  set(value) {
    emit('update:activeKey', value)
  },
})

const prevActiveKey = computed(() => {
  return props.activeKey
})

const updateDimensions = () => {
  if (tabWrapRef.value && tabListRef.value) {
    containerWidth = tabWrapRef.value.offsetWidth
    contentWidth = tabListRef.value.scrollWidth
    currentOffset.value =
      contentWidth <= containerWidth
        ? 0
        : Math.max(
            containerWidth - contentWidth,
            Math.min(currentOffset.value, 0),
          )
    const index = schameList.value.findIndex(
      (item) => item.pagePreferenceId === activeKey.value,
    )
    if (index > -1) {
      const curTabsRef = tabRefs.value[index]
      highlightLeft.value = curTabsRef.offsetLeft
    }
    showLeftBtn.value = currentOffset.value < 0
    showRightBtn.value = currentOffset.value > containerWidth - contentWidth
  }
}

const onWheel = (event) => {
  if (contentWidth <= containerWidth) return
  const direction = event.deltaY > 0 ? 'down' : 'up'
  const step = 100 // 每次滚动移动的像素量
  currentOffset.value =
    direction === 'up'
      ? Math.min(currentOffset.value + step, 0)
      : Math.max(currentOffset.value - step, containerWidth - contentWidth)
  showLeftBtn.value = currentOffset.value < 0
  showRightBtn.value = currentOffset.value > containerWidth - contentWidth
}

let resizeObserver = null
const observeResize = () => {
  resizeObserver = new ResizeObserver(() => {
    updateDimensions()
  })

  if (tabWrapRef.value) {
    resizeObserver.observe(tabWrapRef.value)
  }
  if (tabListRef.value) resizeObserver.observe(tabListRef.value)
}

const handleAnimate = (tabIndex) => {
  // 数据变更，需要重新计算下划线位置以及容器的宽度
  setTimeout(() => {
    doAnimate(tabIndex)
  }, 0)
}

const doAnimate = (tabIndex) => {
  const tab = tabRefs.value[tabIndex]
  const tabReact = tab.getBoundingClientRect()
  const tabWrapReact = tabWrapRef.value.getBoundingClientRect()
  const containerWidth = tabWrapRef.value.offsetWidth
  const contentWidth = tabListRef.value.scrollWidth

  highlightLeft.value = tab.offsetLeft
  highlightWidth.value = tab.offsetWidth
  let newOffset = currentOffset.value
  if (tabReact.left < tabWrapReact.left) {
    const delta = tabWrapReact.left - tabReact.left
    newOffset = Math.min(newOffset + delta, 0)
  }
  if (tabReact.right >= tabWrapReact.right) {
    const delta = tabReact.right - tabWrapReact.right
    newOffset = Math.max(newOffset - delta - 16, containerWidth - contentWidth)
  }
  // 左侧按钮从无到有时，需要向左移动32px
  if (currentOffset.value === 0 && newOffset < 0) {
    newOffset = newOffset - 32
  }
  currentOffset.value = newOffset
  showLeftBtn.value = currentOffset.value < 0
  showRightBtn.value = currentOffset.value > containerWidth - contentWidth
}

const handleTabClick = (key, index) => {
  if (!tabRefs.value[index]) return
  if (prevActiveKey.value === key) {
    isExpand.value = !isExpand.value
    emit('expand', isExpand.value)
  } else {
    isExpand.value = true
    prevActiveKey.value = key
    activeKey.value = key
    emit('change', key)
    emit('expand', isExpand.value)
  }
  // doAnimate(index)
}

const handleToCommon = () => {
  highlightLeft.value = -tabCommonRef.value.offsetWidth
  highlightWidth.value = 0
  if (prevActiveKey.value === 'common') {
    isExpand.value = !isExpand.value
    emit('expand', isExpand.value)
  } else {
    activeKey.value = 'common'
    prevActiveKey.value = 'common'
    isExpand.value = true
    emit('change', 'common')
  }
}

const handleToCustom = () => {
  activeKey.value = 'custom'
  prevActiveKey.value = 'custom'
  doAnimateTocustom()
  emit('change', 'custom')
}

const doAnimateTocustom = () => {
  highlightWidth.value = tabCustomRef.value.offsetWidth
  highlightLeft.value =
    tabListRef.value.offsetWidth - tabRightRef.value.offsetWidth
}

const handleDragEnd = () => {
  // 拖拽结束时下划线需要跟随
  const index = schameList.value.findIndex(
    (item) => item.pagePreferenceId === activeKey.value,
  )
  if (index > -1) {
    doAnimate(index)
  }
}

const handleToExtraCustom = () => {
  activeKey.value = 'custom'
  prevActiveKey.value = 'custom'
  highlightLeft.value = tabWrapRef.value.scrollWidth
  highlightWidth.value = 0
  emit('change', 'custom')
}

const handleRightClick = () => {
  currentOffset.value = containerWidth - contentWidth - 32
  showLeftBtn.value = currentOffset.value < 0
  showRightBtn.value = currentOffset.value > containerWidth - contentWidth
}

const handleLeftClick = () => {
  currentOffset.value = 0
  showLeftBtn.value = currentOffset.value < 0
  showRightBtn.value = currentOffset.value > containerWidth - contentWidth
}

const handleEdit = () => {
  emit('edit')
}

defineExpose({
  doAnimate,
  handleRightClick,
  handleLeftClick,
})

onMounted(() => {
  observeResize()
})
onUnmounted(() => {
  if (resizeObserver) resizeObserver.disconnect()
})
</script>

<template>
  <div class="table-filter-tabs" v-if="filter">
    <div class="tab-nav" :class="{ 'tab-nav-no-expand': !isExpand }">
      <div
        class="tab-left-extra"
        :class="{ 'tab-left-extra-no-padding': !commonFilter && !showLeftBtn }"
      >
        <div
          class="tab-left-extra-item tab-item"
          ref="tabCommonRef"
          :class="{ 'tab-item-active': activeKey === 'common' }"
          @click="handleToCommon"
          v-if="commonFilter"
        >
          常规筛选
          <LyyIcon
            :prop="{ iconName: 'icon-xia_line' }"
            :class="[
              'expand-icon',
              isExpand || activeKey !== 'common'
                ? 'expand-icon-down'
                : 'expand-icon-up',
            ]"
          />
          <div
            class="common-tab-link-bar tab-link-bar ant-tabs-ink-bar-animated"
            :style="{
              opacity: activeKey === 'common' ? 1 : 0,
            }"
          ></div>
        </div>
        <LyyIcon
          v-if="showLeftBtn"
          :prop="{ iconName: 'icon-zuosanjiao_face' }"
          @click="handleLeftClick"
          class="tab-left-icon"
        />
      </div>
      <div
        class="tab-nav-wrap"
        :class="{
          'tab-nav-wrap-ping-left': showLeftBtn,
          'tab-nav-wrap-ping-right': showRightBtn,
        }"
        ref="tabWrapRef"
        @wheel.prevent="onWheel"
      >
        <div
          class="tab-nav-list"
          ref="tabListRef"
          :style="{ transform: `translateX(${currentOffset}px)` }"
        >
          <Draggable
            v-if="schameList.length > 0"
            class="tab-drag-wrap"
            :class="{ 'tab-drag-wrap-no-left-padding': !showLeftBtn }"
            :list="schameList"
            @end="handleDragEnd"
          >
            <template #item="{ element, index }">
              <div
                :ref="(el) => (tabRefs[index] = el)"
                class="tab-item"
                :class="{
                  'tab-item-active': element.pagePreferenceId === activeKey,
                }"
                @click="handleTabClick(element.pagePreferenceId, index)"
              >
                {{ element.name }}
                <LyyIcon
                  :prop="{ iconName: 'icon-xia_line' }"
                  :class="[
                    'expand-icon',
                    (isExpand && element.pagePreferenceId === activeKey) ||
                    element.pagePreferenceId !== activeKey
                      ? 'expand-icon-down'
                      : 'expand-icon-up',
                  ]"
                />
              </div>
            </template>
          </Draggable>
          <div
            class="tab-right-item"
            v-if="!showRightBtn && !showLeftBtn"
            ref="tabRightRef"
          >
            <div
              class="custome-add tab-item"
              ref="tabCustomRef"
              :class="{ 'tab-item-active': activeKey === 'custom' }"
              @click="handleToCustom"
            >
              <LyyIcon class="add-icon" :prop="{ iconName: 'icon-jia_line' }" />
              新建筛选
            </div>
            <template v-if="schameList.length > 0">
              <div class="divider-line"></div>
              <LyyIcon
                class="edit-icon"
                :prop="{ iconName: 'icon-bianjitwo_line' }"
                @click="handleEdit"
              />
            </template>
          </div>
          <div
            class="tab-link-bar ant-tabs-ink-bar-animated"
            :style="{
              width: `${highlightWidth}px`,
              transform: `translateX(${highlightLeft}px)`,
            }"
          ></div>
        </div>
      </div>
      <div class="tab-right-extra" v-if="showRightBtn || showLeftBtn">
        <LyyIcon
          v-if="showRightBtn"
          :prop="{ iconName: 'icon-yousanjiao_face' }"
          @click="handleRightClick"
          class="tab-right-icon"
        />
        <div
          class="tab-right-extra-item tab-item"
          :class="{ 'tab-item-active': activeKey === 'custom' }"
        >
          <div class="custome-add" @click="handleToExtraCustom">
            <LyyIcon class="add-icon" :prop="{ iconName: 'icon-jia_line' }" />
            新建筛选
            <div
              class="custom-tab-link-bar tab-link-bar ant-tabs-ink-bar-animated"
              :style="{
                opacity: activeKey === 'custom' ? 1 : 0,
              }"
            ></div>
          </div>
        </div>
        <template v-if="schameList.length > 0">
          <div class="divider-line"></div>
          <LyyIcon
            class="edit-icon"
            :prop="{ iconName: 'icon-bianjitwo_line' }"
            @click="handleEdit"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.table-filter-tabs {
  display: flex;
  align-items: center;
  padding: 0 12px 0;
  background-color: #fff;
  border-radius: 4px;
  // margin-bottom: 12px;

  .lyy-icon {
    :deep(.icon) {
      font-size: 16px !important;
    }
    display: inline-flex;
    cursor: pointer;
    transition: all 0.3s;
  }

  .tab-nav {
    display: flex;
    flex: none;
    width: 100%;
    position: relative;
    .expand-icon-up {
      transform: rotate(180deg);
    }
    .expand-icon-down {
      transform: rotate(0deg);
    }
    &::before {
      position: absolute;
      bottom: 0;
      right: 0;
      left: 0;
      border-bottom: 1px solid rgba(5, 5, 5, 0.06);
      content: '';
    }
    &.tab-nav-no-expand {
      &::before {
        display: none;
      }
    }
    .tab-nav-wrap {
      position: relative;
      display: flex;
      flex: auto;
      align-self: stretch;
      overflow: hidden;
      white-space: nowrap;
      transform: translate(0);
      &::before,
      &::after {
        position: absolute;
        width: 32px;
        content: '';
        top: 0px;
        bottom: 0px;
        // width: 2px;
        opacity: 0;
        transition: opacity 0.3s;
        // background: linear-gradient(
        //   180deg,
        //   rgba(0, 0, 0, 0) 0%,
        //   rgba(0, 0, 0, 0.25) 51%,
        //   rgba(0, 0, 0, 0) 100%
        // );
        // filter: blur(1px);
      }
      &::before {
        left: 0;
        box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.08);
      }
      &::after {
        right: 0;
        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.08);
        z-index: -1;
      }
      &.tab-nav-wrap-ping-left {
        &::before {
          opacity: 1;
        }
      }
      &.tab-nav-wrap-ping-right {
        &::after {
          opacity: 1;
        }
      }
    }
    .tab-nav-list {
      position: relative;
      display: flex;
      transition: transform 0.3s;
      .tab-drag-wrap {
        display: flex;
        gap: 16px;
        transition: opacity 0.3s;
        white-space: nowrap;
        cursor: pointer;
        padding: 0 8px;
        &.tab-drag-wrap-no-left-padding {
          padding-left: 0;
        }
      }
      .tab-right-item {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-left: 8px;
      }
    }
    .tab-item {
      position: relative;
      display: inline-flex;
      align-items: center;
      padding: 12px 0;
      font-size: 14px;
      background: transparent;
      border: 0;
      outline: none;
      cursor: pointer;
      &:hover {
        color: rgba(9, 135, 245, 1);
      }
      &.tab-item-active {
        color: rgba(9, 135, 245, 1);
      }
    }
    .tab-link-bar {
      position: absolute;
      background: #1677ff;
      pointer-events: none;
      height: 2px;
      bottom: 0;
      &.ant-tabs-ink-bar-animated {
        transition: width 0.3s, transform 0.3s;
      }
      &.common-tab-link-bar,
      &.custom-tab-link-bar {
        left: 0;
        right: 0;
        &.ant-tabs-ink-bar-animated {
          transition: opacity 0.4s;
        }
      }
    }
    .tab-left-extra,
    .tab-right-extra {
      display: flex;
      align-items: center;
      cursor: pointer;
      flex-shrink: 0;
    }
    .tab-left-extra {
      padding-right: 8px;
      &.tab-left-extra-no-padding {
        padding: 0;
      }
    }
    .tab-right-extra {
      gap: 16px;
      padding-left: 8px;
    }
    .tab-left-extra-item {
      position: relative;
      margin-right: 8px;
      display: flex;
      align-items: center;
      height: 100%;
      box-sizing: border-box;
    }
    .tab-right-extra-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 0;
      // margin-left: 16px;
      height: 100%;
      .custome-add {
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
      }
    }
    .divider-line {
      height: 16px;
      width: 1px;
      background: rgba(0, 0, 0, 0.08);
    }
    .tab-left-icon,
    .tab-right-icon {
      position: relative;
      height: 100%;
      display: flex;
      align-items: center;
    }
    .tab-left-icon {
      margin-left: 8px;
    }
    .tab-right-icon {
      // margin-right: -16px;
    }
    .custome-add {
      display: flex;
    }
  }
  .edit-icon {
    &:hover {
      color: #1677ff;
    }
  }
}
</style>
