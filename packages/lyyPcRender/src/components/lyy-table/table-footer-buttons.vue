<template>
  <div ref="containerRef" class="table-footer-buttons">
    <div class="footer-buttons">
      <!-- :class="{ 'button-hidden': !visibleButtons.includes(item.text) }" -->
      <LyyButton
        class="button"
        :prop="{ text: item.text, icon: item.icon, type: 'primary' }"
        v-for="item in visibleButtons"
        :key="item.key"
        @click="handleMenuClick(item)"
      />
    </div>
    <div class="footer-buttons-more" v-if="overflowButtons.length > 0">
      <a-dropdown
        placement="bottomLeft"
        :trigger="['click']"
        :overlayClassName="'table-footer-buttons-dropdown'"
      >
        <MoreOutlined />
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item v-for="item in overflowButtons" :key="item.key">
              <LyyIcon :prop="{ iconName: item.icon }" />
              {{ item.text }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeMount, watchEffect } from 'vue'
import LyyIcon from '../lyy-icon/index.vue'
import LyyButton from '../lyy-button/index.vue'
import { formula } from '@leyaoyao/libs'
import proxyData from '@leyaoyao/libs/utils/proxy-data'

type ButtonItem = {
  key: string
  text: string
  icon: string
  show: string
  authCode: string
}
const props = defineProps<{
  batchButtons: ButtonItem[]
}>()

const emit = defineEmits(['batchButtonClick'])

const authCodeList = ref([])
const getCurrentPageName = function () {
  const arr = location.hash.replace('#').split('/')
  return arr.at(-1).split('?')[0]
}
try {
  authCodeList.value =
    JSON.parse(sessionStorage.getItem('service-authCodeList'))[
      getCurrentPageName()
    ]?.buttonAuthCode || []
} catch {
  authCodeList.value = []
}

const containerRef = ref(null)

const elements = props.batchButtons.filter((item) => {
  const { show, authCode } = item
  const res = formula(show)(proxyData.getProxyData())
  const isAuth =
    authCode && authCodeList.value.length > 0
      ? authCodeList.value.includes(authCode)
      : true
  return res && isAuth
})

const visibleButtons = ref<ButtonItem[]>([...elements]) // 显示的按钮

const overflowButtons = ref<ButtonItem[]>([]) // 超出容器的按钮
let observer = null

const getButtonWidth = (buttonItem) => {
  const { text, icon } = buttonItem
  if (text.length <= 4) {
    const width = text.length * 14 + 12 + (icon ? 18 : 0)
    if (width < 70) {
      return 70
    }
    return width
  }
  if (text.length > 4) {
    const width = text.length * 14 + 12 + (icon ? 18 : 0)
    if (width < 100) {
      return 100
    }
    return width
  }
}

const adjustButtons = () => {
  const availableWidth = containerRef.value?.offsetWidth - 18
  let usedWidth = 0

  const visible: ButtonItem[] = []
  const overflow: ButtonItem[] = []

  for (let index = 0; index < elements.length; index++) {
    const button = elements[index]
    const buttonWidth = getButtonWidth(button) + 8
    if (usedWidth + buttonWidth <= availableWidth) {
      visible.push(button)
      usedWidth += buttonWidth
    } else {
      overflow.push(...elements.slice(index))
      break
    }
  }
  visibleButtons.value = visible
  overflowButtons.value = overflow
}

const handleMenuClick = ({ key }) => {
  emit('batchButtonClick', key)
}

onMounted(() => {
  observer = new ResizeObserver(() => {
    adjustButtons()
  })
  observer.observe(containerRef.value) // 监听容器宽度变化
})

onBeforeMount(() => {
  observer && observer.disconnect()
})
</script>
<style lang="scss" scoped>
.table-footer-buttons {
  flex: 1;
  overflow: hidden;
  display: flex;
  gap: 8px;
  align-items: center;
}
.footer-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  overflow: hidden;
  // .button-hidden {
  //   visibility: hidden;
  // }
  .middle-btn {
    flex-shrink: 0;
  }
}
</style>
