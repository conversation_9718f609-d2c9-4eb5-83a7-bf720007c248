<template>
  <!-- <setting-outlined @click="handleVisible" /> -->
  <div :class="{ 'table-column-setting-wrap': props.isInToolbar }">
    <LyyIcon
      class="table-column-setting"
      :prop="{ iconName: 'icon-shezhi_line' }"
      @click="handleVisible"
    />
  </div>

  <a-modal
    v-model:open="visible"
    title="列设置"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="show-detail-box" v-if="isShowCheckedDetail">
      <a-checkbox v-model:checked="isShowDetailSwitch">展示明细</a-checkbox>
    </div>
    <a-input
      v-model:value="keyword"
      placeholder="请输入关键字搜索"
      allowClear
      @input="handleSearch"
    />
    <div class="popover-header">
      <a-checkbox
        class="checkbox"
        v-model:checked="isCheckAll"
        @change="onCheckAllChange"
      >
        展示全部列
      </a-checkbox>
    </div>
    <div class="columns-checkbox-group" :style="{ maxHeight: contentHeight }">
      <a-tree
        :tree-data="filterColumns"
        :field-names="fieldNames"
        :draggable="!keyword"
        :checkable="true"
        :checkedKeys="checkedKeysList"
        :selectable="false"
        :block-node="true"
        :show-icon="true"
        @check="handleCheck"
        @drop="handleDrop"
      >
        <template #title="{ title, dataIndex, type }">
          <span>{{ title }}</span>
          <template
            v-if="
              checkedKeysList.includes(dataIndex) &&
              !['', '_no'].includes(dataIndex) &&
              type !== 'operation'
            "
          >
            <span style="padding: 12px">
              <PushpinFilled
                v-if="fixedKeys.includes(dataIndex)"
                @click="(e) => handleRemoveFixed(e, dataIndex)"
                style="transform: rotate(-45deg); color: #1677ff"
              />
              <PushpinOutlined
                v-else
                @click="(e) => handleFixed(e, dataIndex)"
                style="transform: rotate(-45deg)"
              />
            </span>
          </template>
        </template>
      </a-tree>
    </div>

    <template #footer>
      <a-button class="reset" type="default" @click="handleRestore">
        恢复默认
      </a-button>
      <a-button class="reset" type="default" @click="handleCancel">
        取消
      </a-button>
      <a-button class="reset" type="primary" @click="handleSave">保存</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { defineComponent, ref, computed, watch, onMounted } from 'vue'
import {
  SettingOutlined,
  PushpinOutlined,
  PushpinFilled,
} from '@ant-design/icons-vue'
import isObject from 'lodash/isObject'
import cloneDeep from 'lodash/cloneDeep'
import { Modal } from 'ant-design-vue'
import LyyIcon from '../lyy-icon/index.vue'

import { Column } from './type'

defineComponent({
  name: 'table-columns-setting',
})

const props = defineProps<{
  isEdit?: boolean
  isInToolbar?: boolean
  isShowDetail?: boolean
  columns: Column[]
  contentHeight?: number
  checkedList: string[]
  parentCompId: string
}>()
const emit = defineEmits(['save', 'restore', 'updateShowDetail'])
const visible = ref<boolean>(false)
const isCheckAll = ref<boolean>(false) // 全选状态
const keyword = ref<string>('') // 搜索关键词
const checkedKeysList = ref<string[]>([])
const fixedKeys = ref<string[]>([])
const isShowCheckedDetail = ref(false)
// 所有列
const allColumns = ref<Column[]>([])
const fieldNames = {
  key: 'dataIndex',
  title: 'title',
}
const contentHeight = computed(() => {
  return (props?.contentHeight ?? 500) + 'px'
})
const isShowDetailSwitch = computed({
  get: () => {
    for (const item of filterColumns.value) {
      item.disabled = !props.isShowDetail && item.isDetail ? true : false
    }
    return props.isShowDetail
  },
  set: (checked) => {
    emit('updateShowDetail', checked)
  },
})
const originCheckedList = ref<string[]>([])
const originFixedList = ref<string[]>([])

watch(
  () => props.checkedList,
  (newValue) => {
    if (Array.isArray(newValue)) {
      originCheckedList.value = [...newValue]
      handleCheck([...newValue])
    } else {
      const showCols = []
      for (const item of newValue?.columnKeys || []) {
        if (item.isShow) {
          showCols.push(item.dataIndex)
        }
      }
      // allColumns.value = handleColumnsWithSort(newValue?.columnKeys)
      originCheckedList.value = [...showCols]
      handleCheck([...showCols])
      originFixedList.value = [...newValue.fixedKeys]
      fixedKeys.value = [...newValue.fixedKeys]
    }
  },
  {
    deep: true,
    immediate: true,
  },
)
watch(
  () => props.columns,
  (newColumns) => {
    const cloneCols = cloneDeep(newColumns)
    for (const item of cloneCols) {
      if (item.isDetail) {
        isShowCheckedDetail.value = true
      }
      item.disabled = !props.isShowDetail && item.isDetail ? true : false
    }
    allColumns.value = cloneCols
  },
  {
    deep: true,
    immediate: true,
  },
)
// 展示列
const filterColumns = ref<Column[]>([])
watch(
  allColumns,
  (newColumns) => {
    filterColumns.value = cloneDeep(newColumns)
  },
  {
    deep: true,
    immediate: true,
  },
)

/**
 * @description 处理全选
 * @param event
 */
function onCheckAllChange(event: { target: { checked: boolean } }) {
  checkedKeysList.value = event.target.checked
    ? getAllNodeKeys(allColumns.value, [])
    : []
}

/**
 * @description 获取所有节点的 key 集合
 */
function getAllNodeKeys(list, preKeys): string[] {
  return list?.reduce((pre, node) => {
    pre.push(node.dataIndex)
    if (node?.children?.length) {
      getAllNodeCounts(node.children, pre)
    }
    return pre
  }, preKeys)
}

/**
 * @description 计算节点数量
 */
function getAllNodeCounts(list, preCount): number {
  return list?.reduce((pre, node) => {
    pre += 1
    if (node?.children?.length) {
      getAllNodeCounts(node.children, pre)
    }
    return pre
  }, preCount)
}

// 处理搜索
function handleSearch() {
  // 关键词转成小写，便于过滤关键词忽略字母大小写
  const lowerCaseKeyword = keyword.value.toLowerCase()
  filterColumns.value = allColumns.value?.filter(
    (item: { title: string | string[] }) => {
      // 标题转成小写，便于过滤关键词忽略字母大小写
      const lowerCaseTitle = item.title?.toLowerCase()
      if (lowerCaseTitle.includes(lowerCaseKeyword)) return item
    },
  )
}
/**
 * @desription 处理勾选回调
 * @param checkedKeys 所有选中节点的 key
 */
function handleCheck(checkedKeys) {
  if (props.isEdit) {
    return
  }
  checkedKeysList.value = [...checkedKeys]
  // 全部节点没有勾选
  if (checkedKeysList.value.length === 0) {
    isCheckAll.value = false
    return
  }
  const allNodeCounts = getAllNodeCounts(allColumns.value, 0)
  // 全部节点勾选
  if (allNodeCounts === checkedKeysList.value.length) {
    isCheckAll.value = true
    return
  }
  // 部分节点勾选
  isCheckAll.value = false
}

/**
 * @description 递归树节点，找到目标节点，做对应 callback
 */
function loop(data: any[], key: string | number, callback: any) {
  data.some((item, index) => {
    if (item?.dataIndex === key) {
      callback(item, index, data)
      return true
    }
    if (item?.children?.length) {
      return loop(item.children, key, callback)
    }
  })
}
/**
 * @description 处理拖拽排序
 */
function handleDrop(info) {
  const dropKey = info.node.key
  const dragKey = info.dragNode.key
  const dropPos = info.node.pos.split('-')
  /**
   * dropPosition = 0  移动到 dropNode 内部
   * dropPosition = -1 移动到 dropNode 前面
   * dropPosition = 1  移动到 dropNode 后面
   */
  const dropPosition = info.dropPosition - Number(dropPos.at(-1))
  // 拖拽到某个节点内部（目前视为拖拽到节点后面）
  if (info.dropToGap) {
    let newIndex // 节点拖拽后的新索引
    // 删除原来拖拽的节点
    loop(filterColumns.value, dragKey, (item, index: number, arr) => {
      arr.splice(index, 1)
    })
    // 插入拖拽后落下的节点
    loop(filterColumns.value, dropKey, (item, index: number, arr) => {
      newIndex = dropPosition === -1 ? index : index + 1
      arr.splice(newIndex, 0, cloneDeep(info.dragNode.dataRef))
    })
  } else {
    let newIndex // 节点拖拽后的新索引
    loop(filterColumns.value, dragKey, (item, index: number, arr) => {
      arr.splice(index, 1)
    })
    // 插入拖拽后落下的节点
    loop(filterColumns.value, dropKey, (item, index: number, arr) => {
      newIndex = dropPosition === -1 ? index : index + 1
      arr.splice(newIndex, 0, cloneDeep(info.dragNode.dataRef))
    })
  }
}

function handleVisible() {
  if (props.isEdit) return
  visible.value = true
}

function getSortableCheckedKeys(treeData, checkedKeys, fixedKeys) {
  const keys: string[] = []
  const sortableCheckedKeys: string[] = []
  const sortableFixedKeys: string[] = []

  for (const node of treeData) {
    if (isObject(node)) {
      keys.push(node.dataIndex)
    }

    if (checkedKeys?.includes(node.dataIndex)) {
      sortableCheckedKeys.push(node.dataIndex)
    }
    if (fixedKeys.includes(node.dataIndex)) {
      sortableFixedKeys.push(node.dataIndex)
    }
  }
  for (let i = sortableFixedKeys.length - 1; i >= 0; i--) {
    const curDataIndex = sortableFixedKeys[i]
    sortableCheckedKeys.splice(sortableCheckedKeys.indexOf(curDataIndex), 1)
    sortableCheckedKeys.unshift(curDataIndex)
  }
  return [sortableCheckedKeys, fixedKeys, keys]
}

// 恢复默认
function handleRestore() {
  const model = Modal.confirm({
    title: '提示',
    content: '确定恢复默认吗？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      emit('restore')
      visible.value = false
      for (const col of filterColumns.value) col.disabled = false
      model.destroy()
    },
    onCancel() {
      model.destroy()
      visible.value = false
    },
  })
}

// 保存
function handleSave() {
  // 由于树组件 checkedKeys 是没有按顺序返回，所以需要遍历数据获取按顺序的 checkedKeys
  const [sortableCheckedKeys, , sortTableKeys] = getSortableCheckedKeys(
    filterColumns.value,
    checkedKeysList.value,
    fixedKeys.value,
  )

  emit('save', sortableCheckedKeys, fixedKeys.value, sortTableKeys)
  visible.value = false
}

// 取消
function handleCancel() {
  handleCheck([...originCheckedList.value])
  fixedKeys.value = [...originFixedList.value]
  visible.value = false
}

const handleFixed = (e: Event, dataIndex: string) => {
  e.stopPropagation()
  fixedKeys.value.push(dataIndex)
}

const handleRemoveFixed = (e: Event, dataIndex: string) => {
  e.stopPropagation()
  const index = fixedKeys.value.indexOf(dataIndex)
  if (index > -1) {
    fixedKeys.value.splice(index, 1)
  }
}
</script>

<style lang="scss" scoped>
.show-detail-box {
  padding-bottom: 4px;
}
.popover-header {
  height: 32px;
  display: flex;
  align-items: center;

  .checkbox {
    flex: 1;
  }

  .reset {
    color: #1890ff;
    cursor: pointer;
    padding: 4px 6px;
  }
}

.columns-checkbox-group {
  overflow-y: auto;
}
.table-column-setting-wrap {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &:hover {
    background: #f5f5f5;
    border-radius: 4px;
  }
}
.table-column-setting {
  display: inline-flex;
  cursor: pointer;
  :deep(.icon) {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px !important;
  }
}
</style>
