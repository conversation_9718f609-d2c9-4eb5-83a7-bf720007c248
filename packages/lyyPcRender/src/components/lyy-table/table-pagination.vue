<template>
  <div class="table-pagination">
    <div class="total-wrap">共&nbsp;{{ total }}&nbsp;条数据</div>
    <div class="size-changer-wrap">
      <a-select
        class="size-changer"
        popupClassName="lyy-select-dropdown"
        v-model:value="pageSize"
        :options="pageSizeOptions"
        @change="handleSizeChange"
      ></a-select>
    </div>
    <div class="pagination-wrap">
      <LyyIcon
        class="first-btn page-btn"
        :class="{ 'btn-disabled': prevDisabled }"
        :prop="{ iconName: 'icon-zuizuo_line' }"
        @click="handleToFirst"
      />
      <LyyIcon
        class="prev-btn page-btn"
        :class="{ 'btn-disabled': prevDisabled }"
        :prop="{ iconName: 'icon-zuo_line' }"
        @click="handlePrev"
      />
      <div class="page-input-wrap">
        <a-input
          type="text"
          v-model:value="current"
          @pressEnter="handlePageChange"
        />
        /
        <span class="page-count">{{ totalPage }}</span>
      </div>

      <LyyIcon
        class="next-btn page-btn"
        :class="{ 'btn-disabled': nextDisabled }"
        :prop="{ iconName: 'icon-you_line' }"
        @click="handleNext"
      />

      <LyyIcon
        class="last-btn page-btn"
        :class="{ 'btn-disabled': nextDisabled }"
        :prop="{ iconName: 'icon-zuiyou_line' }"
        @click="handleToLast"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs, ref } from 'vue'
import LyyIcon from '../lyy-icon/index.vue'

const props = defineProps<{
  showSizeChanger: boolean
  pageSizeOptions: number[]
  current: number
  total: number
  pageSize: number
}>()

const emit = defineEmits(['update:current', 'change', 'update:pageSize'])

const { total } = toRefs(props)

const current = ref(props.current)

const pageSize = ref(props.pageSize)

const prevDisabled = computed(() => {
  return current.value == 1 || !total.value
})

const nextDisabled = computed(() => {
  return current.value == totalPage.value || !total.value
})

// const pageSize = computed({
//   get() {
//     return props.pageSize
//   },
//   set(val) {
//     emit('update:pageSize', val)
//   },
// })

// const current = computed({
//   get() {
//     return props.current
//   },
//   set(val) {
//     emit('update:current', val)
//   },
// })

const pageSizeOptions = computed(() => {
  return props.pageSizeOptions.map((item) => {
    return {
      label: `${item}条/页`,
      value: item,
    }
  })
})

const totalPage = computed(() => {
  return Math.ceil((total.value ?? 0) / pageSize.value)
})

const handlePageChange = (e) => {
  const val = Number(e.target.value)
  current.value = val
  emit('change', val, pageSize.value)
}

const handleSizeChange = (val: number) => {
  emit('change', current.value, val)
}

const handleToFirst = () => {
  if (prevDisabled.value) return
  current.value = 1
  emit('change', 1, pageSize.value)
}

const handleToLast = () => {
  if (nextDisabled.value) return
  current.value = totalPage.value
  emit('change', totalPage.value, pageSize.value)
}

const handlePrev = () => {
  if (prevDisabled.value) return
  current.value = Number(current.value) - 1
  emit('change', current.value, pageSize.value)
}

const handleNext = () => {
  if (nextDisabled.value) return
  current.value = Number(current.value) + 1
  emit('change', Number(current.value), pageSize.value)
}

const setCurrent = () => {
  current.value = 1
}

defineExpose({
  setCurrent,
})
</script>
<style lang="scss" scoped>
.table-pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  gap: 12px;
  .size-changer-wrap {
    width: 96px;
    .ant-select {
      width: 100%;
      :deep(.ant-select-selector) {
        padding: 0 8px;
      }
    }
  }
  .pagination-wrap {
    display: flex;
    align-items: center;
    .lyy-icon {
      width: 28px;
      height: 28px;
      display: inline-flex;
      justify-content: center;
      cursor: pointer;
      :deep(.icon) {
        font-size: 16px !important;
      }
      border-radius: 6px;
      &:hover {
        background-color: rgba(0, 0, 0, 0.06);
      }
      &:active {
        background-color: rgba(0, 0, 0, 0.15);
      }
      &.btn-disabled {
        cursor: not-allowed;
        color: rgba(0, 0, 0, 0.25);
        &:hover {
          background-color: transparent;
        }
      }
    }
  }
  .page-input-wrap {
    display: inline-flex;
    align-items: center;
    margin: 0 4px;
    .ant-input {
      width: 32px;
      padding: 4px 6px;
      margin-right: 4px;
      text-align: center;
    }
  }
  .page-count {
    margin-left: 2px;
  }
}
</style>
