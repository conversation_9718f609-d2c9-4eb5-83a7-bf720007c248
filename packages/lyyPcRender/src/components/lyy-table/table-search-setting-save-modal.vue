<template>
  <a-modal
    class="search-setting-modal"
    v-model:open="open"
    :title="title"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      name="custom-validation"
      class="form-scheme"
      :model="formState"
      labelAlign="right"
      :colon="true"
    >
      <a-form-item
        label="方案名称"
        name="name"
        :rules="[
          { required: true, message: '请输入方案名称', trigger: 'blur' },
        ]"
      >
        <a-input
          v-model:value="formState.name"
          type="text"
          placeholder="请输入方案名"
          autocomplete="off"
        />
      </a-form-item>
      <a-form-item label="方案描述" name="description">
        <a-textarea
          v-model:value="formState.description"
          placeholder="请输入方案描述"
          autocomplete="off"
          :autosize="{
            minRows: 3,
          }"
        ></a-textarea>
      </a-form-item>
      <!-- 共享 -->
      <TableSeachSettingShareComp
        v-model:assignEnable="formState.assignEnable"
        v-model:assignType="formState.assignType"
        v-model:authMemberIdList="formState.authMemberIdList"
      ></TableSeachSettingShareComp>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, defineComponent, onMounted, reactive, ref } from 'vue'
import { getCurrentPageName, useRequest } from '@leyaoyao/libs'
import TableSeachSettingShareComp from './table-search-setting-share-comp.vue'
import { min } from 'lodash'
const emit = defineEmits(['save', 'update:open'])
const props = defineProps<{
  schameName?: string
  open: boolean
  filters: object[]
}>()

const formRef = ref(null)

const open = computed({
  get() {
    return props.open
  },
  set(value) {
    emit('update:open', value)
  },
})

const title = computed(() => {
  if (props.schameName) {
    return '另存过滤方案'
  }
  return '保存过滤方案'
})

const getPageConfigureId = () => {
  const pageName = getCurrentPageName()
  const regex = /^\d+/
  const match = pageName.match(regex)
  const result = match ? match[0] : null
  return result
}

const formState = computed(() => {
  return reactive({
    name: props.schameName,
    type: 'custom_search',
    description: '',
    assignType: '',
    jsonstr: '',
    assignEnable: false,
    pageConfigureId: getPageConfigureId(),
    authMemberIdList: [],
  })
})

const handleOk = async () => {
  await formRef.value?.validateFields()
  const higher = []
  formState.value.jsonstr = JSON.stringify({ filters: props.filters })
  for (const item of Object.keys(formState.value)) {
    higher.push({
      key: item,
      value: formState.value[item],
    })
  }
  const res = await useRequest({
    url: '/page-preference',
    method: 'post',
    baseURL: '/gw/ram-service',
    payloads: {
      type: 'higher',
      static: '',
      higher,
    },
  })
  if (res) {
    open.value = false
    formRef.value?.resetFields()
    emit('save', res)
  }
}

defineComponent({
  name: 'table-search-setting-save-modal',
})
</script>

<style lang="less">
.search-setting-modal.ant-modal {
  .ant-modal-content {
    padding: 0 0 24px 0;
    .ant-modal-close-x {
      color: rgba(0, 0, 0, 0.88);
    }
  }
  .ant-modal-header {
    height: 56px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    background-color: rgba(245, 250, 255, 1);
    margin-bottom: 24px;
    .ant-modal-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
    }
  }
  .ant-modal-body,
  .ant-modal-footer {
    padding: 0 24px;
    margin-top: 24px;
  }
  .ant-modal-body {
    .ant-form-item {
      margin-bottom: 8px;
      &.share-setting {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<style scoped lang="less">
.record-filter-btn {
  margin-right: 8px;
  position: relative;
  .red {
    color: red;
    position: absolute;
    top: -7px;
    right: -2px;
  }
}
.form-scheme {
  :deep(.ant-form-item-label) {
    width: 86px;
  }
}
.list-scheme {
  margin-bottom: 8px;
  align-items: center;
}
.row-out {
  //width: 1120px;
  //padding-bottom: 8px;
  flex-wrap: nowrap;
  .width180 {
    width: 180px;
  }
  .label {
    height: 32px;
    line-height: 32px;
    flex-shrink: 0;
  }
  .options {
    display: flex;
    //flex-grow: 1;
    flex-wrap: wrap;
    margin-bottom: 4px;
    .row-option {
      //flex-grow: 1;
      align-items: center;
      margin-bottom: 8px;
      margin-right: 8px;
    }
    .select {
      width: 155px;
    }
    .op {
      width: 100px;
      margin: 0 5px;
    }
  }
  .buttons {
    flex-shrink: 0;
    button {
      padding: 0 8px 0 0;
    }
  }
  .opIcon {
    margin-left: 8px;
  }
}
</style>
