<script lang="ts" setup>
import {
  defineComponent,
  ref,
  defineEmits,
  onBeforeUnmount,
  onBeforeMount,
  computed,
  inject,
  Ref,
} from 'vue'
import { useShow } from '@leyaoyao/libs/hooks'
import { useI18n } from 'vue-i18n'
import LyyIcon from '../lyy-icon/index.vue'
const { t } = useI18n()

const emit = defineEmits(['find', 'clear'])
defineComponent({
  name: 'table-search',
})

const isEdit = inject<Ref<boolean>>('isEdit')

const props = defineProps<{
  children: IElement[]
  formStorage: boolean
  enableEnterShortcut?: boolean
}>()

const MediaQueryEnum = {
  // md: {
  //   matchMedia: '(min-width: 768px) and (max-width: 991px)',
  //   count: 2,
  //   span: 12,
  // },
  lg: {
    matchMedia: '(min-width: 1024px) and (max-width: 1379px)',
    count: 2,
    span: 12,
  },
  xl: {
    matchMedia: '(min-width: 1380px) and (max-width: 1599px)',
    count: 3,
    span: 8,
  },
  xxl: {
    matchMedia: '(min-width: 1600px) and (max-width: 1999px)',
    count: 4,
    span: 6,
  },
  xxxl: {
    matchMedia: '(min-width: 2000px)',
    count: 6,
    span: 4,
  },
}

const isInModal = inject('isInModal')
const modalSize = inject('modalSize')

const modalSizeObj = {
  large: 12,
  middle: 24,
  small: 24,
}

const isExpand = ref(false)

const searchRef = ref(null)

const mediaHandler = ref({})

const alwaysShowCount = ref(4)

const autoSpan = ref(6)

const span = computed(() => {
  if (isInModal) {
    return modalSizeObj[modalSize as string]
  }
  return autoSpan.value
})

const elements = computed(() => {
  if (isEdit?.value) {
    return props.children ?? []
  }
  const items = []
  for (const e of props.children) {
    if (useShow(e, null)) {
      items.push(e)
    }
  }
  return items
})

const getMore = () => {
  isExpand.value = !isExpand.value
}

const find = () => {
  if (isEdit?.value) {
    return
  }
  emit('find')
}

// 重置
const clear = () => {
  if (isEdit?.value) {
    return
  }
  emit('clear')
}

const handleEnter = (event) => {
  if (event.keyCode === 13) {
    const activeElement = document.activeElement
    if (searchRef.value?.contains(activeElement)) {
      // 由于日期选择器在聚焦的状态下，按下enter键，日历弹窗会弹出，故需将当前激活的元素失焦
      activeElement && activeElement.blur()
      find()
    } else if (activeElement === document.body) {
      find()
    }
  }
}

onBeforeMount(() => {
  if (isInModal || modalSize === 'fullScreen') return
  for (const screen of Object.keys(MediaQueryEnum)) {
    const matchMediaQuery = MediaQueryEnum[screen].matchMedia
    const mql = window.matchMedia(matchMediaQuery)
    const listener = (mql) => {
      if (mql.matches) {
        alwaysShowCount.value = MediaQueryEnum[screen].count
        autoSpan.value = MediaQueryEnum[screen].span
      }
    }
    mql.addEventListener('change', listener)
    mediaHandler.value[matchMediaQuery] = {
      mql,
      listener,
    }
    listener(mql)
  }
  if (props.enableEnterShortcut || props.enableEnterShortcut == undefined) {
    document.addEventListener('keydown', handleEnter)
  }
})

onBeforeUnmount(() => {
  for (const screen of Object.keys(MediaQueryEnum)) {
    const matchMediaQuery = MediaQueryEnum[screen].matchMedia
    if (mediaHandler.value[matchMediaQuery]) {
      const { mql, listener } = mediaHandler.value[matchMediaQuery]
      mql.removeEventListener('change', listener)
    }
  }
  if (props.enableEnterShortcut || props.enableEnterShortcut == undefined) {
    document.removeEventListener('keydown', handleEnter)
  }
})
</script>

<template>
  <div class="table-search" ref="searchRef">
    <div class="table-search-item">
      <a-row :gutter="24" type="flex" :wrap="true" :align="'top'">
        <template v-for="(child, index) in elements" :key="index">
          <a-col v-show="index < alwaysShowCount || isExpand" :span="span">
            <pc-render-template :elements="[child]"></pc-render-template>
          </a-col>
        </template>
      </a-row>
    </div>
    <div class="operation-wrap">
      <div
        class="expand-wrap"
        v-if="elements && elements.length > alwaysShowCount"
        @click="getMore"
      >
        <div class="point mr-4">{{ isExpand ? t('收起') : t('更多') }}</div>
        <LyyIcon
          :prop="{ iconName: 'icon-xia_line' }"
          :class="[
            'expand-icon',
            isExpand ? 'expand-icon-up' : 'expand-icon-down',
          ]"
        />
      </div>
      <div class="button-list">
        <a-button class="button-item" @click="clear" type="text">
          <template #icon>
            <LyyIcon
              class="button-icon"
              :prop="{ iconName: 'icon-qingchu_line' }"
            />
          </template>
          {{ t('重置') }}
        </a-button>
        <a-button class="button-item" type="primary" @click="find">
          <template #icon>
            <LyyIcon
              class="button-icon"
              :prop="{ iconName: 'icon-sousuo_line' }"
            />
          </template>
          {{ t('查找') }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.table-search {
  display: flex;
  gap: 16px;
  padding: 12px 12px 0;
  border-radius: 4px;
  :deep(.ant-form-item) {
    margin-bottom: 12px !important;
  }
  .table-search-item {
    flex: 1;
  }
  .operation-wrap {
    width: 200px;
    height: 32px;
    flex-shrink: 0;
    display: flex;
    gap: 20px;
    .expand-wrap {
      display: flex;
      gap: 2px;
      align-items: center;
      color: #0987f5;
      cursor: pointer;
    }
    .button-list {
      flex: 1;
      display: flex;
      gap: 8px;
    }
  }
}
.button-list {
  display: flex;
  justify-content: right;
  align-items: top;
  gap: 16px;
  .button-item {
    display: inline-flex;
    min-width: 62px;
    font-size: 14px;
    align-items: center;
    justify-content: center;
    :deep(.icon) {
      font-size: 16px !important;
    }
  }
}

.button-icon {
  margin-right: 3px;
  display: inline-flex;
  align-items: center;
}

.expand-icon {
  :deep(.icon) {
    font-size: 16px !important;
  }
  display: inline-flex;
  cursor: pointer;
  transition: all 0.3s;
}
.expand-icon-up {
  transform: rotate(180deg);
}
.expand-icon-down {
  transform: rotate(0deg);
}

.mw-70 {
  min-width: 70px;
}

:deep(.ant-btn) {
  padding: 0;
}
</style>
