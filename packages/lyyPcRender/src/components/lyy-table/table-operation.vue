<script setup lang="ts">
import { computed, defineEmits, ref, onMounted } from 'vue'
import { MoreOutlined } from '@ant-design/icons-vue'
import { formula, usePayload } from '@leyaoyao/libs'
import proxyData from '../../../../libs/utils/proxy-data'
import _ from 'lodash'

const props = defineProps<{
  max?: number
  // eslint-disable-next-line no-undef
  childrens: IComponent[]
  isEdit?: boolean
  rowData: object
  isPreview?: boolean
}>()
const emit = defineEmits(['capture'])

const visibleChange = (visible) => {
  if (visible) emit('capture')
}

const authCodeList = ref([])
const getCurrentPageName = function () {
  const arr = location.hash.replace('#').split('/')
  return arr[arr.length - 1].split('?')[0]
}
try {
  authCodeList.value =
    JSON.parse(sessionStorage.getItem('service-authCodeList'))[
      getCurrentPageName()
    ]?.buttonAuthCode || []
} catch {
  authCodeList.value = []
}

const elements = computed(() => {
  return props.childrens
    ?.filter((item) => {
      const { exp } = item.prop?.show ?? {}
      if (exp) {
        const data = Object.assign(
          {},
          proxyData.getProxyData('elements'),
          props.rowData,
        )
        return formula(exp)(data) ? item : undefined
      }
      return item
    })
    .filter((btn) => {
      return (
        props.isPreview ||
        !btn.prop.authCode ||
        (btn.prop.authCode && authCodeList.value.includes(btn.prop?.authCode))
      )
    })
})

const max = computed(() => props.max ?? 3)
</script>

<template>
  <div class="table-operation">
    <template v-if="isEdit">
      <div class="operation-btns">
        <pc-render-template
          :elements="props.childrens"
          :rowData="rowData"
        ></pc-render-template>
      </div>
    </template>
    <template v-else>
      <div v-if="elements.length > max" class="operation-btns">
        <pc-render-template
          :elements="elements.slice(0, max - 1)"
          :rowData="rowData"
        ></pc-render-template>
        <a-dropdown trigger="click">
          <a class="ant-dropdown-link">
            <!-- 更多 -->
            <MoreOutlined class="more" />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item
                v-for="(element, index) in elements.slice(max - 1)"
                :key="index"
              >
                <div class="lyy-table-operation-dropdown-content">
                  <pc-render-template
                    :elements="[element]"
                    :rowData="rowData"
                    @action="visibleChange"
                  />
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

      <div v-else class="operation-btns">
        <pc-render-template
          v-if="elements"
          :elements="elements"
          :rowData="rowData"
        ></pc-render-template>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
.ant-dropdown-link {
  display: inline-block;
}

.lyy-table-operation-dropdown-content {
  display: flex;

  :deep(.ant-btn.ant-btn-link) {
    flex: 1;
    justify-content: flex-start;
  }
}

.operation-btns {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  column-gap: 12px;

  padding-right: 16px;
  height: 22px;
  .draggable-wrap {
    display: inherit;
    flex: 1;
    align-items: inherit;
    justify-content: inherit;
    column-gap: inherit;
  }
}

.more {
  transform: rotate(90deg);
}
</style>
