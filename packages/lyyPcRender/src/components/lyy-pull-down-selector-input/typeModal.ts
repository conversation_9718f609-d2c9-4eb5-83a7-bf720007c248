import type { ButtonType } from 'ant-design-vue/es/button'

type FieldName = {
  /**
   * @description 映射的原始字段
   */
  origin: string | Array<string>
  /**
   * @description 映射的目标字段
   */
  target: string
  /**
   * @description 多选数据合并配置
   */
  mergeOption?: {
    // default: 合并成数组
    /**
     * @description 合并方式：默认合并成数组
     */
    type: 'join' | 'sum' | 'default'
    /**
     * @description join时的连接符
     */
    seperator?: string
    /**
     * @description 求和时的保留小数位数
     */
    fixed?: number
    /**
     * @description 求和时数字格式化类型
     */
    format?: 'percent' | 'thousands'
  }
}
type ConfirmVerify = {
  conditions: string[]
  tip: string
}

export interface IModalSelectorBase {
  /**
   * @description formId
   */
  formId?: string

  /**
   * @description 模态框标题
   */
  title: string
  /**
   * @description 模态框宽度
   */
  width?: string | number
  /**
   * @description 确认按钮文本
   */
  okText?: string
  /**
   * @description 确认按钮类型
   */
  okType?: ButtonType
  /**
   * @description 取消按钮文本
   */
  cancelText?: string
  /**
   * @description 是否多选
   */
  multiple?: boolean
  /**
   * @description 是否展示右上角关闭按钮
   */
  closable?: boolean
  /**
   * @description 是否展示遮罩
   */
  mask?: boolean
  /**
   * @description 点击蒙层是否允许关闭
   */
  maskClosable?: boolean
  /**
   * @description 是否垂直居中展示模态框
   */
  centered?: boolean
  /**
   * @description 关闭时是否销毁子元素
   */
  destroyOnClose?: boolean
  /**
   * @description 关联的table的compId
   */
  tableCompId: string
}
export interface IModalSelectorProp extends IModalSelectorBase {
  /**
   * @description 多选时是否合并数据
   */
  merge?: boolean
  /**
   * @description 需要回显数据字段映射
   */
  fieldNames?: Array<FieldName>
  /**
   * @description 确定按钮点击条件拦截
   */
  confirmVerify?: ConfirmVerify
  /**
   * 是否将选中的值更新至关联的form
   */
  linkForm?: boolean
}
