<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'permission-panel-usage-example'
})
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import LyyPermissionPanel from './index.vue'
import { processPermissionData } from './utils'
import mockData from './mock-data'
import type { Key } from './interface'

// 后端原始数据
const backendData = ref(mockData)

// 处理后的权限数据
const processedData = computed(() => {
  if (!backendData.value?.body?.resources) {
    return { treeData: [], selectedKeys: [] }
  }
  
  return processPermissionData(backendData.value.body.resources)
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 角色信息
const roleInfo = computed(() => {
  return backendData.value?.body ? {
    roleId: backendData.value.body.roleId,
    roleName: backendData.value.body.roleName,
    roleRemark: backendData.value.body.roleRemark
  } : null
})

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
}

// 保存权限到后端
const savePermissions = async () => {
  try {
    // 构建要发送给后端的数据
    const saveData = {
      roleId: roleInfo.value?.roleId,
      selectedPermissions: selectedKeys.value
    }
    
    console.log('保存权限数据:', saveData)
    
    // 这里调用实际的API
    // const response = await fetch('/api/permissions/save', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(saveData)
    // })
    
    // if (response.ok) {
    //   console.log('权限保存成功')
    // }
    
    alert('权限保存成功！(模拟)')
  } catch (error) {
    console.error('保存权限失败:', error)
    alert('保存权限失败！')
  }
}

// 重置权限
const resetPermissions = () => {
  selectedKeys.value = [...processedData.value.selectedKeys]
}

// 清空所有权限
const clearAllPermissions = () => {
  selectedKeys.value = []
}

// 统计信息
const statistics = computed(() => {
  const total = processedData.value.treeData.length
  const selected = selectedKeys.value.length
  const defaultSelected = processedData.value.selectedKeys.length
  
  return {
    total,
    selected,
    defaultSelected,
    selectionRate: total > 0 ? ((selected / total) * 100).toFixed(1) : '0'
  }
})

// 初始化
onMounted(() => {
  // 设置初始选中状态
  selectedKeys.value = [...processedData.value.selectedKeys]
})

// 模拟从后端加载数据
const loadPermissionData = async (roleId: string) => {
  try {
    console.log('加载角色权限:', roleId)
    
    // 模拟API调用
    // const response = await fetch(`/api/permissions/${roleId}`)
    // const data = await response.json()
    // backendData.value = data
    
    // 使用模拟数据
    console.log('使用模拟数据')
    
  } catch (error) {
    console.error('加载权限数据失败:', error)
  }
}

// 导出权限配置
const exportPermissions = () => {
  const exportData = {
    roleInfo: roleInfo.value,
    selectedPermissions: selectedKeys.value,
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `permissions-${roleInfo.value?.roleName || 'unknown'}-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
</script>

<template>
  <div class="permission-usage-example">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>权限管理面板使用示例</h1>
      <p>演示如何使用权限面板组件处理后端数据</p>
    </div>

    <!-- 角色信息卡片 -->
    <div v-if="roleInfo" class="role-card">
      <a-card title="角色信息" size="small">
        <a-descriptions :column="3" size="small">
          <a-descriptions-item label="角色ID">
            {{ roleInfo.roleId }}
          </a-descriptions-item>
          <a-descriptions-item label="角色名称">
            {{ roleInfo.roleName }}
          </a-descriptions-item>
          <a-descriptions-item label="角色备注">
            {{ roleInfo.roleRemark }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <a-space>
        <a-button type="primary" @click="savePermissions">
          <template #icon>
            <save-outlined />
          </template>
          保存权限
        </a-button>
        
        <a-button @click="resetPermissions">
          <template #icon>
            <reload-outlined />
          </template>
          重置为默认
        </a-button>
        
        <a-button @click="clearAllPermissions">
          <template #icon>
            <clear-outlined />
          </template>
          清空所有
        </a-button>
        
        <a-button @click="exportPermissions">
          <template #icon>
            <download-outlined />
          </template>
          导出配置
        </a-button>
        
        <a-button @click="loadPermissionData('1109525')">
          <template #icon>
            <sync-outlined />
          </template>
          重新加载
        </a-button>
      </a-space>
    </div>

    <!-- 统计信息 -->
    <div class="statistics">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="权限节点总数" :value="statistics.total" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="当前选中" :value="statistics.selected" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="默认选中" :value="statistics.defaultSelected" />
        </a-col>
        <a-col :span="6">
          <a-statistic 
            title="选中率" 
            :value="statistics.selectionRate" 
            suffix="%" 
          />
        </a-col>
      </a-row>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限配置" size="small">
        <lyy-permission-panel
          v-if="processedData.treeData.length > 0"
          :value="selectedKeys"
          @change="handlePermissionChange"
        />
        <div v-else class="empty-state">
          <a-empty description="暂无权限数据" />
        </div>
      </a-card>
    </div>

    <!-- 调试信息 -->
    <div class="debug-section">
      <a-collapse>
        <a-collapse-panel key="backend" header="后端原始数据">
          <pre class="json-display">{{ JSON.stringify(backendData, null, 2) }}</pre>
        </a-collapse-panel>
        
        <a-collapse-panel key="processed" header="处理后的数据">
          <pre class="json-display">{{ JSON.stringify(processedData, null, 2) }}</pre>
        </a-collapse-panel>
        
        <a-collapse-panel key="selected" header="当前选中的权限">
          <pre class="json-display">{{ JSON.stringify(selectedKeys, null, 2) }}</pre>
        </a-collapse-panel>
      </a-collapse>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <a-card title="使用说明" size="small">
        <div class="guide-content">
          <h4>数据处理流程：</h4>
          <ol>
            <li>从后端获取权限数据（包含 resources 数组和 selected 字段）</li>
            <li>使用 <code>processPermissionData</code> 函数转换数据格式</li>
            <li>将转换后的数据传递给权限面板组件</li>
            <li>监听权限变更事件，获取用户选择的权限</li>
            <li>保存时将选中的权限发送给后端</li>
          </ol>
          
          <h4>关键函数：</h4>
          <ul>
            <li><code>processPermissionData(resources)</code> - 转换后端数据为组件格式</li>
            <li><code>@change</code> - 监听权限选择变更</li>
            <li><code>selectedKeys</code> - 当前选中的权限键值数组</li>
          </ul>
          
          <h4>数据结构说明：</h4>
          <ul>
            <li><strong>resources</strong>: 后端返回的权限资源数组</li>
            <li><strong>selected</strong>: 布尔值，表示该权限是否被选中</li>
            <li><strong>children</strong>: 子权限节点数组</li>
            <li><strong>actions</strong>: 叶子节点的操作权限列表</li>
          </ul>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="less">
.permission-usage-example {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .role-card,
  .toolbar,
  .statistics,
  .permission-panel,
  .debug-section,
  .usage-guide {
    margin-bottom: 16px;
  }

  .toolbar {
    background: #fff;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .statistics {
    background: #fff;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .json-display {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 12px;
    color: #586069;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .guide-content {
    h4 {
      color: #262626;
      margin: 16px 0 8px 0;
      
      &:first-child {
        margin-top: 0;
      }
    }

    ol, ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        line-height: 1.6;
        color: #595959;
      }
    }

    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      color: #d73a49;
    }
  }
}
</style>
