<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'data-transform-demo'
})
</script>

<script setup lang="ts">
import { ref, computed } from 'vue'
import LyyPermissionPanel from './index.vue'
import {
  transformBackendPermissionData,
  extractSelectedPermissions,
  transformToBackendFormat,
  filterPermissionsByLevel,
  searchPermissionNodes,
  validatePermissionData,
  type BackendPermissionResponse
} from './utils'
import mockData from './mock-data'
import type { PermissionAction, PermissionLevel } from './types'

// 后端原始数据
const backendData = ref<BackendPermissionResponse>(mockData as BackendPermissionResponse)

// 验证数据有效性
const isDataValid = computed(() => {
  return validatePermissionData(backendData.value)
})

// 转换后的数据
const transformedData = computed(() => {
  if (!isDataValid.value) return null
  return transformBackendPermissionData(backendData.value)
})

// 当前权限状态
const currentPermissions = ref<Record<string, PermissionAction[]>>({})

// 当前权限级别
const currentLevel = ref<PermissionLevel>('view')

// 搜索关键词
const searchKeyword = ref('')

// 过滤后的权限数据
const filteredPermissionData = computed(() => {
  if (!transformedData.value) return []
  
  let data = transformedData.value.treeData
  
  // 按级别过滤
  data = filterPermissionsByLevel(data, currentLevel.value)
  
  // 按关键词搜索
  if (searchKeyword.value.trim()) {
    data = searchPermissionNodes(data, searchKeyword.value)
  }
  
  return data
})

// 角色信息
const roleInfo = computed(() => {
  return transformedData.value?.roleInfo || null
})

// 初始选中的权限
const initialSelectedPermissions = computed(() => {
  if (!transformedData.value) return {}
  return transformedData.value.selectedPermissions
})

// 处理权限保存
const handleSave = (permissions: Record<string, PermissionAction[]>) => {
  console.log('保存权限:', permissions)
  currentPermissions.value = permissions
  
  // 转换回后端格式
  const backendFormat = transformToBackendFormat(backendData.value, permissions)
  console.log('后端格式数据:', backendFormat)
  
  // 这里可以调用API保存
  // await savePermissionsToBackend(backendFormat)
}

// 处理权限变更
const handleChange = (permissions: Record<string, PermissionAction[]>) => {
  console.log('权限变更:', permissions)
  currentPermissions.value = permissions
}

// 重新加载数据
const reloadData = () => {
  // 模拟重新从后端加载数据
  console.log('重新加载权限数据...')
  
  // 重置状态
  currentPermissions.value = initialSelectedPermissions.value
  searchKeyword.value = ''
}

// 导出当前权限配置
const exportPermissions = () => {
  const exportData = {
    roleInfo: roleInfo.value,
    permissions: currentPermissions.value,
    timestamp: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `permissions-${roleInfo.value?.roleName || 'unknown'}-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 统计信息
const statistics = computed(() => {
  if (!transformedData.value) return null
  
  const { treeData } = transformedData.value
  let totalNodes = 0
  let selectedNodes = 0
  let moduleCount = 0
  let menuCount = 0
  let buttonCount = 0
  
  const traverse = (nodes: any[]) => {
    nodes.forEach(node => {
      totalNodes++
      
      if (currentPermissions.value[node.key]) {
        selectedNodes++
      }
      
      switch (node.type) {
        case 'module':
          moduleCount++
          break
        case 'menu':
          menuCount++
          break
        case 'button':
          buttonCount++
          break
      }
      
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
  
  return {
    totalNodes,
    selectedNodes,
    moduleCount,
    menuCount,
    buttonCount,
    selectionRate: totalNodes > 0 ? ((selectedNodes / totalNodes) * 100).toFixed(1) : '0'
  }
})
</script>

<template>
  <div class="data-transform-demo">
    <div class="demo-header">
      <h2>权限数据转换演示</h2>
      <div class="demo-controls">
        <a-space>
          <a-select v-model:value="currentLevel" style="width: 120px">
            <a-select-option value="view">查看权限</a-select-option>
            <a-select-option value="data">数据权限</a-select-option>
            <a-select-option value="field">字段权限</a-select-option>
          </a-select>
          
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索权限节点"
            style="width: 200px"
            allow-clear
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
          
          <a-button @click="reloadData">
            <reload-outlined />
            重新加载
          </a-button>
          
          <a-button @click="exportPermissions">
            <download-outlined />
            导出配置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据验证状态 -->
    <div class="validation-status">
      <a-alert
        v-if="!isDataValid"
        message="数据格式错误"
        description="后端返回的权限数据格式不符合预期，请检查数据结构。"
        type="error"
        show-icon
      />
      <a-alert
        v-else
        message="数据验证通过"
        description="后端权限数据格式正确，已成功转换为组件所需格式。"
        type="success"
        show-icon
      />
    </div>

    <!-- 角色信息 -->
    <div v-if="roleInfo" class="role-info">
      <a-descriptions title="角色信息" :column="3" bordered>
        <a-descriptions-item label="角色ID">{{ roleInfo.roleId }}</a-descriptions-item>
        <a-descriptions-item label="角色名称">{{ roleInfo.roleName }}</a-descriptions-item>
        <a-descriptions-item label="角色备注">{{ roleInfo.roleRemark }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 统计信息 -->
    <div v-if="statistics" class="statistics">
      <a-row :gutter="16">
        <a-col :span="4">
          <a-statistic title="总节点数" :value="statistics.totalNodes" />
        </a-col>
        <a-col :span="4">
          <a-statistic title="已选节点" :value="statistics.selectedNodes" />
        </a-col>
        <a-col :span="4">
          <a-statistic title="模块数量" :value="statistics.moduleCount" />
        </a-col>
        <a-col :span="4">
          <a-statistic title="菜单数量" :value="statistics.menuCount" />
        </a-col>
        <a-col :span="4">
          <a-statistic title="按钮数量" :value="statistics.buttonCount" />
        </a-col>
        <a-col :span="4">
          <a-statistic title="选中率" :value="statistics.selectionRate" suffix="%" />
        </a-col>
      </a-row>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel-container">
      <lyy-permission-panel
        v-if="transformedData"
        :permission-data="filteredPermissionData"
        :default-level="currentLevel"
        @save="handleSave"
        @change="handleChange"
      />
      <div v-else class="loading-placeholder">
        <a-spin size="large">
          <template #tip>正在加载权限数据...</template>
        </a-spin>
      </div>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
      <a-collapse>
        <a-collapse-panel key="1" header="原始后端数据">
          <pre class="json-display">{{ JSON.stringify(backendData, null, 2) }}</pre>
        </a-collapse-panel>
        <a-collapse-panel key="2" header="转换后的数据">
          <pre class="json-display">{{ JSON.stringify(transformedData, null, 2) }}</pre>
        </a-collapse-panel>
        <a-collapse-panel key="3" header="当前权限状态">
          <pre class="json-display">{{ JSON.stringify(currentPermissions, null, 2) }}</pre>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<style scoped lang="less">
.data-transform-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .demo-header {
    background: #fff;
    padding: 16px 24px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      color: #262626;
    }
  }

  .validation-status,
  .role-info,
  .statistics,
  .permission-panel-container,
  .debug-info {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .json-display {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 12px;
    max-height: 400px;
    overflow-y: auto;
    font-size: 12px;
    color: #586069;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
