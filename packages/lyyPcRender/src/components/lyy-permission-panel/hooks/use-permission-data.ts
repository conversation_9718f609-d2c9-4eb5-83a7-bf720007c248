import { reactive, ref, watch, computed } from 'vue'

import { PermissionDataTreeNode, Key, PermissionAction } from '../interface'

interface IUsePermissionData {
  defaultSelectedKeys?: Key[]
  data?: PermissionDataTreeNode[]
}

/**
 * 节点选择状态接口
 */
interface NodeSelectionState {
  isSelected: boolean
  isIndeterminate: boolean
  selectedActions: Key[]
}

/**
 * 权限数据管理 Hook
 * 使用函数式编程思想，提供纯函数操作和完整的状态联动
 *
 * @param options 配置选项
 * @returns 权限管理相关的状态和方法
 */
export function usePermissionData({
  defaultSelectedKeys = [],
  data = [],
}: IUsePermissionData) {
  const selectedKeys = ref<Key[]>([...defaultSelectedKeys])

  // 构建节点映射表，提高查找效率
  const nodeMap = computed(() => {
    const map = new Map<Key, PermissionDataTreeNode>()

    const buildMap = (nodes: PermissionDataTreeNode[]) => {
      nodes.forEach(node => {
        map.set(node.key, node)
        if (node.children) {
          buildMap(node.children)
        }
      })
    }

    buildMap(data)
    return map
  })

  return {
    /** 响应式数据 */
    selectedKeys,

    /** 计算属性 */
    nodeMap,

    /** 核心方法 */
    updateSelectedKeys,
    onCheckedChange,
    getNodeSelectionState,
    getAllChildrenKeys,
    getAllParentKeys,

    /** 工具方法 */
    isNodeSelected,
    isNodeIndeterminate,
    getSelectedActionsForNode,
  }

  /**
   * 更新选中的权限keys
   * @param keys 新的选中keys数组
   */
  function updateSelectedKeys(keys: Key[]) {
    selectedKeys.value = [...keys]
  }

  /**
   * 处理节点选中状态变化 - 核心联动逻辑
   * @param key 节点key
   * @param checked 是否选中
   * @param nodeKey 节点的key（用于查找节点信息）
   */
  function onCheckedChange(key: Key, checked: boolean, nodeKey: Key) {
    const node = nodeMap.value.get(nodeKey)
    if (!node) return

    if (checked) {
      handleCheckItem(key, node)
    } else {
      handleUncheckItem(key, node)
    }
  }

  /**
   * 处理选中操作 - 纯函数式实现
   * @param key 被选中的key
   * @param node 节点信息
   */
  function handleCheckItem(key: Key, node: PermissionDataTreeNode) {
    const newSelectedKeys = new Set(selectedKeys.value)

    // 1. 选中当前节点
    newSelectedKeys.add(key)

    // 2. 如果选中的是节点本身（全选），则选中所有子节点和actions
    if (key === node.key) {
      const allChildKeys = getAllChildrenKeys(node.key)
      allChildKeys.forEach(childKey => newSelectedKeys.add(childKey))
    }

    // 3. 检查并更新父节点状态
    updateParentNodesOnCheck(node, newSelectedKeys)

    // 4. 更新状态
    selectedKeys.value = Array.from(newSelectedKeys)
  }

  /**
   * 处理取消选中操作 - 纯函数式实现
   * @param key 被取消选中的key
   * @param node 节点信息
   */
  function handleUncheckItem(key: Key, node: PermissionDataTreeNode) {
    const newSelectedKeys = new Set(selectedKeys.value)

    // 1. 取消选中当前节点
    newSelectedKeys.delete(key)

    // 2. 如果取消的是节点本身（全选），则取消所有子节点和actions
    if (key === node.key) {
      const allChildKeys = getAllChildrenKeys(node.key)
      allChildKeys.forEach(childKey => newSelectedKeys.delete(childKey))
    }

    // 3. 检查并更新父节点状态
    updateParentNodesOnUncheck(node, newSelectedKeys)

    // 4. 更新状态
    selectedKeys.value = Array.from(newSelectedKeys)
  }

  /**
   * 选中时更新父节点状态
   * @param node 当前节点
   * @param selectedKeysSet 选中的keys集合
   */
  function updateParentNodesOnCheck(node: PermissionDataTreeNode, selectedKeysSet: Set<Key>) {
    if (!node.parentKey) return

    const parentNode = nodeMap.value.get(node.parentKey)
    if (!parentNode) return

    // 检查父节点的所有子节点是否都被选中
    const allSiblingKeys = getAllChildrenKeys(parentNode.key, false) // 不包含父节点自身
    const allSiblingsSelected = allSiblingKeys.every(key => selectedKeysSet.has(key))

    if (allSiblingsSelected) {
      selectedKeysSet.add(parentNode.key)
      // 递归检查更上级的父节点
      updateParentNodesOnCheck(parentNode, selectedKeysSet)
    }
  }

  /**
   * 取消选中时更新父节点状态
   * @param node 当前节点
   * @param selectedKeysSet 选中的keys集合
   */
  function updateParentNodesOnUncheck(node: PermissionDataTreeNode, selectedKeysSet: Set<Key>) {
    if (!node.parentKey) return

    // 取消选中父节点（因为子节点不全选了）
    selectedKeysSet.delete(node.parentKey)

    const parentNode = nodeMap.value.get(node.parentKey)
    if (parentNode) {
      // 递归取消更上级的父节点
      updateParentNodesOnUncheck(parentNode, selectedKeysSet)
    }
  }

  /**
   * 获取节点的所有子节点keys（包括actions）
   * @param nodeKey 节点key
   * @param includeSelf 是否包含节点自身
   * @returns 所有子节点keys
   */
  function getAllChildrenKeys(nodeKey: Key, includeSelf: boolean = true): Key[] {
    const node = nodeMap.value.get(nodeKey)
    if (!node) return []

    const childrenKeys: Key[] = []

    if (includeSelf) {
      childrenKeys.push(node.key)
    }

    // 添加actions
    if (node.actions) {
      node.actions.forEach(action => {
        childrenKeys.push(action.key)
      })
    }

    // 递归添加子节点
    if (node.children) {
      node.children.forEach(child => {
        childrenKeys.push(...getAllChildrenKeys(child.key, true))
      })
    }

    return childrenKeys
  }

  /**
   * 获取节点的所有父节点keys
   * @param nodeKey 节点key
   * @returns 所有父节点keys（从根到直接父节点）
   */
  function getAllParentKeys(nodeKey: Key): Key[] {
    const node = nodeMap.value.get(nodeKey)
    if (!node || !node.parentKey) return []

    const parentKeys: Key[] = []
    let currentNode = node

    while (currentNode.parentKey) {
      parentKeys.unshift(currentNode.parentKey) // 添加到开头，保持从根到父的顺序
      currentNode = nodeMap.value.get(currentNode.parentKey)
      if (!currentNode) break
    }

    return parentKeys
  }

  /**
   * 获取节点的选择状态
   * @param nodeKey 节点key
   * @returns 选择状态信息
   */
  function getNodeSelectionState(nodeKey: Key): NodeSelectionState {
    const node = nodeMap.value.get(nodeKey)
    if (!node) {
      return { isSelected: false, isIndeterminate: false, selectedActions: [] }
    }

    // 如果节点有actions，检查actions的选中状态
    if (node.actions && node.actions.length > 0) {
      const selectedActions = node.actions
        .map(action => action.key)
        .filter(actionKey => selectedKeys.value.includes(actionKey))

      const isSelected = selectedActions.length === node.actions.length
      const isIndeterminate = selectedActions.length > 0 && selectedActions.length < node.actions.length

      return {
        isSelected,
        isIndeterminate,
        selectedActions
      }
    }

    // 如果节点有子节点，检查子节点的选中状态
    if (node.children && node.children.length > 0) {
      const childStates = node.children.map(child =>
        getNodeSelectionState(child.key)
      )

      const selectedChildren = childStates.filter(state => state.isSelected).length
      const indeterminateChildren = childStates.filter(state => state.isIndeterminate).length

      const isSelected = selectedChildren === node.children.length
      const isIndeterminate = (selectedChildren > 0 && selectedChildren < node.children.length) ||
                             indeterminateChildren > 0

      return {
        isSelected,
        isIndeterminate,
        selectedActions: []
      }
    }

    // 叶子节点
    return {
      isSelected: selectedKeys.value.includes(nodeKey),
      isIndeterminate: false,
      selectedActions: selectedKeys.value.includes(nodeKey) ? [nodeKey] : []
    }
  }

  /**
   * 检查节点是否被选中
   * @param nodeKey 节点key
   * @returns 是否选中
   */
  function isNodeSelected(nodeKey: Key): boolean {
    return getNodeSelectionState(nodeKey).isSelected
  }

  /**
   * 检查节点是否为半选状态
   * @param nodeKey 节点key
   * @returns 是否半选
   */
  function isNodeIndeterminate(nodeKey: Key): boolean {
    return getNodeSelectionState(nodeKey).isIndeterminate
  }

  /**
   * 获取节点选中的actions
   * @param nodeKey 节点key
   * @returns 选中的actions
   */
  function getSelectedActionsForNode(nodeKey: Key): Key[] {
    return getNodeSelectionState(nodeKey).selectedActions
  }
}
