import { reactive, ref, watch } from 'vue'
import isNil from 'lodash/isNil'
import uniq from 'lodash/uniq'

import { PermissionDataTreeNode, Key, PermissionAction } from '../interface'
import { difference } from 'lodash'

interface IUsePermissionData {
  defaultSelectedKeys?: Key[]

  data?: PermissionDataTreeNode[]
}

/**
 * 对 PermissionPanel 进行全局数据处理的 Hook
 * 1. 保存所有的选中项，方便后续操作
 * 2. 提供给父子层级用来更新数据的方法
 * 3. 提供给外部使用的方法，用来获取选中项
 * 
 * @param options 
 * @returns 
 */
export function usePermissionData ({
  defaultSelectedKeys = [],
  data = [],
}: IUsePermissionData) {
  const selectedKeys = ref<Key[]>([...defaultSelectedKeys])

  return {
    /** data */
    selectedKeys,

    /** methods */
    updateSelectedKeys,

    onCheckedChange,
  }

  function updateSelectedKeys (keys: Key[]) {
    selectedKeys.value = keys 
  }

  function onCheckedChange(key: Key, checked: boolean, node: PermissionDataTreeNode) {
    checked ? handleCheckItem(key, node) : handleUncheckItem(key, node)
  }

  // 只需要对父级进行处理即可，子级会被 use-permission-action 处理

  function handleCheckItem(key: Key, node: PermissionDataTreeNode) {
    // 处理父节点
    if (!isNil(node.parentKey)) {
      const parentKeys = getParentKeys(node)
    
      for (const parentKey of parentKeys) {
        // 判断父级的所有子节点是否都被选中
        const parentNode = getNodeByKey(parentKey, data)
        const parentChildrenKeys = getChildrenKeys(parentNode!)
        const diff = difference(parentChildrenKeys, selectedKeys.value)

        if (diff.includes(parentKey)) {
          selectedKeys.value = uniq([...selectedKeys.value, parentKey])
        }
      }
    }

    // 处理子节点
    const childrenKeys = getChildrenKeys(node)
    const actionKeys = getActionsByKey(key, data).map(i => i.key)

    selectedKeys.value = uniq([...selectedKeys.value, key, ...childrenKeys, ...actionKeys])

    for (const childKey of childrenKeys) {
      const childNode = getNodeByKey(childKey, data)

      if (childNode) {
        if (childNode.actions && childNode.actions.length > 0) {
          selectedKeys.value = uniq([...selectedKeys.value, ...childNode.actions.map(item => item.key)])
        }
      }

      const childActions = getActionsByKey(childKey, data)

      if (childActions.length > 0) {
        selectedKeys.value = uniq([...selectedKeys.value,...childActions.map(item => item.key)]) 
      }
    }
  }

  function handleUncheckItem(key: Key, node: PermissionDataTreeNode) {
    // 处理父节点
    if (!isNil(node.parentKey)) {
      selectedKeys.value = selectedKeys.value.filter(item => item !== node.parentKey)
    }

    // 将子节点的 key 从 selectedKeys 中移除
    const childrenKeys = getChildrenKeys(node)
    const actionKeys = getActionsByKey(key, data).map(i => i.key)

    selectedKeys.value = selectedKeys.value
      .filter(item => !childrenKeys.includes(item))
      .filter(item =>!actionKeys.includes(item))

    for (const childKey of childrenKeys) {
      const childNode = getNodeByKey(childKey, data)

      if (childNode) {
        if (childNode.actions && childNode.actions.length > 0) {
          selectedKeys.value = selectedKeys.value.filter(item => !childNode.actions?.map(item => item.key).includes(item))
        }
      }
    }
  }

  /**
   * 根据节点 node 查找在 data 中该 node 所处分支的所有父节点的 key
   * @param node 
   * @returns 
   */
  function getParentKeys(node: PermissionDataTreeNode) {
    const parentKeys: Key[] = []
    let currentNode: PermissionDataTreeNode | null = node

    while (currentNode && currentNode.parentKey) {
      parentKeys.push(currentNode.parentKey)
      currentNode = data.find(item => item.key === currentNode?.parentKey) || null
    }

    return parentKeys.reverse()
  }

  
  /**
   * 根据 root 节点，获取其所有子孙节点的 key
   * @param root 
   * @returns 
   */
  function getChildrenKeys(root: PermissionDataTreeNode, includeSelf = false) {
    const childrenKeys: Key[] = []

    const queue: PermissionDataTreeNode[] = [root]

    while (queue.length > 0) {
      const node = queue.shift()
      if (node) {
        childrenKeys.push(node.key)

        queue.push(...(node.children || []))

        if (node.actions && node.actions.length > 0) {
          childrenKeys.push(...(node.actions || []).map(item => item.key))
        }
      }
    }

    return childrenKeys
  }

  function getActions(node: PermissionDataTreeNode) {
    return node.actions || []
  }

  function getNodeByKey(key: Key, data: PermissionDataTreeNode[]): PermissionDataTreeNode | null {
    let currentNode: PermissionDataTreeNode | null = null
    let currentIndex = 0

    while (currentIndex < data.length) {
      currentNode = data[currentIndex]
      if (currentNode.key === key) {
        return currentNode
      }

      currentNode = getNodeByKey(key, currentNode.children || [])

      currentIndex++
    }

    return currentNode
  }

  function getActionsByKey(key: Key, data: PermissionDataTreeNode[]): PermissionAction[] {
    const node = getNodeByKey(key, data)

    return node?.actions || [] 
  }
}
