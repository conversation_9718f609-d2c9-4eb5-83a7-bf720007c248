import { reactive, ref, watch, computed } from 'vue'

import { PermissionDataTreeNode, Key, PermissionAction } from '../interface'

interface IUsePermissionData {
  defaultSelectedKeys?: Key[]
  data?: PermissionDataTreeNode[]
}

/**
 * 节点选择状态接口
 */
interface NodeSelectionState {
  isSelected: boolean
  isIndeterminate: boolean
  selectedActions: Key[]
}

/**
 * 权限数据管理 Hook
 * 使用函数式编程思想，提供纯函数操作和完整的状态联动
 *
 * @param options 配置选项
 * @returns 权限管理相关的状态和方法
 */
export function usePermissionData({
  defaultSelectedKeys = [],
  data = [],
}: IUsePermissionData) {
  const selectedKeys = ref<Key[]>([...defaultSelectedKeys])

  // 构建节点映射表，提高查找效率
  const nodeMap = computed(() => {
    const map = new Map<Key, PermissionDataTreeNode>()

    const buildMap = (nodes: PermissionDataTreeNode[]) => {
      nodes.forEach(node => {
        map.set(node.key, node)
        if (node.children) {
          buildMap(node.children)
        }
      })
    }

    buildMap(data)
    return map
  })

  return {
    /** 响应式数据 */
    selectedKeys,

    /** 计算属性 */
    nodeMap,

    /** 核心方法 */
    updateSelectedKeys,
    onCheckedChange,
    getNodeSelectionState,
    getAllChildrenKeys,
    getAllParentKeys,

    /** 工具方法 */
    isNodeSelected,
    isNodeIndeterminate,
    getSelectedActionsForNode,
  }

  /**
   * 更新选中的权限keys
   * @param keys 新的选中keys数组
   */
  function updateSelectedKeys(keys: Key[]) {
    selectedKeys.value = [...keys]
  }

  /**
   * 处理节点选中状态变化 - 核心联动逻辑
   * @param key 节点key
   * @param checked 是否选中
   * @param nodeKey 节点的key（用于查找节点信息）
   */
  function onCheckedChange(key: Key, checked: boolean, nodeKey: Key) {
    const node = nodeMap.value.get(nodeKey)
    if (!node) return

    if (checked) {
      handleCheckItem(key, node)
    } else {
      handleUncheckItem(key, node)
    }
  }

  /**
   * 处理选中操作 - 纯函数式实现
   * @param key 被选中的key
   * @param node 节点信息
   */
  function handleCheckItem(key: Key, node: PermissionDataTreeNode) {
    const newSelectedKeys = new Set(selectedKeys.value)

    // 1. 选中当前节点
    newSelectedKeys.add(key)

    // 2. 如果选中的是节点本身（全选），则选中所有子节点和actions
    if (key === node.key) {
      const allChildKeys = getAllChildrenKeys(node.key)
      allChildKeys.forEach(childKey => newSelectedKeys.add(childKey))
    }

    // 3. 检查并更新父节点状态
    updateParentNodesOnCheck(node, newSelectedKeys)

    // 4. 更新状态
    selectedKeys.value = Array.from(newSelectedKeys)
  }

  /**
   * 处理取消选中操作 - 纯函数式实现
   * @param key 被取消选中的key
   * @param node 节点信息
   */
  function handleUncheckItem(key: Key, node: PermissionDataTreeNode) {
    const newSelectedKeys = new Set(selectedKeys.value)

    // 1. 取消选中当前节点
    newSelectedKeys.delete(key)

    // 2. 如果取消的是节点本身（全选），则取消所有子节点和actions
    if (key === node.key) {
      const allChildKeys = getAllChildrenKeys(node.key)
      allChildKeys.forEach(childKey => newSelectedKeys.delete(childKey))
    }

    // 3. 检查并更新父节点状态
    updateParentNodesOnUncheck(node, newSelectedKeys)

    // 4. 更新状态
    selectedKeys.value = Array.from(newSelectedKeys)
  }

  /**
   * 选中时更新父节点状态 - 深度修复跨层级联动问题
   * @param node 当前节点
   * @param selectedKeysSet 选中的keys集合
   */
  function updateParentNodesOnCheck(node: PermissionDataTreeNode, selectedKeysSet: Set<Key>) {
    if (!node.parentKey) return

    const parentNode = nodeMap.value.get(node.parentKey)
    if (!parentNode) return

    // 深度修复：根据父节点类型选择合适的子节点检查方式
    let shouldSelectParent = false

    if (parentNode.actions && parentNode.actions.length > 0) {
      // 父节点有actions：检查所有actions是否都被选中
      const allActionsSelected = parentNode.actions.every(action =>
        selectedKeysSet.has(action.key)
      )
      shouldSelectParent = allActionsSelected
    } else if (parentNode.children && parentNode.children.length > 0) {
      // 父节点有子节点：检查所有直接子节点是否都被选中或半选
      const allChildrenValid = parentNode.children.every(child => {
        if (child.actions && child.actions.length > 0) {
          // 子节点有actions：检查是否所有actions都被选中
          return child.actions.every(action => selectedKeysSet.has(action.key))
        } else {
          // 子节点没有actions：检查节点本身是否被选中
          return selectedKeysSet.has(child.key)
        }
      })
      shouldSelectParent = allChildrenValid
    }

    if (shouldSelectParent) {
      selectedKeysSet.add(parentNode.key)
      // 递归检查更上级的父节点
      updateParentNodesOnCheck(parentNode, selectedKeysSet)
    }
  }

  /**
   * 取消选中时更新父节点状态 - 深度修复跨层级联动问题
   * @param node 当前节点
   * @param selectedKeysSet 选中的keys集合
   */
  function updateParentNodesOnUncheck(node: PermissionDataTreeNode, selectedKeysSet: Set<Key>) {
    if (!node.parentKey) return

    const parentNode = nodeMap.value.get(node.parentKey)

    if (!parentNode) return

    // 深度修复：重新计算父节点状态
    let shouldSelectParent = false
    let hasAnySelected = false

    if (parentNode.actions && parentNode.actions.length > 0) {
      // 父节点有actions：检查actions的选中状态
      const selectedActionsCount = parentNode.actions.filter(action =>
        selectedKeysSet.has(action.key)
      ).length

      shouldSelectParent = selectedActionsCount === parentNode.actions.length
      hasAnySelected = selectedActionsCount > 0
    } else if (parentNode.children && parentNode.children.length > 0) {
      // 父节点有子节点：检查子节点的选中状态
      let validChildrenCount = 0
      let totalChildrenCount = 0

      parentNode.children.forEach(child => {
        totalChildrenCount++

        if (child.actions && child.actions.length > 0) {
          // 子节点有actions：检查是否所有actions都被选中
          const allActionsSelected = child.actions.every(action => selectedKeysSet.has(action.key))
          const anyActionSelected = child.actions.some(action => selectedKeysSet.has(action.key))

          if (allActionsSelected) validChildrenCount++
          if (anyActionSelected) hasAnySelected = true
        } else {
          // 子节点没有actions：检查节点本身
          if (selectedKeysSet.has(child.key)) {
            validChildrenCount++
            hasAnySelected = true
          }
        }
      })

      shouldSelectParent = validChildrenCount === totalChildrenCount
    }
    // 更新父节点状态
    if (shouldSelectParent) {
      selectedKeysSet.add(parentNode.key)
      // 递归向上检查
      updateParentNodesOnCheck(parentNode, selectedKeysSet)
    } else {
      selectedKeysSet.delete(parentNode.key)

      if (hasAnySelected) {
        // 有部分选中，继续向上传播半选状态
        updateParentNodesOnPartialCheck(parentNode, selectedKeysSet)
      } else {
        // 完全没有选中，继续向上取消
        updateParentNodesOnUncheck(parentNode, selectedKeysSet)
      }
    }
  }

  /**
   * 部分选中时更新父节点状态 - 修复级联选择逻辑错误
   * @param node 当前节点
   * @param selectedKeysSet 选中的keys集合
   */
  function updateParentNodesOnPartialCheck(node: PermissionDataTreeNode, selectedKeysSet: Set<Key>) {
    // 修复关键问题：首先处理当前节点本身的状态
    let shouldSelectCurrentNode = false
    let hasAnySelectedInCurrentNode = false

    if (node.actions && node.actions.length > 0) {
      // 当前节点有actions：检查actions的选中状态
      const selectedActionsCount = node.actions.filter(action =>
        selectedKeysSet.has(action.key)
      ).length

      shouldSelectCurrentNode = selectedActionsCount === node.actions.length
      hasAnySelectedInCurrentNode = selectedActionsCount > 0
    } else if (node.children && node.children.length > 0) {
      // 当前节点有子节点：检查子节点的选中状态
      let validChildrenCount = 0
      let totalChildrenCount = 0

      node.children.forEach(child => {
        totalChildrenCount++

        if (child.actions && child.actions.length > 0) {
          // 子节点有actions：检查是否所有actions都被选中
          const allActionsSelected = child.actions.every(action => selectedKeysSet.has(action.key))
          const anyActionSelected = child.actions.some(action => selectedKeysSet.has(action.key))

          if (allActionsSelected) validChildrenCount++
          if (anyActionSelected) hasAnySelectedInCurrentNode = true
        } else {
          // 子节点没有actions：检查节点本身
          if (selectedKeysSet.has(child.key)) {
            validChildrenCount++
            hasAnySelectedInCurrentNode = true
          }
        }
      })

      shouldSelectCurrentNode = validChildrenCount === totalChildrenCount
    }

    // 更新当前节点状态
    if (shouldSelectCurrentNode) {
      selectedKeysSet.add(node.key)
    } else {
      selectedKeysSet.delete(node.key)
    }

    // 修复关键问题：然后处理父节点（如果存在）
    if (!node.parentKey) {
      return
    }

    const parentNode = nodeMap.value.get(node.parentKey)
    if (!parentNode) {
      return
    }

    // 重新计算父节点的状态
    let shouldSelectParent = false
    let hasAnySelectedInParent = false

    if (parentNode.actions && parentNode.actions.length > 0) {
      // 父节点有actions：检查actions的选中状态
      const selectedActionsCount = parentNode.actions.filter(action =>
        selectedKeysSet.has(action.key)
      ).length

      shouldSelectParent = selectedActionsCount === parentNode.actions.length
      hasAnySelectedInParent = selectedActionsCount > 0
    } else if (parentNode.children && parentNode.children.length > 0) {
      // 父节点有子节点：检查子节点的选中状态
      let validChildrenCount = 0
      let totalChildrenCount = 0

      parentNode.children.forEach(child => {
        totalChildrenCount++

        if (child.actions && child.actions.length > 0) {
          // 子节点有actions：检查是否所有actions都被选中
          const allActionsSelected = child.actions.every(action => selectedKeysSet.has(action.key))
          const anyActionSelected = child.actions.some(action => selectedKeysSet.has(action.key))

          if (allActionsSelected) validChildrenCount++
          if (anyActionSelected) hasAnySelectedInParent = true
        } else {
          // 子节点没有actions：检查节点本身
          if (selectedKeysSet.has(child.key)) {
            validChildrenCount++
            hasAnySelectedInParent = true
          }
        }
      })

      shouldSelectParent = validChildrenCount === totalChildrenCount
    }

    // 更新父节点状态并继续向上传播
    if (shouldSelectParent) {
      selectedKeysSet.add(parentNode.key)
      // 递归向上检查
      updateParentNodesOnCheck(parentNode, selectedKeysSet)
    } else {
      selectedKeysSet.delete(parentNode.key)

      if (hasAnySelectedInParent) {
        // 继续向上传播半选状态
        updateParentNodesOnPartialCheck(parentNode, selectedKeysSet)
      } else {
        // 完全没有选中，向上取消
        updateParentNodesOnUncheck(parentNode, selectedKeysSet)
      }
    }
  }

  /**
   * 获取节点的所有子节点keys（包括actions）- 修复三层级联动问题
   * @param nodeKey 节点key
   * @param includeSelf 是否包含节点自身
   * @param onlyDirectChildren 是否只获取直接子节点（不递归）
   * @returns 所有子节点keys
   */
  function getAllChildrenKeys(nodeKey: Key, includeSelf: boolean = true, onlyDirectChildren: boolean = false): Key[] {
    const node = nodeMap.value.get(nodeKey)
    if (!node) return []

    const childrenKeys: Key[] = []

    if (includeSelf) {
      childrenKeys.push(node.key)
    }

    // 添加actions
    if (node.actions) {
      node.actions.forEach(action => {
        childrenKeys.push(action.key)
      })
    }

    // 添加子节点
    if (node.children) {
      node.children.forEach(child => {
        if (onlyDirectChildren) {
          // 只添加直接子节点，不递归
          childrenKeys.push(child.key)
          // 但仍然添加直接子节点的actions
          if (child.actions) {
            child.actions.forEach(action => {
              childrenKeys.push(action.key)
            })
          }
        } else {
          // 递归添加所有子节点
          childrenKeys.push(...getAllChildrenKeys(child.key, true, false))
        }
      })
    }

    return childrenKeys
  }

  /**
   * 获取节点的直接子节点keys（只包含第一层子节点）- 深度修复问题2
   * @param nodeKey 节点key
   * @returns 直接子节点keys
   */
  function getDirectChildrenKeys(nodeKey: Key): Key[] {
    const node = nodeMap.value.get(nodeKey)
    if (!node) return []

    const childrenKeys: Key[] = []

    // 修复问题2：重新设计直接子节点获取逻辑
    if (node.children && node.children.length > 0) {
      // 有子节点的情况：只添加直接子节点的key，不添加actions
      node.children.forEach(child => {
        childrenKeys.push(child.key)
      })
    } else if (node.actions && node.actions.length > 0) {
      // 没有子节点但有actions的情况：添加actions
      node.actions.forEach(action => {
        childrenKeys.push(action.key)
      })
    }

    return childrenKeys
  }

  /**
   * 获取节点的所有子权限keys（包括子节点的actions）- 新增函数
   * @param nodeKey 节点key
   * @returns 所有子权限keys
   */
  function getAllSubPermissionKeys(nodeKey: Key): Key[] {
    const node = nodeMap.value.get(nodeKey)
    if (!node) return []

    const permissionKeys: Key[] = []

    if (node.children && node.children.length > 0) {
      // 有子节点：递归获取所有子权限
      node.children.forEach(child => {
        // 添加子节点本身
        permissionKeys.push(child.key)

        // 添加子节点的actions
        if (child.actions && child.actions.length > 0) {
          child.actions.forEach(action => {
            permissionKeys.push(action.key)
          })
        }

        // 递归添加更深层的权限
        const subPermissions = getAllSubPermissionKeys(child.key)
        permissionKeys.push(...subPermissions)
      })
    } else if (node.actions && node.actions.length > 0) {
      // 叶子节点：添加actions
      node.actions.forEach(action => {
        permissionKeys.push(action.key)
      })
    }

    return permissionKeys
  }

  /**
   * 获取节点的所有父节点keys
   * @param nodeKey 节点key
   * @returns 所有父节点keys（从根到直接父节点）
   */
  function getAllParentKeys(nodeKey: Key): Key[] {
    const node = nodeMap.value.get(nodeKey)
    if (!node || !node.parentKey) return []

    const parentKeys: Key[] = []
    let currentNode = node

    while (currentNode.parentKey) {
      parentKeys.unshift(currentNode.parentKey) // 添加到开头，保持从根到父的顺序
      currentNode = nodeMap.value.get(currentNode.parentKey)
      if (!currentNode) break
    }

    return parentKeys
  }

  /**
   * 获取节点的选择状态 - 修复状态计算逻辑
   * @param nodeKey 节点key
   * @returns 选择状态信息
   */
  function getNodeSelectionState(nodeKey: Key): NodeSelectionState {
    const node = nodeMap.value.get(nodeKey)
    if (!node) {
      return { isSelected: false, isIndeterminate: false, selectedActions: [] }
    }

    // 如果节点有actions，检查actions的选中状态
    if (node.actions && node.actions.length > 0) {
      const selectedActions = node.actions
        .map(action => action.key)
        .filter(actionKey => selectedKeys.value.includes(actionKey))

      // 修复：只有所有actions都被选中才算全选
      const isSelected = selectedActions.length === node.actions.length && node.actions.length > 0
      const isIndeterminate = selectedActions.length > 0 && selectedActions.length < node.actions.length

      return {
        isSelected,
        isIndeterminate,
        selectedActions
      }
    }

    // 如果节点有子节点，检查子节点的选中状态
    if (node.children && node.children.length > 0) {
      const childStates = node.children.map(child =>
        getNodeSelectionState(child.key)
      )

      const selectedChildren = childStates.filter(state => state.isSelected).length
      const indeterminateChildren = childStates.filter(state => state.isIndeterminate).length

      // 修复：更准确的状态计算
      const isSelected = selectedChildren === node.children.length && node.children.length > 0
      const isIndeterminate = (selectedChildren > 0 && selectedChildren < node.children.length) ||
                             indeterminateChildren > 0

      return {
        isSelected,
        isIndeterminate,
        selectedActions: []
      }
    }

    // 叶子节点
    return {
      isSelected: selectedKeys.value.includes(nodeKey),
      isIndeterminate: false,
      selectedActions: selectedKeys.value.includes(nodeKey) ? [nodeKey] : []
    }
  }

  /**
   * 检查节点是否被选中
   * @param nodeKey 节点key
   * @returns 是否选中
   */
  function isNodeSelected(nodeKey: Key): boolean {
    return getNodeSelectionState(nodeKey).isSelected
  }

  /**
   * 检查节点是否为半选状态
   * @param nodeKey 节点key
   * @returns 是否半选
   */
  function isNodeIndeterminate(nodeKey: Key): boolean {
    return getNodeSelectionState(nodeKey).isIndeterminate
  }

  /**
   * 获取节点选中的actions
   * @param nodeKey 节点key
   * @returns 选中的actions
   */
  function getSelectedActionsForNode(nodeKey: Key): Key[] {
    return getNodeSelectionState(nodeKey).selectedActions
  }
}
