import { ref, computed, watch, type Ref } from 'vue'
import type { 
  PermissionTreeNode, 
  PermissionLevel, 
  PermissionAction,
  PermissionTreeHook 
} from '../types'

/**
 * 权限树管理 Hook
 * 使用函数式编程思想，提供纯函数操作
 */
export function usePermissionTree(
  initialData: PermissionTreeNode[] = []
): PermissionTreeHook {
  // 原始树数据
  const originalTreeData = ref<PermissionTreeNode[]>([...initialData])
  
  // 当前显示的树数据（支持搜索过滤）
  const treeData = ref<PermissionTreeNode[]>([...initialData])
  
  // 展开的节点keys
  const expandedKeys = ref<string[]>([])
  
  // 权限状态存储 { nodeKey: actions[] }
  const permissions = ref<Record<string, PermissionAction[]>>({})
  
  // 搜索关键词
  const searchKeyword = ref('')

  /**
   * 深度优先遍历树节点
   */
  const traverseTree = (
    nodes: PermissionTreeNode[],
    callback: (node: PermissionTreeNode, parent?: PermissionTreeNode) => void,
    parent?: PermissionTreeNode
  ) => {
    nodes.forEach(node => {
      callback(node, parent)
      if (node.children?.length) {
        traverseTree(node.children, callback, node)
      }
    })
  }

  /**
   * 获取所有叶子节点
   */
  const getLeafNodes = (nodes: PermissionTreeNode[]): PermissionTreeNode[] => {
    const leafNodes: PermissionTreeNode[] = []
    traverseTree(nodes, (node) => {
      if (!node.children?.length) {
        leafNodes.push(node)
      }
    })
    return leafNodes
  }

  /**
   * 获取节点的所有子节点keys
   */
  const getChildrenKeys = (nodeKey: string): string[] => {
    const keys: string[] = []
    const findNode = (nodes: PermissionTreeNode[]): PermissionTreeNode | null => {
      for (const node of nodes) {
        if (node.key === nodeKey) return node
        if (node.children?.length) {
          const found = findNode(node.children)
          if (found) return found
        }
      }
      return null
    }

    const targetNode = findNode(treeData.value)
    if (targetNode?.children) {
      traverseTree(targetNode.children, (node) => {
        keys.push(node.key)
      })
    }
    return keys
  }

  /**
   * 获取节点的所有父节点keys
   */
  const getParentKeys = (nodeKey: string): string[] => {
    const keys: string[] = []
    const findParents = (nodes: PermissionTreeNode[], targetKey: string, parents: string[] = []): boolean => {
      for (const node of nodes) {
        const currentPath = [...parents, node.key]
        if (node.key === targetKey) {
          keys.push(...parents)
          return true
        }
        if (node.children?.length && findParents(node.children, targetKey, currentPath)) {
          return true
        }
      }
      return false
    }
    findParents(treeData.value, nodeKey)
    return keys
  }

  /**
   * 切换节点展开状态
   */
  const toggleExpand = (nodeKey: string) => {
    const index = expandedKeys.value.indexOf(nodeKey)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    } else {
      expandedKeys.value.push(nodeKey)
    }
  }

  /**
   * 更新节点权限
   */
  const updatePermission = (nodeKey: string, actions: PermissionAction[]) => {
    permissions.value[nodeKey] = [...actions]
    
    // 自动处理父子节点联动
    updateChildrenPermissions(nodeKey, actions)
    updateParentPermissions(nodeKey)
  }

  /**
   * 更新子节点权限（向下传播）
   */
  const updateChildrenPermissions = (nodeKey: string, actions: PermissionAction[]) => {
    const childrenKeys = getChildrenKeys(nodeKey)
    childrenKeys.forEach(childKey => {
      // 只有当子节点没有权限或权限为空时才自动设置
      if (!permissions.value[childKey] || permissions.value[childKey].length === 0) {
        permissions.value[childKey] = [...actions]
      }
    })
  }

  /**
   * 更新父节点权限（向上传播）
   */
  const updateParentPermissions = (nodeKey: string) => {
    const parentKeys = getParentKeys(nodeKey)
    parentKeys.forEach(parentKey => {
      const siblingKeys = getChildrenKeys(parentKey)
      const siblingPermissions = siblingKeys.map(key => permissions.value[key] || [])
      
      // 如果所有子节点都有相同权限，则父节点也设置相同权限
      if (siblingPermissions.length > 0) {
        const commonActions = siblingPermissions.reduce((common, current) => {
          return common.filter(action => current.includes(action))
        })
        permissions.value[parentKey] = commonActions
      }
    })
  }

  /**
   * 全选指定级别的权限
   */
  const selectAll = (level: PermissionLevel) => {
    const allActions: PermissionAction[] = ['view', 'add', 'edit', 'delete', 'export', 'import']
    
    traverseTree(treeData.value, (node) => {
      if (shouldApplyToLevel(node, level)) {
        permissions.value[node.key] = [...allActions]
      }
    })
  }

  /**
   * 清空指定级别的权限
   */
  const clearAll = (level: PermissionLevel) => {
    traverseTree(treeData.value, (node) => {
      if (shouldApplyToLevel(node, level)) {
        permissions.value[node.key] = []
      }
    })
  }

  /**
   * 判断节点是否应用于指定级别
   */
  const shouldApplyToLevel = (node: PermissionTreeNode, level: PermissionLevel): boolean => {
    switch (level) {
      case 'view':
        return node.type === 'module' || node.type === 'menu'
      case 'data':
        return node.type === 'menu' || node.type === 'button'
      case 'field':
        return node.type === 'field' || node.type === 'button'
      default:
        return true
    }
  }

  /**
   * 获取指定级别的权限
   */
  const getPermissionsByLevel = (level: PermissionLevel): Record<string, PermissionAction[]> => {
    const result: Record<string, PermissionAction[]> = {}
    
    traverseTree(treeData.value, (node) => {
      if (shouldApplyToLevel(node, level) && permissions.value[node.key]) {
        result[node.key] = permissions.value[node.key]
      }
    })
    
    return result
  }

  /**
   * 搜索节点
   */
  const searchNodes = (keyword: string) => {
    searchKeyword.value = keyword
    if (!keyword.trim()) {
      treeData.value = [...originalTreeData.value]
      return
    }

    const filterTree = (nodes: PermissionTreeNode[]): PermissionTreeNode[] => {
      return nodes.reduce((filtered: PermissionTreeNode[], node) => {
        const matchesKeyword = node.title.toLowerCase().includes(keyword.toLowerCase())
        const filteredChildren = node.children ? filterTree(node.children) : []
        
        if (matchesKeyword || filteredChildren.length > 0) {
          filtered.push({
            ...node,
            children: filteredChildren
          })
        }
        
        return filtered
      }, [])
    }

    treeData.value = filterTree(originalTreeData.value)
    
    // 自动展开搜索结果
    const expandSearchResults = (nodes: PermissionTreeNode[]) => {
      nodes.forEach(node => {
        if (!expandedKeys.value.includes(node.key)) {
          expandedKeys.value.push(node.key)
        }
        if (node.children?.length) {
          expandSearchResults(node.children)
        }
      })
    }
    expandSearchResults(treeData.value)
  }

  /**
   * 重置搜索
   */
  const resetSearch = () => {
    searchKeyword.value = ''
    treeData.value = [...originalTreeData.value]
  }

  // 监听初始数据变化
  watch(() => initialData, (newData) => {
    originalTreeData.value = [...newData]
    if (!searchKeyword.value) {
      treeData.value = [...newData]
    }
  }, { deep: true })

  return {
    treeData,
    expandedKeys,
    permissions,
    toggleExpand,
    updatePermission,
    selectAll,
    clearAll,
    getPermissionsByLevel,
    searchNodes,
    resetSearch
  }
}
