import { ref, computed, Ref, reactive, watch } from 'vue'
import { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface'
import { CheckboxOptionType } from 'ant-design-vue'

import { Key, PermissionDataTreeNode } from '../interface'

/**
 * 权限操作管理 Hook - 简化版本
 * 专注于单个节点的权限操作管理，与全局状态管理分离
 */
interface IUsePermissionAction {
  node: PermissionDataTreeNode
  selectedKeys: Ref<Key[]>
  options: CheckboxOptionType[]
  onChange?: (key: Key, checked: boolean) => void
}

export const usePermissionAction = ({
  node,
  selectedKeys,
  options,
  onChange
}: IUsePermissionAction) => {
  // 计算当前节点的选中状态
  const checkState = computed(() => {
    const nodeKey = node.key
    const optionKeys = options.map(opt => opt.value as Key)

    // 检查当前节点的actions选中情况
    const selectedActions = optionKeys.filter(key => selectedKeys.value.includes(key))

    // 检查是否全选（节点本身被选中 或 所有actions都被选中）
    const isNodeSelected = selectedKeys.value.includes(nodeKey)
    const allActionsSelected = optionKeys.length > 0 && selectedActions.length === optionKeys.length
    const checkedAll = isNodeSelected || allActionsSelected

    // 检查是否半选（部分actions被选中，但不是全部）
    const indeterminate = !checkedAll && selectedActions.length > 0

    return {
      checkedAll,
      indeterminate,
      checkedKeys: selectedActions
    }
  })

  // 检查单个action是否被选中
  const isItemChecked = (key: Key): boolean => {
    return selectedKeys.value.includes(key)
  }

  return {
    checkState,
    onCheckAllStateChange,
    onCheckStateChange,
  }

  /**
   * 全选框状态改变
   *
   * @param e
   * @returns {void}
   */
  function onCheckAllStateChange (e: CheckboxChangeEvent) {
    const { checked, value } = e.target as HTMLInputElement
    const payload = {}

    Object.assign(payload, {
      checkedKeys: checked ? options.map((item) => item.value) : [],
      checkedAll: checked,
      indeterminate: checked ? false : undefined,
    })

    Object.assign(checkState, payload)

    onChange && onChange(node.key, checked)
  }

  /**
   * 单选框状态改变
   *
   * @param keys {string[]}
   * @returns {void}
   */
  function onCheckStateChange (e: CheckboxChangeEvent) {
    const { checked, value } = e.target as HTMLInputElement

    const newKeys = checked
      ? [...checkState.checkedKeys, value]
      : checkState.checkedKeys.filter((item) => item !== value)

    Object.assign(checkState, {
      checkedKeys: reactive(newKeys),
    })

    onChange && onChange(key, checked)
  }
}
