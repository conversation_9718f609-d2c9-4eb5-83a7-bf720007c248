import { ref, computed, Ref, reactive, watch } from 'vue'
import { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface'
import { CheckboxOptionType } from 'ant-design-vue'

import { Key, PermissionDataTreeNode } from '../interface'

/**
 * 权限操作管理 Hook - 简化版本
 * 专注于单个节点的权限操作管理，与全局状态管理分离
 */
interface IUsePermissionAction {
  node: PermissionDataTreeNode
  selectedKeys: Ref<Key[]>
  options: CheckboxOptionType[]
  onChange?: (key: Key, checked: boolean) => void
}

export const usePermissionAction = ({
  node,
  selectedKeys,
  options,
  onChange
}: IUsePermissionAction) => {
  // 计算当前节点的选中状态 - 深度修复问题1
  const checkState = computed(() => {
    const nodeKey = node.key
    const optionKeys = options.map(opt => opt.value as Key)

    // 添加调试日志
    console.log(`[DEBUG] 计算节点状态 - nodeKey: ${nodeKey}, optionKeys:`, optionKeys, 'selectedKeys:', selectedKeys.value)

    // 检查当前节点的actions选中情况
    const selectedActions = optionKeys.filter(key => selectedKeys.value.includes(key))

    // 深度修复问题1：重新设计状态计算逻辑
    let checkedAll = false
    let indeterminate = false

    if (optionKeys.length > 0) {
      // 有actions的情况：严格按照actions的选中状态计算
      const allActionsSelected = selectedActions.length === optionKeys.length
      const noActionsSelected = selectedActions.length === 0

      checkedAll = allActionsSelected
      indeterminate = !allActionsSelected && !noActionsSelected

      console.log(`[DEBUG] 有actions节点 - 总数: ${optionKeys.length}, 已选: ${selectedActions.length}, 全选: ${checkedAll}, 半选: ${indeterminate}`)
    } else {
      // 没有actions的情况：检查节点本身是否被选中
      checkedAll = selectedKeys.value.includes(nodeKey)
      indeterminate = false

      console.log(`[DEBUG] 无actions节点 - 节点选中: ${checkedAll}`)
    }

    const result = {
      checkedAll,
      indeterminate,
      checkedKeys: selectedActions
    }

    console.log(`[DEBUG] 最终状态:`, result)
    return result
  })

  // 检查单个action是否被选中
  const isItemChecked = (key: Key): boolean => {
    return selectedKeys.value.includes(key)
  }

  return {
    checkState,
    isItemChecked,
    onCheckAllStateChange,
    onCheckStateChange,
  }

  /**
   * 全选框状态改变
   * @param e 复选框事件
   */
  function onCheckAllStateChange(e: CheckboxChangeEvent) {
    const { checked } = e.target as HTMLInputElement

    // 触发节点本身的选中/取消选中
    onChange?.(node.key, checked)
  }

  /**
   * 单个权限操作状态改变
   * @param e 复选框事件
   */
  function onCheckStateChange(e: CheckboxChangeEvent) {
    const { checked, value } = e.target as HTMLInputElement

    // 触发具体action的选中/取消选中
    onChange?.(value as Key, checked)
  }
}
