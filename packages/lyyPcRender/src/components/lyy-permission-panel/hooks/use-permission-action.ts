import { ref, computed, Ref, reactive, watch, onMounted } from 'vue'
import { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface'
import { CheckboxOptionType } from 'ant-design-vue'
import uniq from 'lodash/uniq'
import difference from 'lodash/difference'

import { Key, PermissionDataTreeNode } from '../interface'

/**
 * 管理选中操作的 hook 函数。
 * @param {Object} options - 配置选项。
 * @param {Function} options.onChange - 当选中操作发生变化时调用的回调函数。
 * @returns {Object} - 返回一个包含选中操作和更新选中操作的对象。
 */

interface IUsePermissionAction {
  node: PermissionDataTreeNode
  defaultCheckedKeys?: Key[]
  checkedKeys?: Ref<Key[]>
  options: CheckboxOptionType[]
  onChange?: (key: Key, checked: boolean) => void
}

export const usePermissionAction = ({
  node,
  defaultCheckedKeys = [],
  checkedKeys = ref([]),
  options,
  onChange
}: IUsePermissionAction) => {
  const { key } = node

  const checkState = reactive<{
    checkedAll: boolean,
    indeterminate: boolean,
    disabled: boolean,
    checkedKeys: Key[],
  }>({
    checkedAll: false,
    indeterminate: false,
    disabled: false,
    checkedKeys: [],
  })

  watch(
    () => checkedKeys.value,
    (newVal, oldValue) => {
      // _add 为新增的 key，_remove 为删除的 key
      const _add = difference(newVal, oldValue)
      const _remove = difference(oldValue, newVal)
      const hasChildren = Boolean(node.children?.length)

      console.log('checkedKeys.value------------->', _add, _remove)

      if (_add.includes(node.key)) {
        Object.assign(checkState, {
          checkedAll: true,
          indeterminate: false,
          checkedKeys: [checkState.checkedKeys, ..._add],
        })
      }

      if (_remove.includes(node.key)) {
        Object.assign(checkState, {
          checkedAll: false,
          indeterminate: hasChildren,
          checkedKeys: checkState.checkedKeys.filter(item => _remove.includes(item)),
        }) 
      }
    }
  )

  watch(
    () => checkState.checkedKeys,
    val => {
      // 当前项没有子权限时，直接返回
      if (options.length === 0) return

      const checkedAll = (options.length === val.length) && (options.length > 0) || val.includes(key)
      const indeterminate = val.length > 0 && val.length < options.length

      Object.assign(checkState, {
        checkedAll,
        indeterminate,
      })
    }
  )

  onMounted(() => {
    // 由于 defaultCheckedKeys 是全体的选中 key
    // 所以需要根据 options 来过滤出 defaultCheckedKeys 中存在的 key

    let _defaultCheckedKeys: Key[] = [...defaultCheckedKeys]

    const optionKeys = options.map((item) => item.value) as Key[]

    // 如果传入的 defaultCheckedKeys 中包含当前的 key
    // 则为全选状态
    if (_defaultCheckedKeys.includes(key)) {
      Object.assign(checkState, {
        checkedKeys: uniq([...optionKeys]),
        checkedAll: true,
      })
    // 如果传入的 defaultCheckedKeys 中不包含当前的 key
    // 则为非全选状态，需要根据 defaultCheckedKeys 来过滤出当前 key 下的选中 key
    } else {
      if (defaultCheckedKeys.length) {
        _defaultCheckedKeys = optionKeys.filter((item) => defaultCheckedKeys.includes(item))
      }

      checkState.checkedKeys = _defaultCheckedKeys
    }
  })
  
  return {
    checkState,
    onCheckAllStateChange,
    onCheckStateChange,
  }

  /**
   * 全选框状态改变
   * 
   * @param e 
   * @returns {void}
   */
  function onCheckAllStateChange (e: CheckboxChangeEvent) {
    const { checked, value } = e.target as HTMLInputElement
    const payload = {}

    Object.assign(payload, {
      checkedKeys: checked ? options.map((item) => item.value) : [],
      checkedAll: checked,
      indeterminate: checked ? false : undefined,
    })

    Object.assign(checkState, payload)

    onChange && onChange(node.key, checked)
  }
  
  /**
   * 单选框状态改变 
   *
   * @param keys {string[]}
   * @returns {void}
   */
  function onCheckStateChange (e: CheckboxChangeEvent) {
    const { checked, value } = e.target as HTMLInputElement

    const newKeys = checked
      ? [...checkState.checkedKeys, value]
      : checkState.checkedKeys.filter((item) => item !== value)

    Object.assign(checkState, {
      checkedKeys: reactive(newKeys),
    })

    onChange && onChange(key, checked)
  }
}
