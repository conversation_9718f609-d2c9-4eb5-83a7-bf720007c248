<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'indeterminate-state-fix-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData } from './utils'
import type { Key } from './interface'

// 测试数据 - 专门设计来验证半选状态管理问题
const testData = ref({
  code: "200",
  message: "success",
  body: {
    roleId: "indeterminate-test",
    roleName: "半选状态管理测试",
    roleRemark: "专门测试顶级节点半选状态和级联逻辑",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "商家管理", // 第一级 - 顶级节点
        value: "merchant-manage",
        selected: false, // 初始未选中
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2",
            parentId: "1",
            name: "商家列表", // 第二级
            value: "merchant-list",
            selected: false, // 初始未选中
            htmlTemplet: "/merchant/list",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2",
                name: "商家列表查询", // 第三级
                value: "merchant-list-query",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "商家列表新增",
                value: "merchant-list-add",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              },
              {
                adResourcesId: "5",
                parentId: "2",
                name: "商家列表编辑",
                value: "merchant-list-edit",
                selected: false, // 未选中 - 关键：部分选中状态
                htmlTemplet: null,
                icon: null,
                seq: 3,
                children: []
              }
            ]
          },
          {
            adResourcesId: "6",
            parentId: "1",
            name: "商家分组", // 第二级
            value: "merchant-group",
            selected: false, // 初始未选中
            htmlTemplet: "/merchant/group",
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "7",
                parentId: "6",
                name: "分组查询",
                value: "group-query",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "8",
                parentId: "6",
                name: "分组新增",
                value: "group-add",
                selected: false, // 未选中 - 关键：部分选中状态
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          }
        ]
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试结果
const testResults = ref<Array<{
  test: string
  description: string
  expected: string
  actual: string
  passed: boolean
  timestamp: string
}>>([])

// 调试日志
const debugLogs = ref<string[]>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
  
  // 记录调试日志
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 权限变更: ${keys.join(', ')}`)
  
  // 保持最新的50条日志
  if (debugLogs.value.length > 50) {
    debugLogs.value = debugLogs.value.slice(-50)
  }
}

// 获取节点状态
const getNodeState = (nodeKey: string) => {
  const isSelected = selectedKeys.value.includes(nodeKey)
  return {
    key: nodeKey,
    selected: isSelected
  }
}

// 运行半选状态管理测试
const runIndeterminateStateTest = async () => {
  testResults.value = []
  debugLogs.value = []
  
  console.log('开始半选状态管理测试...')
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 开始半选状态管理测试`)
  
  // 测试1: 初始半选状态验证
  await testInitialIndeterminateState()
  
  setTimeout(async () => {
    // 测试2: 取消子节点后顶级节点半选状态
    await testTopLevelIndeterminateAfterUncheck()
  }, 1000)
  
  setTimeout(async () => {
    // 测试3: 恢复子节点后状态变化
    await testStateChangeAfterRecheck()
  }, 2000)
  
  setTimeout(async () => {
    // 测试4: 完全取消后状态验证
    await testCompleteUncheckState()
  }, 3000)
}

// 测试1: 初始半选状态验证
const testInitialIndeterminateState = async () => {
  // 重置为初始状态
  selectedKeys.value = [...processedData.value.selectedResources]
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 初始选中状态: ${selectedKeys.value.join(', ')}`)
  
  // 验证顶级节点状态
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantGroupSelected = selectedKeys.value.includes('merchant-group')
  
  // 期望：顶级节点不应该显示为选中（因为有部分子节点未选中），应该显示半选状态
  const expected = "顶级节点应该显示半选状态（不在selectedKeys中，但有部分子节点选中）"
  const actual = `商家管理${merchantManageSelected ? '已选中' : '未选中'}，商家列表${merchantListSelected ? '已选中' : '未选中'}，商家分组${merchantGroupSelected ? '已选中' : '未选中'}`
  const passed = !merchantManageSelected && !merchantListSelected && !merchantGroupSelected
  
  testResults.value.push({
    test: "测试1: 初始半选状态验证",
    description: "验证有部分子节点选中时，父节点不在selectedKeys中（通过getNodeSelectionState计算半选）",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试1结果: ${passed ? '通过' : '失败'}`)
}

// 测试2: 取消子节点后顶级节点半选状态
const testTopLevelIndeterminateAfterUncheck = async () => {
  // 取消商家列表查询
  selectedKeys.value = selectedKeys.value.filter(key => key !== 'merchant-list-query')
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 取消merchant-list-query后`)
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  
  // 期望：顶级节点仍然应该保持半选状态（不在selectedKeys中）
  const expected = "顶级节点仍应保持半选状态，不应该完全取消选中"
  const actual = `商家管理${merchantManageSelected ? '已选中' : '未选中'}，商家列表${merchantListSelected ? '已选中' : '未选中'}`
  const passed = !merchantManageSelected && !merchantListSelected
  
  testResults.value.push({
    test: "测试2: 取消子节点后顶级节点半选状态",
    description: "取消部分第三级权限后，第一级和第二级应该保持半选状态",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试2结果: ${passed ? '通过' : '失败'}`)
}

// 测试3: 恢复子节点后状态变化
const testStateChangeAfterRecheck = async () => {
  // 恢复商家列表查询，并选中商家列表编辑
  selectedKeys.value = [...selectedKeys.value, 'merchant-list-query', 'merchant-list-edit']
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 恢复并选中所有商家列表权限`)
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  
  // 期望：商家列表应该自动选中，但商家管理仍然半选（因为商家分组还是部分选中）
  const expected = "商家列表应该自动选中，商家管理仍保持半选状态"
  const actual = `商家管理${merchantManageSelected ? '已选中' : '未选中'}，商家列表${merchantListSelected ? '已选中' : '未选中'}`
  const passed = !merchantManageSelected && merchantListSelected
  
  testResults.value.push({
    test: "测试3: 恢复子节点后状态变化",
    description: "选中所有第三级权限后，第二级应该自动选中，第一级仍保持半选",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试3结果: ${passed ? '通过' : '失败'}`)
}

// 测试4: 完全取消后状态验证
const testCompleteUncheckState = async () => {
  // 取消所有权限
  selectedKeys.value = []
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 取消所有权限`)
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantGroupSelected = selectedKeys.value.includes('merchant-group')
  
  // 期望：所有节点都应该完全取消选中
  const expected = "所有节点都应该完全取消选中"
  const actual = `商家管理${merchantManageSelected ? '已选中' : '未选中'}，商家列表${merchantListSelected ? '已选中' : '未选中'}，商家分组${merchantGroupSelected ? '已选中' : '未选中'}`
  const passed = !merchantManageSelected && !merchantListSelected && !merchantGroupSelected
  
  testResults.value.push({
    test: "测试4: 完全取消后状态验证",
    description: "取消所有权限后，所有节点都应该完全取消选中",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试4结果: ${passed ? '通过' : '失败'}`)
}

// 手动测试函数
const manualTest = (testName: string) => {
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 手动测试: ${testName}`)
  
  switch (testName) {
    case 'reset':
      selectedKeys.value = [...processedData.value.selectedResources]
      break
    case 'uncheckQuery':
      selectedKeys.value = selectedKeys.value.filter(key => key !== 'merchant-list-query')
      break
    case 'uncheckAdd':
      selectedKeys.value = selectedKeys.value.filter(key => key !== 'merchant-list-add')
      break
    case 'selectAllMerchantList':
      selectedKeys.value = selectedKeys.value.filter(key => !['merchant-list-query', 'merchant-list-add', 'merchant-list-edit'].includes(key))
      selectedKeys.value.push('merchant-list-query', 'merchant-list-add', 'merchant-list-edit')
      break
    case 'selectGroupAdd':
      if (!selectedKeys.value.includes('group-add')) {
        selectedKeys.value = [...selectedKeys.value, 'group-add']
      }
      break
    case 'clear':
      selectedKeys.value = []
      break
  }
}

// 清空调试日志
const clearDebugLogs = () => {
  debugLogs.value = []
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="indeterminate-state-fix-test">
    <div class="test-header">
      <h1>半选状态管理问题修复验证</h1>
      <p>验证顶级节点半选状态和 updateParentNodesOnPartialCheck 函数的级联逻辑</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runIndeterminateStateTest">
            🧪 运行半选状态管理测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('reset')">
            重置初始状态
          </a-button>
          <a-button @click="manualTest('uncheckQuery')">
            取消商家列表查询
          </a-button>
          <a-button @click="manualTest('uncheckAdd')">
            取消商家列表新增
          </a-button>
          <a-button @click="manualTest('selectAllMerchantList')">
            全选商家列表
          </a-button>
          <a-button @click="manualTest('selectGroupAdd')">
            选中分组新增
          </a-button>
          <a-button @click="manualTest('clear')">
            清空所有
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="半选状态管理测试结果" size="small">
        <div class="results-summary">
          <a-statistic 
            title="通过率" 
            :value="((testResults.filter(r => r.passed).length / testResults.length) * 100).toFixed(1)" 
            suffix="%" 
            :value-style="{ color: testResults.every(r => r.passed) ? '#3f8600' : '#cf1322' }"
          />
        </div>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected }}</div>
              <div><strong>实际:</strong> {{ result.actual }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 调试日志 -->
    <div class="debug-logs">
      <a-card title="调试日志" size="small">
        <template #extra>
          <a-button size="small" @click="clearDebugLogs">清空日志</a-button>
        </template>
        
        <div class="logs-container">
          <div 
            v-for="(log, index) in debugLogs" 
            :key="index"
            class="log-item"
          >
            {{ log }}
          </div>
          <div v-if="debugLogs.length === 0" class="no-logs">
            暂无调试日志
          </div>
        </div>
      </a-card>
    </div>

    <!-- 半选状态说明 -->
    <div class="indeterminate-explanation">
      <a-card title="半选状态管理机制说明" size="small">
        <div class="explanation-content">
          <h4>🔍 半选状态表示方式：</h4>
          <ul>
            <li><strong>全选状态：</strong>节点key在 selectedKeys 中，且所有子项都被选中</li>
            <li><strong>半选状态：</strong>节点key不在 selectedKeys 中，但有部分子项被选中</li>
            <li><strong>未选中：</strong>节点key不在 selectedKeys 中，且没有子项被选中</li>
          </ul>
          
          <h4>🔧 修复的关键问题：</h4>
          <ul>
            <li>顶级节点被跳过：修复了 <code>if (!node.parentKey) return</code> 导致的问题</li>
            <li>半选状态设置缺失：通过 <code>selectedKeysSet.delete(node.key)</code> 正确设置半选</li>
            <li>状态管理不一致：使用统一的 <code>calculateNodeSelectionState</code> 函数</li>
          </ul>
          
          <h4>✅ 期望效果：</h4>
          <p>当子节点部分选中时，父节点应该显示半选状态（复选框显示为半选图标），而不是完全取消选中。</p>
        </div>
      </a-card>
    </div>

    <!-- 状态监控 -->
    <div class="state-monitor">
      <a-card title="实时状态监控" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="monitor-card level-1">
              <h4>第一级：商家管理</h4>
              <div class="state-info">
                <p><strong>在selectedKeys中:</strong> 
                  <a-tag :color="getNodeState('merchant-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-manage').selected ? '是' : '否' }}
                  </a-tag>
                </p>
                <p><strong>期望状态:</strong> 半选（不在selectedKeys中，但有子项选中）</p>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="monitor-card level-2">
              <h4>第二级：商家列表</h4>
              <div class="state-info">
                <p><strong>在selectedKeys中:</strong> 
                  <a-tag :color="getNodeState('merchant-list').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-list').selected ? '是' : '否' }}
                  </a-tag>
                </p>
                <p><strong>子项状态:</strong></p>
                <div class="sub-items">
                  <a-tag 
                    :color="getNodeState('merchant-list-query').selected ? 'green' : 'red'"
                    size="small"
                  >
                    查询
                  </a-tag>
                  <a-tag 
                    :color="getNodeState('merchant-list-add').selected ? 'green' : 'red'"
                    size="small"
                  >
                    新增
                  </a-tag>
                  <a-tag 
                    :color="getNodeState('merchant-list-edit').selected ? 'green' : 'red'"
                    size="small"
                  >
                    编辑
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="monitor-card level-2">
              <h4>第二级：商家分组</h4>
              <div class="state-info">
                <p><strong>在selectedKeys中:</strong> 
                  <a-tag :color="getNodeState('merchant-group').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-group').selected ? '是' : '否' }}
                  </a-tag>
                </p>
                <p><strong>子项状态:</strong></p>
                <div class="sub-items">
                  <a-tag 
                    :color="getNodeState('group-query').selected ? 'green' : 'red'"
                    size="small"
                  >
                    查询
                  </a-tag>
                  <a-tag 
                    :color="getNodeState('group-add').selected ? 'green' : 'red'"
                    size="small"
                  >
                    新增
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 当前选中状态 -->
    <div class="current-state">
      <a-card title="当前选中状态" size="small">
        <div class="state-info">
          <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
          <div class="selected-keys">
            <a-tag 
              v-for="key in selectedKeys" 
              :key="key"
              color="blue"
              style="margin: 2px;"
            >
              {{ key }}
            </a-tag>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="false"
          :show-select-all="true"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.indeterminate-state-fix-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .debug-logs,
  .indeterminate-explanation,
  .state-monitor,
  .current-state,
  .permission-panel {
    margin-bottom: 16px;
  }

  .results-summary {
    margin-bottom: 16px;
    text-align: center;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-description {
        font-size: 13px;
        color: #595959;
        margin-bottom: 8px;
        font-style: italic;
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 8px;
    
    .log-item {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #586069;
      margin-bottom: 2px;
      line-height: 1.4;
    }
    
    .no-logs {
      text-align: center;
      color: #8c8c8c;
      font-style: italic;
    }
  }

  .explanation-content {
    h4 {
      margin: 16px 0 8px 0;
      color: #262626;
      
      &:first-child {
        margin-top: 0;
      }
    }

    p, ul {
      margin: 8px 0;
      color: #595959;
      line-height: 1.6;
    }

    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
      }
    }

    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      color: #d73a49;
    }
  }

  .monitor-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    
    &.level-1 {
      border-color: #1890ff;
      background: #e6f7ff;
    }
    
    &.level-2 {
      border-color: #52c41a;
      background: #f6ffed;
    }
    
    h4 {
      margin: 0 0 8px 0;
      color: #262626;
    }
    
    .state-info {
      p {
        margin: 4px 0;
        font-size: 12px;
        color: #595959;
      }
      
      .sub-items {
        margin-top: 4px;
        
        .ant-tag {
          margin: 2px;
        }
      }
    }
  }

  .state-info {
    .selected-keys {
      margin-top: 8px;
      max-height: 100px;
      overflow-y: auto;
    }
  }
}
</style>
