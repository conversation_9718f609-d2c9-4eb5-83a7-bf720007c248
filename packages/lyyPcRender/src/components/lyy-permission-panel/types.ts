import {Ref} from 'vue'

/**
 * 权限操作类型
 */
export type PermissionAction =
  | 'view'      // 查看
  | 'add'       // 新增
  | 'edit'      // 编辑
  | 'delete'    // 删除
  | 'export'    // 导出
  | 'import'    // 导入
  | 'approve'   // 审批
  | 'query'     // 查询
  | 'download'  // 下载
  | 'upload'    // 上传
  | 'print'     // 打印
  | 'copy'      // 复制

/**
 * 权限级别
 */
export type PermissionLevel = 'view' | 'data' | 'field'

/**
 * 权限树节点
 */
export interface PermissionTreeNode {
  /** 节点唯一标识 */
  key: string
  /** 节点名称 */
  title: string
  /** 节点类型 */
  type: 'module' | 'menu' | 'button' | 'field'
  /** 父节点key */
  parentKey?: string
  /** 子节点 */
  children?: PermissionTreeNode[]
  /** 可用的权限操作 */
  availableActions?: PermissionAction[]
  /** 是否为叶子节点 */
  isLeaf?: boolean
  /** 节点图标 */
  icon?: string
  /** 节点描述 */
  description?: string
  /** 扩展属性 */
  extra?: Record<string, any>
}

/**
 * 权限状态
 */
export interface PermissionState {
  /** 节点key */
  nodeKey: string
  /** 权限级别 */
  level: PermissionLevel
  /** 已选中的权限操作 */
  actions: PermissionAction[]
  /** 是否全选 */
  isSelectAll?: boolean
  /** 是否半选 */
  isIndeterminate?: boolean
}

/**
 * 权限配置
 */
export interface PermissionConfig {
  /** 权限级别配置 */
  levels: {
    key: PermissionLevel
    label: string
    description?: string
  }[]
  /** 权限操作配置 */
  actions: {
    key: PermissionAction
    label: string
    description?: string
    icon?: string
  }[]
}

/**
 * 权限面板属性
 */
export interface PermissionPanelProps {
  /** 权限数据 */
  permissionData?: PermissionTreeNode[]
  /** 默认权限级别 */
  defaultLevel?: PermissionLevel
  /** 是否启用权限查询 */
  enableQuery?: boolean
  /** 是否只读模式 */
  readonly?: boolean
  /** 权限配置 */
  config?: PermissionConfig
  /** 是否显示全选按钮 */
  showSelectAll?: boolean
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 树的高度 */
  treeHeight?: number
}

/**
 * 权限节点属性
 */
export interface PermissionNodeProps {
  /** 节点数据 */
  node: PermissionTreeNode
  /** 当前权限级别 */
  level: PermissionLevel
  /** 权限状态 */
  permissions: Record<string, PermissionAction[]>
  /** 展开的节点keys */
  expandedKeys: string[]
  /** 是否只读 */
  readonly?: boolean
  /** 缩进级别 */
  indent?: number
}

/**
 * 权限操作事件
 */
export interface PermissionEvents {
  /** 保存权限 */
  save: (permissions: Record<string, PermissionAction[]>) => void
  /** 权限变更 */
  change: (permissions: Record<string, PermissionAction[]>) => void
  /** 节点展开/收起 */
  toggleExpand: (nodeKey: string) => void
  /** 更新权限 */
  updatePermission: (nodeKey: string, actions: PermissionAction[]) => void
}

/**
 * 权限工具函数返回类型
 */
export interface PermissionTreeHook {
  /** 树数据 */
  treeData: Ref<PermissionTreeNode[]>
  /** 展开的节点 */
  expandedKeys: Ref<string[]>
  /** 权限状态 */
  permissions: Ref<Record<string, PermissionAction[]>>
  /** 切换展开状态 */
  toggleExpand: (nodeKey: string) => void
  /** 更新权限 */
  updatePermission: (nodeKey: string, actions: PermissionAction[]) => void
  /** 全选 */
  selectAll: (level: PermissionLevel) => void
  /** 清空 */
  clearAll: (level: PermissionLevel) => void
  /** 获取指定级别的权限 */
  getPermissionsByLevel: (level: PermissionLevel) => Record<string, PermissionAction[]>
  /** 搜索节点 */
  searchNodes: (keyword: string) => void
  /** 重置搜索 */
  resetSearch: () => void
}

/**
 * 默认权限配置
 */
export const DEFAULT_PERMISSION_CONFIG: PermissionConfig = {
  levels: [
    { key: 'view', label: '查看权限' },
    { key: 'data', label: '数据权限' },
    { key: 'field', label: '字段权限' }
  ],
  actions: [
    { key: 'view', label: '全部', icon: 'eye' },
    { key: 'add', label: '新增', icon: 'plus' },
    { key: 'edit', label: '编辑', icon: 'edit' },
    { key: 'delete', label: '删除', icon: 'delete' },
    { key: 'export', label: '导出', icon: 'export' },
    { key: 'import', label: '导入', icon: 'import' },
    { key: 'approve', label: '审批', icon: 'check' },
    { key: 'query', label: '查询', icon: 'search' },
    { key: 'download', label: '下载', icon: 'download' },
    { key: 'upload', label: '上传', icon: 'upload' },
    { key: 'print', label: '打印', icon: 'printer' },
    { key: 'copy', label: '复制', icon: 'copy' }
  ]
}
