<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'fix-validation-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData } from './utils'
import type { Key } from './interface'

// 测试数据 - 模拟商家管理模块的结构
const testData = ref({
  code: "200",
  message: "success", 
  body: {
    roleId: "fix-test-role",
    roleName: "修复验证角色",
    roleRemark: "用于验证问题修复效果",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "商家管理",
        value: "merchant-manage",
        selected: false,
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2", 
            parentId: "1",
            name: "商家列表",
            value: "merchant-list",
            selected: true, // 节点本身被选中
            htmlTemplet: "/merchant/list",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2", 
                name: "商家查询",
                value: "merchant-query",
                selected: false, // 但具体操作未选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "商家新增", 
                value: "merchant-add",
                selected: false, // 具体操作未选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              },
              {
                adResourcesId: "5",
                parentId: "2",
                name: "商家编辑",
                value: "merchant-edit", 
                selected: true, // 只有这个操作被选中
                htmlTemplet: null,
                icon: null,
                seq: 3,
                children: []
              }
            ]
          },
          {
            adResourcesId: "6",
            parentId: "1",
            name: "商家分组",
            value: "merchant-group",
            selected: false,
            htmlTemplet: "/merchant/group", 
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "7",
                parentId: "6",
                name: "分组查询",
                value: "group-query",
                selected: true,
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "8", 
                parentId: "6",
                name: "分组新增",
                value: "group-add",
                selected: true,
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          }
        ]
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试结果
const testResults = ref<Array<{
  test: string
  description: string
  expected: string
  actual: string
  passed: boolean
  timestamp: string
}>>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
}

// 问题1验证：初始数据选中状态一致性
const validateProblem1 = async () => {
  // 重置数据
  selectedKeys.value = [...processedData.value.selectedResources]
  
  await nextTick()
  
  // 检查商家列表节点的状态
  const merchantListNode = processedData.value.processedData.treeData
    .find(node => node.key === 'merchant-manage')?.children
    ?.find(node => node.key === 'merchant-list')
  
  if (merchantListNode && merchantListNode.actions) {
    const selectedActions = merchantListNode.actions.filter(action => 
      selectedKeys.value.includes(action.key)
    )
    
    const allActionsSelected = selectedActions.length === merchantListNode.actions.length
    const nodeSelected = selectedKeys.value.includes('merchant-list')
    
    // 期望：如果节点被选中但不是所有actions都被选中，全选框应该不显示为选中
    const expected = "全选框应该不显示为选中状态（因为不是所有actions都被选中）"
    const actual = allActionsSelected ? "全选框显示为选中" : "全选框显示为未选中"
    const passed = !allActionsSelected // 修复后应该是false
    
    testResults.value.push({
      test: "问题1验证：初始数据选中状态一致性",
      description: "商家列表节点被选中，但只有部分actions被选中",
      expected,
      actual,
      passed,
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

// 问题2验证：半选状态联动逻辑
const validateProblem2 = async () => {
  // 先选中商家分组的所有子节点
  selectedKeys.value = ['group-query', 'group-add']
  
  await nextTick()
  
  // 验证父节点是否被自动选中
  const step1Passed = selectedKeys.value.includes('merchant-group')
  
  testResults.value.push({
    test: "问题2验证-步骤1：所有子节点选中时父节点自动选中",
    description: "选中分组查询和分组新增后，商家分组应该自动选中",
    expected: "商家分组节点被自动选中",
    actual: step1Passed ? "商家分组节点已选中" : "商家分组节点未选中",
    passed: step1Passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 取消其中一个子节点
  selectedKeys.value = selectedKeys.value.filter(key => key !== 'group-add')
  
  await nextTick()
  
  // 验证父节点是否正确更新为半选状态
  const step2Passed = !selectedKeys.value.includes('merchant-group') && 
                      selectedKeys.value.includes('group-query')
  
  testResults.value.push({
    test: "问题2验证-步骤2：部分子节点取消时父节点显示半选",
    description: "取消分组新增后，商家分组应该显示半选状态",
    expected: "商家分组节点取消选中（显示半选状态）",
    actual: step2Passed ? "商家分组节点已取消选中" : "商家分组节点仍然选中",
    passed: step2Passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 取消所有子节点
  selectedKeys.value = selectedKeys.value.filter(key => key !== 'group-query')
  
  await nextTick()
  
  // 验证父节点是否完全取消选中
  const step3Passed = !selectedKeys.value.includes('merchant-group')
  
  testResults.value.push({
    test: "问题2验证-步骤3：所有子节点取消时父节点完全取消",
    description: "取消所有子节点后，商家分组应该完全取消选中",
    expected: "商家分组节点完全取消选中",
    actual: step3Passed ? "商家分组节点已取消选中" : "商家分组节点仍然选中",
    passed: step3Passed,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 运行所有验证测试
const runValidationTests = async () => {
  testResults.value = []
  
  console.log('开始运行修复验证测试...')
  
  await validateProblem1()
  
  setTimeout(async () => {
    await validateProblem2()
  }, 1000)
}

// 手动测试函数
const manualTest = (testName: string) => {
  switch (testName) {
    case 'reset':
      selectedKeys.value = [...processedData.value.selectedResources]
      break
    case 'selectMerchantList':
      selectedKeys.value = ['merchant-list']
      break
    case 'selectAllGroupActions':
      selectedKeys.value = ['group-query', 'group-add']
      break
    case 'selectPartialGroupActions':
      selectedKeys.value = ['group-query']
      break
    case 'clear':
      selectedKeys.value = []
      break
  }
}

// 获取节点状态信息
const getNodeStatus = (nodeKey: string) => {
  const node = processedData.value.processedData.treeData
    .find(n => n.key === nodeKey) || 
    processedData.value.processedData.treeData
      .flatMap(n => n.children || [])
      .find(n => n.key === nodeKey)
  
  if (!node) return null
  
  const isSelected = selectedKeys.value.includes(nodeKey)
  const selectedActions = node.actions?.filter(action => 
    selectedKeys.value.includes(action.key)
  ) || []
  
  return {
    nodeKey,
    nodeName: node.title,
    isSelected,
    totalActions: node.actions?.length || 0,
    selectedActionsCount: selectedActions.length,
    selectedActions: selectedActions.map(a => a.label)
  }
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="fix-validation-test">
    <div class="test-header">
      <h1>权限组件问题修复验证</h1>
      <p>验证初始数据选中状态一致性和半选状态联动逻辑的修复效果</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runValidationTests">
            🧪 运行修复验证测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('reset')">
            重置为初始状态
          </a-button>
          <a-button @click="manualTest('selectMerchantList')">
            选中商家列表
          </a-button>
          <a-button @click="manualTest('selectAllGroupActions')">
            选中所有分组操作
          </a-button>
          <a-button @click="manualTest('selectPartialGroupActions')">
            部分选中分组操作
          </a-button>
          <a-button @click="manualTest('clear')">
            清空选择
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="验证测试结果" size="small">
        <div class="results-summary">
          <a-statistic 
            title="通过率" 
            :value="((testResults.filter(r => r.passed).length / testResults.length) * 100).toFixed(1)" 
            suffix="%" 
            :value-style="{ color: testResults.every(r => r.passed) ? '#3f8600' : '#cf1322' }"
          />
        </div>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected }}</div>
              <div><strong>实际:</strong> {{ result.actual }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 节点状态监控 -->
    <div class="node-status">
      <a-card title="节点状态监控" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="status-card">
              <h4>商家列表</h4>
              <div v-if="getNodeStatus('merchant-list')" class="status-info">
                <p><strong>选中状态:</strong> {{ getNodeStatus('merchant-list')?.isSelected ? '是' : '否' }}</p>
                <p><strong>操作数量:</strong> {{ getNodeStatus('merchant-list')?.totalActions }}</p>
                <p><strong>已选操作:</strong> {{ getNodeStatus('merchant-list')?.selectedActionsCount }}</p>
                <p><strong>选中操作:</strong> {{ getNodeStatus('merchant-list')?.selectedActions.join(', ') || '无' }}</p>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="status-card">
              <h4>商家分组</h4>
              <div v-if="getNodeStatus('merchant-group')" class="status-info">
                <p><strong>选中状态:</strong> {{ getNodeStatus('merchant-group')?.isSelected ? '是' : '否' }}</p>
                <p><strong>操作数量:</strong> {{ getNodeStatus('merchant-group')?.totalActions }}</p>
                <p><strong>已选操作:</strong> {{ getNodeStatus('merchant-group')?.selectedActionsCount }}</p>
                <p><strong>选中操作:</strong> {{ getNodeStatus('merchant-group')?.selectedActions.join(', ') || '无' }}</p>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="status-card">
              <h4>商家管理</h4>
              <div v-if="getNodeStatus('merchant-manage')" class="status-info">
                <p><strong>选中状态:</strong> {{ getNodeStatus('merchant-manage')?.isSelected ? '是' : '否' }}</p>
                <p><strong>操作数量:</strong> {{ getNodeStatus('merchant-manage')?.totalActions }}</p>
                <p><strong>已选操作:</strong> {{ getNodeStatus('merchant-manage')?.selectedActionsCount }}</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 当前选中状态 -->
    <div class="current-state">
      <a-card title="当前选中状态" size="small">
        <div class="state-info">
          <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
          <div class="selected-keys">
            <a-tag 
              v-for="key in selectedKeys" 
              :key="key"
              color="blue"
              style="margin: 2px;"
            >
              {{ key }}
            </a-tag>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="false"
          :show-select-all="true"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>

    <!-- 问题说明 -->
    <div class="problem-description">
      <a-card title="修复的问题说明" size="small">
        <div class="problem-list">
          <div class="problem-item">
            <h4>🐛 问题1：初始数据选中状态不一致</h4>
            <p><strong>现象：</strong>商家列表节点的"全部"复选框显示为选中，但具体权限操作未选中</p>
            <p><strong>原因：</strong>use-permission-action.ts中状态计算逻辑错误</p>
            <p><strong>修复：</strong>只有所有actions都被选中时才显示全选状态</p>
          </div>
          
          <div class="problem-item">
            <h4>🐛 问题2：半选状态联动逻辑错误</h4>
            <p><strong>现象：</strong>取消子节点时父节点状态更新不正确</p>
            <p><strong>原因：</strong>updateParentNodesOnUncheck函数逻辑过于简单</p>
            <p><strong>修复：</strong>重新计算父节点状态，正确处理半选状态</p>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.fix-validation-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .node-status,
  .current-state,
  .permission-panel,
  .problem-description {
    margin-bottom: 16px;
  }

  .results-summary {
    margin-bottom: 16px;
    text-align: center;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-description {
        font-size: 13px;
        color: #595959;
        margin-bottom: 8px;
        font-style: italic;
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .status-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    
    h4 {
      margin: 0 0 8px 0;
      color: #262626;
    }
    
    .status-info {
      p {
        margin: 4px 0;
        font-size: 12px;
        color: #595959;
      }
    }
  }

  .state-info {
    .selected-keys {
      margin-top: 8px;
      max-height: 100px;
      overflow-y: auto;
    }
  }

  .problem-list {
    .problem-item {
      margin-bottom: 16px;
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      background: #fafafa;
      
      h4 {
        margin: 0 0 8px 0;
        color: #262626;
      }
      
      p {
        margin: 4px 0;
        font-size: 13px;
        color: #595959;
        line-height: 1.5;
      }
    }
  }
}
</style>
