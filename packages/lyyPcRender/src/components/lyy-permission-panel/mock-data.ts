export default {
  "code": "0000000",
    "message": "操作成功",
      "body": {
    "roleId": "1109525",
      "roleName": "黄金射手",
        "roleRemark": "数据库时1",
          "resources": [
            {
              "adResourcesId": "1112833",
              "parentId": null,
              "name": "商家管理",
              "value": "charging-dealer-catalog-merchant",
              "selected": true,
              "htmlTemplet": null,
              "icon": null,
              "seq": 1,
              "children": [
                {
                  "adResourcesId": "1112841",
                  "parentId": "1112833",
                  "name": "商家列表",
                  "value": "charging-dealer-menu-merchantList",
                  "selected": true,
                  "htmlTemplet": "merchant/merchantManagement",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1112969",
                      "parentId": "1112841",
                      "name": "商家列表查询",
                      "value": "charging-dealer-button-merchantRelationPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112970",
                      "parentId": "1112841",
                      "name": "归属渠道列表查询",
                      "value": "charging-dealer-button-m-agentRelationList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112971",
                      "parentId": "1112841",
                      "name": "客户地区列表查询",
                      "value": "charging-dealer-button-m-districtList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112972",
                      "parentId": "1112841",
                      "name": "分组标签查询",
                      "value": "charging-dealer-button-m-merchantLabelList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112973",
                      "parentId": "1112841",
                      "name": "商家详情查询",
                      "value": "charging-dealer-button-m-merchantRelationList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112974",
                      "parentId": "1112841",
                      "name": "商家编辑",
                      "value": "charging-dealer-button-m-merchantEdit",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112975",
                      "parentId": "1112841",
                      "name": "商家获取手机号验证码",
                      "value": "charging-dealer-button-m-merchantGetSMS",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112976",
                      "parentId": "1112841",
                      "name": "商家验证码校验",
                      "value": "charging-dealer-button-m-merchantCheckSMS",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112977",
                      "parentId": "1112841",
                      "name": "根据手机号获取主题信息",
                      "value": "charging-dealer-button-m-getMerchantListByPhone",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112978",
                      "parentId": "1112841",
                      "name": "新增商家",
                      "value": "charging-dealer-button-m-merchantAdd",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113096",
                      "parentId": "1112841",
                      "name": "分组标签详情",
                      "value": "charging-dealer-button-m-groupLabelDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113100",
                      "parentId": "1112841",
                      "name": "商家设备转移/撤回记录",
                      "value": "charging-dealer-button-findMerchantTransferredRecordByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113101",
                      "parentId": "1112841",
                      "name": "结算信息查询",
                      "value": "charging-dealer-button-getMerchantMeterialPageUrl",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112842",
                  "parentId": "1112833",
                  "name": "商家分组",
                  "value": "charging-dealer-menu-merchantGroup",
                  "selected": true,
                  "htmlTemplet": "merchant/merchantGroup",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1112960",
                      "parentId": "1112842",
                      "name": "分组列表查询",
                      "value": "charging-dealer-button-queryPageGroupLabel",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112961",
                      "parentId": "1112842",
                      "name": "分组新增和编辑",
                      "value": "charging-dealer-button-addOrEditGroupLabel",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112962",
                      "parentId": "1112842",
                      "name": "删除分组",
                      "value": "charging-dealer-button-deleteGroupLabel",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1113098",
                  "parentId": "1112833",
                  "name": "商家渠道管理",
                  "value": "charging-dealer-menu-merchantSource",
                  "selected": false,
                  "htmlTemplet": "merchant/merchantChannelManagement",
                  "icon": null,
                  "seq": null,
                  "children": []
                }
              ]
            },
            {
              "adResourcesId": "1112871",
              "parentId": null,
              "name": "订单管理",
              "value": "charging-dealer-catalog-order",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 2,
              "children": [
                {
                  "adResourcesId": "1112873",
                  "parentId": "1112871",
                  "name": "订单列表",
                  "value": "charging-dealer-menu-orderList",
                  "selected": false,
                  "htmlTemplet": "chargeOrderManagement/index",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1113086",
                      "parentId": "1112873",
                      "name": "经销商分页查询订单列表",
                      "value": "charging-dealer-orderList-findByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113087",
                      "parentId": "1112873",
                      "name": "经销商查询订单详情",
                      "value": "charging-dealer-orderList-detail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113088",
                      "parentId": "1112873",
                      "name": "经销商查询订单过程数据",
                      "value": "charging-dealer-orderList-findOrderProcess",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1112834",
              "parentId": null,
              "name": "渠道管理",
              "value": "charging-dealer-catalog-channel",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 4,
              "children": [
                {
                  "adResourcesId": "1112843",
                  "parentId": "1112834",
                  "name": "渠道列表",
                  "value": "charging-dealer-menu-channelList",
                  "selected": false,
                  "htmlTemplet": "channel/channelManagement",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1112979",
                      "parentId": "1112843",
                      "name": "渠道列表查询",
                      "value": "charging-dealer-button-cl-saleList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112980",
                      "parentId": "1112843",
                      "name": "地区列表查询",
                      "value": "charging-dealer-button-cl-districtList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112981",
                      "parentId": "1112843",
                      "name": "分组标签查询",
                      "value": "charging-dealer-button-cl-groupLabelList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112984",
                      "parentId": "1112843",
                      "name": "渠道列表统计查询",
                      "value": "charging-dealer-button-cl-salesChannelStatistics",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112985",
                      "parentId": "1112843",
                      "name": "渠道列表编辑详情",
                      "value": "charging-dealer-button-cl-editSalesDetails",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112986",
                      "parentId": "1112843",
                      "name": "渠道编辑",
                      "value": "charging-dealer-button-cl-editSales",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112987",
                      "parentId": "1112843",
                      "name": "渠道禁用/启用",
                      "value": "charging-dealer-button-cl-editStatus",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112988",
                      "parentId": "1112843",
                      "name": "渠道累计设备统计",
                      "value": "charging-dealer-button-cl-equipmentStatistics",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112989",
                      "parentId": "1112843",
                      "name": "渠道设备转移/撤回记录",
                      "value": "charging-dealer-button-cl-findDownAgentTransferredRecordByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112990",
                      "parentId": "1112843",
                      "name": "渠道设备转移/撤回记录明细",
                      "value": "charging-dealer-button-cl-findTransferEquipmentByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112991",
                      "parentId": "1112843",
                      "name": "渠道验证码获取",
                      "value": "charging-dealer-button-cl-getSMS",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112992",
                      "parentId": "1112843",
                      "name": "渠道新增",
                      "value": "charging-dealer-button-cl-addSales",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113092",
                      "parentId": "1112843",
                      "name": "分组标签详情",
                      "value": "charging-dealer-button-cl-groupLabelDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112844",
                  "parentId": "1112834",
                  "name": "渠道分组",
                  "value": "charging-dealer-menu-channelGroup",
                  "selected": false,
                  "htmlTemplet": "channel/channelGroup",
                  "icon": null,
                  "seq": null,
                  "children": []
                },
                {
                  "adResourcesId": "1112964",
                  "parentId": "1112834",
                  "name": "分组列表查询",
                  "value": "charging-dealer-button-cl-queryPageGroupLabel",
                  "selected": false,
                  "htmlTemplet": null,
                  "icon": null,
                  "seq": 2,
                  "children": []
                },
                {
                  "adResourcesId": "1112965",
                  "parentId": "1112834",
                  "name": "分组新增和编辑",
                  "value": "charging-dealer-button-cl--addOrEditGroupLabel",
                  "selected": false,
                  "htmlTemplet": null,
                  "icon": null,
                  "seq": 2,
                  "children": []
                },
                {
                  "adResourcesId": "1112966",
                  "parentId": "1112834",
                  "name": "删除分组",
                  "value": "charging-dealer-button-cl-deleteGroupLabel",
                  "selected": false,
                  "htmlTemplet": null,
                  "icon": null,
                  "seq": 2,
                  "children": []
                }
              ]
            },
            {
              "adResourcesId": "1112835",
              "parentId": null,
              "name": "场地管理",
              "value": "charging-dealer-catalog-station",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 5,
              "children": [
                {
                  "adResourcesId": "1112845",
                  "parentId": "1112835",
                  "name": "场地列表",
                  "value": "charging-dealer-menu-stationList",
                  "selected": false,
                  "htmlTemplet": "ground/management",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1112904",
                      "parentId": "1112845",
                      "name": "场地列表查询接口",
                      "value": "charging-dealer-button-stationList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112906",
                      "parentId": "1112845",
                      "name": "获取省市区接口",
                      "value": "charging-dealer-button-stationGetDistrict",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112907",
                      "parentId": "1112845",
                      "name": "站点详情接口",
                      "value": "charging-dealer-button-stationDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112908",
                      "parentId": "1112845",
                      "name": "新增编辑站点接口",
                      "value": "charging-dealer-button-addOrUpdateStation",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112909",
                      "parentId": "1112845",
                      "name": "删除场地接口",
                      "value": "charging-dealer-button-deleteStation",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112910",
                      "parentId": "1112845",
                      "name": "获取有权限代理商接口",
                      "value": "charging-dealer-button-getRelationAgent",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112911",
                      "parentId": "1112845",
                      "name": "获取有权限商家接口",
                      "value": "charging-dealer-button-getRelationMerchant",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112915",
                      "parentId": "1112845",
                      "name": "获取有权限商家（不跨级）接口",
                      "value": "charging-dealer-button-getRelationMerchantSelect",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112953",
                      "parentId": "1112845",
                      "name": "站点列表详情接口",
                      "value": "charging-dealer-button-stationListDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112954",
                      "parentId": "1112845",
                      "name": "保存品牌信息",
                      "value": "charging-dealer-button-saveBrandInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112955",
                      "parentId": "1112845",
                      "name": "查询回显站点品牌信息",
                      "value": "charging-dealer-button-queryBrandInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112956",
                      "parentId": "1112845",
                      "name": "保存充值套餐信息",
                      "value": "charging-dealer-button-saveDiscountInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112957",
                      "parentId": "1112845",
                      "name": "查询回显充值套餐信息",
                      "value": "charging-dealer-button-queryDiscountInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112958",
                      "parentId": "1112845",
                      "name": "上传图片",
                      "value": "charging-dealer-button-uploadImage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1112959",
                      "parentId": "1112845",
                      "name": "通过站点获取对应的设备支持的计费方式",
                      "value": "charging-dealer-button-queryEquipmentCostWay",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113227",
                      "parentId": "1112845",
                      "name": "查询回显IC卡充值套餐信息",
                      "value": "charging-dealer-button-queryIcDiscountInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1112836",
              "parentId": null,
              "name": "设备管理",
              "value": "charging-dealer-catalog-equipment",
              "selected": false,
              "htmlTemplet": "equipment/equipmentlist",
              "icon": null,
              "seq": 6,
              "children": [
                {
                  "adResourcesId": "1112846",
                  "parentId": "1112836",
                  "name": "设备列表",
                  "value": "charging-dealer-menu-equipmentList",
                  "selected": false,
                  "htmlTemplet": "equipment/equipmentlist",
                  "icon": null,
                  "seq": 1,
                  "children": [
                    {
                      "adResourcesId": "1113038",
                      "parentId": "1112846",
                      "name": "经销商设备列表-分页查询设备列表",
                      "value": "charging-dealer-equipment-page",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113039",
                      "parentId": "1112846",
                      "name": "经销商设备列表-设备详情",
                      "value": "charging-dealer-equipment-detail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113040",
                      "parentId": "1112846",
                      "name": "经销商设备列表-设备列表统计信息",
                      "value": "charging-dealer-equipment-stat",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113041",
                      "parentId": "1112846",
                      "name": "经销商设备列表-查询设备支持的计费方式",
                      "value": "charging-dealer-equipment-costWay",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113042",
                      "parentId": "1112846",
                      "name": "经销商设备列表-查询设备计费套餐",
                      "value": "charging-dealer-equipment-groupService",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113043",
                      "parentId": "1112846",
                      "name": "经销商设备列表-修改设备状态",
                      "value": "charging-dealer-equipment-updateStatus",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113044",
                      "parentId": "1112846",
                      "name": "经销商设备列表-查询协议列表",
                      "value": "charging-dealer-equipment-findProtocolList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113045",
                      "parentId": "1112846",
                      "name": "经销商设备列表-修改设备协议",
                      "value": "charging-dealer-equipment-editEquipmentProtocol",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113046",
                      "parentId": "1112846",
                      "name": "经销商设备列表-回显设备修改详情",
                      "value": "charging-dealer-equipment-findEquipmentEditDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113047",
                      "parentId": "1112846",
                      "name": "经销商设备列表-编辑设备基础信息",
                      "value": "charging-dealer-equipment-editEquipmentInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113048",
                      "parentId": "1112846",
                      "name": "经销商设备列表-生成二维码",
                      "value": "charging-dealer-equipment-showQrcode",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113049",
                      "parentId": "1112846",
                      "name": "经销商设备列表-转移设备给代理商",
                      "value": "charging-dealer-equipment-transferEquipmentToAgent",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113050",
                      "parentId": "1112846",
                      "name": "经销商设备列表-撤回设备",
                      "value": "charging-dealer-equipment-withdrawEquipment",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113051",
                      "parentId": "1112846",
                      "name": "经销商设备列表-批量解绑设备",
                      "value": "charging-dealer-equipment-batchUnBundleEquipment",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113052",
                      "parentId": "1112846",
                      "name": "经销商设备列表-批量注册设备",
                      "value": "charging-dealer-equipment-batchRegisterEquipment",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113053",
                      "parentId": "1112846",
                      "name": "经销商设备列表-下载二维码",
                      "value": "charging-dealer-equipment-downloadQrcode",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113103",
                      "parentId": "1112846",
                      "name": "经销商设备列表-设置计费套餐",
                      "value": "charging-dealer-equipment-batchInsertCostWay",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113104",
                      "parentId": "1112846",
                      "name": "经销商设备列表-扫码枪前置校验",
                      "value": "charging-dealer-equipment-checkRegister",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112847",
                  "parentId": "1112836",
                  "name": "待转移设备",
                  "value": "charging-dealer-menu-equipmentNeedTransfer",
                  "selected": false,
                  "htmlTemplet": "equipment/notransferList",
                  "icon": null,
                  "seq": 2,
                  "children": [
                    {
                      "adResourcesId": "1113054",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-分页查询设备列表",
                      "value": "charging-dealer-equipmentNeedTransfer-page",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113055",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-设备详情",
                      "value": "charging-dealer-equipmentNeedTransfer-detail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113056",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-设备列表统计信息",
                      "value": "charging-dealer-equipmentNeedTransfer-stat",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113057",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-查询设备支持的计费方式",
                      "value": "charging-dealer-equipmentNeedTransfer-costWay",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113058",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-查询设备计费套餐",
                      "value": "charging-dealer-equipmentNeedTransfer-groupService",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113059",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-修改设备状态",
                      "value": "charging-dealer-equipmentNeedTransfer-updateStatus",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113060",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-查询协议列表",
                      "value": "charging-dealer-equipmentNeedTransfer-findProtocolList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113061",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-修改设备协议",
                      "value": "charging-dealer-equipmentNeedTransfer-editEquipmentProtocol",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113062",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-回显设备修改详情",
                      "value": "charging-dealer-equipmentNeedTransfer-findEquipmentEditDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113063",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-编辑设备基础信息",
                      "value": "charging-dealer-equipmentNeedTransfer-editEquipmentInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113064",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-生成二维码",
                      "value": "charging-dealer-equipmentNeedTransfer-showQrcode",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113065",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-转移设备给代理商",
                      "value": "charging-dealer-equipmentNeedTransfer-transferEquipmentToAgent",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113066",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-撤回设备",
                      "value": "charging-dealer-equipmentNeedTransfer-withdrawEquipment",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113067",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-批量解绑设备",
                      "value": "charging-dealer-equipmentNeedTransfer-batchUnBundleEquipment",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113068",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-批量注册设备",
                      "value": "charging-dealer-equipmentNeedTransfer-batchRegisterEquipment",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113069",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-下载二维码",
                      "value": "charging-dealer-equipmentNeedTransfer-downloadQrcode",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113105",
                      "parentId": "1112847",
                      "name": "经销商待转移设备列表-扫码枪前置校验",
                      "value": "charging-dealer-equipmentNeedTransfer-checkRegister",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112934",
                  "parentId": "1112836",
                  "name": "设备转移记录",
                  "value": "charging-dealer-menu-equipmentTransfer",
                  "selected": false,
                  "htmlTemplet": "equipment/equipmentTransferLog",
                  "icon": null,
                  "seq": 3,
                  "children": [
                    {
                      "adResourcesId": "1113070",
                      "parentId": "1112934",
                      "name": "经销商分页查询转移记录批次",
                      "value": "charging-dealer-equipmentTransfer-findTransferRecordByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113071",
                      "parentId": "1112934",
                      "name": "经销商查询转移记录批次明细详情",
                      "value": "charging-dealer-equipmentTransfer-findTransferRecordDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113072",
                      "parentId": "1112934",
                      "name": "经销商分页查询转移批次详情",
                      "value": "charging-dealer-equipmentTransfer-findTransferRecordDetailByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113073",
                      "parentId": "1112934",
                      "name": "经销商分页查询转移记录明细",
                      "value": "charging-dealer-equipmentTransfer-findTransferEquipmentByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112935",
                  "parentId": "1112836",
                  "name": "设备撤回记录",
                  "value": "charging-dealer-menu-equipmentWithdraw",
                  "selected": false,
                  "htmlTemplet": "equipment/equipmentWithdrawLog",
                  "icon": null,
                  "seq": 4,
                  "children": [
                    {
                      "adResourcesId": "1113074",
                      "parentId": "1112935",
                      "name": "经销商分页查询撤回记录批次",
                      "value": "charging-dealer-equipmentWithdraw-findTransferRecordByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113075",
                      "parentId": "1112935",
                      "name": "经销商查询撤回记录批次明细详情",
                      "value": "charging-dealer-equipmentWithdraw-findTransferRecordDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113076",
                      "parentId": "1112935",
                      "name": "经销商分页查询撤回批次详情",
                      "value": "charging-dealer-equipmentWithdraw-findTransferRecordDetailByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113077",
                      "parentId": "1112935",
                      "name": "经销商分页查询撤回记录明细",
                      "value": "charging-dealer-equipmentWithdraw-findTransferEquipmentByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112936",
                  "parentId": "1112836",
                  "name": "设备采购记录",
                  "value": "charging-dealer-menu-equipmentPurchase",
                  "selected": false,
                  "htmlTemplet": "equipment/equipmentBuyLog",
                  "icon": null,
                  "seq": 5,
                  "children": [
                    {
                      "adResourcesId": "1113078",
                      "parentId": "1112936",
                      "name": "经销商分页查询采购记录批次",
                      "value": "charging-dealer-equipmentPurchase-findTransferRecordByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113079",
                      "parentId": "1112936",
                      "name": "经销商查询采购记录批次明细详情",
                      "value": "charging-dealer-equipmentPurchase-findTransferRecordDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113080",
                      "parentId": "1112936",
                      "name": "经销商分页查询采购批次详情",
                      "value": "charging-dealer-equipmentPurchase-findTransferRecordDetailByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113081",
                      "parentId": "1112936",
                      "name": "经销商分页查询采购记录明细",
                      "value": "charging-dealer-equipmentPurchase-findTransferEquipmentByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112937",
                  "parentId": "1112836",
                  "name": "设备退回记录",
                  "value": "charging-dealer-menu-equipmentRetrun",
                  "selected": false,
                  "htmlTemplet": "equipment/equipmentReturnLog",
                  "icon": null,
                  "seq": 6,
                  "children": [
                    {
                      "adResourcesId": "1113082",
                      "parentId": "1112937",
                      "name": "经销商分页查询退回记录批次",
                      "value": "charging-dealer-equipmentRetrun-findTransferRecordByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113083",
                      "parentId": "1112937",
                      "name": "经销商查询退回记录批次明细详情",
                      "value": "charging-dealer-equipmentRetrun-findTransferRecordDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113084",
                      "parentId": "1112937",
                      "name": "经销商分页查询退回批次详情",
                      "value": "charging-dealer-equipmentRetrun-findTransferRecordDetailByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113085",
                      "parentId": "1112937",
                      "name": "经销商分页查询退回记录明细",
                      "value": "charging-dealer-equipmentRetrun-findTransferEquipmentByPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1112837",
              "parentId": null,
              "name": "任务记录",
              "value": "charging-dealer-catalog-taskRecord",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 7,
              "children": [
                {
                  "adResourcesId": "1112848",
                  "parentId": "1112837",
                  "name": "任务记录",
                  "value": "charging-dealer-menu-taskRecordList",
                  "selected": false,
                  "htmlTemplet": "task/taskLog",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1113011",
                      "parentId": "1112848",
                      "name": "任务总览",
                      "value": "charging-dealer-button-taskTotalInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113012",
                      "parentId": "1112848",
                      "name": "场地设备任务分页列表",
                      "value": "charging-dealer-button-taskInfoPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113013",
                      "parentId": "1112848",
                      "name": "任务详情",
                      "value": "charging-dealer-button-taskDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113014",
                      "parentId": "1112848",
                      "name": "任务明细分页(根据任务id的明细)",
                      "value": "charging-dealer-button-taskDetailsPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113015",
                      "parentId": "1112848",
                      "name": "任务明细详情",
                      "value": "charging-dealer-button-taskInfoDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112849",
                  "parentId": "1112837",
                  "name": "明细列表",
                  "value": "charging-dealer-menu-taskRecordDetailList",
                  "selected": false,
                  "htmlTemplet": "task/taskDetailList",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1113016",
                      "parentId": "1112849",
                      "name": "任务明细分页",
                      "value": "charging-dealer-button-taskDetailPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1112838",
              "parentId": null,
              "name": "模板管理",
              "value": "charging-dealer-catalog-template",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 8,
              "children": [
                {
                  "adResourcesId": "1112850",
                  "parentId": "1112838",
                  "name": "模板列表",
                  "value": "charging-dealer-menu-templateList",
                  "selected": false,
                  "htmlTemplet": "template/management",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1113017",
                      "parentId": "1112850",
                      "name": "模版总览",
                      "value": "charging-dealer-button-findStat",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113018",
                      "parentId": "1112850",
                      "name": "添加模版",
                      "value": "charging-dealer-button-addTemplate",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113019",
                      "parentId": "1112850",
                      "name": "模版详情",
                      "value": "charging-dealer-button-templastDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113020",
                      "parentId": "1112850",
                      "name": "模版分页列表",
                      "value": "charging-dealer-button-templastPage",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113021",
                      "parentId": "1112850",
                      "name": "更新模版状态",
                      "value": "charging-dealer-button-updateTemplateStatus",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1112839",
              "parentId": null,
              "name": "下载中心",
              "value": "charging-dealer-catalog-down",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 9,
              "children": [
                {
                  "adResourcesId": "1112851",
                  "parentId": "1112839",
                  "name": "下载中心",
                  "value": "charging-dealer-menu-downList",
                  "selected": false,
                  "htmlTemplet": "download",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1112917",
                      "parentId": "1112851",
                      "name": "下载中心查询接口",
                      "value": "charging-dealer-button-downList",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1112840",
              "parentId": null,
              "name": "员工管理",
              "value": "charging-dealer-catalog-staff",
              "selected": false,
              "htmlTemplet": null,
              "icon": null,
              "seq": 10,
              "children": [
                {
                  "adResourcesId": "1112852",
                  "parentId": "1112840",
                  "name": "员工列表",
                  "value": "charging-dealer-menu-staffList",
                  "selected": false,
                  "htmlTemplet": "staffManagement/index",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1113022",
                      "parentId": "1112852",
                      "name": "员工总览",
                      "value": "charging-dealer-button-staffTotal",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113023",
                      "parentId": "1112852",
                      "name": "员工分页列表",
                      "value": "charging-dealer-button-findPageStaffInfo",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113024",
                      "parentId": "1112852",
                      "name": "添加员工",
                      "value": "charging-dealer-button-addStaff",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113025",
                      "parentId": "1112852",
                      "name": "编辑员工",
                      "value": "charging-dealer-button-editStaff",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113026",
                      "parentId": "1112852",
                      "name": "停用员工",
                      "value": "charging-dealer-button-stopStaff",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113027",
                      "parentId": "1112852",
                      "name": "删除员工",
                      "value": "charging-dealer-button-deleteStaff",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113028",
                      "parentId": "1112852",
                      "name": "员工详情",
                      "value": "charging-dealer-button-findStaffDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1112853",
                  "parentId": "1112840",
                  "name": "员工角色管理",
                  "value": "charging-dealer-menu-staffRoleList",
                  "selected": false,
                  "htmlTemplet": "staffRole/index",
                  "icon": null,
                  "seq": null,
                  "children": [
                    {
                      "adResourcesId": "1113029",
                      "parentId": "1112853",
                      "name": "员工角色管理列表",
                      "value": "charging-dealer-button-findAuthorityRoles",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113030",
                      "parentId": "1112853",
                      "name": "添加或编辑角色",
                      "value": "charging-dealer-button-addOrEditRoles",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113031",
                      "parentId": "1112853",
                      "name": "根据用户获取资源",
                      "value": "charging-dealer-button-findResourcesUser",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113032",
                      "parentId": "1112853",
                      "name": "角色获取菜单",
                      "value": "charging-dealer-button-getResourcesRole",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113033",
                      "parentId": "1112853",
                      "name": "角色获取员工信息",
                      "value": "charging-dealer-button-getStaffByRole",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113034",
                      "parentId": "1112853",
                      "name": "删除角色",
                      "value": "charging-dealer-button-deleteRole",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    },
                    {
                      "adResourcesId": "1113035",
                      "parentId": "1112853",
                      "name": "角色详情",
                      "value": "charging-dealer-button-getRoleDetail",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 2,
                      "children": []
                    }
                  ]
                }
              ]
            },
            {
              "adResourcesId": "1103199",
              "parentId": null,
              "name": "公众号对接",
              "value": "/officialAccount",
              "selected": false,
              "htmlTemplet": "officialAccount",
              "icon": "tencentButtJoint",
              "seq": 124,
              "children": [
                {
                  "adResourcesId": "1103200",
                  "parentId": "1103199",
                  "name": "公众号对接",
                  "value": "OfficialAccountDocker",
                  "selected": false,
                  "htmlTemplet": "officialAccount/OfficialAccountDocker",
                  "icon": null,
                  "seq": 24,
                  "children": [
                    {
                      "adResourcesId": "1110562",
                      "parentId": "1103200",
                      "name": "查询代理商绑定情况",
                      "value": "OfficialAccountDocker_authTypes",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 24,
                      "children": []
                    }
                  ]
                },
                {
                  "adResourcesId": "1103201",
                  "parentId": "1103199",
                  "name": "公众号功能配置",
                  "value": "OfficialAccountConfig",
                  "selected": false,
                  "htmlTemplet": "officialAccount/OfficialAccountConfig",
                  "icon": null,
                  "seq": 34,
                  "children": [
                    {
                      "adResourcesId": "1110793",
                      "parentId": "1103201",
                      "name": "获取公众号模板消息配置和菜单配置v2",
                      "value": "OfficialAccountConfig_getConfig",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": null,
                      "children": []
                    },
                    {
                      "adResourcesId": "1110794",
                      "parentId": "1103201",
                      "name": "公众号菜单和模板功能配置",
                      "value": "OfficialAccountConfig_updateConfig",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": null,
                      "children": []
                    },
                    {
                      "adResourcesId": "1110795",
                      "parentId": "1103201",
                      "name": "公众号解绑",
                      "value": "OfficialAccountConfig_deleteAuthorization",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": null,
                      "children": []
                    },
                    {
                      "adResourcesId": "1110563",
                      "parentId": "1103201",
                      "name": "获取公众号模板消息配置和菜单配置",
                      "value": "OfficialAccountConfig_getAllResult",
                      "selected": false,
                      "htmlTemplet": null,
                      "icon": null,
                      "seq": 24,
                      "children": []
                    }
                  ]
                }
              ]
            }
          ],
            "adResourceIds": [
              "1112833",
              "1112841",
              "1112842"
            ],
              "staffList": [
                {
                  "currentAgentUserId": "1004869",
                  "agentUserId": "1004869",
                  "adUserId": null,
                  "adOrgId": null,
                  "contractId": null,
                  "agentUserName": "1111",
                  "linkName": null,
                  "sellerRealName": null,
                  "phone": "***********",
                  "address": null,
                  "bdId": null,
                  "agentUserLoginId": null,
                  "password": null,
                  "associateSellerPhone": null,
                  "associateSellerName": null,
                  "parentAgentUserId": null,
                  "created": "2025-04-17 15:30:44",
                  "createdby": null,
                  "updated": null,
                  "updatedby": null,
                  "authorityAdUserId": null,
                  "type": null,
                  "associatedType": null,
                  "issend": null,
                  "kdMerchantNo": null,
                  "sendTarget": null,
                  "desensitization": null,
                  "dataMasking": null,
                  "accountPhone": "***********",
                  "email": null,
                  "districtId": null,
                  "state": 1,
                  "aliasName": null,
                  "agentType": null,
                  "logo": null,
                  "authorizationFlag": null,
                  "relationType": null,
                  "registerCode": null,
                  "factoryName": null,
                  "channelType": null
                }
              ]
  }
}