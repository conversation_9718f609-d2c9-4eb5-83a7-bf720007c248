import { PermissionSelection, PermissionTreeNode, PermissionDataTreeNode, Key } from './interface'
import type {
  PermissionTreeNode as ComponentPermissionTreeNode,
  PermissionAction,
  PermissionLevel
} from './types'

/**
 * 后端返回的原始权限资源节点结构
 */
interface BackendResourceNode {
  adResourcesId: string
  parentId: string | null
  name: string
  value: string
  selected: boolean
  htmlTemplet: string | null
  icon: string | null
  seq: number | null
  children: BackendResourceNode[]
}

/**
 * 后端返回的完整权限数据结构
 */
interface BackendPermissionResponse {
  code: string
  message: string
  body: {
    roleId: string
    roleName: string
    roleRemark: string
    resources: BackendResourceNode[]
  }
}

/**
 * 处理后的权限数据
 */
interface ProcessedPermissionData {
  treeData: ComponentPermissionTreeNode[]
  selectedPermissions: Record<string, PermissionAction[]>
  roleInfo: {
    roleId: string
    roleName: string
    roleRemark: string
  }
}

// 原有接口保持兼容
interface ResourceNode {
  adResourcesId: string
  parentId: string | null
  name: string
  value: string
  selected: boolean
  htmlTemplet: string | null
  icon: string | null
  seq: number | null
  children: ResourceNode[]
}

interface ProcessedData {
  treeData: PermissionDataTreeNode[]
  selectedKeys: Key[]
}

/**
 * 权限节点选择状态
 */
export interface NodeSelectionState {
  isSelected: boolean
  isIndeterminate: boolean
  selectedActions: Key[]
}


/**
 * 处理权限数据，将原始数据转换为树形结构并收集选中项
 * 优化版本：正确处理三层权限结构（模块→菜单→操作）
 * @param resources 原始资源数据
 * @returns 处理后的树形数据和选中项
 */
export const processPermissionData = (resources: ResourceNode[]): ProcessedData => {
  const selectedKeys: Key[] = []
  const selectedMap = new Map<Key, boolean>()

  /**
   * 递归收集选中状态 - 修复初始状态解析问题
   * 正确处理父子节点的选中状态联动
   */
  const collectSelectedKeys = (node: ResourceNode): void => {
    console.log(`[DEBUG] collectSelectedKeys - 处理节点: ${node.value}, selected: ${node.selected}`)

    if (node.selected) {
      selectedKeys.push(node.value)
      selectedMap.set(node.value, true)
      console.log(`[DEBUG] 添加选中节点: ${node.value}`)

      // 修复问题：当父节点选中时，递归收集所有子孙节点
      if (node.children && node.children.length > 0) {
        const allDescendantKeys = collectAllDescendantKeys(node)
        allDescendantKeys.forEach(key => {
          if (!selectedKeys.includes(key)) {
            selectedKeys.push(key)
            selectedMap.set(key, true)
            console.log(`[DEBUG] 自动添加子孙节点: ${key}`)
          }
        })
      }
    }

    // 继续递归处理子节点（处理子节点自身的 selected 状态）
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        collectSelectedKeys(child)
      })
    }
  }

  /**
   * 收集节点的所有子孙节点keys - 新增辅助函数
   * @param node 父节点
   * @returns 所有子孙节点的keys数组
   */
  const collectAllDescendantKeys = (node: ResourceNode): string[] => {
    const descendantKeys: string[] = []

    const traverse = (currentNode: ResourceNode) => {
      if (currentNode.children && currentNode.children.length > 0) {
        currentNode.children.forEach(child => {
          descendantKeys.push(child.value)
          console.log(`[DEBUG] 收集子孙节点: ${child.value}`)
          // 递归收集更深层的子节点
          traverse(child)
        })
      }
    }

    traverse(node)
    return descendantKeys
  }

  /**
   * 递归处理节点 - 纯函数
   * 根据三层结构正确转换数据
   */
  const processNode = (node: ResourceNode, level: number = 0, parentKey?: string): PermissionDataTreeNode => {
    const hasChildren = node.children && node.children.length > 0

    // 判断是否为操作层（第三层，所有子节点都是叶子节点）
    const isActionLevel = hasChildren && node.children.every(child =>
      !child.children || child.children.length === 0
    )

    // 如果是操作层，将子节点转换为 actions
    if (isActionLevel) {
      const actions = node.children.map(child => ({
        key: child.value,
        label: child.name,
      }))

      return {
        key: node.value,
        title: node.name,
        level,
        parentKey,
        actions,
        type: level === 0 ? 'menu' : level === 1 ? 'menu' : 'action'
      }
    }

    // 处理非操作层节点
    const children = hasChildren
      ? node.children.map(child => processNode(child, level + 1, node.value))
      : undefined

    return {
      key: node.value,
      title: node.name,
      level,
      parentKey,
      children,
      type: level === 0 ? 'menu' : level === 1 ? 'menu' : 'action'
    }
  }

  // 首先收集所有选中状态
  resources.forEach(node => collectSelectedKeys(node))

  // 然后处理树形结构
  const treeData = resources.map(node => processNode(node))

  return {
    treeData,
    selectedKeys
  }
}

/**
 * 从后端权限数据中提取有效数据
 * 专门处理包含 selected 字段的权限资源数据
 */
export const extractPermissionData = (backendData: any) => {
  // 验证数据结构
  if (!backendData || !backendData.body || !Array.isArray(backendData.body.resources)) {
    return {
      roleInfo: null,
      resources: [],
      selectedResources: [],
      processedData: { treeData: [], selectedKeys: [] }
    }
  }

  const { body } = backendData
  const { roleId, roleName, roleRemark, resources } = body

  // 角色信息
  const roleInfo = {
    roleId,
    roleName,
    roleRemark
  }

  // 提取所有选中的资源 - 修复初始状态解析问题
  const selectedResources: string[] = []

  const extractSelected = (nodes: ResourceNode[]) => {
    nodes.forEach(node => {
      console.log(`[DEBUG] extractSelected - 处理节点: ${node.value}, selected: ${node.selected}`)

      if (node.selected) {
        selectedResources.push(node.value)
        console.log(`[DEBUG] 添加选中资源: ${node.value}`)

        // 修复问题：当父节点选中时，递归收集所有子孙节点
        if (node.children && node.children.length > 0) {
          const allDescendantKeys = extractAllDescendantKeys(node)
          allDescendantKeys.forEach(key => {
            if (!selectedResources.includes(key)) {
              selectedResources.push(key)
              console.log(`[DEBUG] 自动添加子孙资源: ${key}`)
            }
          })
        }
      }

      // 继续递归处理子节点（处理子节点自身的 selected 状态）
      if (node.children && node.children.length > 0) {
        extractSelected(node.children)
      }
    })
  }

  /**
   * 提取节点的所有子孙节点keys - 新增辅助函数
   * @param node 父节点
   * @returns 所有子孙节点的keys数组
   */
  const extractAllDescendantKeys = (node: ResourceNode): string[] => {
    const descendantKeys: string[] = []

    const traverse = (currentNode: ResourceNode) => {
      if (currentNode.children && currentNode.children.length > 0) {
        currentNode.children.forEach(child => {
          descendantKeys.push(child.value)
          console.log(`[DEBUG] 提取子孙节点: ${child.value}`)
          // 递归收集更深层的子节点
          traverse(child)
        })
      }
    }

    traverse(node)
    return descendantKeys
  }

  extractSelected(resources)

  // 处理数据为组件格式
  const processedData = processPermissionData(resources)

  return {
    roleInfo,
    resources,
    selectedResources,
    processedData
  }
}

/**
 * 构建权限保存数据
 * 将组件的选中状态转换为后端需要的格式
 */
export const buildSaveData = (
  originalData: any,
  selectedKeys: Key[]
): any => {
  if (!originalData || !originalData.body) {
    return null
  }

  // 递归更新节点的 selected 状态
  const updateNodeSelected = (node: ResourceNode): ResourceNode => {
    const isSelected = selectedKeys.includes(node.value)

    return {
      ...node,
      selected: isSelected,
      children: node.children.map(child => updateNodeSelected(child))
    }
  }

  // 更新所有资源节点
  const updatedResources = originalData.body.resources.map((node: ResourceNode) =>
    updateNodeSelected(node)
  )

  return {
    ...originalData,
    body: {
      ...originalData.body,
      resources: updatedResources
    }
  }
}

/**
 * 获取权限统计信息
 */
export const getPermissionStatistics = (
  resources: ResourceNode[],
  selectedKeys: Key[]
) => {
  let totalNodes = 0
  let selectedNodes = 0
  let moduleCount = 0
  let menuCount = 0
  let actionCount = 0

  const traverse = (nodes: ResourceNode[], level: number = 0) => {
    nodes.forEach(node => {
      totalNodes++

      if (selectedKeys.includes(node.value)) {
        selectedNodes++
      }

      // 根据层级和子节点情况判断类型
      if (level === 0) {
        moduleCount++
      } else if (node.children && node.children.length > 0) {
        menuCount++
      } else {
        actionCount++
      }

      if (node.children && node.children.length > 0) {
        traverse(node.children, level + 1)
      }
    })
  }

  traverse(resources)

  return {
    totalNodes,
    selectedNodes,
    moduleCount,
    menuCount,
    actionCount,
    selectionRate: totalNodes > 0 ? ((selectedNodes / totalNodes) * 100).toFixed(1) : '0'
  }
}

/**
 * 搜索权限节点
 */
export const searchPermissionResources = (
  resources: ResourceNode[],
  keyword: string
): ResourceNode[] => {
  if (!keyword.trim()) {
    return resources
  }

  const lowerKeyword = keyword.toLowerCase()

  const searchNodes = (nodes: ResourceNode[]): ResourceNode[] => {
    return nodes.reduce((results: ResourceNode[], node) => {
      // 检查当前节点是否匹配
      const matchesKeyword =
        node.name.toLowerCase().includes(lowerKeyword) ||
        node.value.toLowerCase().includes(lowerKeyword)

      // 递归搜索子节点
      const matchedChildren = node.children ? searchNodes(node.children) : []

      // 如果当前节点匹配或有匹配的子节点，则包含在结果中
      if (matchesKeyword || matchedChildren.length > 0) {
        results.push({
          ...node,
          children: matchedChildren.length > 0 ? matchedChildren : node.children
        })
      }

      return results
    }, [])
  }

  return searchNodes(resources)
}

/**
 * 获取节点的选择状态（包括半选状态）
 * @param nodeKey 节点key
 * @param selectedKeys 已选中的keys
 * @param treeData 树形数据
 * @returns 选择状态信息
 */
export const getNodeSelectionState = (
  nodeKey: Key,
  selectedKeys: Key[],
  treeData: PermissionDataTreeNode[]
): NodeSelectionState => {
  // 查找节点
  const findNode = (nodes: PermissionDataTreeNode[], key: Key): PermissionDataTreeNode | null => {
    for (const node of nodes) {
      if (node.key === key) return node
      if (node.children) {
        const found = findNode(node.children, key)
        if (found) return found
      }
    }
    return null
  }

  const node = findNode(treeData, nodeKey)
  if (!node) {
    return { isSelected: false, isIndeterminate: false, selectedActions: [] }
  }

  // 如果节点有 actions，检查 actions 的选中状态
  if (node.actions) {
    const selectedActions = node.actions
      .map(action => action.key)
      .filter(actionKey => selectedKeys.includes(actionKey))

    const isSelected = selectedActions.length === node.actions.length
    const isIndeterminate = selectedActions.length > 0 && selectedActions.length < node.actions.length

    return {
      isSelected,
      isIndeterminate,
      selectedActions
    }
  }

  // 如果节点有子节点，检查子节点的选中状态
  if (node.children) {
    const childStates = node.children.map(child =>
      getNodeSelectionState(child.key, selectedKeys, treeData)
    )

    const selectedChildren = childStates.filter(state => state.isSelected).length
    const indeterminateChildren = childStates.filter(state => state.isIndeterminate).length

    const isSelected = selectedChildren === node.children.length
    const isIndeterminate = (selectedChildren > 0 && selectedChildren < node.children.length) ||
                           indeterminateChildren > 0

    return {
      isSelected,
      isIndeterminate,
      selectedActions: []
    }
  }

  // 叶子节点
  return {
    isSelected: selectedKeys.includes(nodeKey),
    isIndeterminate: false,
    selectedActions: selectedKeys.includes(nodeKey) ? [nodeKey] : []
  }
}
/**
 * 导出所有工具函数和类型
 */
export type {
  BackendResourceNode,
  BackendPermissionResponse,
  ProcessedPermissionData
}
