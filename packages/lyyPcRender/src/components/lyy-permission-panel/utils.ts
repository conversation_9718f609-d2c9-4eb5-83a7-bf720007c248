import { PermissionSelection, PermissionTreeNode, PermissionDataTreeNode, Key } from './interface'

interface ResourceNode {
  adResourcesId: string
  parentId: string | null
  name: string
  value: string
  selected: boolean
  htmlTemplet: string | null
  icon: string | null
  seq: number | null
  children: ResourceNode[]
}

interface ProcessedData {
  treeData: PermissionDataTreeNode[]
  selectedKeys: Key[]
}


/**
 * 处理权限数据，将原始数据转换为树形结构并收集选中项
 * @param resources 原始资源数据
 * @returns 处理后的树形数据和选中项
 */
export const processPermissionData = (resources: ResourceNode[]): ProcessedData => {
  const selectedKeys: Key[] = []
  
  // 递归处理节点
  const processNode = (node: ResourceNode, level: number = 0, parentKey?: string): PermissionDataTreeNode => {
    // 如果节点被选中，添加到选中项列表
    if (node.selected) {
      selectedKeys.push(node.value)

      if (node.children && node.children.length > 0) {
        node.children.forEach(child => {
          selectedKeys.push(child.value)
        })
      }
    }

    // 判断是否所有子节点都没有子节点
    // 如果所有子节点都没有子节点，则将子节点作为 actions
    const isAllChildrenNoChild = node.children?.every(child => child.children?.length === 0)

    // 如果是叶子节点，直接返回
    if (isAllChildrenNoChild) {
      return {
        key: node.value,
        title: node.name,
        level,
        parentKey,
        actions: node.children.map(item => ({
          key: item.value,
          label: item.name,
        }))
      }
    }

    // 处理子节点
    const children = node.children?.map(child => processNode(child, level + 1, node.value)) || []

    // 构建权限节点数据
    return {
      key: node.value,
      title: node.name,
      level,
      parentKey,
      children: children.length  > 0 && !isAllChildrenNoChild ? children : undefined,
      actions: isAllChildrenNoChild ? children.map(item => ({
        key: item.key,
        label: item.title,
      })) : undefined
    }
  }

  // 处理所有根节点
  const treeData = resources.map(node => processNode(node))

  return {
    treeData,
    selectedKeys
  }
}
