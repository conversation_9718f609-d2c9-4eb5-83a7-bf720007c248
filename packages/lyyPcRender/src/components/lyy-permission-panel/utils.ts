import { PermissionSelection, PermissionTreeNode, PermissionDataTreeNode, Key } from './interface'
import type {
  PermissionTreeNode as ComponentPermissionTreeNode,
  PermissionAction,
  PermissionLevel
} from './types'

/**
 * 后端返回的原始权限资源节点结构
 */
interface BackendResourceNode {
  adResourcesId: string
  parentId: string | null
  name: string
  value: string
  selected: boolean
  htmlTemplet: string | null
  icon: string | null
  seq: number | null
  children: BackendResourceNode[]
}

/**
 * 后端返回的完整权限数据结构
 */
interface BackendPermissionResponse {
  code: string
  message: string
  body: {
    roleId: string
    roleName: string
    roleRemark: string
    resources: BackendResourceNode[]
  }
}

/**
 * 处理后的权限数据
 */
interface ProcessedPermissionData {
  treeData: ComponentPermissionTreeNode[]
  selectedPermissions: Record<string, PermissionAction[]>
  roleInfo: {
    roleId: string
    roleName: string
    roleRemark: string
  }
}

// 原有接口保持兼容
interface ResourceNode {
  adResourcesId: string
  parentId: string | null
  name: string
  value: string
  selected: boolean
  htmlTemplet: string | null
  icon: string | null
  seq: number | null
  children: ResourceNode[]
}

interface ProcessedData {
  treeData: PermissionDataTreeNode[]
  selectedKeys: Key[]
}

/**
 * 权限节点选择状态
 */
export interface NodeSelectionState {
  isSelected: boolean
  isIndeterminate: boolean
  selectedActions: Key[]
}


/**
 * 处理权限数据，将原始数据转换为树形结构并收集选中项
 * 优化版本：正确处理三层权限结构（模块→菜单→操作）
 * @param resources 原始资源数据
 * @returns 处理后的树形数据和选中项
 */
export const processPermissionData = (resources: ResourceNode[]): ProcessedData => {
  const selectedKeys: Key[] = []
  const selectedMap = new Map<Key, boolean>()

  /**
   * 递归收集选中状态 - 纯函数
   * 正确处理父子节点的选中状态联动
   */
  const collectSelectedKeys = (node: ResourceNode): void => {
    if (node.selected) {
      selectedKeys.push(node.value)
      selectedMap.set(node.value, true)
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        collectSelectedKeys(child)
      })
    }
  }

  /**
   * 递归处理节点 - 纯函数
   * 根据三层结构正确转换数据
   */
  const processNode = (node: ResourceNode, level: number = 0, parentKey?: string): PermissionDataTreeNode => {
    const hasChildren = node.children && node.children.length > 0

    // 判断是否为操作层（第三层，所有子节点都是叶子节点）
    const isActionLevel = hasChildren && node.children.every(child =>
      !child.children || child.children.length === 0
    )

    // 如果是操作层，将子节点转换为 actions
    if (isActionLevel) {
      const actions = node.children.map(child => ({
        key: child.value,
        label: child.name,
      }))

      return {
        key: node.value,
        title: node.name,
        level,
        parentKey,
        actions,
        type: level === 0 ? 'menu' : level === 1 ? 'menu' : 'action'
      }
    }

    // 处理非操作层节点
    const children = hasChildren
      ? node.children.map(child => processNode(child, level + 1, node.value))
      : undefined

    return {
      key: node.value,
      title: node.name,
      level,
      parentKey,
      children,
      type: level === 0 ? 'menu' : level === 1 ? 'menu' : 'action'
    }
  }

  // 首先收集所有选中状态
  resources.forEach(node => collectSelectedKeys(node))

  // 然后处理树形结构
  const treeData = resources.map(node => processNode(node))

  return {
    treeData,
    selectedKeys
  }
}

/**
 * 从后端权限数据中提取有效数据
 * 专门处理包含 selected 字段的权限资源数据
 */
export const extractPermissionData = (backendData: any) => {
  // 验证数据结构
  if (!backendData || !backendData.body || !Array.isArray(backendData.body.resources)) {
    console.warn('权限数据格式不正确:', backendData)
    return {
      roleInfo: null,
      resources: [],
      selectedResources: [],
      processedData: { treeData: [], selectedKeys: [] }
    }
  }

  const { body } = backendData
  const { roleId, roleName, roleRemark, resources } = body

  // 角色信息
  const roleInfo = {
    roleId,
    roleName,
    roleRemark
  }

  // 提取所有选中的资源
  const selectedResources: string[] = []

  const extractSelected = (nodes: ResourceNode[]) => {
    nodes.forEach(node => {
      if (node.selected) {
        selectedResources.push(node.value)
      }
      if (node.children && node.children.length > 0) {
        extractSelected(node.children)
      }
    })
  }

  extractSelected(resources)

  // 处理数据为组件格式
  const processedData = processPermissionData(resources)

  return {
    roleInfo,
    resources,
    selectedResources,
    processedData
  }
}

/**
 * 构建权限保存数据
 * 将组件的选中状态转换为后端需要的格式
 */
export const buildSaveData = (
  originalData: any,
  selectedKeys: Key[]
): any => {
  if (!originalData || !originalData.body) {
    return null
  }

  // 递归更新节点的 selected 状态
  const updateNodeSelected = (node: ResourceNode): ResourceNode => {
    const isSelected = selectedKeys.includes(node.value)

    return {
      ...node,
      selected: isSelected,
      children: node.children.map(child => updateNodeSelected(child))
    }
  }

  // 更新所有资源节点
  const updatedResources = originalData.body.resources.map((node: ResourceNode) =>
    updateNodeSelected(node)
  )

  return {
    ...originalData,
    body: {
      ...originalData.body,
      resources: updatedResources
    }
  }
}

/**
 * 获取权限统计信息
 */
export const getPermissionStatistics = (
  resources: ResourceNode[],
  selectedKeys: Key[]
) => {
  let totalNodes = 0
  let selectedNodes = 0
  let moduleCount = 0
  let menuCount = 0
  let actionCount = 0

  const traverse = (nodes: ResourceNode[], level: number = 0) => {
    nodes.forEach(node => {
      totalNodes++

      if (selectedKeys.includes(node.value)) {
        selectedNodes++
      }

      // 根据层级和子节点情况判断类型
      if (level === 0) {
        moduleCount++
      } else if (node.children && node.children.length > 0) {
        menuCount++
      } else {
        actionCount++
      }

      if (node.children && node.children.length > 0) {
        traverse(node.children, level + 1)
      }
    })
  }

  traverse(resources)

  return {
    totalNodes,
    selectedNodes,
    moduleCount,
    menuCount,
    actionCount,
    selectionRate: totalNodes > 0 ? ((selectedNodes / totalNodes) * 100).toFixed(1) : '0'
  }
}

/**
 * 搜索权限节点
 */
export const searchPermissionResources = (
  resources: ResourceNode[],
  keyword: string
): ResourceNode[] => {
  if (!keyword.trim()) {
    return resources
  }

  const lowerKeyword = keyword.toLowerCase()

  const searchNodes = (nodes: ResourceNode[]): ResourceNode[] => {
    return nodes.reduce((results: ResourceNode[], node) => {
      // 检查当前节点是否匹配
      const matchesKeyword =
        node.name.toLowerCase().includes(lowerKeyword) ||
        node.value.toLowerCase().includes(lowerKeyword)

      // 递归搜索子节点
      const matchedChildren = node.children ? searchNodes(node.children) : []

      // 如果当前节点匹配或有匹配的子节点，则包含在结果中
      if (matchesKeyword || matchedChildren.length > 0) {
        results.push({
          ...node,
          children: matchedChildren.length > 0 ? matchedChildren : node.children
        })
      }

      return results
    }, [])
  }

  return searchNodes(resources)
}

/**
 * 获取节点的选择状态（包括半选状态）
 * @param nodeKey 节点key
 * @param selectedKeys 已选中的keys
 * @param treeData 树形数据
 * @returns 选择状态信息
 */
export const getNodeSelectionState = (
  nodeKey: Key,
  selectedKeys: Key[],
  treeData: PermissionDataTreeNode[]
): NodeSelectionState => {
  // 查找节点
  const findNode = (nodes: PermissionDataTreeNode[], key: Key): PermissionDataTreeNode | null => {
    for (const node of nodes) {
      if (node.key === key) return node
      if (node.children) {
        const found = findNode(node.children, key)
        if (found) return found
      }
    }
    return null
  }

  const node = findNode(treeData, nodeKey)
  if (!node) {
    return { isSelected: false, isIndeterminate: false, selectedActions: [] }
  }

  // 如果节点有 actions，检查 actions 的选中状态
  if (node.actions) {
    const selectedActions = node.actions
      .map(action => action.key)
      .filter(actionKey => selectedKeys.includes(actionKey))

    const isSelected = selectedActions.length === node.actions.length
    const isIndeterminate = selectedActions.length > 0 && selectedActions.length < node.actions.length

    return {
      isSelected,
      isIndeterminate,
      selectedActions
    }
  }

  // 如果节点有子节点，检查子节点的选中状态
  if (node.children) {
    const childStates = node.children.map(child =>
      getNodeSelectionState(child.key, selectedKeys, treeData)
    )

    const selectedChildren = childStates.filter(state => state.isSelected).length
    const indeterminateChildren = childStates.filter(state => state.isIndeterminate).length

    const isSelected = selectedChildren === node.children.length
    const isIndeterminate = (selectedChildren > 0 && selectedChildren < node.children.length) ||
                           indeterminateChildren > 0

    return {
      isSelected,
      isIndeterminate,
      selectedActions: []
    }
  }

  // 叶子节点
  return {
    isSelected: selectedKeys.includes(nodeKey),
    isIndeterminate: false,
    selectedActions: selectedKeys.includes(nodeKey) ? [nodeKey] : []
  }
}

/**
 * 获取节点的所有子节点keys（包括actions）
 * @param nodeKey 节点key
 * @param treeData 树形数据
 * @returns 所有子节点keys
 */
export const getAllChildrenKeys = (
  nodeKey: Key,
  treeData: PermissionDataTreeNode[]
): Key[] => {
  const findNode = (nodes: PermissionDataTreeNode[], key: Key): PermissionDataTreeNode | null => {
    for (const node of nodes) {
      if (node.key === key) return node
      if (node.children) {
        const found = findNode(node.children, key)
        if (found) return found
      }
    }
    return null
  }

  const node = findNode(treeData, nodeKey)
  if (!node) return []

  const childrenKeys: Key[] = []

  const collectKeys = (currentNode: PermissionDataTreeNode) => {
    // 收集 actions
    if (currentNode.actions) {
      currentNode.actions.forEach(action => {
        childrenKeys.push(action.key)
      })
    }

    // 收集子节点
    if (currentNode.children) {
      currentNode.children.forEach(child => {
        childrenKeys.push(child.key)
        collectKeys(child)
      })
    }
  }

  collectKeys(node)
  return childrenKeys
}

/**
 * 获取节点的所有父节点keys
 * @param nodeKey 节点key
 * @param treeData 树形数据
 * @returns 所有父节点keys
 */
export const getAllParentKeys = (
  nodeKey: Key,
  treeData: PermissionDataTreeNode[]
): Key[] => {
  const parentKeys: Key[] = []

  const findParents = (
    nodes: PermissionDataTreeNode[],
    targetKey: Key,
    currentPath: Key[] = []
  ): boolean => {
    for (const node of nodes) {
      const newPath = [...currentPath, node.key]

      // 检查当前节点
      if (node.key === targetKey) {
        parentKeys.push(...currentPath)
        return true
      }

      // 检查 actions
      if (node.actions) {
        for (const action of node.actions) {
          if (action.key === targetKey) {
            parentKeys.push(...newPath)
            return true
          }
        }
      }

      // 递归检查子节点
      if (node.children && findParents(node.children, targetKey, newPath)) {
        return true
      }
    }
    return false
  }

  findParents(treeData, nodeKey)
  return parentKeys
}

/**
 * 根据节点名称和层级判断节点类型
 */
const determineNodeType = (name: string, level: number, hasChildren: boolean): 'module' | 'menu' | 'button' | 'field' => {
  // 根据层级和是否有子节点判断类型
  if (level === 0) {
    return 'module' // 第一级为模块
  } else if (hasChildren) {
    return 'menu' // 有子节点的为菜单
  } else {
    // 根据名称关键词判断是按钮还是字段
    const buttonKeywords = ['查询', '新增', '编辑', '删除', '导出', '导入', '审批', '下载', '上传', '打印', '复制']
    const isButton = buttonKeywords.some(keyword => name.includes(keyword))
    return isButton ? 'button' : 'field'
  }
}

/**
 * 根据节点名称推断可用的权限操作
 */
const inferAvailableActions = (name: string, nodeType: 'module' | 'menu' | 'button' | 'field'): string[] => {
  const actionMap: Record<string, string> = {
    '查询': 'query',
    '新增': 'add',
    '编辑': 'edit',
    '删除': 'delete',
    '导出': 'export',
    '导入': 'import',
    '审批': 'approve',
    '下载': 'download',
    '上传': 'upload',
    '打印': 'print',
    '复制': 'copy',
    '全部': 'view'
  }

  // 如果是按钮类型，根据名称推断具体操作
  if (nodeType === 'button') {
    for (const [keyword, action] of Object.entries(actionMap)) {
      if (name.includes(keyword)) {
        return [action]
      }
    }
    return ['view'] // 默认为查看权限
  }

  // 菜单和模块类型返回常用操作
  if (nodeType === 'menu' || nodeType === 'module') {
    return ['view', 'add', 'edit', 'delete', 'export', 'query']
  }

  // 字段类型返回基础操作
  return ['view', 'edit']
}

// 移除了复杂的转换函数，专注于核心的 processPermissionData 函数

/**
 * 从权限树中提取所有选中的权限
 * 使用深度优先遍历算法
 */
export const extractSelectedPermissions = (
  treeData: ComponentPermissionTreeNode[]
): Record<string, PermissionAction[]> => {
  const selectedPermissions: Record<string, PermissionAction[]> = {}

  const traverse = (nodes: ComponentPermissionTreeNode[]) => {
    nodes.forEach(node => {
      // 检查节点的 extra.selected 属性
      if (node.extra?.selected && node.availableActions) {
        selectedPermissions[node.key] = [...node.availableActions]
      }

      // 递归处理子节点
      if (node.children) {
        traverse(node.children)
      }
    })
  }

  traverse(treeData)
  return selectedPermissions
}

/**
 * 将权限数据转换回后端格式
 * 用于保存权限时发送给后端
 */
export const transformToBackendFormat = (
  originalData: BackendPermissionResponse,
  selectedPermissions: Record<string, PermissionAction[]>
): BackendPermissionResponse => {
  const { body } = originalData

  // 递归更新节点的 selected 状态
  const updateNodeSelected = (node: BackendResourceNode): BackendResourceNode => {
    const isSelected = selectedPermissions.hasOwnProperty(node.value)

    return {
      ...node,
      selected: isSelected,
      children: node.children.map(child => updateNodeSelected(child))
    }
  }

  // 更新所有资源节点
  const updatedResources = body.resources.map(node => updateNodeSelected(node))

  return {
    ...originalData,
    body: {
      ...body,
      resources: updatedResources
    }
  }
}

/**
 * 根据权限级别过滤权限数据
 * 实现策略模式，不同级别使用不同的过滤策略
 */
export const filterPermissionsByLevel = (
  treeData: ComponentPermissionTreeNode[],
  level: PermissionLevel
): ComponentPermissionTreeNode[] => {
  // 定义过滤策略
  const filterStrategies = {
    view: (node: ComponentPermissionTreeNode) =>
      node.type === 'module' || node.type === 'menu',
    data: (node: ComponentPermissionTreeNode) =>
      node.type === 'menu' || node.type === 'button',
    field: (node: ComponentPermissionTreeNode) =>
      node.type === 'button' || node.type === 'field'
  }

  const strategy = filterStrategies[level]

  const filterNodes = (nodes: ComponentPermissionTreeNode[]): ComponentPermissionTreeNode[] => {
    return nodes.reduce((filtered: ComponentPermissionTreeNode[], node) => {
      // 递归过滤子节点
      const filteredChildren = node.children ? filterNodes(node.children) : undefined

      // 如果节点符合策略或有符合条件的子节点，则保留
      if (strategy(node) || (filteredChildren && filteredChildren.length > 0)) {
        filtered.push({
          ...node,
          children: filteredChildren
        })
      }

      return filtered
    }, [])
  }

  return filterNodes(treeData)
}

/**
 * 搜索权限节点
 * 支持按名称、值或描述搜索
 */
export const searchPermissionNodes = (
  treeData: ComponentPermissionTreeNode[],
  keyword: string
): ComponentPermissionTreeNode[] => {
  if (!keyword.trim()) {
    return treeData
  }

  const lowerKeyword = keyword.toLowerCase()

  const searchNodes = (nodes: ComponentPermissionTreeNode[]): ComponentPermissionTreeNode[] => {
    return nodes.reduce((results: ComponentPermissionTreeNode[], node) => {
      // 检查当前节点是否匹配
      const matchesKeyword =
        node.title.toLowerCase().includes(lowerKeyword) ||
        node.key.toString().toLowerCase().includes(lowerKeyword) ||
        (node.description && node.description.toLowerCase().includes(lowerKeyword))

      // 递归搜索子节点
      const matchedChildren = node.children ? searchNodes(node.children) : []

      // 如果当前节点匹配或有匹配的子节点，则包含在结果中
      if (matchesKeyword || matchedChildren.length > 0) {
        results.push({
          ...node,
          children: matchedChildren.length > 0 ? matchedChildren : node.children
        })
      }

      return results
    }, [])
  }

  return searchNodes(treeData)
}

/**
 * 获取节点的所有父节点路径
 * 用于权限继承和联动处理
 */
export const getNodePath = (
  treeData: ComponentPermissionTreeNode[],
  targetKey: string
): ComponentPermissionTreeNode[] => {
  const path: ComponentPermissionTreeNode[] = []

  const findPath = (
    nodes: ComponentPermissionTreeNode[],
    target: string,
    currentPath: ComponentPermissionTreeNode[]
  ): boolean => {
    for (const node of nodes) {
      const newPath = [...currentPath, node]

      if (node.key === target) {
        path.push(...newPath)
        return true
      }

      if (node.children && findPath(node.children, target, newPath)) {
        return true
      }
    }
    return false
  }

  findPath(treeData, targetKey, [])
  return path
}

/**
 * 验证权限数据的完整性
 * 检查数据结构是否符合预期
 */
export const validatePermissionData = (data: any): data is BackendPermissionResponse => {
  try {
    return (
      typeof data === 'object' &&
      data !== null &&
      typeof data.code === 'string' &&
      typeof data.message === 'string' &&
      typeof data.body === 'object' &&
      data.body !== null &&
      typeof data.body.roleId === 'string' &&
      typeof data.body.roleName === 'string' &&
      Array.isArray(data.body.resources)
    )
  } catch {
    return false
  }
}

/**
 * 导出所有工具函数和类型
 */
export type {
  BackendResourceNode,
  BackendPermissionResponse,
  ProcessedPermissionData
}
