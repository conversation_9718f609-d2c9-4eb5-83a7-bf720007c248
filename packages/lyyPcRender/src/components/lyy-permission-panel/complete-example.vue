<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'permission-panel-complete-example'
})
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData, buildSaveData, getPermissionStatistics } from './utils'
import mockData from './mock-data'
import type { Key } from './interface'

// 演示配置
const demoConfig = ref({
  readonly: false,
  showSelectAll: true,
  useCustomData: false
})

// 权限数据
const permissionData = ref(mockData)

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的权限数据
const processedData = computed(() => {
  return extractPermissionData(permissionData.value)
})

// 角色信息
const roleInfo = computed(() => {
  return processedData.value.roleInfo
})

// 统计信息
const statistics = computed(() => {
  if (!processedData.value.resources) return null
  return getPermissionStatistics(processedData.value.resources, selectedKeys.value)
})

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
}

// 处理保存
const handleSave = (keys: Key[]) => {
  console.log('保存权限:', keys)
  
  // 构建保存数据
  const saveData = buildSaveData(permissionData.value, keys)
  console.log('保存数据格式:', saveData)
  
  // 模拟API调用
  simulateApiSave(saveData)
}

// 模拟API保存
const simulateApiSave = async (data: any) => {
  try {
    console.log('正在保存权限...')
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('权限保存成功!')
    alert('权限保存成功!')
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败!')
  }
}

// 切换只读模式
const toggleReadonly = () => {
  demoConfig.value.readonly = !demoConfig.value.readonly
}

// 切换全选按钮显示
const toggleSelectAll = () => {
  demoConfig.value.showSelectAll = !demoConfig.value.showSelectAll
}

// 重置权限
const resetPermissions = () => {
  selectedKeys.value = [...processedData.value.selectedResources]
}

// 清空权限
const clearPermissions = () => {
  selectedKeys.value = []
}

// 加载自定义数据
const loadCustomData = () => {
  // 模拟加载不同的权限数据
  const customData = {
    ...mockData,
    body: {
      ...mockData.body,
      roleName: '自定义角色',
      roleRemark: '这是一个自定义的权限角色'
    }
  }
  
  permissionData.value = customData
  demoConfig.value.useCustomData = true
}

// 恢复默认数据
const restoreDefaultData = () => {
  permissionData.value = mockData
  demoConfig.value.useCustomData = false
  resetPermissions()
}

// 导出权限配置
const exportPermissions = () => {
  const exportData = {
    roleInfo: roleInfo.value,
    selectedPermissions: selectedKeys.value,
    statistics: statistics.value,
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  }
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `permissions-${roleInfo.value?.roleName || 'unknown'}-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 初始化
onMounted(() => {
  // 设置初始选中状态
  selectedKeys.value = [...processedData.value.selectedResources]
})
</script>

<template>
  <div class="permission-complete-example">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>权限管理面板 - 完整示例</h1>
      <p>展示权限编辑组件的完整功能和使用方法</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <a-card title="演示控制" size="small">
        <a-space wrap>
          <a-button 
            :type="demoConfig.readonly ? 'default' : 'primary'"
            @click="toggleReadonly"
          >
            {{ demoConfig.readonly ? '启用编辑' : '只读模式' }}
          </a-button>
          
          <a-button @click="toggleSelectAll">
            {{ demoConfig.showSelectAll ? '隐藏工具栏' : '显示工具栏' }}
          </a-button>
          
          <a-button @click="resetPermissions">
            重置权限
          </a-button>
          
          <a-button @click="clearPermissions">
            清空权限
          </a-button>
          
          <a-button 
            :type="demoConfig.useCustomData ? 'primary' : 'default'"
            @click="demoConfig.useCustomData ? restoreDefaultData() : loadCustomData()"
          >
            {{ demoConfig.useCustomData ? '恢复默认' : '加载自定义' }}
          </a-button>
          
          <a-button @click="exportPermissions">
            导出配置
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 角色信息 -->
    <div v-if="roleInfo" class="role-info">
      <a-card title="角色信息" size="small">
        <a-descriptions :column="3" size="small">
          <a-descriptions-item label="角色ID">
            {{ roleInfo.roleId }}
          </a-descriptions-item>
          <a-descriptions-item label="角色名称">
            {{ roleInfo.roleName }}
          </a-descriptions-item>
          <a-descriptions-item label="角色备注">
            {{ roleInfo.roleRemark }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 统计信息 -->
    <div v-if="statistics" class="statistics">
      <a-card title="权限统计" size="small">
        <a-row :gutter="16">
          <a-col :span="4">
            <a-statistic title="总节点数" :value="statistics.totalNodes" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="已选节点" :value="statistics.selectedNodes" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="模块数量" :value="statistics.moduleCount" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="菜单数量" :value="statistics.menuCount" />
          </a-col>
          <a-col :span="4">
            <a-statistic title="操作数量" :value="statistics.actionCount" />
          </a-col>
          <a-col :span="4">
            <a-statistic 
              title="选中率" 
              :value="statistics.selectionRate" 
              suffix="%" 
            />
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel-container">
      <a-card title="权限配置" size="small">
        <lyy-permission-panel
          :permission-data="permissionData"
          v-model:value="selectedKeys"
          :readonly="demoConfig.readonly"
          :show-select-all="demoConfig.showSelectAll"
          @change="handlePermissionChange"
          @save="handleSave"
        />
      </a-card>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
      <a-card title="调试信息" size="small">
        <a-collapse>
          <a-collapse-panel key="selected" header="当前选中的权限">
            <div class="debug-content">
              <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
              <div class="selected-keys">
                <a-tag 
                  v-for="key in selectedKeys" 
                  :key="key"
                  color="blue"
                  style="margin: 2px;"
                >
                  {{ key }}
                </a-tag>
              </div>
            </div>
          </a-collapse-panel>
          
          <a-collapse-panel key="processed" header="处理后的数据结构">
            <pre class="json-display">{{ JSON.stringify(processedData, null, 2) }}</pre>
          </a-collapse-panel>
          
          <a-collapse-panel key="original" header="原始后端数据">
            <pre class="json-display">{{ JSON.stringify(permissionData, null, 2) }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </a-card>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <a-card title="使用说明" size="small">
        <div class="guide-content">
          <h4>组件特性：</h4>
          <ul>
            <li>✅ 支持三层权限结构（模块 → 菜单 → 操作）</li>
            <li>✅ 自动处理后端数据格式转换</li>
            <li>✅ 支持权限选择状态联动</li>
            <li>✅ 支持只读模式和编辑模式</li>
            <li>✅ 支持全选/清空功能</li>
            <li>✅ 完整的事件回调机制</li>
            <li>✅ TypeScript 类型安全</li>
            <li>✅ 响应式设计</li>
          </ul>
          
          <h4>使用方法：</h4>
          <pre class="code-example">
&lt;lyy-permission-panel
  :permission-data="backendData"
  v-model:value="selectedKeys"
  :readonly="false"
  :show-select-all="true"
  @change="handleChange"
  @save="handleSave"
/&gt;</pre>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.permission-complete-example {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .control-panel,
  .role-info,
  .statistics,
  .permission-panel-container,
  .debug-info,
  .usage-guide {
    margin-bottom: 16px;
  }

  .debug-content {
    .selected-keys {
      max-height: 200px;
      overflow-y: auto;
      margin-top: 8px;
    }
  }

  .json-display {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 12px;
    color: #586069;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .guide-content {
    h4 {
      color: #262626;
      margin: 16px 0 8px 0;
      
      &:first-child {
        margin-top: 0;
      }
    }

    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        line-height: 1.6;
        color: #595959;
      }
    }

    .code-example {
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      padding: 12px;
      font-size: 12px;
      color: #586069;
      overflow-x: auto;
    }
  }
}
</style>
