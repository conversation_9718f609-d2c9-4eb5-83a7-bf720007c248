<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'three-level-linkage-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData } from './utils'
import type { Key } from './interface'

// 测试数据 - 精确模拟三层级结构
const testData = ref({
  code: "200",
  message: "success",
  body: {
    roleId: "three-level-test",
    roleName: "三层级联动测试",
    roleRemark: "专门测试三层级联动逻辑",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "商家管理", // 第一级
        value: "merchant-manage",
        selected: true, // 初始全选
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2",
            parentId: "1",
            name: "商家列表", // 第二级
            value: "merchant-list",
            selected: false,
            htmlTemplet: "/merchant/list",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2",
                name: "商家列表查询", // 第三级
                value: "merchant-list-query",
                selected: true, // 初始选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "商家列表新增",
                value: "merchant-list-add",
                selected: true, // 初始选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              },
              {
                adResourcesId: "5",
                parentId: "2",
                name: "商家列表编辑",
                value: "merchant-list-edit",
                selected: true, // 初始选中
                htmlTemplet: null,
                icon: null,
                seq: 3,
                children: []
              }
            ]
          },
          {
            adResourcesId: "6",
            parentId: "1",
            name: "商家分组", // 第二级
            value: "merchant-group",
            selected: false,
            htmlTemplet: "/merchant/group",
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "7",
                parentId: "6",
                name: "分组查询", // 第三级
                value: "group-query",
                selected: true, // 初始选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "8",
                parentId: "6",
                name: "分组新增",
                value: "group-add",
                selected: true, // 初始选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          }
        ]
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试结果
const testResults = ref<Array<{
  test: string
  description: string
  expected: string
  actual: string
  passed: boolean
  timestamp: string
}>>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
}

// 获取节点状态
const getNodeState = (nodeKey: string) => {
  const isSelected = selectedKeys.value.includes(nodeKey)
  return {
    key: nodeKey,
    selected: isSelected
  }
}

// 三层级联动测试
const runThreeLevelLinkageTest = async () => {
  testResults.value = []
  
  console.log('开始三层级联动测试...')
  
  // 测试1: 初始状态验证
  await testInitialState()
  
  setTimeout(async () => {
    // 测试2: 取消第三级权限，验证向上传播
    await testUncheckThirdLevel()
  }, 1000)
  
  setTimeout(async () => {
    // 测试3: 恢复第三级权限，验证向上传播
    await testRestoreThirdLevel()
  }, 2000)
  
  setTimeout(async () => {
    // 测试4: 取消所有第三级权限，验证完全取消
    await testUncheckAllThirdLevel()
  }, 3000)
}

// 测试1: 初始状态验证
const testInitialState = async () => {
  // 重置为初始状态
  selectedKeys.value = [...processedData.value.selectedResources]
  
  await nextTick()
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantGroupSelected = selectedKeys.value.includes('merchant-group')
  
  // 期望：第一级应该选中（因为所有第三级都选中了）
  const expected = "第一级(商家管理)应该选中，第二级应该选中"
  const actual = `第一级${merchantManageSelected ? '已选中' : '未选中'}，商家列表${merchantListSelected ? '已选中' : '未选中'}，商家分组${merchantGroupSelected ? '已选中' : '未选中'}`
  const passed = merchantManageSelected && merchantListSelected && merchantGroupSelected
  
  testResults.value.push({
    test: "测试1: 初始状态验证",
    description: "所有第三级权限选中时，第一级和第二级应该自动选中",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 测试2: 取消第三级权限，验证向上传播
const testUncheckThirdLevel = async () => {
  // 取消商家列表查询（第三级）
  selectedKeys.value = selectedKeys.value.filter(key => key !== 'merchant-list-query')
  
  await nextTick()
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  
  // 期望：第二级(商家列表)应该取消选中（显示半选），第一级(商家管理)也应该取消选中（显示半选）
  const expected = "第二级(商家列表)和第一级(商家管理)都应该取消选中（显示半选状态）"
  const actual = `第一级${merchantManageSelected ? '仍然选中' : '已取消选中'}，第二级${merchantListSelected ? '仍然选中' : '已取消选中'}`
  const passed = !merchantManageSelected && !merchantListSelected
  
  testResults.value.push({
    test: "测试2: 取消第三级权限向上传播",
    description: "取消商家列表查询后，商家列表和商家管理都应该显示半选状态",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 测试3: 恢复第三级权限，验证向上传播
const testRestoreThirdLevel = async () => {
  // 恢复商家列表查询（第三级）
  selectedKeys.value = [...selectedKeys.value, 'merchant-list-query']
  
  await nextTick()
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  
  // 期望：第二级(商家列表)应该重新选中，第一级(商家管理)也应该重新选中
  const expected = "第二级(商家列表)和第一级(商家管理)都应该重新选中"
  const actual = `第一级${merchantManageSelected ? '已选中' : '未选中'}，第二级${merchantListSelected ? '已选中' : '未选中'}`
  const passed = merchantManageSelected && merchantListSelected
  
  testResults.value.push({
    test: "测试3: 恢复第三级权限向上传播",
    description: "恢复商家列表查询后，商家列表和商家管理都应该重新选中",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 测试4: 取消所有第三级权限，验证完全取消
const testUncheckAllThirdLevel = async () => {
  // 取消商家列表下的所有第三级权限
  selectedKeys.value = selectedKeys.value.filter(key => 
    !['merchant-list-query', 'merchant-list-add', 'merchant-list-edit'].includes(key)
  )
  
  await nextTick()
  
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantGroupSelected = selectedKeys.value.includes('merchant-group')
  
  // 期望：商家列表应该完全取消，但商家管理应该仍然显示半选（因为商家分组还有选中的）
  const expected = "商家列表应该完全取消，商家管理应该显示半选状态（因为商家分组还有权限选中）"
  const actual = `商家列表${merchantListSelected ? '仍然选中' : '已取消'}，商家管理${merchantManageSelected ? '仍然选中' : '已取消'}，商家分组${merchantGroupSelected ? '仍然选中' : '已取消'}`
  const passed = !merchantListSelected && !merchantManageSelected && merchantGroupSelected
  
  testResults.value.push({
    test: "测试4: 取消所有第三级权限验证",
    description: "取消商家列表下所有权限后，商家列表完全取消，商家管理显示半选",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 手动测试函数
const manualTest = (testName: string) => {
  switch (testName) {
    case 'reset':
      selectedKeys.value = [...processedData.value.selectedResources]
      break
    case 'uncheckQuery':
      selectedKeys.value = selectedKeys.value.filter(key => key !== 'merchant-list-query')
      break
    case 'uncheckAdd':
      selectedKeys.value = selectedKeys.value.filter(key => key !== 'merchant-list-add')
      break
    case 'uncheckAllMerchantList':
      selectedKeys.value = selectedKeys.value.filter(key => 
        !['merchant-list-query', 'merchant-list-add', 'merchant-list-edit'].includes(key)
      )
      break
    case 'uncheckAllGroup':
      selectedKeys.value = selectedKeys.value.filter(key => 
        !['group-query', 'group-add'].includes(key)
      )
      break
    case 'clear':
      selectedKeys.value = []
      break
  }
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="three-level-linkage-test">
    <div class="test-header">
      <h1>三层级联动状态计算修复验证</h1>
      <p>专门测试商家管理 → 商家列表 → 具体权限操作的三层级联动逻辑</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runThreeLevelLinkageTest">
            🧪 运行三层级联动测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('reset')">
            重置初始状态
          </a-button>
          <a-button @click="manualTest('uncheckQuery')">
            取消商家列表查询
          </a-button>
          <a-button @click="manualTest('uncheckAdd')">
            取消商家列表新增
          </a-button>
          <a-button @click="manualTest('uncheckAllMerchantList')">
            取消所有商家列表权限
          </a-button>
          <a-button @click="manualTest('uncheckAllGroup')">
            取消所有分组权限
          </a-button>
          <a-button @click="manualTest('clear')">
            清空所有
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="三层级联动测试结果" size="small">
        <div class="results-summary">
          <a-statistic 
            title="通过率" 
            :value="((testResults.filter(r => r.passed).length / testResults.length) * 100).toFixed(1)" 
            suffix="%" 
            :value-style="{ color: testResults.every(r => r.passed) ? '#3f8600' : '#cf1322' }"
          />
        </div>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected }}</div>
              <div><strong>实际:</strong> {{ result.actual }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 三层级状态监控 -->
    <div class="level-status">
      <a-card title="三层级状态实时监控" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="level-card level-1">
              <h4>第一级：商家管理</h4>
              <div class="status-info">
                <p><strong>选中状态:</strong> 
                  <a-tag :color="getNodeState('merchant-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-manage').selected ? '选中' : '未选中' }}
                  </a-tag>
                </p>
                <p><strong>Key:</strong> merchant-manage</p>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="level-card level-2">
              <h4>第二级：商家列表</h4>
              <div class="status-info">
                <p><strong>选中状态:</strong> 
                  <a-tag :color="getNodeState('merchant-list').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-list').selected ? '选中' : '未选中' }}
                  </a-tag>
                </p>
                <p><strong>Key:</strong> merchant-list</p>
              </div>
              
              <div class="level-card level-2" style="margin-top: 12px;">
                <h4>第二级：商家分组</h4>
                <div class="status-info">
                  <p><strong>选中状态:</strong> 
                    <a-tag :color="getNodeState('merchant-group').selected ? 'green' : 'red'">
                      {{ getNodeState('merchant-group').selected ? '选中' : '未选中' }}
                    </a-tag>
                  </p>
                  <p><strong>Key:</strong> merchant-group</p>
                </div>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="level-card level-3">
              <h4>第三级：具体权限</h4>
              <div class="third-level-items">
                <div class="third-item">
                  <span>商家列表查询:</span>
                  <a-tag :color="getNodeState('merchant-list-query').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-list-query').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="third-item">
                  <span>商家列表新增:</span>
                  <a-tag :color="getNodeState('merchant-list-add').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-list-add').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="third-item">
                  <span>商家列表编辑:</span>
                  <a-tag :color="getNodeState('merchant-list-edit').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-list-edit').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="third-item">
                  <span>分组查询:</span>
                  <a-tag :color="getNodeState('group-query').selected ? 'green' : 'red'">
                    {{ getNodeState('group-query').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="third-item">
                  <span>分组新增:</span>
                  <a-tag :color="getNodeState('group-add').selected ? 'green' : 'red'">
                    {{ getNodeState('group-add').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 当前选中状态 -->
    <div class="current-state">
      <a-card title="当前选中状态" size="small">
        <div class="state-info">
          <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
          <div class="selected-keys">
            <a-tag 
              v-for="key in selectedKeys" 
              :key="key"
              color="blue"
              style="margin: 2px;"
            >
              {{ key }}
            </a-tag>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="false"
          :show-select-all="true"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>

    <!-- 问题说明 -->
    <div class="problem-description">
      <a-card title="修复的三层级联动问题" size="small">
        <div class="problem-content">
          <h4>🐛 问题描述：</h4>
          <p>在三层级权限结构中（商家管理 → 商家列表 → 商家列表查询），当取消第三级权限时：</p>
          <ul>
            <li>✅ 第二级（商家列表）正确显示半选状态</li>
            <li>❌ 第一级（商家管理）错误显示为未选中状态（应该显示半选）</li>
          </ul>
          
          <h4>🔧 修复方案：</h4>
          <ul>
            <li>重写 <code>getAllChildrenKeys</code> 函数，支持层级控制</li>
            <li>新增 <code>getDirectChildrenKeys</code> 函数，只获取直接子节点</li>
            <li>修复 <code>updateParentNodesOnUncheck</code> 使用直接子节点计算状态</li>
            <li>修复 <code>updateParentNodesOnPartialCheck</code> 正确传播半选状态</li>
          </ul>
          
          <h4>✅ 期望效果：</h4>
          <p>任何层级的子节点状态变化都应正确向上传播到所有父级节点，父级节点根据直接子节点的状态正确显示全选、半选或未选中。</p>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.three-level-linkage-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .level-status,
  .current-state,
  .permission-panel,
  .problem-description {
    margin-bottom: 16px;
  }

  .results-summary {
    margin-bottom: 16px;
    text-align: center;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-description {
        font-size: 13px;
        color: #595959;
        margin-bottom: 8px;
        font-style: italic;
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .level-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    
    &.level-1 {
      border-color: #1890ff;
      background: #e6f7ff;
    }
    
    &.level-2 {
      border-color: #52c41a;
      background: #f6ffed;
    }
    
    &.level-3 {
      border-color: #faad14;
      background: #fffbe6;
    }
    
    h4 {
      margin: 0 0 8px 0;
      color: #262626;
    }
    
    .status-info {
      p {
        margin: 4px 0;
        font-size: 12px;
        color: #595959;
      }
    }
    
    .third-level-items {
      .third-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        
        span {
          color: #595959;
        }
      }
    }
  }

  .state-info {
    .selected-keys {
      margin-top: 8px;
      max-height: 100px;
      overflow-y: auto;
    }
  }

  .problem-content {
    h4 {
      margin: 16px 0 8px 0;
      color: #262626;
      
      &:first-child {
        margin-top: 0;
      }
    }

    p, ul {
      margin: 8px 0;
      color: #595959;
      line-height: 1.6;
    }

    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
      }
    }

    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      color: #d73a49;
    }
  }
}
</style>
