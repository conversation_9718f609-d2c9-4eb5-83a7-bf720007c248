<script setup lang="ts">
import { ref, computed, toRefs, inject } from 'vue'
import { CaretDownOutlined } from '@ant-design/icons-vue'
import isNil from 'lodash/isNil'

import { usePermissionAction } from '../hooks/use-permission-action'
import { PermissionDataTreeNode, Key, PermissionPanelContext } from '../interface'
import { CheckboxChangeEvent } from 'ant-design-vue/es/_util/EventInterface'
import { COMPONENT_KEY } from '../config'

const props = defineProps<{
  node: PermissionDataTreeNode
  checkedKeys: Key[]
  value?: Key[]
  onChange?: (keys: Key[]) => void
}>()

const { node, checkedKeys } = toRefs(props)
const { onCheckedChange } = inject<PermissionPanelContext>(COMPONENT_KEY)!

const activeKey = ref<Key[]>(props.checkedKeys)
const isExpanded = ref(true)

const isLeaf = computed(() => {
  return !Boolean(node.value.children?.length)
})

const toggleExpand = () => {
  if (!isLeaf.value) {
    isExpanded.value = !isExpanded.value
  }
}

const checkboxOptions = computed(() => {
  return [
    ...(node.value.actions || []),
  ].map((item) => ({
    value: item.key,
    label: item.label,
  }))
})

const { checkState, onCheckAllStateChange, onCheckStateChange } = usePermissionAction({
  node: node.value,
  defaultCheckedKeys: activeKey.value,
  options: checkboxOptions.value,
  checkedKeys,
  onChange: handleChange
})

const isItemChecked = (key: Key) => {
  return checkState.checkedKeys.includes(key)
}

defineExpose({
  isItemChecked
})

function handleChange (key: Key, checked: boolean) {
  // 触发上级节点状态更新
  onCheckedChange?.(key, checked, node.value)
}
</script>

<template>
  <section
    class="lyy-permission-panel__node"
    :class="[`${isLeaf ? 'lyy-permission-panel__node--leaf' : ''}`]"
  >
    <section
      class="lyy-permission-panel__node-label"
      :class="[`lyy-permission-panel__node--level-${node.level}`]"
      @click="toggleExpand"
    >
      <span
        class="lyy-permission-panel__node-icon--prefix"
        :class="[
          isLeaf ? 'lyy-permission-panel__node-icon--leaf' : 'lyy-permission-panel__node-icon--folder',
          { 'lyy-permission-panel__node-icon--expanded': isExpanded }
        ]"
      >
        <template v-if="!isLeaf">
          <CaretDownOutlined />
        </template>
        <template v-else>
          <span class="lyy-permission-panel__node-icon--leaf-inner" />
        </template>
      </span>

      <span class="lyy-permission-panel__node-title">
        {{ node.title }}
      </span>
    </section>

    <section 
      class="lyy-permission-panel__node-content"
    >
      <a-checkbox
        :checked="checkState.checkedAll"
        :indeterminate="checkState.indeterminate"
        @change="onCheckAllStateChange"
      >
        全选
      </a-checkbox>

      <template
        v-for="option in checkboxOptions"
        :key="option.value"
      >
        <a-checkbox
          :checked="isItemChecked(option.value)"
          :value="option.value"
          @change="onCheckStateChange"
        >
          {{ option.label }}
        </a-checkbox>
      </template>
    </section>
  </section>
</template>

<!-- @click.stop="() => onCheckStateChange(option.value, isItemChecked(option.value))" -->

<style lang="scss">
.lyy-permission-panel__node {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 8px 12px;
  border-block: 1px solid #f0f0f0;
  cursor: pointer;

  &.lyy-permission-panel__node--leaf {
    border-block: none;
  }

  &:hover {
    background-color: #fafafa;
  }

  &-label {
    display: flex;
    align-items: center;
    min-width: 208px;
    padding-block: 8px;
    line-height: 20px;
    box-sizing: border-box;
    user-select: none;
  }

  &-content {
    display: inline-flex;
    align-self: start;
    flex: 1;
    flex-wrap: wrap;
    gap: 8px 32px;
    transition: all 0.3s ease;
  }

  &-icon {
    &--prefix {
      margin-right: 14px;

      svg {
        transform: rotate(-90deg);
        transition: transform 0.3s ease;
      }
    }

    &--expanded {
      svg {
        transform: rotate(0);
      }
    }
  }

  &-icon--leaf {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;

    &-inner {
      display: inline-block;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background-color: #000;
    }
  }

  &-content {
    .ant-checkbox-wrapper {
      padding-block: 5px;
    }
  }

  $levels: 0, 1, 2, 3, 4, 5;

  @each $level in $levels {
    &--level-#{$level} {
      padding-left: 20px * $level;
    }
  }
}
</style>

<script lang="ts">
export default {
  name: 'PermissionNode'
}
</script>
