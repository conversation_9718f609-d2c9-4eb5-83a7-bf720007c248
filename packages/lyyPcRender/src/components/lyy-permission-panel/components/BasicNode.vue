<script setup lang="ts">
import { ref, computed, toRefs } from 'vue'

import PermissionNode from './PermissionNode.vue'
import type { PermissionDataTreeNode, Key } from '../interface'

interface IBasicNode {
  node: PermissionDataTreeNode
  checkedKeys: Key[]
  value: Key[]
  readonly?: boolean

  onChange?: (keys: Key[]) => void
}

const props = withDefaults(defineProps<IBasicNode>(), {
  readonly: false
})

const { node, checkedKeys, value, readonly } = toRefs(props)
</script>

<template>
  <PermissionNode
    v-memo="[node.key, checkedKeys, readonly]"
    :node="node"
    :checked-keys="checkedKeys"
    :readonly="readonly"
    @change="props.onChange"
  />

  <template v-if="node.children && node.children.length">
    <template v-for="child in node.children" :key="child.key">
      <PermissionNode
        :node="child"
        :checked-keys="checkedKeys"
        :readonly="readonly"
        @change="props.onChange"
      />
    </template>
  </template>
</template>
