<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'indeterminate-logic-fix-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData } from './utils'
import type { Key } from './interface'

// 测试数据 - 专门设计来验证半选状态判断逻辑修复
const testData = ref({
  code: "200",
  message: "success",
  body: {
    roleId: "indeterminate-logic-test",
    roleName: "半选状态判断逻辑测试",
    roleRemark: "验证有子孙节点的节点使用 isNodeIndeterminate 判断半选状态",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "系统管理", // 第一级 - 有子孙节点，无actions
        value: "system-manage",
        selected: false,
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2",
            parentId: "1",
            name: "用户管理", // 第二级 - 有子孙节点，无actions
            value: "user-manage",
            selected: false,
            htmlTemplet: "/user",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2",
                name: "用户查询", // 第三级 - 叶子节点，无actions
                value: "user-query",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "用户新增",
                value: "user-add",
                selected: false, // 未选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          },
          {
            adResourcesId: "5",
            parentId: "1",
            name: "角色管理", // 第二级 - 有actions
            value: "role-manage",
            selected: false,
            htmlTemplet: "/role",
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "6",
                parentId: "5",
                name: "角色查询",
                value: "role-query",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "7",
                parentId: "5",
                name: "角色新增",
                value: "role-add",
                selected: false, // 未选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          }
        ]
      },
      {
        adResourcesId: "8",
        parentId: null,
        name: "业务管理", // 第一级 - 叶子节点，无子孙节点
        value: "business-manage",
        selected: true, // 选中
        htmlTemplet: null,
        icon: null,
        seq: 2,
        children: []
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试结果
const testResults = ref<Array<{
  test: string
  description: string
  expected: string
  actual: string
  passed: boolean
  timestamp: string
}>>([])

// 调试日志
const debugLogs = ref<string[]>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
  
  // 记录调试日志
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 权限变更: ${keys.join(', ')}`)
  
  // 保持最新的50条日志
  if (debugLogs.value.length > 50) {
    debugLogs.value = debugLogs.value.slice(-50)
  }
}

// 获取节点状态
const getNodeState = (nodeKey: string) => {
  const isSelected = selectedKeys.value.includes(nodeKey)
  return {
    key: nodeKey,
    selected: isSelected
  }
}

// 运行半选状态判断逻辑测试
const runIndeterminateLogicTest = async () => {
  testResults.value = []
  debugLogs.value = []
  
  console.log('开始半选状态判断逻辑测试...')
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 开始半选状态判断逻辑测试`)
  
  // 测试1: 有子孙节点的节点半选状态判断
  await testNodeWithChildrenIndeterminate()
  
  setTimeout(async () => {
    // 测试2: 无子孙节点的节点状态判断
    await testLeafNodeState()
  }, 1000)
  
  setTimeout(async () => {
    // 测试3: 修改子节点后父节点半选状态变化
    await testParentIndeterminateChange()
  }, 2000)
}

// 测试1: 有子孙节点的节点半选状态判断
const testNodeWithChildrenIndeterminate = async () => {
  // 重置为初始状态
  selectedKeys.value = [...processedData.value.selectedResources]
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 初始选中状态: ${selectedKeys.value.join(', ')}`)
  
  // 验证有子孙节点的节点状态
  const systemManageSelected = selectedKeys.value.includes('system-manage')
  const userManageSelected = selectedKeys.value.includes('user-manage')
  
  // 期望：系统管理和用户管理都不应该在selectedKeys中（因为只有部分子节点选中）
  // 但它们应该通过 isNodeIndeterminate 函数判断为半选状态
  const expected = "有子孙节点的节点应该不在selectedKeys中，但通过isNodeIndeterminate判断为半选"
  const actual = `系统管理${systemManageSelected ? '在selectedKeys中' : '不在selectedKeys中'}，用户管理${userManageSelected ? '在selectedKeys中' : '不在selectedKeys中'}`
  const passed = !systemManageSelected && !userManageSelected
  
  testResults.value.push({
    test: "测试1: 有子孙节点的节点半选状态判断",
    description: "验证有子孙节点且部分子节点选中时，父节点不在selectedKeys中但应显示半选",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试1结果: ${passed ? '通过' : '失败'}`)
}

// 测试2: 无子孙节点的节点状态判断
const testLeafNodeState = async () => {
  // 检查叶子节点状态
  const businessManageSelected = selectedKeys.value.includes('business-manage')
  const userQuerySelected = selectedKeys.value.includes('user-query')
  
  // 期望：叶子节点如果被选中应该在selectedKeys中，不应该有半选状态
  const expected = "叶子节点如果被选中应该在selectedKeys中，不应该有半选状态"
  const actual = `业务管理${businessManageSelected ? '在selectedKeys中' : '不在selectedKeys中'}，用户查询${userQuerySelected ? '在selectedKeys中' : '不在selectedKeys中'}`
  const passed = businessManageSelected && userQuerySelected
  
  testResults.value.push({
    test: "测试2: 无子孙节点的节点状态判断",
    description: "验证叶子节点的选中状态应该直接反映在selectedKeys中",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试2结果: ${passed ? '通过' : '失败'}`)
}

// 测试3: 修改子节点后父节点半选状态变化
const testParentIndeterminateChange = async () => {
  // 选中用户新增，使用户管理变为全选
  selectedKeys.value = [...selectedKeys.value, 'user-add']
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 选中user-add后`)
  
  const userManageSelected = selectedKeys.value.includes('user-manage')
  const systemManageSelected = selectedKeys.value.includes('system-manage')
  
  // 期望：用户管理应该自动选中（因为所有子节点都选中了），系统管理仍然半选
  const expected = "用户管理应该自动选中，系统管理仍保持半选状态"
  const actual = `用户管理${userManageSelected ? '已选中' : '未选中'}，系统管理${systemManageSelected ? '已选中' : '未选中'}`
  const passed = userManageSelected && !systemManageSelected
  
  testResults.value.push({
    test: "测试3: 修改子节点后父节点半选状态变化",
    description: "选中所有子节点后，直接父节点应该自动选中，上级父节点仍保持半选",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试3结果: ${passed ? '通过' : '失败'}`)
}

// 手动测试函数
const manualTest = (testName: string) => {
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 手动测试: ${testName}`)
  
  switch (testName) {
    case 'reset':
      selectedKeys.value = [...processedData.value.selectedResources]
      break
    case 'selectUserAdd':
      if (!selectedKeys.value.includes('user-add')) {
        selectedKeys.value = [...selectedKeys.value, 'user-add']
      }
      break
    case 'unselectUserQuery':
      selectedKeys.value = selectedKeys.value.filter(key => key !== 'user-query')
      break
    case 'selectAllUser':
      selectedKeys.value = selectedKeys.value.filter(key => !['user-query', 'user-add'].includes(key))
      selectedKeys.value.push('user-query', 'user-add')
      break
    case 'selectRoleAdd':
      if (!selectedKeys.value.includes('role-add')) {
        selectedKeys.value = [...selectedKeys.value, 'role-add']
      }
      break
    case 'clear':
      selectedKeys.value = []
      break
  }
}

// 清空调试日志
const clearDebugLogs = () => {
  debugLogs.value = []
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="indeterminate-logic-fix-test">
    <div class="test-header">
      <h1>半选状态判断逻辑修复验证</h1>
      <p>验证有子孙节点的节点使用 isNodeIndeterminate 判断半选状态，无子孙节点的节点设置为 false</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runIndeterminateLogicTest">
            🧪 运行半选状态判断逻辑测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('reset')">
            重置初始状态
          </a-button>
          <a-button @click="manualTest('selectUserAdd')">
            选中用户新增
          </a-button>
          <a-button @click="manualTest('unselectUserQuery')">
            取消用户查询
          </a-button>
          <a-button @click="manualTest('selectAllUser')">
            全选用户管理
          </a-button>
          <a-button @click="manualTest('selectRoleAdd')">
            选中角色新增
          </a-button>
          <a-button @click="manualTest('clear')">
            清空所有
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="半选状态判断逻辑测试结果" size="small">
        <div class="results-summary">
          <a-statistic 
            title="通过率" 
            :value="((testResults.filter(r => r.passed).length / testResults.length) * 100).toFixed(1)" 
            suffix="%" 
            :value-style="{ color: testResults.every(r => r.passed) ? '#3f8600' : '#cf1322' }"
          />
        </div>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected }}</div>
              <div><strong>实际:</strong> {{ result.actual }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 调试日志 -->
    <div class="debug-logs">
      <a-card title="调试日志" size="small">
        <template #extra>
          <a-button size="small" @click="clearDebugLogs">清空日志</a-button>
        </template>
        
        <div class="logs-container">
          <div 
            v-for="(log, index) in debugLogs" 
            :key="index"
            class="log-item"
          >
            {{ log }}
          </div>
          <div v-if="debugLogs.length === 0" class="no-logs">
            暂无调试日志
          </div>
        </div>
      </a-card>
    </div>

    <!-- 修复说明 -->
    <div class="fix-explanation">
      <a-card title="修复说明" size="small">
        <div class="explanation-content">
          <h4>🐛 修复的问题：</h4>
          <p>在 <code>use-permission-action.ts</code> 中，对于没有 actions 的节点，原来的逻辑直接设置 <code>indeterminate = false</code>，没有考虑该节点是否有子孙节点。</p>
          
          <h4>🔧 修复方案：</h4>
          <div class="code-comparison">
            <div class="code-block">
              <h5>修复前（错误逻辑）：</h5>
              <pre><code>} else {
  // 没有actions的情况：检查节点本身是否被选中
  checkedAll = selectedKeys.value.includes(nodeKey)
  indeterminate = false // ❌ 直接设置为false
}</code></pre>
            </div>
            
            <div class="code-block">
              <h5>修复后（正确逻辑）：</h5>
              <pre><code>} else {
  // 修复：检查是否有子孙节点
  const hasChildren = node.children && node.children.length > 0
  
  if (hasChildren && isNodeIndeterminate) {
    // 有子孙节点：使用 isNodeIndeterminate 判断半选状态
    checkedAll = selectedKeys.value.includes(nodeKey)
    indeterminate = isNodeIndeterminate(nodeKey) // ✅ 使用函数判断
  } else {
    // 没有子孙节点：检查节点本身
    checkedAll = selectedKeys.value.includes(nodeKey)
    indeterminate = false // ✅ 叶子节点不会有半选状态
  }
}</code></pre>
            </div>
          </div>
          
          <h4>✅ 期望效果：</h4>
          <ul>
            <li><strong>有子孙节点的节点：</strong>使用 <code>isNodeIndeterminate</code> 函数正确判断半选状态</li>
            <li><strong>叶子节点：</strong>不会有半选状态，<code>indeterminate</code> 设置为 <code>false</code></li>
            <li><strong>状态一致性：</strong>确保组件显示状态与实际数据状态完全一致</li>
          </ul>
        </div>
      </a-card>
    </div>

    <!-- 节点类型说明 -->
    <div class="node-types">
      <a-card title="测试数据中的节点类型" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="node-type-card has-children">
              <h4>有子孙节点的节点</h4>
              <div class="node-list">
                <div class="node-item">
                  <span class="node-name">系统管理</span>
                  <a-tag :color="getNodeState('system-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('system-manage').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="node-item">
                  <span class="node-name">用户管理</span>
                  <a-tag :color="getNodeState('user-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('user-manage').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="node-item">
                  <span class="node-name">角色管理</span>
                  <a-tag :color="getNodeState('role-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('role-manage').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
              </div>
              <p class="note">这些节点应该使用 isNodeIndeterminate 判断半选状态</p>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="node-type-card leaf-nodes">
              <h4>叶子节点（无子孙节点）</h4>
              <div class="node-list">
                <div class="node-item">
                  <span class="node-name">业务管理</span>
                  <a-tag :color="getNodeState('business-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('business-manage').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="node-item">
                  <span class="node-name">用户查询</span>
                  <a-tag :color="getNodeState('user-query').selected ? 'green' : 'red'">
                    {{ getNodeState('user-query').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
                <div class="node-item">
                  <span class="node-name">用户新增</span>
                  <a-tag :color="getNodeState('user-add').selected ? 'green' : 'red'">
                    {{ getNodeState('user-add').selected ? '选中' : '未选中' }}
                  </a-tag>
                </div>
              </div>
              <p class="note">这些节点的 indeterminate 应该设置为 false</p>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="node-type-card current-state">
              <h4>当前选中状态</h4>
              <div class="state-info">
                <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
                <div class="selected-keys">
                  <a-tag 
                    v-for="key in selectedKeys" 
                    :key="key"
                    color="blue"
                    style="margin: 2px;"
                  >
                    {{ key }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="false"
          :show-select-all="true"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.indeterminate-logic-fix-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .debug-logs,
  .fix-explanation,
  .node-types,
  .permission-panel {
    margin-bottom: 16px;
  }

  .results-summary {
    margin-bottom: 16px;
    text-align: center;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-description {
        font-size: 13px;
        color: #595959;
        margin-bottom: 8px;
        font-style: italic;
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 8px;
    
    .log-item {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #586069;
      margin-bottom: 2px;
      line-height: 1.4;
    }
    
    .no-logs {
      text-align: center;
      color: #8c8c8c;
      font-style: italic;
    }
  }

  .explanation-content {
    h4 {
      margin: 16px 0 8px 0;
      color: #262626;
      
      &:first-child {
        margin-top: 0;
      }
    }

    p, ul {
      margin: 8px 0;
      color: #595959;
      line-height: 1.6;
    }

    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
      }
    }

    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      color: #d73a49;
    }

    .code-comparison {
      display: flex;
      gap: 16px;
      margin: 16px 0;
      
      .code-block {
        flex: 1;
        
        h5 {
          margin: 0 0 8px 0;
          color: #262626;
        }
        
        pre {
          background: #f6f8fa;
          border: 1px solid #e1e4e8;
          border-radius: 4px;
          padding: 12px;
          margin: 0;
          font-size: 12px;
          overflow-x: auto;
          
          code {
            background: none;
            padding: 0;
            color: #24292e;
          }
        }
      }
    }
  }

  .node-type-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    height: 100%;
    
    &.has-children {
      border-color: #1890ff;
      background: #e6f7ff;
    }
    
    &.leaf-nodes {
      border-color: #52c41a;
      background: #f6ffed;
    }
    
    &.current-state {
      border-color: #faad14;
      background: #fffbe6;
    }
    
    h4 {
      margin: 0 0 12px 0;
      color: #262626;
    }
    
    .node-list {
      .node-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .node-name {
          font-size: 12px;
          color: #595959;
        }
      }
    }
    
    .note {
      margin: 12px 0 0 0;
      font-size: 11px;
      color: #8c8c8c;
      font-style: italic;
    }
    
    .state-info {
      p {
        margin: 4px 0;
        font-size: 12px;
        color: #595959;
      }
      
      .selected-keys {
        margin-top: 8px;
        max-height: 100px;
        overflow-y: auto;
      }
    }
  }
}
</style>
