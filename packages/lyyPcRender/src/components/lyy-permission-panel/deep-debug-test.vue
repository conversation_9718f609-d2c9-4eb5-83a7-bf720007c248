<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'deep-debug-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData } from './utils'
import type { Key } from './interface'

// 测试数据 - 精确模拟问题场景
const testData = ref({
  code: "200",
  message: "success",
  body: {
    roleId: "deep-debug-test",
    roleName: "深度调试测试",
    roleRemark: "专门用于调试初始化状态和跨层级联动问题",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "商家管理", // 第一级
        value: "merchant-manage",
        selected: false, // 初始未选中
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2",
            parentId: "1",
            name: "商家列表", // 第二级
            value: "merchant-list",
            selected: false, // 初始未选中
            htmlTemplet: "/merchant/list",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2",
                name: "商家列表查询", // 第三级
                value: "merchant-list-query",
                selected: true, // 只有这个选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "商家列表新增",
                value: "merchant-list-add",
                selected: false, // 未选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              },
              {
                adResourcesId: "5",
                parentId: "2",
                name: "商家列表编辑",
                value: "merchant-list-edit",
                selected: false, // 未选中
                htmlTemplet: null,
                icon: null,
                seq: 3,
                children: []
              }
            ]
          },
          {
            adResourcesId: "6",
            parentId: "1",
            name: "商家分组", // 第二级
            value: "merchant-group",
            selected: false, // 初始未选中
            htmlTemplet: "/merchant/group",
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "7",
                parentId: "6",
                name: "分组查询", // 第三级
                value: "group-query",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "8",
                parentId: "6",
                name: "分组新增",
                value: "group-add",
                selected: true, // 选中
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          }
        ]
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试结果
const testResults = ref<Array<{
  test: string
  description: string
  expected: string
  actual: string
  passed: boolean
  timestamp: string
}>>([])

// 调试日志
const debugLogs = ref<string[]>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
  
  // 记录调试日志
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 权限变更: ${keys.join(', ')}`)
  
  // 保持最新的50条日志
  if (debugLogs.value.length > 50) {
    debugLogs.value = debugLogs.value.slice(-50)
  }
}

// 获取节点状态
const getNodeState = (nodeKey: string) => {
  const isSelected = selectedKeys.value.includes(nodeKey)
  return {
    key: nodeKey,
    selected: isSelected
  }
}

// 深度调试测试
const runDeepDebugTest = async () => {
  testResults.value = []
  debugLogs.value = []
  
  console.log('开始深度调试测试...')
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 开始深度调试测试`)
  
  // 测试1: 初始化状态验证
  await testInitialStateConsistency()
  
  setTimeout(async () => {
    // 测试2: 单个第三级权限变更
    await testSingleThirdLevelChange()
  }, 1000)
  
  setTimeout(async () => {
    // 测试3: 跨层级联动验证
    await testCrossLevelLinkage()
  }, 2000)
}

// 测试1: 初始化状态一致性
const testInitialStateConsistency = async () => {
  // 重置为初始状态
  selectedKeys.value = [...processedData.value.selectedResources]
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 初始选中状态: ${selectedKeys.value.join(', ')}`)
  
  // 验证商家列表节点的状态
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantListActions = ['merchant-list-query', 'merchant-list-add', 'merchant-list-edit']
  const selectedMerchantListActions = merchantListActions.filter(action => selectedKeys.value.includes(action))
  
  // 期望：商家列表不应该显示为全选（因为只有部分actions被选中）
  const expected = "商家列表节点不应该显示为全选状态（只有1/3的actions被选中）"
  const actual = `商家列表节点${merchantListSelected ? '显示为选中' : '显示为未选中'}，已选actions: ${selectedMerchantListActions.length}/${merchantListActions.length}`
  const passed = !merchantListSelected && selectedMerchantListActions.length === 1
  
  testResults.value.push({
    test: "测试1: 初始化状态一致性",
    description: "验证节点的全选状态与其actions的实际选中状态是否一致",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试1结果: ${passed ? '通过' : '失败'}`)
}

// 测试2: 单个第三级权限变更
const testSingleThirdLevelChange = async () => {
  // 新增选中商家列表新增
  selectedKeys.value = [...selectedKeys.value, 'merchant-list-add']
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 新增选中merchant-list-add`)
  
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  
  // 期望：商家列表仍然不应该全选，商家管理也不应该全选
  const expected = "商家列表和商家管理都不应该显示为全选（因为还有未选中的actions）"
  const actual = `商家列表${merchantListSelected ? '已选中' : '未选中'}，商家管理${merchantManageSelected ? '已选中' : '未选中'}`
  const passed = !merchantListSelected && !merchantManageSelected
  
  testResults.value.push({
    test: "测试2: 单个第三级权限变更",
    description: "新增选中一个第三级权限后，验证父级节点状态",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试2结果: ${passed ? '通过' : '失败'}`)
}

// 测试3: 跨层级联动验证
const testCrossLevelLinkage = async () => {
  // 选中商家列表的所有actions
  selectedKeys.value = selectedKeys.value.filter(key => !['merchant-list-query', 'merchant-list-add', 'merchant-list-edit'].includes(key))
  selectedKeys.value.push('merchant-list-query', 'merchant-list-add', 'merchant-list-edit')
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 选中商家列表所有actions`)
  
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  
  // 期望：商家列表应该自动选中，但商家管理不应该全选（因为商家分组还有未选中的）
  const expected = "商家列表应该自动选中，商家管理应该显示半选状态"
  const actual = `商家列表${merchantListSelected ? '已选中' : '未选中'}，商家管理${merchantManageSelected ? '已选中' : '未选中'}`
  const passed = merchantListSelected && !merchantManageSelected
  
  testResults.value.push({
    test: "测试3: 跨层级联动验证",
    description: "选中所有第三级权限后，验证第二级和第一级的联动效果",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试3结果: ${passed ? '通过' : '失败'}`)
}

// 手动测试函数
const manualTest = (testName: string) => {
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 手动测试: ${testName}`)
  
  switch (testName) {
    case 'reset':
      selectedKeys.value = [...processedData.value.selectedResources]
      break
    case 'addMerchantListAdd':
      if (!selectedKeys.value.includes('merchant-list-add')) {
        selectedKeys.value = [...selectedKeys.value, 'merchant-list-add']
      }
      break
    case 'addMerchantListEdit':
      if (!selectedKeys.value.includes('merchant-list-edit')) {
        selectedKeys.value = [...selectedKeys.value, 'merchant-list-edit']
      }
      break
    case 'selectAllMerchantList':
      selectedKeys.value = selectedKeys.value.filter(key => !['merchant-list-query', 'merchant-list-add', 'merchant-list-edit'].includes(key))
      selectedKeys.value.push('merchant-list-query', 'merchant-list-add', 'merchant-list-edit')
      break
    case 'unselectGroupQuery':
      selectedKeys.value = selectedKeys.value.filter(key => key !== 'group-query')
      break
    case 'clear':
      selectedKeys.value = []
      break
  }
}

// 清空调试日志
const clearDebugLogs = () => {
  debugLogs.value = []
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="deep-debug-test">
    <div class="test-header">
      <h1>深度调试测试 - 初始化状态与跨层级联动</h1>
      <p>专门调试初始化状态显示不一致和跨层级选中状态向上传播失效问题</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runDeepDebugTest">
            🔍 运行深度调试测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('reset')">
            重置初始状态
          </a-button>
          <a-button @click="manualTest('addMerchantListAdd')">
            +商家列表新增
          </a-button>
          <a-button @click="manualTest('addMerchantListEdit')">
            +商家列表编辑
          </a-button>
          <a-button @click="manualTest('selectAllMerchantList')">
            全选商家列表
          </a-button>
          <a-button @click="manualTest('unselectGroupQuery')">
            -分组查询
          </a-button>
          <a-button @click="manualTest('clear')">
            清空所有
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="深度调试测试结果" size="small">
        <div class="results-summary">
          <a-statistic 
            title="通过率" 
            :value="((testResults.filter(r => r.passed).length / testResults.length) * 100).toFixed(1)" 
            suffix="%" 
            :value-style="{ color: testResults.every(r => r.passed) ? '#3f8600' : '#cf1322' }"
          />
        </div>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected }}</div>
              <div><strong>实际:</strong> {{ result.actual }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 调试日志 -->
    <div class="debug-logs">
      <a-card title="调试日志" size="small">
        <template #extra>
          <a-button size="small" @click="clearDebugLogs">清空日志</a-button>
        </template>
        
        <div class="logs-container">
          <div 
            v-for="(log, index) in debugLogs" 
            :key="index"
            class="log-item"
          >
            {{ log }}
          </div>
          <div v-if="debugLogs.length === 0" class="no-logs">
            暂无调试日志
          </div>
        </div>
      </a-card>
    </div>

    <!-- 状态监控 -->
    <div class="state-monitor">
      <a-card title="实时状态监控" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="monitor-card">
              <h4>第一级：商家管理</h4>
              <div class="state-info">
                <p><strong>选中状态:</strong> 
                  <a-tag :color="getNodeState('merchant-manage').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-manage').selected ? '选中' : '未选中' }}
                  </a-tag>
                </p>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="monitor-card">
              <h4>第二级：商家列表</h4>
              <div class="state-info">
                <p><strong>选中状态:</strong> 
                  <a-tag :color="getNodeState('merchant-list').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-list').selected ? '选中' : '未选中' }}
                  </a-tag>
                </p>
                <p><strong>Actions状态:</strong></p>
                <div class="actions-status">
                  <a-tag 
                    :color="getNodeState('merchant-list-query').selected ? 'green' : 'red'"
                    size="small"
                  >
                    查询
                  </a-tag>
                  <a-tag 
                    :color="getNodeState('merchant-list-add').selected ? 'green' : 'red'"
                    size="small"
                  >
                    新增
                  </a-tag>
                  <a-tag 
                    :color="getNodeState('merchant-list-edit').selected ? 'green' : 'red'"
                    size="small"
                  >
                    编辑
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
          
          <a-col :span="8">
            <div class="monitor-card">
              <h4>第二级：商家分组</h4>
              <div class="state-info">
                <p><strong>选中状态:</strong> 
                  <a-tag :color="getNodeState('merchant-group').selected ? 'green' : 'red'">
                    {{ getNodeState('merchant-group').selected ? '选中' : '未选中' }}
                  </a-tag>
                </p>
                <p><strong>Actions状态:</strong></p>
                <div class="actions-status">
                  <a-tag 
                    :color="getNodeState('group-query').selected ? 'green' : 'red'"
                    size="small"
                  >
                    查询
                  </a-tag>
                  <a-tag 
                    :color="getNodeState('group-add').selected ? 'green' : 'red'"
                    size="small"
                  >
                    新增
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 当前选中状态 -->
    <div class="current-state">
      <a-card title="当前选中状态" size="small">
        <div class="state-info">
          <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
          <div class="selected-keys">
            <a-tag 
              v-for="key in selectedKeys" 
              :key="key"
              color="blue"
              style="margin: 2px;"
            >
              {{ key }}
            </a-tag>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="false"
          :show-select-all="true"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.deep-debug-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .debug-logs,
  .state-monitor,
  .current-state,
  .permission-panel {
    margin-bottom: 16px;
  }

  .results-summary {
    margin-bottom: 16px;
    text-align: center;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-description {
        font-size: 13px;
        color: #595959;
        margin-bottom: 8px;
        font-style: italic;
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 8px;
    
    .log-item {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #586069;
      margin-bottom: 2px;
      line-height: 1.4;
    }
    
    .no-logs {
      text-align: center;
      color: #8c8c8c;
      font-style: italic;
    }
  }

  .monitor-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    
    h4 {
      margin: 0 0 8px 0;
      color: #262626;
    }
    
    .state-info {
      p {
        margin: 4px 0;
        font-size: 12px;
        color: #595959;
      }
      
      .actions-status {
        margin-top: 4px;
        
        .ant-tag {
          margin: 2px;
        }
      }
    }
  }

  .state-info {
    .selected-keys {
      margin-top: 8px;
      max-height: 100px;
      overflow-y: auto;
    }
  }
}
</style>
