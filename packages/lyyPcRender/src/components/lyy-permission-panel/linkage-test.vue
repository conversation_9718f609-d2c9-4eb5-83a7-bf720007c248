<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'permission-linkage-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData } from './utils'
import mockData from './mock-data'
import type { Key } from './interface'

// 测试数据 - 创建多层级结构
const testData = ref({
  code: "200",
  message: "success",
  body: {
    roleId: "test-role",
    roleName: "联动测试角色",
    roleRemark: "用于测试权限联动逻辑",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "系统管理",
        value: "system",
        selected: false,
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2",
            parentId: "1",
            name: "用户管理",
            value: "user-manage",
            selected: false,
            htmlTemplet: "/user",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2",
                name: "用户查询",
                value: "user-query",
                selected: true,
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "用户新增",
                value: "user-add",
                selected: false,
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              },
              {
                adResourcesId: "5",
                parentId: "2",
                name: "用户编辑",
                value: "user-edit",
                selected: true,
                htmlTemplet: null,
                icon: null,
                seq: 3,
                children: []
              }
            ]
          },
          {
            adResourcesId: "6",
            parentId: "1",
            name: "角色管理",
            value: "role-manage",
            selected: false,
            htmlTemplet: "/role",
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "7",
                parentId: "6",
                name: "角色查询",
                value: "role-query",
                selected: true,
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "8",
                parentId: "6",
                name: "角色新增",
                value: "role-add",
                selected: true,
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          }
        ]
      },
      {
        adResourcesId: "9",
        parentId: null,
        name: "业务管理",
        value: "business",
        selected: false,
        htmlTemplet: null,
        icon: null,
        seq: 2,
        children: [
          {
            adResourcesId: "10",
            parentId: "9",
            name: "订单管理",
            value: "order-manage",
            selected: false,
            htmlTemplet: "/order",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "11",
                parentId: "10",
                name: "订单查询",
                value: "order-query",
                selected: false,
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              }
            ]
          }
        ]
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试配置
const testConfig = ref({
  readonly: false,
  showSelectAll: true,
  autoTest: false
})

// 测试结果
const testResults = ref<Array<{
  test: string
  expected: string[]
  actual: string[]
  passed: boolean
  timestamp: string
}>>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
}

// 自动化测试函数
const runLinkageTests = () => {
  testResults.value = []
  
  // 测试1: 选中父节点，检查子节点是否自动选中
  test1_ParentToChild()
  
  setTimeout(() => {
    // 测试2: 取消父节点，检查子节点是否自动取消
    test2_ParentUncheckToChild()
  }, 1000)
  
  setTimeout(() => {
    // 测试3: 选中所有子节点，检查父节点是否自动选中
    test3_AllChildrenToParent()
  }, 2000)
  
  setTimeout(() => {
    // 测试4: 部分子节点选中，检查父节点半选状态
    test4_PartialChildrenToParent()
  }, 3000)
}

const test1_ParentToChild = () => {
  // 清空选择
  selectedKeys.value = []
  
  // 选中"系统管理"
  selectedKeys.value = ['system']
  
  setTimeout(() => {
    const expected = ['system', 'user-manage', 'user-query', 'user-add', 'user-edit', 'role-manage', 'role-query', 'role-add']
    const actual = [...selectedKeys.value].sort()
    const passed = expected.every(key => actual.includes(key))
    
    testResults.value.push({
      test: '测试1: 选中父节点 -> 子节点自动选中',
      expected: expected.sort(),
      actual,
      passed,
      timestamp: new Date().toLocaleTimeString()
    })
  }, 100)
}

const test2_ParentUncheckToChild = () => {
  // 取消选中"系统管理"
  selectedKeys.value = selectedKeys.value.filter(key => key !== 'system')
  
  setTimeout(() => {
    const expected: string[] = []
    const actual = [...selectedKeys.value].sort()
    const passed = actual.length === 0
    
    testResults.value.push({
      test: '测试2: 取消父节点 -> 子节点自动取消',
      expected,
      actual,
      passed,
      timestamp: new Date().toLocaleTimeString()
    })
  }, 100)
}

const test3_AllChildrenToParent = () => {
  // 清空选择
  selectedKeys.value = []
  
  // 选中"角色管理"下的所有子节点
  selectedKeys.value = ['role-query', 'role-add']
  
  setTimeout(() => {
    const expected = ['role-manage', 'role-query', 'role-add']
    const actual = [...selectedKeys.value].sort()
    const passed = expected.every(key => actual.includes(key))
    
    testResults.value.push({
      test: '测试3: 所有子节点选中 -> 父节点自动选中',
      expected: expected.sort(),
      actual,
      passed,
      timestamp: new Date().toLocaleTimeString()
    })
  }, 100)
}

const test4_PartialChildrenToParent = () => {
  // 清空选择
  selectedKeys.value = []
  
  // 只选中"用户管理"下的部分子节点
  selectedKeys.value = ['user-query']
  
  setTimeout(() => {
    const expected = ['user-query']
    const actual = [...selectedKeys.value].sort()
    const passed = actual.includes('user-query') && !actual.includes('user-manage')
    
    testResults.value.push({
      test: '测试4: 部分子节点选中 -> 父节点半选状态',
      expected: expected.sort(),
      actual,
      passed,
      timestamp: new Date().toLocaleTimeString()
    })
  }, 100)
}

// 手动测试函数
const manualTest = (testName: string) => {
  switch (testName) {
    case 'selectSystem':
      selectedKeys.value = ['system']
      break
    case 'selectUserManage':
      selectedKeys.value = ['user-manage']
      break
    case 'selectPartialUser':
      selectedKeys.value = ['user-query', 'user-edit']
      break
    case 'selectAllRole':
      selectedKeys.value = ['role-query', 'role-add']
      break
    case 'clear':
      selectedKeys.value = []
      break
  }
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="linkage-test">
    <div class="test-header">
      <h1>权限联动逻辑测试</h1>
      <p>测试父子节点之间的选中状态联动和半选状态显示</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runLinkageTests">
            运行自动化测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('selectSystem')">
            选中系统管理
          </a-button>
          <a-button @click="manualTest('selectUserManage')">
            选中用户管理
          </a-button>
          <a-button @click="manualTest('selectPartialUser')">
            部分选中用户权限
          </a-button>
          <a-button @click="manualTest('selectAllRole')">
            全选角色权限
          </a-button>
          <a-button @click="manualTest('clear')">
            清空选择
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-switch 
            v-model:checked="testConfig.readonly" 
            checked-children="只读" 
            un-checked-children="编辑" 
          />
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="测试结果" size="small">
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected.join(', ') || '无' }}</div>
              <div><strong>实际:</strong> {{ result.actual.join(', ') || '无' }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 当前状态显示 -->
    <div class="current-state">
      <a-card title="当前选中状态" size="small">
        <div class="state-info">
          <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
          <div class="selected-keys">
            <a-tag 
              v-for="key in selectedKeys" 
              :key="key"
              color="blue"
              style="margin: 2px;"
            >
              {{ key }}
            </a-tag>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="testConfig.readonly"
          :show-select-all="testConfig.showSelectAll"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>

    <!-- 数据结构显示 -->
    <div class="data-structure">
      <a-card title="测试数据结构" size="small">
        <a-collapse>
          <a-collapse-panel key="processed" header="处理后的数据">
            <pre class="json-display">{{ JSON.stringify(processedData, null, 2) }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.linkage-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .current-state,
  .permission-panel,
  .data-structure {
    margin-bottom: 16px;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .state-info {
    .selected-keys {
      margin-top: 8px;
      max-height: 100px;
      overflow-y: auto;
    }
  }

  .json-display {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 12px;
    color: #586069;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
