<script setup lang="ts">
import { ref, provide, computed, watch, onMounted } from 'vue'

import type { Key } from './interface'
import { COMPONENT_KEY } from './config'
import { processPermissionData, extractPermissionData } from './utils'
import { usePermissionData } from './hooks/use-permission-data'
import BasicNode from './components/BasicNode.vue'
import MockData from './mock-data'

// 组件属性定义
interface Props {
  /** 权限数据 - 可以是后端原始数据或处理后的数据 */
  permissionData?: any
  /** 初始选中的权限keys */
  value?: Key[]
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示全选按钮 */
  showSelectAll?: boolean
}

// 事件定义
interface Emits {
  (e: 'change', selectedKeys: Key[]): void
  (e: 'update:value', selectedKeys: Key[]): void
  (e: 'save', selectedKeys: Key[]): void
}

const props = withDefaults(defineProps<Props>(), {
  permissionData: () => MockData,
  value: () => [],
  readonly: false,
  showSelectAll: true
})

const emit = defineEmits<Emits>()

// 处理权限数据
const processedPermissionData = computed(() => {
  if (!props.permissionData) {
    return { treeData: [], selectedKeys: [] }
  }

  // 如果是后端原始数据格式
  if (props.permissionData.body && props.permissionData.body.resources) {
    const { processedData } = extractPermissionData(props.permissionData)
    return processedData
  }

  // 如果是已处理的数据格式
  if (Array.isArray(props.permissionData)) {
    return processPermissionData(props.permissionData)
  }

  // 默认使用 mock 数据
  const { processedData } = extractPermissionData(MockData)
  return processedData
})

// 权限管理 hook
const {
  selectedKeys,
  onCheckedChange,
} = usePermissionData({
  defaultSelectedKeys: props.value.length > 0 ? props.value : processedPermissionData.value.selectedKeys,
  data: processedPermissionData.value.treeData,
})

// 处理节点选中状态变化
const handleNodeChange = (keys: Key[]) => {
  if (props.readonly) return

  selectedKeys.value = keys
  emit('change', keys)
  emit('update:value', keys)
}

// 全选功能
const handleSelectAll = () => {
  if (props.readonly) return

  // 获取所有可选择的keys
  const allKeys: Key[] = []
  const collectAllKeys = (nodes: any[]) => {
    nodes.forEach(node => {
      allKeys.push(node.key)
      if (node.actions) {
        node.actions.forEach((action: any) => {
          allKeys.push(action.key)
        })
      }
      if (node.children) {
        collectAllKeys(node.children)
      }
    })
  }

  collectAllKeys(processedPermissionData.value.treeData)
  handleNodeChange(allKeys)
}

// 清空选择
const handleClearAll = () => {
  if (props.readonly) return
  handleNodeChange([])
}

// 保存权限
const handleSave = () => {
  emit('save', selectedKeys.value)
}

// 监听外部 value 变化
watch(() => props.value, (newValue) => {
  if (newValue && newValue.length !== selectedKeys.value.length) {
    selectedKeys.value = [...newValue]
  }
}, { deep: true })

// 监听权限数据变化
watch(() => props.permissionData, () => {
  // 重新初始化选中状态
  const initialKeys = props.value.length > 0 ? props.value : processedPermissionData.value.selectedKeys
  selectedKeys.value = [...initialKeys]
}, { deep: true })

provide(COMPONENT_KEY, {
  onCheckedChange,
})
</script>

<template>
  <div class="lyy-permission-panel">
    <!-- 工具栏 -->
    <div v-if="showSelectAll && !readonly" class="permission-toolbar">
      <a-space>
        <a-button size="small" @click="handleSelectAll">
          全选
        </a-button>
        <a-button size="small" @click="handleClearAll">
          清空
        </a-button>
        <a-button size="small" type="primary" @click="handleSave">
          保存
        </a-button>
      </a-space>
    </div>

    <!-- 权限树 -->
    <div class="permission-tree">
      <template v-for="node in processedPermissionData.treeData" :key="node.key">
        <BasicNode
          :node="node"
          :checked-keys="selectedKeys"
          :value="selectedKeys"
          :readonly="readonly"
          @change="handleNodeChange"
        />
      </template>
    </div>

    <!-- 空状态 -->
    <div v-if="processedPermissionData.treeData.length === 0" class="permission-empty">
      <a-empty description="暂无权限数据" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.lyy-permission-panel {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;

  .permission-toolbar {
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .permission-tree {
    flex: 1;
    overflow-y: auto;
    max-height: 600px;
  }

  .permission-empty {
    padding: 40px 20px;
    text-align: center;
  }
}
</style>

