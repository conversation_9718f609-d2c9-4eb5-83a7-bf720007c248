<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'initial-state-fix-test'
})
</script>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import LyyPermissionPanel from './index.vue'
import { extractPermissionData, processPermissionData } from './utils'
import type { Key } from './interface'

// 测试数据 - 专门设计来验证初始状态解析问题
const testData = ref({
  code: "200",
  message: "success",
  body: {
    roleId: "initial-state-test",
    roleName: "初始状态解析测试",
    roleRemark: "专门测试父节点选中时子孙节点的自动收集",
    resources: [
      {
        adResourcesId: "1",
        parentId: null,
        name: "商家管理", // 第一级 - 父节点选中
        value: "merchant-manage",
        selected: true, // 🔑 关键：父节点选中
        htmlTemplet: null,
        icon: null,
        seq: 1,
        children: [
          {
            adResourcesId: "2",
            parentId: "1",
            name: "商家列表", // 第二级 - 子节点未显式选中
            value: "merchant-list",
            selected: false, // 未显式选中，但应该被自动收集
            htmlTemplet: "/merchant/list",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "3",
                parentId: "2",
                name: "商家列表查询", // 第三级 - 孙子节点未显式选中
                value: "merchant-list-query",
                selected: false, // 未显式选中，但应该被自动收集
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              },
              {
                adResourcesId: "4",
                parentId: "2",
                name: "商家列表新增",
                value: "merchant-list-add",
                selected: false, // 未显式选中，但应该被自动收集
                htmlTemplet: null,
                icon: null,
                seq: 2,
                children: []
              }
            ]
          },
          {
            adResourcesId: "5",
            parentId: "1",
            name: "商家分组", // 第二级 - 另一个子节点
            value: "merchant-group",
            selected: false, // 未显式选中，但应该被自动收集
            htmlTemplet: "/merchant/group",
            icon: null,
            seq: 2,
            children: [
              {
                adResourcesId: "6",
                parentId: "5",
                name: "分组查询",
                value: "group-query",
                selected: false, // 未显式选中，但应该被自动收集
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              }
            ]
          }
        ]
      },
      {
        adResourcesId: "7",
        parentId: null,
        name: "订单管理", // 第一级 - 另一个根节点，未选中
        value: "order-manage",
        selected: false, // 未选中
        htmlTemplet: null,
        icon: null,
        seq: 2,
        children: [
          {
            adResourcesId: "8",
            parentId: "7",
            name: "订单列表",
            value: "order-list",
            selected: true, // 🔑 关键：子节点选中，但父节点未选中
            htmlTemplet: "/order/list",
            icon: null,
            seq: 1,
            children: [
              {
                adResourcesId: "9",
                parentId: "8",
                name: "订单查询",
                value: "order-query",
                selected: false, // 未显式选中，但应该被自动收集
                htmlTemplet: null,
                icon: null,
                seq: 1,
                children: []
              }
            ]
          }
        ]
      }
    ]
  }
})

// 当前选中的权限
const selectedKeys = ref<Key[]>([])

// 处理后的数据
const processedData = computed(() => {
  return extractPermissionData(testData.value)
})

// 测试结果
const testResults = ref<Array<{
  test: string
  description: string
  expected: string[]
  actual: string[]
  passed: boolean
  timestamp: string
}>>([])

// 调试日志
const debugLogs = ref<string[]>([])

// 处理权限变更
const handlePermissionChange = (keys: Key[]) => {
  console.log('权限变更:', keys)
  selectedKeys.value = keys
  
  // 记录调试日志
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 权限变更: ${keys.join(', ')}`)
  
  // 保持最新的50条日志
  if (debugLogs.value.length > 50) {
    debugLogs.value = debugLogs.value.slice(-50)
  }
}

// 运行初始状态解析测试
const runInitialStateTest = async () => {
  testResults.value = []
  debugLogs.value = []
  
  console.log('开始初始状态解析测试...')
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 开始初始状态解析测试`)
  
  // 测试1: 父节点选中时子孙节点自动收集
  await testParentNodeAutoCollection()
  
  setTimeout(async () => {
    // 测试2: 子节点选中时不影响父节点
    await testChildNodeIndependence()
  }, 1000)
  
  setTimeout(async () => {
    // 测试3: 初始状态一致性验证
    await testInitialStateConsistency()
  }, 2000)
}

// 测试1: 父节点选中时子孙节点自动收集
const testParentNodeAutoCollection = async () => {
  // 重置为初始状态
  selectedKeys.value = [...processedData.value.selectedResources]
  
  await nextTick()
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 初始选中状态: ${selectedKeys.value.join(', ')}`)
  
  // 期望：商家管理选中时，其所有子孙节点都应该被自动收集
  const expectedKeys = [
    'merchant-manage',      // 父节点
    'merchant-list',        // 子节点1
    'merchant-list-query',  // 孙子节点1
    'merchant-list-add',    // 孙子节点2
    'merchant-group',       // 子节点2
    'group-query',          // 孙子节点3
    'order-list',           // 独立选中的节点
    'order-query'           // 其子节点
  ]
  
  const actualKeys = [...selectedKeys.value].sort()
  const expectedKeysSorted = [...expectedKeys].sort()
  
  const passed = expectedKeysSorted.every(key => actualKeys.includes(key)) &&
                 actualKeys.length >= expectedKeysSorted.length
  
  testResults.value.push({
    test: "测试1: 父节点选中时子孙节点自动收集",
    description: "当商家管理(父节点)选中时，应该自动收集所有子孙节点",
    expected: expectedKeysSorted,
    actual: actualKeys,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试1结果: ${passed ? '通过' : '失败'}`)
}

// 测试2: 子节点选中时不影响父节点
const testChildNodeIndependence = async () => {
  // 检查订单列表的情况（子节点选中，父节点未选中）
  const orderManageSelected = selectedKeys.value.includes('order-manage')
  const orderListSelected = selectedKeys.value.includes('order-list')
  const orderQuerySelected = selectedKeys.value.includes('order-query')
  
  // 期望：订单列表选中时，其子节点应该被收集，但父节点(订单管理)不应该被选中
  const expected = ['order-list', 'order-query']
  const actual = ['order-manage', 'order-list', 'order-query'].filter(key => selectedKeys.value.includes(key))
  
  const passed = !orderManageSelected && orderListSelected && orderQuerySelected
  
  testResults.value.push({
    test: "测试2: 子节点选中时不影响父节点",
    description: "订单列表(子节点)选中时，应该收集其子节点，但不影响父节点(订单管理)",
    expected,
    actual,
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试2结果: ${passed ? '通过' : '失败'}`)
}

// 测试3: 初始状态一致性验证
const testInitialStateConsistency = async () => {
  // 验证组件显示状态与实际选中状态的一致性
  const merchantManageSelected = selectedKeys.value.includes('merchant-manage')
  const merchantListSelected = selectedKeys.value.includes('merchant-list')
  const merchantListQuerySelected = selectedKeys.value.includes('merchant-list-query')
  const merchantListAddSelected = selectedKeys.value.includes('merchant-list-add')
  
  // 期望：如果父节点选中，所有子节点也应该选中
  const expected = "所有商家管理相关节点都应该被选中"
  const actual = `商家管理:${merchantManageSelected}, 商家列表:${merchantListSelected}, 查询:${merchantListQuerySelected}, 新增:${merchantListAddSelected}`
  
  const passed = merchantManageSelected && merchantListSelected && 
                 merchantListQuerySelected && merchantListAddSelected
  
  testResults.value.push({
    test: "测试3: 初始状态一致性验证",
    description: "验证父节点选中时，组件显示状态与实际选中状态的一致性",
    expected: [expected],
    actual: [actual],
    passed,
    timestamp: new Date().toLocaleTimeString()
  })
  
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 测试3结果: ${passed ? '通过' : '失败'}`)
}

// 手动测试函数
const manualTest = (testName: string) => {
  debugLogs.value.push(`[${new Date().toLocaleTimeString()}] 手动测试: ${testName}`)
  
  switch (testName) {
    case 'reset':
      selectedKeys.value = [...processedData.value.selectedResources]
      break
    case 'clearAll':
      selectedKeys.value = []
      break
    case 'selectMerchantManage':
      selectedKeys.value = ['merchant-manage', 'merchant-list', 'merchant-list-query', 'merchant-list-add', 'merchant-group', 'group-query']
      break
    case 'selectOrderList':
      selectedKeys.value = ['order-list', 'order-query']
      break
    case 'showRawData':
      console.log('原始测试数据:', testData.value)
      console.log('处理后数据:', processedData.value)
      break
  }
}

// 清空调试日志
const clearDebugLogs = () => {
  debugLogs.value = []
}

// 初始化
selectedKeys.value = [...processedData.value.selectedResources]

// 监听选中状态变化
watch(selectedKeys, (newKeys) => {
  console.log('选中状态变化:', newKeys)
}, { deep: true })
</script>

<template>
  <div class="initial-state-fix-test">
    <div class="test-header">
      <h1>初始状态解析问题修复验证</h1>
      <p>验证父节点选中时子孙节点的自动收集逻辑是否正确工作</p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制" size="small">
        <a-space wrap>
          <a-button type="primary" @click="runInitialStateTest">
            🧪 运行初始状态解析测试
          </a-button>
          
          <a-divider type="vertical" />
          
          <a-button @click="manualTest('reset')">
            重置初始状态
          </a-button>
          <a-button @click="manualTest('clearAll')">
            清空所有
          </a-button>
          <a-button @click="manualTest('selectMerchantManage')">
            手动选中商家管理
          </a-button>
          <a-button @click="manualTest('selectOrderList')">
            手动选中订单列表
          </a-button>
          <a-button @click="manualTest('showRawData')">
            查看原始数据
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <a-card title="初始状态解析测试结果" size="small">
        <div class="results-summary">
          <a-statistic 
            title="通过率" 
            :value="((testResults.filter(r => r.passed).length / testResults.length) * 100).toFixed(1)" 
            suffix="%" 
            :value-style="{ color: testResults.every(r => r.passed) ? '#3f8600' : '#cf1322' }"
          />
        </div>
        
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'passed': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.test }}</span>
              <span class="test-status">
                {{ result.passed ? '✅ 通过' : '❌ 失败' }}
              </span>
              <span class="test-time">{{ result.timestamp }}</span>
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div class="result-details">
              <div><strong>期望:</strong> {{ result.expected.join(', ') }}</div>
              <div><strong>实际:</strong> {{ result.actual.join(', ') }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 调试日志 -->
    <div class="debug-logs">
      <a-card title="调试日志" size="small">
        <template #extra>
          <a-button size="small" @click="clearDebugLogs">清空日志</a-button>
        </template>
        
        <div class="logs-container">
          <div 
            v-for="(log, index) in debugLogs" 
            :key="index"
            class="log-item"
          >
            {{ log }}
          </div>
          <div v-if="debugLogs.length === 0" class="no-logs">
            暂无调试日志
          </div>
        </div>
      </a-card>
    </div>

    <!-- 数据对比 -->
    <div class="data-comparison">
      <a-card title="数据对比分析" size="small">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="comparison-card">
              <h4>原始后端数据中的选中状态</h4>
              <div class="data-list">
                <div class="data-item">
                  <span class="node-name">商家管理:</span>
                  <a-tag color="green">selected: true</a-tag>
                </div>
                <div class="data-item">
                  <span class="node-name">商家列表:</span>
                  <a-tag color="red">selected: false</a-tag>
                </div>
                <div class="data-item">
                  <span class="node-name">商家列表查询:</span>
                  <a-tag color="red">selected: false</a-tag>
                </div>
                <div class="data-item">
                  <span class="node-name">订单管理:</span>
                  <a-tag color="red">selected: false</a-tag>
                </div>
                <div class="data-item">
                  <span class="node-name">订单列表:</span>
                  <a-tag color="green">selected: true</a-tag>
                </div>
                <div class="data-item">
                  <span class="node-name">订单查询:</span>
                  <a-tag color="red">selected: false</a-tag>
                </div>
              </div>
            </div>
          </a-col>
          
          <a-col :span="12">
            <div class="comparison-card">
              <h4>修复后的初始选中状态</h4>
              <div class="data-list">
                <div 
                  v-for="key in ['merchant-manage', 'merchant-list', 'merchant-list-query', 'merchant-list-add', 'merchant-group', 'group-query', 'order-manage', 'order-list', 'order-query']" 
                  :key="key"
                  class="data-item"
                >
                  <span class="node-name">{{ key }}:</span>
                  <a-tag :color="selectedKeys.includes(key) ? 'green' : 'red'">
                    {{ selectedKeys.includes(key) ? '已选中' : '未选中' }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 当前选中状态 -->
    <div class="current-state">
      <a-card title="当前选中状态" size="small">
        <div class="state-info">
          <p><strong>选中数量:</strong> {{ selectedKeys.length }}</p>
          <div class="selected-keys">
            <a-tag 
              v-for="key in selectedKeys" 
              :key="key"
              color="blue"
              style="margin: 2px;"
            >
              {{ key }}
            </a-tag>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 权限面板 -->
    <div class="permission-panel">
      <a-card title="权限面板" size="small">
        <lyy-permission-panel
          :permission-data="testData"
          v-model:value="selectedKeys"
          :readonly="false"
          :show-select-all="true"
          @change="handlePermissionChange"
        />
      </a-card>
    </div>

    <!-- 修复说明 -->
    <div class="fix-description">
      <a-card title="修复说明" size="small">
        <div class="fix-content">
          <h4>🐛 修复的问题：</h4>
          <p>在 <code>utils.ts</code> 的 <code>collectSelectedKeys</code> 函数中，当父节点 <code>selected: true</code> 时，只添加了父节点本身，没有递归收集其所有子孙节点。</p>
          
          <h4>🔧 修复方案：</h4>
          <ul>
            <li>在 <code>collectSelectedKeys</code> 函数中添加子孙节点自动收集逻辑</li>
            <li>新增 <code>collectAllDescendantKeys</code> 辅助函数递归收集所有子孙节点</li>
            <li>在 <code>extractPermissionData</code> 函数中同步修复 <code>extractSelected</code> 逻辑</li>
            <li>添加详细的调试日志便于验证修复效果</li>
          </ul>
          
          <h4>✅ 期望效果：</h4>
          <p>当后端数据中父节点 <code>selected: true</code> 时，初始化时应该自动收集该父节点下的所有子孙节点，确保状态一致性。</p>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.initial-state-fix-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 24px;
    
    h1 {
      color: #262626;
      margin-bottom: 8px;
    }
    
    p {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .test-controls,
  .test-results,
  .debug-logs,
  .data-comparison,
  .current-state,
  .permission-panel,
  .fix-description {
    margin-bottom: 16px;
  }

  .results-summary {
    margin-bottom: 16px;
    text-align: center;
  }

  .results-list {
    .result-item {
      padding: 12px;
      margin-bottom: 8px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      &.passed {
        border-color: #52c41a;
        background: #f6ffed;
      }
      
      &.failed {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .test-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .result-description {
        font-size: 13px;
        color: #595959;
        margin-bottom: 8px;
        font-style: italic;
      }
      
      .result-details {
        font-size: 12px;
        color: #595959;
        
        div {
          margin-bottom: 4px;
        }
      }
    }
  }

  .logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 4px;
    padding: 8px;
    
    .log-item {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #586069;
      margin-bottom: 2px;
      line-height: 1.4;
    }
    
    .no-logs {
      text-align: center;
      color: #8c8c8c;
      font-style: italic;
    }
  }

  .comparison-card {
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    
    h4 {
      margin: 0 0 12px 0;
      color: #262626;
    }
    
    .data-list {
      .data-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .node-name {
          font-size: 12px;
          color: #595959;
        }
      }
    }
  }

  .state-info {
    .selected-keys {
      margin-top: 8px;
      max-height: 100px;
      overflow-y: auto;
    }
  }

  .fix-content {
    h4 {
      margin: 16px 0 8px 0;
      color: #262626;
      
      &:first-child {
        margin-top: 0;
      }
    }

    p, ul {
      margin: 8px 0;
      color: #595959;
      line-height: 1.6;
    }

    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
      }
    }

    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      color: #d73a49;
    }
  }
}
</style>
