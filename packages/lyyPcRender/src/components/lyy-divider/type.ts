export interface PropType {
  /**
   * 垂直对齐方式
   *
   * 默认值：false
   */
  dashed?: boolean

  /**
   * 分割线标题的位置
   */
  orientation?: 'left' | 'right' | 'center'

  /**
   * 	标题和最近 left/right 边框之间的距离，去除了分割线，同时 orientation 必须为 left 或 right
   */
  orientationMargin?: string | number

  /**
   * 文字是否显示为普通正文样式
   *
   * 默认值：false
   */
  plain?: boolean

  /**
   * 水平还是垂直类型
   *
   * 默认值：horizontal
   */
  type?: 'horizontal' | 'vertical'
  /**
   * 文字内容
   *
   * 默认值：''
   */
  text?: '内容'
}

export interface LyyDivider extends IElement {
  compName: 'lyy-divider'
  prop: PropType
}
