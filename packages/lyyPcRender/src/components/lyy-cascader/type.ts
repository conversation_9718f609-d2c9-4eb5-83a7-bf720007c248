import type { CascaderOptionType } from 'ant-design-vue/es/cascader'
import type { Rule } from 'ant-design-vue/es/form'

export interface PropType {
  /**
   * 字段名
   */
  field?: string
  fields?: string[]
  /**
   * 预请求
   */
  preFetch: IRequestConfig
  /**
   * 字段关联的formId
   */
  formId?: string
  /**
   * @description label 标签文本
   */
  label: string
  /**
   * @description 表单验证规则 object | array
   */
  rules?: Rule
  /**
   * @description 配合 label 属性使用，表示是否显示 label 后面的冒号
   */
  colon?: boolean
  /**
   * @description 设置字段校验的时机
   */
  validateTrigger?: string | string[]
  /**
   * @description 输入框提示文本
   */
  placeholder?: string
  /**
   * @description 是否支持点击清除图标清除内容
   */
  allowClear?: boolean
  /**
   * @description 是否支持自动获取焦点 默认true
   */
  autofocus?: boolean
  /**
   * @description 是否禁用
   */
  disabled?: boolean
  /**
   * @description 是否有边框
   */
  bordered?: boolean
  /**
   * @description 单选时，是否点击每级菜单选项值都会发生改变
   */
  changeOnSelect?: boolean
  /**
   * @description 默认选中的值
   */
  defaultValue?: string
  /**
   * @description 自定义options中的 label、name、children字段
   */
  fieldNames?: {
    label?: string
    value?: string
    children?: string
    level?: string
  }
  /**
   * @description 最多显示多少个tag
   */
  maxTagCount?: number
  /**
   * @description 是否支持多选
   */
  multiple?: boolean
  /**
   * @description 可选数据源
   */
  options?: CascaderOptionType[]
  /**
   * @description 在选中节点改变时是否返回该节点所在的各级菜单的值所组成的数组，若为false，则只返回该节点的值
   */
  emitPath?: boolean
  /**
   * @description 是否懒加载, 不能与emitpath一起使用,（暂未支持）
   */
  lazy?: boolean
  /**
   * @description 懒加载最深层级
   */
  maxLevel?: boolean
  /**
   * @description 懒加载接口入参名
   */
  parentId?: string
  /**
   * 多选的回填模式
   */
  showCheckedStrategy?: 'SHOW_PARENT' | 'SHOW_CHILD'

  showSearch?: boolean | object

  labelCol?: object
}

export type SingleSelectType = string | number

export type ValueType =
  | SingleSelectType
  | SingleSelectType[]
  | SingleSelectType[][]

export interface LyyCascader extends IElement {
  compName: 'lyy-cascader'
  modelValue?: ValueType
  prop: PropType
}
