import { RuleObject } from 'ant-design-vue/lib/form/interface'
import { IFetchConfig, IPayload } from '../../types/interface-type'

export type Validator = (
  rule: RuleObject,
  value: string,
  callback: (error?: string) => void,
) => Promise<void> | void

export type ActionItem = {
  /**
   * @description 动作类型
   */
  type: 'router' | 'store' | 'tip'
  /**
   * @description 需要触发的store 的 action 的 name
   */
  storeActionName?: string
  /**
   * @description 路由跳转路径
   */
  path?: string
  /**
   * @description 动作执行的条件
   */
  condition?: ICondition
  /**
   * @description 提示语
   */
  message?: string
}

export interface ICondition {
  type: 'equal' | 'unequal'
  conditionList: Array<{
    key: string
    value: string | number | boolean
  }>
}

export type AfterLoginActions = {
  /**
   * 登录成功之后的动作
   */
  actions: ActionItem[]
}

export interface ILoginFieldNames {
  /**
   * 用户名对应的字段名
   */
  username?: string
  /**
   * 密码对应的字段名
   */
  password?: string
  /**
   * 图片验证码对应的字段名
   */
  verifyCode?: string
  /**
   * 短信验证码的字段名
   */
  smsVerifyCode?: string
  /**
   * 新密码对应的字段名
   */
  newPassword?: string
  /**
   * 确认密码对应的字段名
   */
  confirmPassword?: string
}

export interface ILoginProps {
  /**
   * @description 是否扫码登录
   */
  showScanLogin?: boolean
  /**
   * @description 登录的title
   */
  loginTitle: string
  /**
   * @description 是否验证码登录
   */
  showVerifyCode?: boolean
  /**
   * @description 是否展示注册入口
   */
  showRegister?: boolean
  /**
   * @description 是否展示忘记密码入口
   */
  showForgetPwd?: boolean
  /**
   * @description 用户名校验规则
   */
  usernamePattern?: RegExp
  /**
   * @description 密码校验规则
   */
  passwordPattern?: RegExp
  /**
   * @description 请求配置
   */
  loginFetch: IFetchConfig
  /**
   * @description 获取图片验证码请求配置
   */
  imgVerifyCodeFetch?: IFetchConfig
  /**
   * @description 登录之后需要执行的动作
   */
  afterLoginActions?: AfterLoginActions
  /**
   * @description 是否使用路由权限
   */
  usePermission?: boolean
  /**
   * @description 输入框字段映射
   */
  fieldNames?: ILoginFieldNames
  /**
   * @description token对应的path，从fetch的jsonPath之后开始的路径
   */
  tokenPath?: string
  /**
   * token 存储的位置
   */
  storageType?: 'sessionStorage' | 'localStorage'
}

export interface IsmsVerifyCodeFetch {
  /**
   * 忘记密码对应的发送验证码的请求配置
   */
  forgetPassword?: IFetchConfig
  /**
   * 注册对应的发送验证码的请求配置
   */
  register?: IFetchConfig
}

export interface PropType extends ILoginProps {
  /**
   * @description 忘记密码请求配置
   */
  forgetFetch?: IFetchConfig
  /**
   * @description 修改密码请求配置
   */
  amendFetch?: IFetchConfig
  /**
   * @description 注册请求配置
   */
  registerFetch?: IFetchConfig
  /**
   * @description 获取验证码请求配置
   */
  smsVerifyCodeFetch?: IsmsVerifyCodeFetch
  /**
   * @description 校验验证码接口
   */
  checkSmsVerifyCodeFetch?: IFetchConfig
}

// export interface LyyLogin extends IElement {
//   compName: 'lyy-login'
//   prop: PropType
// }

export type LyyLogin = PropType
