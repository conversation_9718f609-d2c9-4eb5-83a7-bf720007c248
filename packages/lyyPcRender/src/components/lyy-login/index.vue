<template>
  <div class="wrap">
    <div class="formWrap">
      <transition name="panel" mode="out-in">
        <component
          :is="comName"
          :key="comName"
          v-bind="prop"
          @switch="handleSwitch"
          @loginSuccess="handleLoginSuccess"
        ></component>
      </transition>
    </div>
  </div>
</template>
<script lang="ts">
import {
  LoginPanel,
  WxLoginPanel,
  ForgetPanel,
  AmendPanel,
  RegisterPanel,
} from './components'
export default {
  name: 'login-index',
  components: {
    LoginPanel,
    WxLoginPanel,
    ForgetPanel,
    AmendPanel,
    RegisterPanel,
  },
}
</script>
<script setup lang="ts">
import { ref, toRefs, defineEmits } from 'vue'
import { PropType } from './type'
const props = defineProps<{ prop: PropType }>()
const { prop } = toRefs(props)
const emits = defineEmits(['loginSuccess'])

let comName = ref('LoginPanel')
const handleSwitch = (compName) => {
  comName.value = compName
}

const handleLoginSuccess = (data) => {
  emits('loginSuccess', data)
}
</script>
<style lang="scss" scoped>
.wrap {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: 100vh;
  background: url('./assets/images/bodyBg.png') no-repeat;
  background-size: cover;
  .formWrap {
    position: absolute;
    top: 47%;
    left: 65%;
    align-items: center;
    transform: translate(-20%, -47%);
  }
}

.panel-enter-active,
.panel-leave-active {
  transition: all 0.2s ease-in-out;
  transform: translateX(0px);
  opacity: 1;
}
.panel-enter {
  opacity: 0;
  transform: translateX(-20px);
}
.panel-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>
