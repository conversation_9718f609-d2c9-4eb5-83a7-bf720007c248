<template>
  <span>
    <template v-for="tag in tags" :key="tag">
      <a-tag>{{ tag }}</a-tag>
    </template>
  </span>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
const props = defineProps<{ val?: string | number | Array<string> }>()
const tags = computed(() => {
  const { val } = props
  let arr: Array<string | number> = []
  arr = val ? (Array.isArray(val) ? [...val] : [val]) : []
  return arr
})
</script>
<script lang="ts">
export default {
  name: 'tag-comp',
}
</script>
