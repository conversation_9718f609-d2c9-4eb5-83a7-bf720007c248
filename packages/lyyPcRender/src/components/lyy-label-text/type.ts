// import { ILink } from '../../types/interface-type'
import { PropType as iconPropType } from '../lyy-icon/type'
import type * as CSS from 'csstype'
import { IPayload } from '../../global'

type FlagType = {
  /**
   * @description form中对应的字段名
   */
  field?: string
  /**
   * @description flag的style样式
   */
  style?: CSS.Properties
  /**
   * @description flag默认值
   */
  value?: string | number
  /**
   * @description flag值的类型类型
   */
  type?: ValueType
  /**
   * @description 字段映射
   */
  maps?: {
    [key: string]: string | number
  }
}

type ValueType = 'link' | 'tag'

export type ModalValueType =
  | string
  | number
  | Array<string | number>
  | null
  | undefined

export interface PropType {
  /**
   * @description 描述的值的转换类型
   * 如果配置了type，则暂不支持配多个字段情况
   */
  type?: ValueType
  /**
   * @description 描述的label
   */
  label?: string
  /**
   * @description 描述的静态值
   */
  value?: string | number | Array<string | number>

  /**
   * @description 描述的值后的icon的name
   */
  suffixIcon?: iconPropType
  /**
   * @description 描述的label的icon
   */
  prefixIcon?: iconPropType
  /**
   * @description 是否展示value后的flag
   */
  flag?: FlagType

  /**
   * @description 描述的值的样式
   */
  textStyle?: CSS.Properties
  /**
   * @description 描述的label的样式
   */
  labelStyle?: CSS.Properties

  /**
   * 文本的分隔符
   */
  seperator?: string
  /**
   * @description label的宽度
   */
  labelWidth?: string | number

  /**
   * @description 文案过长时是否使用tooltip
   */
  tooltip?: boolean
  textPrefix: string
  textSuffix: string
  /**
   * 格式化处理管道
   */
  pipes?: Pipe[] | string[]
  field: string
  expValue: string
  payloads: IPayload[]
  formId: string
  showCopyBtn?: boolean,
  showDetailBtn?: boolean,
}

export interface LyyLableText extends IElement {
  compName: 'lyy-label-text'
  prop: PropType
  modelValue?: ModalValueType
}
