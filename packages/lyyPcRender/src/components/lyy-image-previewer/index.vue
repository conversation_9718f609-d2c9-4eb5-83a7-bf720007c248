<script setup lang="ts">
import { computed, toRefs, ref, watch, inject } from 'vue'
import { PropType, ModelValueType } from './type'
import { getIn } from '@leyaoyao/libs/utils/get-in'
import _ from 'lodash'
import { usePayload } from '@leyaoyao/libs/hooks'
import { UPDATE_MODELVALUE } from '../../constants/action-type'
import { useFormState } from '../../hooks'
const props = defineProps<{
  prop: PropType
  modelValue?: ModelValueType
}>()
const {
  formId,
  field,
  fallback,
  height = ref(200),
  width = ref(200),
  placeholder,
  jsonPath = ref('url'),
  defaultSrcs,
  showCover,
  payloads,
  styleImg,
} = toRefs(props.prop)
// const widthDefault = width?.value ?? 200
// const heightDefault = height?.value ?? 200
// const jsonPathDefault = ref(jsonPath?.value ?? 'url')

const emit = defineEmits([UPDATE_MODELVALUE])

const modelValue = computed(() => props.modelValue ?? [])

// 注入formState,并监听formState的变化，更新modelValue的值
useFormState(props)

const imageUrlList = computed(() => {
  let urlList: string | string[] | undefined = []
  let targetValue: string | string[] | Array<object> | undefined = undefined
  if (defaultSrcs?.value) {
    targetValue = defaultSrcs.value
  }
  if (modelValue.value.length > 0) {
    targetValue = modelValue.value
  }
  if (payloads?.value) {
    targetValue = usePayload(payloads.value)[field.value]
  }
  if (_.isUndefined(targetValue)) {
    urlList = []
  }
  if (typeof targetValue === 'string') {
    urlList = [targetValue]
  }
  if (Array.isArray(targetValue)) {
    const arr: Array<string> = []
    for (const item of targetValue) {
      if (_.isObject(item)) {
        arr.push(getIn(item as object, jsonPath.value))
      } else {
        arr.push(item as string)
      }
    }
    urlList = arr
  }

  return urlList
})
</script>

<template>
  <div :class="[{ 'lyy-file-cover': showCover }, 'lyy-component']">
    <a-image-preview-group>
      <template v-for="item in imageUrlList" :key="item">
        <a-image
          :src="item"
          v-bind="prop"
          :width="width"
          :height="height"
          :style="styleImg"
        >
          <template #previewMask>预览</template>
        </a-image>
      </template>
    </a-image-preview-group>
  </div>
</template>
<style lang="scss" scoped>
::v-deep .ant-image {
  border: 1px solid #d9d9d9 !important;
  padding: 8px;
  margin-right: 8px;
  &:last-child {
    margin-right: 0;
  }
  img {
    height: 100% !important;
  }
}
.lyy-file-cover {
  ::v-deep .ant-image {
    &:first-child {
      position: relative;
      &:after {
        content: '封面';
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 22px;
        text-align: center;
        line-height: 22px;
        color: #fff;
        font-size: 12px;
        background: rgba(24, 144, 255, 0.8);
        border: 1px solid rgba(24, 144, 255, 0.35);
        border-radius: 0 4px 0 8px;
      }
    }
  }
}
</style>
