<script lang="ts" setup>
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { BEFORE_MOUNT, MOUNTED } from '../../constants/event-type'
import {
  defineComponent,
  onMounted,
  computed,
  onBeforeMount,
  reactive,
  toRefs,
} from 'vue'
import _ from 'lodash'
import { datapipe } from '@leyaoyao/libs/utils/pipe'
import { tpl, getIn } from '@leyaoyao/libs/utils'
import { PropType, ModelValueType } from './type'
import { useFormState } from '../../hooks'

defineComponent({
  name: 'lyy-card-list',
})

const props = defineProps<{
  prop: PropType
  modelValue: ModelValueType
  style: any
  childrens?: IElement[]
}>()

const { prop, childrens } = toRefs(props)
const { title, extra, content, showHeader } = toRefs(prop.value)

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

useFormState(props, false, true)

onBeforeMount(() => {
  initModelValue()
  emit(ACTION, { event: BEFORE_MOUNT })
})

onMounted(() => {
  emit(ACTION, { event: MOUNTED })
})

const initModelValue = () => {
  const modelValue = props.modelValue ?? {}
  if (!modelValue.datasource) {
    modelValue.datasource = []
  }
  if (!modelValue.currentRow) {
    modelValue.currentRow = {}
  }
  emit(UPDATE_MODELVALUE, modelValue)
}

const datasource = computed(() => props.modelValue?.datasource)
const key = computed(() => props.prop.key)
// 获取单个组件值
const getItemValue = (prop, data) => {
  const { field, joins, separator = ', ', pipes } = prop || {}
  const value = getIn(data, field)
  // 处理: 空值
  if (value === undefined || value === null || value === '') return '-'

  // 多字段拼接处理
  if (joins) {
    const values = joins.map((item) => {
      const value = getIn(data, item)
      if (!pipes) return value
      return datapipe(value, pipes)
    })
    return values
      ?.filter((item) => ![null, undefined, ''].includes(item))
      .join(separator)
  }
  // 处理: 数字、日期、映射
  if (!pipes) return value
  return datapipe(value, pipes)
}
// 获取单个组件
const generateComp = (comp, data) => {
  const modelValue = getItemValue(comp.prop, data)
  const prop = _.cloneDeep(comp.prop || {})
  let config = reactive({
    compName: comp.compName,
    prop: prop,
    compId: comp.compId,
    actions: comp.actions,
    style: comp.style ?? {},
  })
  if (comp.compName === 'lyy-link') {
    config.style = Object.assign(comp.style ?? {}, {
      'margin-right': '10px',
      'text-decoration': 'none',
    })
  }
  if (comp.compName === 'lyy-button') {
    config.style = Object.assign(comp.style ?? {}, {
      'margin-right': '10px',
    })
  }
  if (comp.compName === 'lyy-html') {
    config.modelValue = data
  }
  if (comp.compName === 'lyy-qrcode') {
    config.prop.url = modelValue && modelValue != '-' ? modelValue : ''
  }
  if (comp.compName === 'lyy-label-text' || comp.type === 'lyy-tags') {
    config.modelValue = modelValue
  }
  if (comp.compName === 'lyy-label-text' || comp.type === 'lyy-tags') {
    config.style = Object.assign(comp.style ?? {}, {
      display: 'inline',
      'margin-right': '10px',
    })
  }
  return [config]
}
const itemClick = (dataItem, index) => {
  emit(
    UPDATE_MODELVALUE,
    Object.assign(props.modelValue ?? {}, { currentRow: dataItem }),
  )
  emit(
    UPDATE_MODELVALUE,
    Object.assign(props.modelValue ?? {}, { currentIndex: index }),
  )
}
</script>
<template>
  <div class="lyy-container">
    <template v-if="datasource?.length > 0">
      <template v-for="(dataItem, i) in datasource" :key="dataItem">
        <div
          class="lyy-for-panel"
          :style="props.style"
          @click.capture="itemClick(dataItem, i)"
        >
          <a-card :bodyStyle="prop.cardBodyStyle">
            <template
              #title
              v-if="props.prop.showHeader && !props.prop?.titleComp?.length"
            >
              {{ title }}
            </template>
            <template
              #title
              v-else-if="props.prop.showHeader && props.prop?.titleComp?.length"
            >
              <template v-for="ele in props.prop.titleComp" :key="ele">
                <pc-render-template
                  :elements="generateComp(ele, dataItem)"
                  :rowData="dataItem"
                ></pc-render-template>
              </template>
            </template>
            <template #extra v-if="props.prop.showHeader">
              <template v-for="ele in props.prop.extraList" :key="ele">
                <span>
                  <pc-render-template
                    :elements="generateComp(ele, dataItem)"
                    :rowData="dataItem"
                  ></pc-render-template>
                </span>
              </template>
            </template>
            <template
              v-if="
                _.isArray(getItemValue({ field: prop.contentField }, dataItem))
              "
            >
              <template
                v-for="cont in getItemValue(
                  { field: prop.contentField },
                  dataItem,
                )"
                :key="cont"
              >
                <div>
                  <template v-for="ele in prop.children" :key="ele">
                    <pc-render-template
                      :elements="generateComp(ele, cont)"
                      :rowData="cont"
                    ></pc-render-template>
                  </template>
                </div>
              </template>
            </template>
            <template v-else>
              <template v-for="ele in prop.children" :key="ele">
                <pc-render-template
                  :rowData="
                    prop?.contentField ? dataItem[prop.contentField] : dataItem
                  "
                  :elements="
                    generateComp(
                      ele,
                      prop?.contentField
                        ? dataItem[prop.contentField]
                        : dataItem,
                    )
                  "
                ></pc-render-template>
              </template>
            </template>
          </a-card>
        </div>
      </template>
    </template>
    <pc-render-template v-else :elements="[]"></pc-render-template>
  </div>
</template>

<style lang="scss" scoped>
::v-deep .ant-card-head {
  font-weight: normal;
}
.lyy-for-panel {
  margin-bottom: 10px;
}
::v-deep .ant-card-body {
  position: relative;
  &::before {
    content: none !important;
  }
}
::v-deep .ant-card-body .draggable-wrap {
  display: inline;
}
</style>
