<script setup lang="ts">
import {
  ref,
  computed,
  onBeforeMount,
  onMounted,
  onBeforeUnmount,
  defineComponent,
  watch,
  onActivated,
  StyleValue,
  provide,
  watchEffect,
  inject,
  Ref,
  onUnmounted,
} from 'vue'
import {
  BEFORE_MOUNT,
  BEFORE_UNMOUNT,
  MOUNTED,
  UPDATE,
  ACTIVATED,
} from '../../constants/event-type'
import { ACTION, UPDATE_MODELVALUE } from '../../constants/action-type'
import { PropType, ModelValueType } from './type'
import { store } from '../../render'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import _ from 'lodash'
import { treeFind } from '@leyaoyao/libs/utils/tree-find'
import { getIn, useDep, removeDep } from '@leyaoyao/libs'
import { FORM_REF } from '@leyaoyao/libs/constants/store-key'
import { IElement } from '../../global'

defineComponent({
  name: 'lyy-form',
})

const props = defineProps<{
  prop: PropType
  modelValue: ModelValueType
  compId: string
  // eslint-disable-next-line no-undef
  childrens: IComponent[]
  entityId?: string
  style?: StyleValue
}>()

const emit = defineEmits([UPDATE_MODELVALUE, ACTION])

const isEdit = inject<Ref<boolean>>('isEdit')
const globalConfig = inject('globalConfig')
const parentMainLayout = inject('mainLayout')
const formRef = ref(null)
const isRequest = ref(false)
const formState = computed({
  get: () => {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    isRequest.value = true
    //props.prop.form 不会触发数据改变 所以改成props.modelValue
    return props.modelValue ?? {}
  },
  set: (newValue) => {
    emit(UPDATE_MODELVALUE, newValue)
    emit(ACTION, { event: UPDATE })
  },
})

const originState = computed(() => {
  return props.prop.form ?? {}
})

const disabled = computed(() => {
  return props.prop.disabled ?? false
})

const labelCol = computed(() => {
  const _labelCol = props.prop.labelCol ?? { style: { width: '100px' } }
  return _labelCol
})
// 注入数据
provide('formState', formState)
provide('formId', props.compId)
provide('formDisabled', disabled)
provide('formOriginState', originState)
provide('formLayout', props.prop.layout)
provide('formLabelCol', labelCol)
onBeforeMount(() => {
  emit(ACTION, { event: BEFORE_MOUNT })
})
onActivated(() => {
  emit(ACTION, { event: ACTIVATED })
})
const isSpecialStyle = computed(() => {
  return globalConfig?.isSpecialStyle ?? props.prop.isSpecialStyle ?? true
})
// 拍平对象
const smashing = (item) => {
  const res = {}
  const flat = (obj, preKey = '') => {
    for (const [key, value] of Object.entries(obj)) {
      let newKey = key
      if (preKey && !Array.isArray(obj)) {
        newKey = `${preKey}.${newKey}`
      }
      if (_.isObject(value) && !_.isArray(value)) {
        flat(value, newKey)
        continue
      }
      res[newKey] = value
    }
  }
  flat(item)
  return res
}

function executeExpressionWatch() {
  const targetForm = formState.value
  const expressionNodes: IElement[] = [] // 节点集合，带有公式的属性值
  const sameFormNodes: IElement[] = [] // 同一表单下的节点集合
  // 遍历树状数据，查找带有公式的节点和同一表单下的节点
  treeFind(props.childrens, (node) => {
    if (
      node.prop?.show?.exp &&
      (node.prop?.show?.exp.includes(props.compId) ||
        node.prop?.show?.exp.includes(props.entityId))
    ) {
      expressionNodes.push(node)
    }
    if (
      (node.prop?.field || node.prop?.fields) &&
      node?.prop?.formId === props.compId
    ) {
      sameFormNodes.push(node)
    }
  })
  // 监听表单变化
  watch(
    () => _.cloneDeep(targetForm),
    (newForm, oldForm) => {
      newForm = props.prop.smashing ? smashing(newForm) : newForm
      const changeList = Object.keys(newForm).filter((key) => {
        // 旧表单的值
        const oldValue =
          _.isObject(oldForm[key]) || _.isArray(oldForm[key])
            ? JSON.stringify(oldForm[key])
            : oldForm[key]
        // 新表单的值
        const newValue =
          _.isObject(newForm[key]) || _.isArray(newForm[key])
            ? JSON.stringify(newForm[key])
            : newForm[key]
        return oldValue !== newValue
      })
      if (changeList.length > 0) {
        const updateNodes: IElement[] = [] // 需要更新的节点集合

        for (const node of expressionNodes) {
          for (const field of changeList) {
            if (node.prop.show.exp.includes(field)) {
              node.prop.update = Math.random()
              updateNodes.push(node)
            }
          }
        }

        for (const node of sameFormNodes) {
          // 单字段
          if (
            changeList.includes(node?.prop?.field) &&
            !updateNodes.includes(node)
          ) {
            node.prop.update = Math.random()
          }
          // 复合字段
          if (
            node.prop.fields &&
            node.prop.fields.some(
              (field: string) =>
                changeList.includes(field) && !updateNodes.includes(node),
            )
          ) {
            node.prop.update = Math.random()
          }
        }
      }
    },
  )
}

onBeforeMount(() => {
  if (!isEdit?.value) {
    executeExpressionWatch()
  }
})

onMounted(() => {
  provide('formRef', formRef)
  emit(ACTION, {
    event: MOUNTED,
    payload: { formRef: formRef.value },
  })
  if (props.prop?.isSignChange) {
    watch(
      formState,
      () => {
        if (isRequest.value) {
          isRequest.value = false
        } else {
          emit(ACTION, { event: 'formChange' })
        }
        store.setValue('formChange', true, 'isChange')
      },
      { deep: true },
    )
  }
})
onBeforeUnmount(() => {
  emit(ACTION, { event: BEFORE_UNMOUNT })
})
// 获取表单对象
const getFormRef = () => {
  return store.getValue(FORM_REF, props.compId)
}
// 重置表单
const resetForm = async () => {
  await getFormRef().resetFields()
}
// 校验表单
const validate = async (fields) => {
  try {
    await (fields
      ? getFormRef().validateFields(fields)
      : getFormRef().validate())
  } catch (error) {
    console.log(error)
  }
}
// 表单赋值
const setData = (data = {}) => {
  const dataSets = proxyData.getProxyData('setData --> form')
  // 目标数据
  const toValue = getIn(dataSets, `${props.compId}.modelValue`)
  Object.assign(toValue, _.cloneDeep(data))
}
const actionsSet = {
  resetForm,
  validate,
  setData,
}
const fnDep = (type, ...args) => {
  if (actionsSet[type]) return actionsSet[type](...args)
}
// 使用联动模式
useDep(props.compId, fnDep)

onUnmounted(() => {
  removeDep(props.compId, fnDep)
})
</script>

<template>
  <a-form
    :style="style"
    ref="formRef"
    :model="formState"
    :label-col="labelCol"
    :wrapper-col="prop.wrapperCol"
    :labelAlign="prop.labelAlign"
    :colon="prop.colon ?? true"
    :layout="prop.layout"
    :class="[
      props.prop.class,
      isSpecialStyle ? 'isSpecialStyle' : '',
      { 'lyy-main-form': prop.mainLayout || parentMainLayout, view: !isEdit },
      'lyy-container',
    ]"
  >
    <pc-render-template :elements="props.childrens"></pc-render-template>
    <!--    <slot></slot>-->
  </a-form>
</template>
<style lang="scss" scoped>
.ant-form {
  & > :deep(.draggable-wrap) {
    display: flex;
    flex-direction: column;
  }

  &.isSpecialStyle {
    :deep(.ant-input-disabled),
    //:deep(.ant-input-disabled::placeholder),
    :deep(.ant-select-disabled .ant-select-selection-item),
    :deep(.ant-input-number-disabled .ant-input-number-input),
    :deep(.ant-picker-disabled .ant-picker-input input) {
      color: rgba(0, 0, 0, 0.85);
    }

    :deep(.ant-select-disabled .ant-select-selector) {
      pointer-events: none;
    }

    :deep(
        .ant-select-disabled .ant-select-selector .ant-select-selection-item
      ) {
      user-select: auto;
    }
  }
}

.lyy-main-form {
  height: 100%;

  // display: flex;
  // flex-direction: column;
  // overflow 会影响编辑器中工具栏展示
  &.view {
    overflow: auto;
  }

  & > :deep(.draggable-wrap) {
    display: flex;
    flex-direction: column;
  }
}

// 表格表单样式
.excel-table-form {
  background: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;

  & > ::v-deep .ant-spin-nested-loading {
    height: 100%;

    & > .ant-spin-container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  ::v-deep .lyy-erp-action-bar {
    .lyy-label-text {
      margin-bottom: 0;

      .lyy-label {
        color: #555;
      }
    }
  }

  ::v-deep > .lyy-card {
    flex: 1 !important;
    overflow-y: auto;

    .ant-card {
      background: #f5f5f5;
      border: none;

      .ant-card-body {
        background: #fff;
        margin: 0 14px;
        padding: 24px 16px 0;
        height: auto;
        min-height: calc(100% - 10px);
      }

      .ant-form-item {
        margin-bottom: 16px;
      }

      .vxe-table {
        margin-bottom: 10px;
      }

      // tabs样式
      .excel-one-tabs {
        background: url(img/lyy-tabs-one-bg.png) no-repeat 0 10px;

        .ant-tabs-nav {
          .ant-tabs-nav-wrap {
            padding: 0 20px;
          }

          .ant-tabs-ink-bar {
            width: 0;
            background: none;
          }

          .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: #0a1b29;
            }
          }
        }

        .ant-card-body {
          padding: 0;
        }
      }

      .ant-tabs {
        .ant-tabs-nav {
          margin-bottom: 8px;
        }

        .ant-tabs-nav-wrap {
          padding: 0 8px;

          .ant-tabs-tab {
            padding: 7px 0;
          }
        }
      }
    }
  }

  // footer样式
  ::v-deep .lyy-footer {
    padding: 8px 16px;
    position: inherit;

    .ant-form-item {
      margin-bottom: 0;
      margin-right: 20px;

      .ant-form-item-label {
        width: auto !important;
      }

      .ant-form-item-control-input-content {
        font-weight: bold;
        font-size: 16px;
        font-family: D-DIN-Bold, D-DIN, Sans-serif;
      }
    }
  }
}
</style>
