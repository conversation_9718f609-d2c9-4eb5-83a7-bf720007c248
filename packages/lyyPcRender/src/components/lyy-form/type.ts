export interface LyyForm extends IElement {
  compName: 'lyy-form'
  prop: PropType
  modelValue?: ModelValueType
  childrens?: IComponent[]
}

export type ModelValueType = IObject

export interface PropType {
  /**
   * form 默认值，默认 {}
   */
  form?: IObject
  /**
   * isSignChange是否监听表单是否改变数据
   * **/
  isSignChange?: boolean
  /**
   * 特殊样式erp禁用样式修改
   * **/
  isSpecialStyle?: boolean
  /**
   * formState 值来源之一（从 payloads 取值）
   */
  payloads?: IPayload[]

  /**
   * 配置 Form.Item 的 colon 的默认值 (只有在属性 layout 为 horizontal 时有效)
   * 默认 true
   */
  colon?: boolean

  /**
   * 表单布局，默认 horizontal
   */
  layout?: 'horizontal' | 'vertical' | 'inline'

  /**
   * label 标签布局
   */
  labelCol?: Col

  /**
   * 需要为输入控件设置布局样式时，使用该属性
   */
  wrapperCol?: Col

  /**
   * label 标签的文本对齐方式
   */
  labelAlign?: 'left' | 'right'
  /**
   * css类名
   */
  class?: string
  /**
   * 是否列表页顶级form
   */
  mainLayout?: boolean
  /**
   * 表单值拍平处理
   */
  smashing?: boolean
  /**
   * 表单禁用
   */
  disabled?: boolean
}

interface Col {
  /**
   * flex 布局填充
   */
  flex?: string | number

  /**
   * 栅格左侧的间隔格数，间隔内不可以有栅格
   */
  offset?: number

  /**
   * 栅格顺序，flex 布局模式下有效
   */
  order?: number

  /**
   * 栅格向左移动格数
   */
  pull?: number

  /**
   * 栅格向右移动格数
   */
  push?: number

  /**
   * 栅格占位格数，为 0 时相当于 display: none
   */
  span?: number

  /**
   * ≥2000px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  xxxl?: number | IObject

  /**
   * <576px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  xs?: number | IObject

  /**
   * ≥576px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  sm?: number | IObject

  /**
   * ≥768px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  md?: number | IObject

  /**
   * ≥992px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  lg?: number | IObject

  /**
   * ≥1200px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  xl?: number | IObject

  /**
   * ≥1600px 响应式栅格，可为栅格数或一个包含其他属性的对象
   */
  xxl?: number | IObject
}
