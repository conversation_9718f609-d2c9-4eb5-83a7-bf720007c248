import LyyPcRender from '@/render/lyy-render.vue'
import PcRenderTemplate from '@/render/pc-render-template.vue'

import { vue3ToVue2, vue3ToAny } from './utils/transform'

export { WatcherContrl, qt } from '@leyaoyao/libs/utils'

export const createRender = (id, router, notKeepAlive) => {
  return vue3ToVue2(LyyPcRender, id, router, notKeepAlive)
}
export const createRenderTemplate = (id, router, notKeepAlive) => {
  return vue3ToVue2(PcRenderTemplate, id, router, notKeepAlive)
}
export const createRenderAnyTemplate = (
  id,
  router,
  pageConfig,
  notKeepAlive,
) => {
  return vue3ToAny(id, router, pageConfig, notKeepAlive)
}
export { default as httpConfig } from './http-config'
export { default as configManager } from './config/configManager'
export { default as routerConfig } from './vue2-set-router'
export { components } from '@/components'
export { default as PcRenderTemplate } from '@/render/pc-render-template.vue'
export { default as LyyPcRender } from '@/render/lyy-render.vue'
export { usePermission } from '@/plugins'
