import type { App } from 'vue'
import { useStore } from '@leyaoyao/libs/store'

const store = useStore()

import PcRenderTemplate from './pc-render-template.vue'
import PcEditRenderTemplate from './pc-editor-render-template.vue'
import LyyPcRender from './lyy-render.vue'
import { components } from '../components'
export { WatcherContrl, qt } from '@leyaoyao/libs'
import { default as version } from '../../config/version.mjs'
import { vue3ToAny } from '../utils/transform'
import { customComponents } from '../../../customComponents/index'
import VueI18n from '../locales/index'
export { default as configManager } from '../config/configManager'

// 自定义组件和系统组件合并
const componentsResult = Object.assign(customComponents, components)
Object.assign(window, {
  compIdList: {},
})
const Render = {
  install: (app: App) => {
    for (const [name, comp] of Object.entries(componentsResult)) {
      let aliasName = window.__aliasName__ || ''
      if (aliasName) {
        aliasName += version.verVue2
      }
      app.component(name + aliasName, comp)
    }
    app.use(VueI18n)
    console.log('++++++++++++++++组件国际化++++++++++++++++++')
    app.component('pc-render-template', PcRenderTemplate)
    app.component('lyy-pc-render', LyyPcRender)
    app.component('pc-edit-render-template', PcEditRenderTemplate)
  },
}
export function setAliasName(name: string) {
  window.__aliasName__ = name
}
export default Render
export { useAction, shortcutKeyHandle, useRequest } from '@leyaoyao/libs'
export { store }
export { usePermission } from '../plugins'
export { default as httpConfig } from '../http-config'
export const createRenderAnyTemplate = (
  id,
  router,
  pageConfig,
  notKeepAlive,
) => {
  return vue3ToAny(id, router, pageConfig, notKeepAlive)
}
export { default as version } from '../../config/version.mjs'
