<script setup lang="ts">
import { ref, useAttrs, inject, Ref } from 'vue'
import { useStore, getCurrentPageName } from '@leyaoyao/libs/store'
import Draggable from 'vuedraggable'
import {
  useAction,
  useUpdate,
  useValue,
  usePayload,
  useShow,
  eventManager,
} from '@leyaoyao/libs'
import { cloneDeep } from 'lodash'
import { LYY_FORM, LYY_EXCEL_TABLE } from '../constants/component-name'
import { EXCEL_TABLE, FORM_REF, ORIGIN } from '../constants/store-key'
import { MOUNTED, BEFORE_MOUNT, CLICK } from '../constants/event-type'
import { publishInfo } from '../pubsub'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import CompPopSelect from './comp-popover-select.vue'

const props = defineProps<{
  elements: IElement[]
  rowData?: any
  tag?: string
  wrap?: boolean
  gutter?: number
  isCanvasModal?: boolean
}>()
const store = useStore()
const attr = useAttrs()

const isEdit = inject<Ref<boolean>>('isEdit')

const handleAction = (ev: IObject, element: IElement) => {
  if (isEdit?.value && ev.event === CLICK) {
    return
  }
  // form 挂载前
  if (element.compName === LYY_FORM && [BEFORE_MOUNT].includes(ev.event)) {
    const { payloads } = element.prop || {}
    // element.modelValue = element.prop.form
    // 编辑状态下modelValue和prop.form进行失去关联，否则会把动态值写进默认值里面
    // element.modelValue = new Proxy(cloneDeep(element.prop.form), {})
    // element.modelValue = new Proxy(element.prop.form, {})
    payloads && Object.assign(element.prop.form, usePayload(payloads))
    store.setValue(element.compId, props.modelValue)
    store.setValue(ORIGIN, cloneDeep(element.prop.form), element.compId)
    element.entityId &&
      store.setValue(ORIGIN, cloneDeep(element.prop.form), element.entityId)
  }

  // form 挂载后
  if (element.compName === LYY_FORM && [MOUNTED].includes(ev.event)) {
    const { formRef } = ev.payload
    store.setValue(FORM_REF, formRef, element.compId)
    element.entityId && store.setValue(FORM_REF, formRef, element.entityId)
  }
  if (ev.data) {
    store.setValue('payload', ev.data, element.compId)
  }

  // 通知给外部
  publishInfo({ ev, element })

  const actions = element.actions?.filter(
    (item: any) => item.event === ev.event,
  )
  actions && useAction(actions, element)
}
const endDrag = () => {
  eventManager.emit('endDrag', {})
}

const handleUpdate = (newValue: any, element: IElement) => {
  useUpdate(newValue, element)
}
const getComponentData = () => {
  return {
    gutter: props.gutter,
    wrap: props.wrap,
  }
}
const compName = (element) => {
  if (element.compName === 'lyy-modal' && !props.isCanvasModal) {
    return undefined
  }
  return element.compName
}

/*
 * 工具栏拖拽释放完成
 * */
const onDragDone = () => {
  try {
    console.info('工具栏拖拽释放完成 更新proxyData ====>')
    proxyData.updateProxyData(getCurrentPageName())
  } catch (error) {
    console.error('工具栏拖拽释放完成 更新proxyData失败', error)
  }
}

const handleSelect = (comp) => {
  // eslint-disable-next-line vue/no-mutating-props
  props.elements.push(comp)
}

const componentList = ref([])
const customsComplist = ref([])
const getComList = (compList) => {
  componentList.value = compList
  window['lyyComponentList'] = [
    ...componentList.value,
    ...customsComplist.value,
  ]
}
const getCustomsComList = (compList) => {
  customsComplist.value = compList
}

eventManager.on('deliver-model', getComList)
eventManager.on('deliver-customsComp-model', getCustomsComList)
</script>

<template>
  <Draggable
    :list="elements"
    handle=".handle"
    group="people"
    item-key="compId"
    :tag="tag"
    :component-data="getComponentData()"
    @end="endDrag"
    class="draggable-wrap"
    @add="onDragDone"
  >
    <template #header>
      <div v-if="elements.length === 0" class="draggable-tips-wrap">
        <div class="pop-comp-area">
          <CompPopSelect
            class="pop-comp-area"
            @hamdle-select="handleSelect"
            v-bind="$attrs"
          ></CompPopSelect>
        </div>
        <span class="content-area">组件放置区域</span>
      </div>
    </template>
    <template #item="{ element }">
      <component
        v-memo="[element.prop, element.modelValue, element.prop?.update]"
        :is="compName(element)"
        :element="element"
        :entityId="element.entityId"
        :aliasName="element.prop.aliasName"
        :compNameCn="element.compNameCn"
        :comp-id="element.compId"
        :id="element.compId"
        :key="element.compId"
        :style="element.style"
        :prop="element.prop"
        :childrens="element.childrens"
        v-model:modelValue="element.modelValue"
        :rowData="rowData"
        @action="handleAction($event, element)"
        class="edit-status"
      >
        <pc-editor-render-template
          v-if="element.childrens?.length"
          :elements="element.childrens"
          v-bind="$attrs"
        ></pc-editor-render-template>
      </component>
    </template>
  </Draggable>
</template>

<style lang="less" scoped>
.draggable-wrap {
  position: relative;
  // min-height: 40px;
  // min-width: 200px;
  // width: 100%;
  // display: inherit;
  // justify-content: inherit;
  // gap: inherit;
}
.draggable-tips-wrap {
  // position: absolute;
  // top: 0;
  // left: 0;
  color: rgb(184, 186, 191);
  text-align: center;
  display: flex;
  // flex-direction: column;
  // min-width: 200px;
  min-height: 40px;
  align-items: center;
  justify-content: center;
  background-color: rgba(10, 19, 37, 0.05);
  border: 1px dashed #e8e9eb;
  // padding: 8px;
  box-sizing: border-box;
  // margin: 4px;
  // height: calc(100% - 8px);
  width: 100%;
  height: 100%;
  .draggable-wrap {
    margin: 0 auto;
  }
  .pop-comp-area {
    margin: 5px 0;
    margin-left: 10px;
  }
  .content-area {
    padding: 0 10px;
    font-weight: normal;
  }
}
</style>
<style lang="less">
.edit-status {
  .ant-table-content {
    // overflow: inherit !important;
  }
}
</style>
