import { IProxyDataHigher } from '@leyaoyao/libs/actions/set-new-value'

declare type RequestConfig = import('./service/types').RequestConfig
declare type IStyle = Partial<CSSStyleDeclaration>
declare type IObject = Record<string | number | symbol, any>

declare type EventType =
  | string
  | 'click'
  | 'dbclick'
  | 'touch'
  | 'scroll'
  | 'update'
  | 'beforeMount'
  | 'mounted'
  | 'beforeUnmount'
  | 'activated'
  | 'deactivated'
  | 'submit'
  | 'confirm'
  | 'cancel'
declare type ActionType =
  | 'openModal'
  | 'closeModal'
  | 'linkto'
  | 'request'
  | 'download'
  | 'setValue'
  | 'resetValue'
  | 'print'
  | 'upload'
  | 'openConfirm'
  | 'htmlToPdf'
  | 'setNewValue'
declare type IAction =
  | IOpenModal
  | ICloseModal
  | ILinkto
  | IValidate
  | IRequest
  | ISetValue
  | IResetValue
  | IBroadcast
  | IDownload
  | IReload
  | IUpload
  | IPrint
  | IOpenConfirm
  | IHtmlToPdf
declare type BelongType = 'success' | 'error' | 'finally'

declare type IComponent = import('./export').IComponent
declare type getNewValueOption = {
  type: 'static' | 'dynamic' | 'higher' // 简易设置 高级设置
  dynamic?: {
    nodePath: string // 资源路径
  }
  higher?: IProxyDataHigher[]
  static?: string
}
declare interface IElement {
  compId: string // 组件Id
  entityId?: string // 实体Id
  compName: string // 组件名
  style?: IStyle // 样式
  modelValue?: any // 组件值
  prop?: any // 自定义属性
  actions?: IAction[]
  childrens?: IElement[]
}

declare interface IPayload {
  key?: string
  value?: string
  source?: string
  sourceKey?: string
  decompose?: boolean
}

/**
 * action option（选项类型）
 */
declare interface ITarget {
  targetId: string
  fields?: string[]
  event?: EventType
}
declare interface ExcelTarget {
  targetId: string
  fields?: string[]
  dataSource?: IObject
  event?: EventType
}
declare interface IRequestConfig extends RequestConfig {
  url: string
  method?: 'get' | 'post' | 'delete' | 'put' | 'patch' | 'head'
  payloads?: getNewValueOption
  headerPayloads?: IPayload[]
  isSuccessStop?: boolean //是否成功响应后才停止请求
}

declare interface ILinktoOption {
  go?: number // 0表示当前页，-1后退一页，1前进1页，以此类推
  url?: string
  tab?: '_self' | '_blank'
  mode?: 'query' | 'params'
  name?: string
  isAddRandom?: boolean // 是否在连接上带上随机数
  payloads?: IProxyDataHigher[]
}
declare interface IRunJSObject {
  pageConfigureInfoId: string // 逻辑集id
  name: string // 逻辑集名称
}
export declare interface IQtOption {
  trackerEventCode: string
  eventType?: string
  appId?: string
  eventParams: getNewValueOption
}
declare interface IOpenConfirmOption {
  title?: string // 0表示当前页，-1后退一页，1前进1页，以此类推
  content?: string
  type?: 'info' | 'success' | 'error' | 'warning' | 'confirm'
  okText?: string
  cancelText?: string
  styleType: 'modal' | 'message' | 'notification'
  placement: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
  duration: number
}

declare interface IHtmlToPdfOption {
  id: string // html 标签的 id
  fileName?: string // 导出 pdf 文件名称，默认为：'下载.pdf'
}

declare interface ISetValueOption extends IResponseSetValue {
  payloads?: IPayload[]
}

declare type IRequestOption = IRequestConfig & IResponseSetValue
declare type IRequestDatasetOption = {
  id: string | number
  name: string
}
declare type IDownloadOption = IRequestConfig

interface ISetValueCommonOption {
  // 赋值给单个目标
  sourceKey?: string // 不配置，则数据取返回的整个 data
  targetId?: string // 不配置，则当前触发 actions 的组件为目标组件
  targetPath?: string | string[]

  // 设置值方式，替换/合并/（数组）插入，默认替换
  type: 'replace' | 'merge' | 'splice'
  index: number | string // string 类型时，只支持（'start' 和 'end'）
}

interface IResponseSetValue extends ISetValueCommonOption {
  targets?: ISetValueCommonOption[] // 赋值给多个目标
}
/** action option（选项类型） */

/**
 * action 类型
 */
declare interface IOpenModal {
  event?: EventType
  action: 'openModal'
  option: ITarget
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}
declare interface ICloseModal {
  event?: EventType
  action: 'closeModal'
  option: ITarget
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface ILinkto {
  event?: EventType
  action: 'linkto'
  option: ILinktoOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IValidate {
  event?: EventType
  action: 'validate'
  option: ITarget
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IRequest {
  event?: EventType
  action: 'request'
  option: IRequestOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IDownload {
  event?: EventType
  action: 'download'
  option: IDownloadOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface ISetValue {
  event?: EventType
  action: 'setValue'
  option: {
    payloads?: IPayload[]
    sourceKey?: string
    targetId?: string
    targetPath?: string
    targets?: any[]
  }
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IResetValue {
  event?: EventType
  action: 'resetValue'
  option: ITarget
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  belong?: BelongType
}

declare interface IBroadcast {
  event?: EventType
  action: 'broadcast'
  option: ITarget
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IReload {
  event?: EventType
  action: 'reload'
  option?: null
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IUpload {
  event?: EventType
  action: 'upload'
  option: IRequestOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  thenActions?: IAction[]
  belong?: BelongType
}

declare interface IPrint {
  event?: EventType
  action: 'print'
  option: IRequestConfig
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  belong?: BelongType
}

declare interface IOpenConfirm {
  event?: EventType
  action: 'openConfirm'
  option: IOpenConfirmOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  belong?: BelongType
}

declare interface IHtmlToPdf {
  event?: EventType
  action: 'htmlToPdf'
  option: IHtmlToPdfOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
  belong?: BelongType
}
declare interface IQtTrack {
  event?: EventType
  action: 'qtTrack'
  option: IQtOption
  condition: {
    exp?: string
    payloads?: IPayload[]
  }
}
/** action 类型 */

/**
 * 其他
 */
/**
 * 显隐
 */
declare interface IShow {
  payloads?: IPayload[]
  exp: string
}

/**
 * 禁用
 */
declare interface IForbid {
  payloads?: IPayload[]
  exp: string
}

declare interface FormatNumberOption {
  fixed?: number
  format?: 'percent' | 'thousands'
  preappend?: string
  append?: string
}

declare interface DatePipe {
  format?: string
}
declare interface PercentPipe {
  /**
   * 保留小数点位数，默认 2 位
   */
  fixed?: number
}

declare interface DecimalPipe {
  /**
   * 保留小数点位数，默认 2 位
   */
  fixed?: number
  /**
   * 是否千分位，默认 true
   */
  thousands?: boolean
}

declare interface CurrencyPipe {
  /**
   * 货币名称，如 CNY | USD | EUR | GBP | JPY | HKD | 等
   */
  currency?: 'CNY' | 'USD' | 'EUR' | 'GBP' | 'CHF' | 'JPY' | 'KRW' | 'HKD'
  /**
   * 前缀
   */
  prefix?: string
  /**
   * 后缀
   */
  suffix?: string
  /**
   * 保留小数点位数，默认 2 位
   */
  fixed?: number
  /**
   * 是否千分位，默认 true
   */
  thousands?: boolean
}
declare interface TextmapPipe {
  textMap: {
    origin?: number | string | boolean
    result?: string
    pipes?: Pipe[]
  }[]
}

declare interface JoinPipe {
  separator: string
}

declare interface Pipe {
  pipe?: 'date' | 'decimal' | 'percent' | 'currency' | 'textmap' | 'join'
  option?:
    | DatePipe
    | PercentPipe
    | DecimalPipe
    | CurrencyPipe
    | TextmapPipe
    | JoinPipe
}

declare interface Window {
  __aliasName__: string
}
