import { formula } from '@leyaoyao/libs'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
import _ from 'lodash'
import isEdit from '@leyaoyao/libs/utils/is-edit'
export default function disableHoc(Base: object) {
  return function (props, { slots }) {
    let disabled = false
    // eslint-disable-next-line prefer-const
    // let formDisabled = undefined
    const _data = proxyData.getProxyData('disableHoc')
    // if (props?.prop?.formId) {
    //   const form = _data[props.prop.formId]
    //   formDisabled = form ? form.prop?.disabled : undefined
    // }
    let newProp = props.prop || {}
    // 处理禁用属性转化为disabled
    if (props.prop?.forbid && props.prop?.forbid?.exp) {
      const { exp } = props.prop.forbid
      const payload = props.rowData ? { ...props.rowData, ..._data } : _data
      //表单禁用则禁用 否则走公式逻辑
      // disabled =
      //   formDisabled === true
      //     ? true
      //     : exp && payload
      //     ? formula(exp)(payload)
      //     : false
      disabled = exp && payload ? formula(exp)(payload) : false
      // 编辑器设置默认值的时候会是空字符串
      if (exp) {
        newProp = _.clone(props.prop || {})
        newProp.disabled = disabled
        props.prop = newProp
      }
    }
    // else {
    //   if (props && props.prop && props?.prop?.formId) {
    //     newProp = props?.prop || {}
    //     // 如果表单禁用 则元素禁用并且首次时存储自带禁用属性
    //     if (formDisabled === true) {
    //       newProp.disabled = true
    //     } else {
    //       if (formDisabled !== undefined) {
    //         // 如果存储了旧的禁用 则配置旧的禁用 否则不禁用
    //         newProp.disabled =
    //           newProp.oldDisabled === undefined ? disabled : newProp.oldDisabled
    //       }
    //     }
    //     props.prop = newProp
    //   }
    // }
    if (isEdit()) {
      return (
        <Base {...props}>
          {props.childrens?.length ? (
            <pc-render-template elements={props.childrens}></pc-render-template>
          ) : (
            <></>
          )}
        </Base>
      )
    }
    return <Base {...props}>{slots.default ? slots.default() : null}</Base>
  }
}
