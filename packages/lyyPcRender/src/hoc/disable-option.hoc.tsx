import { formula, usePayload } from '@leyaoyao/libs'
import proxyData from '@leyaoyao/libs/utils/proxy-data'
const disabledFn = (forbid: { exp: ''; payloads: [] }) => {
  const { exp } = forbid
  if (exp) {
    const payload = proxyData.getProxyData('disabledFn --> disable-option.hoc')
    return formula(exp)(payload)
  }
  return false
}
const showFn = (show: { exp: '' }) => {
  const { exp } = show
  if (exp) {
    const payload = proxyData.getProxyData('showFn --> disable-option.hoc')
    return formula(exp)(payload)
  }
  return true
}

export default function disableOptionHoc(Base: object) {
  return function (props) {
    const options = props.prop.options || []
    if (Array.isArray(options)) {
      for (const option of options) {
        if (!option || typeof option != 'object') continue
        option.disabled = option.disabled || disabledFn(option.forbid || {})
        option.show = showFn(option.show || {}) ?? true
      }
    }

    return (
      <Base {...props}>
        {props.childrens?.length ? (
          <pc-render-template elements={props.childrens}></pc-render-template>
        ) : (
          <></>
        )}
      </Base>
    )
  }
}
