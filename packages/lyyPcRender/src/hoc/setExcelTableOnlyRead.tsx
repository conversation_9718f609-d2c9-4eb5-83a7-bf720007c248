import { formula, usePayload } from '@leyaoyao/libs'
import _ from 'lodash'
import { store } from '../render'
import { useValue } from '@leyaoyao/libs'
export default function setExcelTableOnlyRead(Base: object) {
  return function (props) {
    let newProps = props
    // isReadOnly
    const isReadOnly = store.getValue('isReadOnly')
    if (isReadOnly) {
      newProps = _.cloneDeep(props)
      newProps.prop.disabled = isReadOnly
      newProps.prop.isReadOnly = isReadOnly
    }
    return (
      <Base {...newProps}>
        {props.childrens?.length ? (
          <pc-render-template elements={props.childrens}></pc-render-template>
        ) : (
          <></>
        )}
      </Base>
    )
  }
}
