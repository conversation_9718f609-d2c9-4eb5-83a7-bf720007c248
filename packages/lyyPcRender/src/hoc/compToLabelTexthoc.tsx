import { getCompId, useValue } from '@leyaoyao/libs'
import _ from 'lodash'
import { store } from '../render'

// 需要设置的组件
const isTargets = (target = [], compId: string) => {
  if (target.length === 0) return true // 没设置默认是全部
  return findInTarget(target, compId)
}
const findInTarget = (target: [], compId: string) => {
  let result = false
  for (const element of target) {
    if (getCompId(element) === compId) {
      result = true
      break
    }
  }
  return result
}
// 不包含在内的
const isExclude = (target = [], compId: string) => {
  return findInTarget(target, compId)
}
export default function compToLabelTexthoc(Base: object, Other: object) {
  return function (props) {
    let newProps = props
    // isReadOnly
    const readOnlyConfig = store.getValue('readOnlyConfig')
    const targets = readOnlyConfig?.targets || []
    const exclude = readOnlyConfig?.exclude || []
    const isReadOnly = readOnlyConfig?.isReadOnly
    if (
      isReadOnly &&
      isTargets(targets, props.id) &&
      !isExclude(exclude, props.id)
    ) {
      // 由于copy了一份会导致设置prop属性的时候不会同步进去，先注释
      // newProps = _.cloneDeep(props)
      newProps.prop.disabled = isReadOnly
      newProps.prop.isReadOnly = isReadOnly
      if (newProps.prop.labelField) {
        newProps.prop.field = newProps.prop.labelField
        newProps.modelValue = useValue(newProps.element)
      }
    } else if (isReadOnly === false) {
      newProps.prop.disabled = isReadOnly
      newProps.prop.isReadOnly = isReadOnly
    }
    return newProps.prop.isReadOnly ? (
      <Other {...newProps}>
        {props.childrens?.length ? (
          <pc-render-template elements={props.childrens}></pc-render-template>
        ) : (
          <></>
        )}
      </Other>
    ) : (
      <Base {...newProps}>
        {props.childrens?.length ? (
          <pc-render-template elements={props.childrens}></pc-render-template>
        ) : (
          <></>
        )}
      </Base>
    )
  }
}
