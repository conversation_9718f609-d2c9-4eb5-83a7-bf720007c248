import { PermissionParams } from './type'
import { createPermissionModule } from './store'
import { ActionsTypes } from './store/type'
import type { App } from 'vue'

function usePermission({
  store,
  router,
  fetch,
  renderComp,
  whiteList,
}: PermissionParams) {
  const constRoutesLength = router.getRoutes().length
  // 挂载一下子系统应用的路由
  window.$applicationRouter = router
  if (!store.hasModule('permission')) {
    const permissionStore = createPermissionModule({
      router,
      fetch,
      renderComp,
    })
    store.registerModule('permission', permissionStore)
  }
  const whitePathList = whiteList ?? ['/login']
  // 刷新页面时动态路由会失效，会直接匹配到404，故需要在入口先移除404，动态添加路由时再重新添加上
  router.removeRoute('notFound')
  router.beforeEach(async (to, from, next) => {
    if (
      !whitePathList.includes(to.path) &&
      router.getRoutes().length <= constRoutesLength
    ) {
      await store.dispatch(ActionsTypes.setMenus)
      store.dispatch(ActionsTypes.setRoutes, store.state.permission.menus)
      next({ ...to, replace: true })
    } else {
      next()
    }
  })
  const install = (app: App) => {
    app.config.globalProperties.$addRoutes = () => {
      return new Promise((resolve) => {
        store.dispatch(ActionsTypes.setMenus).then(() => {
          store.dispatch(ActionsTypes.setRoutes, store.state.permission.menus)
          resolve(true)
        })
      })
    }
    app.config.globalProperties.$resetMenu = () => {
      store.dispatch(ActionsTypes.resetMenuRoutes)
    }
  }
  return {
    install,
  }
}

export default usePermission
