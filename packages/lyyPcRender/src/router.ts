import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

import Home from './home.vue'
import Detail from './detail.vue'
import List from './list.vue'
import Table from './table-demo.vue'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: 'home',
  },
  {
    path: '/home',
    name: 'home',
    component: Home,
  },
  {
    path: '/detail',
    name: 'detail',
    component: Detail,
  },
  {
    path: '/table',
    name: 'table',
    component: Table,
  },
  {
    path: '/list',
    name: 'list',
    component: List,
  },
]

const router = createRouter({
  routes,
  history: createWebHistory(),
})

export default router
