#!/usr/bin/env node
/* eslint-disable import/no-unresolved */

import enquirer from 'enquirer'
import process from 'node:process'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import fs from 'node:fs'
import fsp from 'node:fs/promises'
import { execa } from 'execa'

const LOGIN_MODES = [
  { name: '单点登录', value: 'sso' },
  { name: '常规登录', value: 'normal' },
]

const CONTEXT = process.cwd()

const {
  pkgName,
  sysName,
  sysFlag,
  sysShortName,
  loginMode,
  sysId,
  selectMerchant,
} = await enquirer
  .prompt([
    {
      type: 'input',
      name: 'pkgName',
      message: '请输入项目名称',
      initial: 'leyaoyao-pc',
      result(val) {
        return val.trim()
      },
      validate(val) {
        const regExp = /^(?:@[\d*a-z~-][\d*._a-z~-]*\/)?[\da-z~-][\d._a-z~-]*$/
        if (!regExp.test(val)) {
          return '项目名称不合法'
        }
        const filePath = path.join(CONTEXT, val)
        if (fs.existsSync(filePath)) {
          return '文件夹已存在'
        }
        return true
      },
    },
    {
      type: 'input',
      name: 'sysName',
      message: '请输入系统名称',
      initial: 'admin管理',
      result(val) {
        return val.trim()
      },
    },
    {
      type: 'input',
      name: 'sysId',
      message: '请输入系统id,用来获取系统菜单',
      initial: '',
      result(val) {
        return val.trim()
      },
      validate(val) {
        if (!val) {
          return '请输入系统ID'
        }
        return true
      },
    },
    {
      type: 'input',
      name: 'sysFlag',
      message: '请输入系统标识',
      initial: 'authSys',
      result(val) {
        return val.trim()
      },
    },
    {
      type: 'input',
      name: 'sysShortName',
      message: '请输入系统简称',
      initial: 'admin',
      result(val) {
        return val.trim()
      },
      validate(val) {
        if (!val) {
          return '请输入系统简称'
        }
        // 将中文字符替换成两个英文字符，计算字节数
        if (val.replace(/[^\u0000-\u00ff]/g, 'aa').length > 6) {
          return '长度需控制在6个及以内字节数（一个中文字符占两个字节）'
        }
        return true
      },
    },
    {
      type: 'select',
      name: 'loginMode',
      message: '请选择登录模式',
      choices: LOGIN_MODES,
      initial: 'normal',
      result(names) {
        const MAPS = this.map(names)
        return Object.values(MAPS)[0]
      },
    },
    {
      type: 'confirm',
      name: 'selectMerchant',
      message: '是否开启选择商户功能',
      initial: false,
      result(val) {
        return val
      },
    },
  ])
  .catch(() => process.exit(1))

const SOURCE_DIR = fileURLToPath(path.join(import.meta.url, '../../template'))

const OUT_DIR = path.join(CONTEXT, pkgName)

await fsp.mkdir(OUT_DIR, { recursive: true })

/**
 * 将源文件夹文件递归复制到目标文件夹
 */
async function copyFile(sourcePath, targetPath) {
  /**
   * 读取源文件夹目录
   */
  const sourceFile = await fsp.readdir(sourcePath, { withFileTypes: true })

  for (const file of sourceFile) {
    const CUR_SOURCE_PATH = path.resolve(sourcePath, file.name)
    let curTargetPath = ''
    if (['normal', 'sso'].includes(file.name)) {
      curTargetPath = targetPath
    } else if (['_gitignore', '_npmrc'].includes(file.name)) {
      curTargetPath = path.resolve(targetPath, file.name.replace('_', '.'))
    } else {
      curTargetPath = path.resolve(targetPath, file.name)
    }
    /**
     * 处理登录模式，只复制对应登录模式文件夹下的文件
     */
    if (
      ['login', 'apis', 'user'].includes(sourcePath.split(path.sep).pop()) &&
      file.isDirectory() &&
      file.name !== loginMode
    ) {
      continue
    }
    if (file.isFile()) {
      await fsp.copyFile(CUR_SOURCE_PATH, curTargetPath)
      const relativeCurTargetPath = curTargetPath
        .split(path.sep)
        .slice(-2)
        .join('/')
      /**
       * 处理是否选择商户的条件注释
       * */
      if (
        ['header-right.vue', 'routes.ts', 'user.ts', 'user.vue'].includes(
          file.name,
        ) ||
        ['user/index.ts', 'user/type.ts', 'login/index.vue'].includes(
          relativeCurTargetPath,
        )
      ) {
        let jsonStr = await fsp.readFile(CUR_SOURCE_PATH, {
          encoding: 'utf8',
        })
        const reg =
          /(\/\/#if selectMerchant)[\S\s]*?(\/\/#endif selectMerchant)/g
        const antiReg =
          /(\/\/#if !selectMerchant)[\S\s]*?(\/\/#endif !selectMerchant)/g
        jsonStr = selectMerchant
          ? jsonStr
              .replace(
                /(\/\/#if selectMerchant|\/\/#endif selectMerchant)/g,
                '',
              )
              .replace(antiReg, '')
              .trim()
              .replace(/\r?\n\s*\r?\n/g, '\r\n')
          : jsonStr
              .replace(reg, '')
              .replace(
                /(\/\/#if !selectMerchant|\/\/#endif !selectMerchant)/g,
                '',
              )
              .trim()
              .replace(/\r?\n\s*\r?\n/g, '\r\n')
        await fsp.writeFile(curTargetPath, jsonStr, { encoding: 'utf8' })
      }
      /**
       * 处理环境变量
       */
      if (file.name === '.env') {
        let jsonStr = await fsp.readFile(CUR_SOURCE_PATH, {
          encoding: 'utf8',
        })
        jsonStr = jsonStr
          .replace('admin系统管理', sysName)
          .replace('ram-admin', sysFlag)
          .replace('admin', sysShortName)
          .replace('123', sysId)
        await fsp.writeFile(curTargetPath, jsonStr, { encoding: 'utf8' })
      }
      /**
       * 根据系统标识修改环境变量REMOTE_ENTRY的值
       */
      if (
        ['.env.dev', '.env.sit', '.env.release', '.env.master'].includes(
          file.name,
        )
      ) {
        let jsonStr = await fsp.readFile(CUR_SOURCE_PATH, {
          encoding: 'utf8',
        })
        jsonStr = jsonStr.replace('SYS_FLAG', sysFlag)
        await fsp.writeFile(curTargetPath, jsonStr, { encoding: 'utf8' })
      }
      /**
       * 处理package.json中项目名称
       */
      if (file.name === 'package.json') {
        let jsonStr = await fsp.readFile(CUR_SOURCE_PATH, {
          encoding: 'utf8',
        })
        jsonStr = jsonStr.replace('admin', pkgName)
        await fsp.writeFile(curTargetPath, jsonStr, { encoding: 'utf8' })
      }
    } else {
      if (
        selectMerchant === false &&
        curTargetPath.split(path.sep).pop() === 'selectMerchant'
      ) {
        continue
      }

      if (!fs.existsSync(curTargetPath)) {
        await fsp.mkdir(curTargetPath)
      }

      copyFile(CUR_SOURCE_PATH, curTargetPath)
    }
  }
}

copyFile(SOURCE_DIR, OUT_DIR)

const gitExecOpts = {
  cwd: OUT_DIR,
  stdio: ['ignore', 'ignore', 'ignore'],
}

await execa('git', ['init'], gitExecOpts)
await execa('git', ['add', '.'], gitExecOpts)
await execa('git', ['commit', '-m', 'initialize project'], gitExecOpts)

console.log('应用创建成功...')

console.log('开始安装依赖...')

/** 执行安装依赖 */
await execa('npm', ['install', '--loglevel=error'], {
  cwd: OUT_DIR,
  stdio: 'inherit',
})

console.log('开始启动项目...')

/** 启动项目 */
await execa('npm', ['run', 'dev'], {
  cwd: OUT_DIR,
  stdio: 'inherit',
})
