import { createApp } from 'vue/dist/vue.esm-bundler'
import App from './lyy-app.vue'
import router from './router'
import { store } from './store'
import Antd from 'ant-design-vue'
// import 'ant-design-vue/dist/antd.css'
import SvgIcon from '@/components/svgIcon/index'
import { loadRemoteComponent, loadScript, menufetch, getVersion } from './utils'
import renderComp from '@/pages/render/index.vue'
import { getHttpConfig } from './request/apis/app'

loadScript({
  url: import.meta.env.REMOTE_ENTRY,
}).then(() => {
  // const url = `${
  //   import.meta.env.REMOTE_URL
  // }/${getVersion()}/web/lyyRemoteEntry.js`

  loadRemoteComponent({
    url: 'http://localhost:9001',
    scope: 'leyaoyaoPcRender',
    module: './pcrender',
  }).then(async (Module) => {
    const app = createApp(App)
    const { usePermission, qt } = Module ?? {}
    qt?.initBuryPoint({
      appId: `${import.meta.env.SYS_FLAG}-pc`, //应用id
      url: import.meta.env.BURY_URL, //埋点上报链接 根据环境不同 切换不同链接
    })
    try {
      const httpConfig = await getHttpConfig(import.meta.env.SYS_ID)
      window.__httpConfig__.setHttpConfig(httpConfig)
    } catch {
      console.log('请求出错')
    }

    //#if selectMerchant
    const whiteList = ['/login', '/select-merchant']
    //#endif selectMerchant

    const permission = usePermission({
      store,
      router,
      fetch: menufetch,
      renderComp,
      //#if selectMerchant
      whiteList,
      //#endif selectMerchant
    })

    app.use(Module?.default)
    app.use(SvgIcon)
    app.use(store)
    app.use(router)
    app.use(Antd)
    app.use(permission)
    app.mount('#app')
  })
})
