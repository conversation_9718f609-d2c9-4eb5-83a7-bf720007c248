<template>
  <icon-font class="icon" :type="name"></icon-font>
</template>

<script lang="ts">
import { createFromIconfontCN } from '@ant-design/icons-vue'
const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3964711_vb3x029rgrk.js',
})
export default {
  name: 'svg-icon',
  props: {
    name: {
      type: String,
      default: '',
    },
  },
  components: {
    IconFont,
  },
}
</script>

<style lang="scss">
.icon {
  font-size: 20px !important;
  vertical-align: -0.3em;
}
</style>
