<template>
  <router-view v-slot="{ Component }">
    <transition name="page" mode="out-in">
      <keep-alive :include="cachedViews">
        <component :is="Component"></component>
      </keep-alive>
    </transition>
  </router-view>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, computed, ref } from 'vue'

export default defineComponent({
  /**
   * name 中不能包含'Transition'字符串
   * vue 会检查router-view的parent的name是否包含'Transition',包含则会认为使用了旧模式，触发warn提示
   * */
  name: 'page-trans',
  setup() {
    const $store = useStore()
    const cachedViews = computed(() => {
      return $store.state.tagsView.cachedViews
    })
    return {
      cachedViews,
    }
  },
})
</script>
<style lang="scss">
.page-enter-active,
.page-leave-active {
  opacity: 1;
  transform: translateX(0px);
}

.page-enter-active {
  transition: all 0.15s ease-out;
}

.page-leave-active {
  transition: all 0.15s ease-in;
}

.page-enter {
  opacity: 0;
  transform: translateX(-20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>
