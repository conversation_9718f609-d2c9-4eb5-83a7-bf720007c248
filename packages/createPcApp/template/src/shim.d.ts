/* eslint-disable no-var */

/// <reference types="@leyaoyao/vue-cli/client/assets" />
/// <reference types="@leyaoyao/vue-cli/client/env" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'

  const components: DefineComponent
  export default components
}

// declare module 'leyaoyaoRenderTemplate/lyyrender' {
//   export const GlobalConfig: any
//   export const LyyComp: any
//   export const UsePermission: any
// }

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface ImportMetaEnv {
  [name: string]: string
}

declare var Watcher: any
declare var authSystem: any

interface Window {
  [key: string]: any
}

declare module 'vue/dist/vue.esm-bundler'
