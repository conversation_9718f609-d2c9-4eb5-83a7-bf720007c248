import { createStore } from 'vuex'
import createPersistedState from 'vuex-persistedstate'
import { store as tagsView } from './modules/tagsView'
import { store as user } from './modules/user'
import type { State as TagsViewState } from './modules/tagsView'
import type { State as UserState } from './modules/user/type'

export interface RootState {
  tagsView: TagsViewState
  user: UserState
}
export const store = createStore<RootState>({
  modules: {
    tagsView,
    user,
  },
  plugins: [
    createPersistedState({
      storage: window.sessionStorage,
      key: 'lyy-vuex',
    }),
  ],
})

export function useStore() {
  return store
}
