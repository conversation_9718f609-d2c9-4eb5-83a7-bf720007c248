export type UserInfo = {
  // 账号id
  authAccountId: string
  // 姓名
  name: string
  // 密码
  password: string
  // 联系电话
  telephone: string
  // 邮件
  email: string | null
  // 微信unionid
  wxUnionid: string | null
  // 企业微信id
  wxWorkId: string | null
  // 是否激活
  isActive?: 'Y' | 'N'
  // 创建时间
  created: string
  // 更新时间
  updated: string
  // 由谁创建
  createdby: string
  // 由谁更新
  updatedby: string
}
//#if selectMerchant
export type TenantInfo = {
  [key: string]: string | number
}
//#endif selectMerchant
export interface State {
  userInfo: UserInfo
  //#if selectMerchant
  tenantInfo: TenantInfo
  //#endif selectMerchant
}

export enum MutationTypes {
  SET_USER_INFO = 'SET_USER_INFO',
  //#if selectMerchant
  SET_TENANT_INFO = 'SET_TENANT_INFO',
  //#endif selectMerchant
}
export enum ActionsTypes {
  setUserInfo = 'setUserInfo',
  //#if selectMerchant
  setTenantInfo = 'setTenantInfo',
  //#endif selectMerchant
}
