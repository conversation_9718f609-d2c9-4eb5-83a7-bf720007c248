import type { MutationTree, ActionTree } from 'vuex'
import type { State } from './type'
import { MutationTypes, ActionsTypes } from './type'
import { getUserInfo } from '@/request/apis/user'
//#if selectMerchant
import { getTenantInfo } from '@/request/apis/user'
//#endif selectMerchant
const state: State = {
  userInfo: {
    authAccountId: '',
    name: '',
    password: '',
    telephone: '',
    email: null,
    wxUnionid: null,
    wxWorkId: '',
    isActive: 'Y',
    created: '',
    updated: '',
    createdby: '',
    updatedby: '',
  },
  //#if selectMerchant
  tenantInfo: {},
  //#endif selectMerchant
}

const mutations: MutationTree<State> = {
  [MutationTypes.SET_USER_INFO](curState: State, data) {
    curState.userInfo = data
  },
  //#if selectMerchant
  [MutationTypes.SET_TENANT_INFO](curState: State, data) {
    curState.tenantInfo = data
  },
  //#endif selectMerchant
}

const actions: ActionTree<State, any> = {
  async [ActionsTypes.setUserInfo]({ commit }) {
    const res = await getUserInfo()
    if (res && res.code === '0000000') {
      commit(MutationTypes.SET_USER_INFO, res.body)
    }
  },
  //#if selectMerchant
  async [ActionsTypes.setTenantInfo]({ commit }) {
    const res = await getTenantInfo()
    if (res && res.code === '0000000') {
      commit(MutationTypes.SET_TENANT_INFO, res.body)
    }
  },
  //#endif selectMerchant
}

export const store = {
  state,
  mutations,
  actions,
}
