export type UserInfo = {
  // 姓名
  name?: string
  // 密码
  telephone?: string
  // 邮件
  email?: string | null
  [key: string]: any
}
//#if selectMerchant
export type TenantInfo = {
  [key: string]: string | number
}
//#endif selectMerchant
export interface State {
  userInfo: UserInfo
  //#if selectMerchant
  tenantInfo: TenantInfo
  //#endif selectMerchant
}

export enum MutationTypes {
  SET_USER_INFO = 'SET_USER_INFO',
  //#if selectMerchant
  SET_TENANT_INFO = 'SET_TENANT_INFO',
  //#endif selectMerchant
}
export enum ActionsTypes {
  setUserInfo = 'setUserInfo',
  //#if selectMerchant
  setTenantInfo = 'setTenantInfo',
  //#endif selectMerchant
}
