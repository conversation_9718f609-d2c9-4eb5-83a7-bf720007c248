import type { ActionTree, MutationTree } from 'vuex'
import { MutationTypes, ActionsTypes } from './types'
import type { State, TagView } from './types'
import type { RootState } from '@/store'

export * from './types'

const state: State = {
  visitedViews: [],
  cachedViews: [],
}

const mutations: MutationTree<State> = {
  [MutationTypes.ADD_CACHED_VIEW](curState: State, view: TagView) {
    if (view.name === null) return
    if (curState.cachedViews.includes(view.name ? view.name : '')) return
    if (view.name && view.meta?.cache) {
      curState.cachedViews.push(view.name)
    }
  },
  [MutationTypes.ADD_VISITED_VIEW](curState: State, view: TagView) {
    const index = curState.visitedViews.findIndex(
      (item) => item.path === view.path,
    )
    if (index > -1) {
      curState.visitedViews[index] = { ...view, title: view.meta?.title || '' }
    } else {
      curState.visitedViews.push({ ...view, title: view.meta?.title || '' })
    }
  },
  [MutationTypes.DELETE_CACHED_VIEW](curState: State, view: TagView) {
    if (view.name === null) return
    const index = curState.cachedViews.indexOf(view.name ? view.name : '')
    if (index > -1) {
      curState.cachedViews.splice(index, 1)
    }
  },
  [MutationTypes.DELETE_VISITED_VIEW](curState: State, view: TagView) {
    const index = curState.visitedViews.findIndex(
      (item) => item.path === view.path,
    )
    if (index > -1) {
      curState.visitedViews.splice(index, 1)
    }
  },
  [MutationTypes.DELETE_ALL_VISITED_VIWES](curState: State) {
    const scopeState = curState
    const affixTag = scopeState.visitedViews.filter((item) => {
      return item.meta?.affix
    })
    scopeState.visitedViews = affixTag
  },
  [MutationTypes.DELETE_ALL_CACHED_VIEWS](curState: State) {
    const scopeState = curState
    scopeState.cachedViews = []
  },
  [MutationTypes.DELETE_OTHER_CACHED_VIEWS](curState: State, view: TagView) {
    const scopeState = curState
    if (view.name) {
      const index = scopeState.cachedViews.indexOf(view.name)
      scopeState.cachedViews =
        index > -1 ? scopeState.cachedViews.slice(index, index + 1) : []
    }
  },
  [MutationTypes.DELETE_OTHER_VISITED_VIEWS](curState: State, view: TagView) {
    const scopeState = curState
    scopeState.visitedViews = scopeState.visitedViews.filter((item) => {
      return item.meta?.affix || item.path === view.path
    })
  },
}

const actions: ActionTree<State, RootState> = {
  [ActionsTypes.addView]({ commit }, view: TagView) {
    commit(MutationTypes.ADD_CACHED_VIEW, view)
    commit(MutationTypes.ADD_VISITED_VIEW, view)
  },
  [ActionsTypes.deleteView]({ commit }, view: TagView) {
    commit(MutationTypes.DELETE_CACHED_VIEW, view)
    commit(MutationTypes.DELETE_VISITED_VIEW, view)
  },
  [ActionsTypes.deleteOtherViews]({ commit }, view: TagView) {
    commit(MutationTypes.DELETE_OTHER_CACHED_VIEWS, view)
    commit(MutationTypes.DELETE_OTHER_VISITED_VIEWS, view)
  },
  [ActionsTypes.deleteAllViews]({ commit }) {
    commit(MutationTypes.DELETE_ALL_CACHED_VIEWS)
    commit(MutationTypes.DELETE_ALL_VISITED_VIWES)
  },
}
export const store = {
  state,
  mutations,
  actions,
}
