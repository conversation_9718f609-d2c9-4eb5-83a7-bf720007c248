import type { RouteLocationNormalized, RouteRecordName } from 'vue-router'

export interface TagView extends Partial<RouteLocationNormalized> {
  title?: string
}

export interface State {
  visitedViews: TagView[]
  cachedViews: RouteRecordName[]
}

export enum MutationTypes {
  ADD_VISITED_VIEW = 'ADD_VISITED_VIEW',
  ADD_CACHED_VIEW = 'ADD_CACHED_VIEW',
  DELETE_VISITED_VIEW = 'DELETE_VISITED_VIEW',
  DELETE_CACHED_VIEW = 'DELETE_CACHED_VIEW',
  DELETE_ALL_VISITED_VIWES = 'DELETE_ALL_VISITED_VIWES',
  DELETE_OTHER_VISITED_VIEWS = 'DELETE_OTHER_VISITED_VIEWS',
  DELETE_ALL_CACHED_VIEWS = 'DELETE_ALL_CACHED_VIEWS',
  DELETE_OTHER_CACHED_VIEWS = 'DELETE_OTHER_CACHED_VIEWS',
}

export enum ActionsTypes {
  addView = 'addView',
  deleteView = 'deleteView',
  deleteAllViews = 'deleteAllView',
  deleteOtherViews = 'deleteOtherViews',
}
