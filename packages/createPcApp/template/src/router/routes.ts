import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/pages/layout/index.vue'

declare module 'vue-router' {
  interface RouteMeta {
    active?: string
    hide?: boolean
    title?: string
    affix?: boolean
    cache?: boolean
    id?: string
    // 权限码
    code?: string
  }
}

export const constRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'admin',
    component: Layout,
    children: [
      {
        path: '/',
        name: 'home',
        component: () => import('@/pages/home/<USER>'),
        meta: {
          title: '首页',
          affix: true,
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/pages/login/index.vue'),
  },
  //#if selectMerchant
  {
    path: '/select-merchant',
    name: 'selectMerchant',
    component: () => import('@/pages/selectMerchant/index.vue'),
  },
  //#endif selectMerchant
  {
    path: '/404',
    name: '404',
    component: () => import('@/pages/notFound/index.vue'),
  },
]
