export const getVersion = () => {
  return window[import.meta.env.SYS_FLAG]
}
export const menufetch = {
  baseURL: '',
  url: `/gw/ram-service/permission/menu/tree?authSystemId=${
    import.meta.env.SYS_ID
  }`,
  method: 'get',
  headerPayloads: [
    {
      key: 'ram-token',
      value: 'sessionStorage.token',
    },
    {
      key: 'ram-system',
      value: import.meta.env.SYS_ID,
    },
  ],
  customOption: {
    codeKey: 'code',
    codeValue: '0000000',
    jsonPath: 'body',
  },
}
