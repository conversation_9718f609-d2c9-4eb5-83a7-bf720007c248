<template>
  <a-layout-content class="main">
    <lyy-login :prop="prop" @loginSuccess="handleSuccess"></lyy-login>
  </a-layout-content>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ActionsTypes } from '@/store/modules/user/type'
import { useStore } from 'vuex'
const prop = ref({
  // 各接口入参字段映射
  fieldNames: {
    username: 'username',
    password: 'password',
    // 图片验证码的字段
    verifyCode: 'verifyCode',
    // 短信验证码的字段
    smsVerifyCode: 'smsVerifyCode',
    // 注册账号、修改密码、忘记密码的新密码字段
    newPassword: 'password',
    // 注册账号、修改密码的确认密码字段
    confirmPassword: 'confirmPassword',
  },
  loginTitle: '登录',
  // showVerifyCode: true,
  // passwordPattern: /123/,
  usernamePattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
  // showForgetPwd: true,
  // showRegister: true,

  // 注册接口配置
  registerFetch: {
    url: '',
    method: 'post',
  },

  // 登录接口配置
  loginFetch: {
    url: '/account/login',
    method: 'post',
  },

  // 图片验证码接口配置
  // imgVerifyCodeFetch: {
  // },

  // 忘记密码第一步校验验证码接口配置
  checkSmsVerifyCodeFetch: {
    url: '',
    method: 'get',
  },

  // 忘记密码接口配置
  forgetFetch: {
    url: '',
    method: 'patch',
  },

  // 短信验证码接口配置
  smsVerifyCodeFetch: {
    register: {
      url: '',
      method: 'get',
      customOption: {
        jsonPath: '$',
      },
    },
    forgetPassword: {
      url: '',
      method: 'get',
    },
  },
})

const $router = useRouter()
const $store = useStore()
// 登录接口调用成功之后的动作

interface ResData {
  token: string
  [key: string]: any
}

const handleSuccess = async (data: ResData) => {
  const { token } = data
  sessionStorage.setItem('token', token)
  await $store.dispatch(ActionsTypes.setUserInfo)
  //#if selectMerchant
  $router.push({
    path: '/select-merchant',
  })
  //#endif selectMerchant
  //#if !selectMerchant
  $router.push('/')
  //#endif !selectMerchant
}
</script>
<script lang="ts">
export default {
  name: 'login-page',
}
</script>
