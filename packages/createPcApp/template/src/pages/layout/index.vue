<template>
  <a-layout class="main">
    <a-layout-sider
      v-model:collapsed="collapsed"
      :collapsedWidth="60"
      class="sider"
    >
      <AsideMenu :collapsed="collapsed" @collapse="handleToggle"></AsideMenu>
    </a-layout-sider>
    <a-layout>
      <LyyHeader
        @collapse="handleToggle"
        @info="handleInfo"
        @logout="handleLogout"
      />
      <a-layout-content class="mainConten">
        <PageTransition />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { defineComponent, ref, watch, nextTick } from 'vue'
import LyyHeader from './components/header/index.vue'
import AsideMenu from './components/asideMenu/index.vue'
import { useRouter } from 'vue-router'
import { logout } from '../../request/apis/system'
import PageTransition from '@/components/pageTransition/index.vue'

defineComponent({
  name: 'layout-container',
})

const collapsed = ref(false)
const handleToggle = (collapse: boolean) => {
  collapsed.value = collapse
}
const router = useRouter()
const handleInfo = () => {
  console.log('个人档案')
}
const handleLogout = async () => {
  const res = await logout()
  if (res.code === '0000000') {
    sessionStorage.clear()
    router.push('/login')
  }
}
</script>

<style>
.ant-layout {
  overflow: auto;
}
</style>

<style lang="scss" scoped>
.main {
  height: 100vh;
  overflow: hidden;
}
.header {
  height: auto;
  padding: 0;
  line-height: 1;
}
.sider {
  height: 100vh;
  z-index: 1;
}
.content {
  overflow: auto;
  margin: 16px;
  background-color: #fff;
}
.mainConten {
  position: relative;
  height: 100%;
  :deep(.ant-layout-content) {
    position: relative;
    height: 100%;
  }
}
</style>
