<template>
  <div :class="$style.subWrap">
    <TagsView></TagsView>
    <HeaderRight v-bind="$attrs" />
  </div>
</template>

<script setup lang="ts">
import { defineComponent } from 'vue'
import TagsView from '../tabs-view.vue'
import HeaderRight from './header-right.vue'

defineComponent({
  name: 'header-container',
})
</script>

<style lang="scss" module>
.wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: 56px;
  padding: 0 16px;
  font-size: 13px;
  color: #333333;
  background: #fff;

  .breadCrumb {
    flex: 2;
  }
}

.subWrap {
  display: flex;
  align-items: center;
  width: 100%;
  height: 48px;
  flex-shrink: 0;
  background: #ffffff;
  // border-bottom: 1px solid #f0f0f0;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);

  .collapsedWrap {
    display: inline-flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 100%;
    margin-right: 7px;
    cursor: pointer;
    border-right: 1px solid #f0f0f0;
  }
  .tagsViewWrap {
    flex: 1;
    height: 100%;
    padding-right: 48px;
    // overflow: hidden;
  }
}
</style>
