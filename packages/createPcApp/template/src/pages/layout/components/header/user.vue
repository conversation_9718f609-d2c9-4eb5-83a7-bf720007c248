<template>
  <div class="reference user">
    <a-avatar :src="userAvatar" :size="24"></a-avatar>
    <div class="name">{{ userName }}</div>
  </div>
</template>
<script setup lang="ts">
import { computed, defineComponent, ref } from 'vue'

import defaultAvatar from '@/assets/leyaoyao.png'
import { useStore } from 'vuex'

const $store = useStore()
//#if selectMerchant
const props = defineProps<{
  from?: 1 | 2 // 1: 选择商户头部 2：内页头部
}>()
//#endif selectMerchant

defineComponent({
  name: 'header-user',
})

const userName = computed(() => {
  //#if !selectMerchant
  return $store.state.user.userInfo.name
  //#endif !selectMerchant
  //#if selectMerchant
  return props.from === 1
    ? $store.state.user.userInfo.name
    : $store.state.user.tenantInfo.name
  //#endif selectMerchant
})

const userAvatar = ref(defaultAvatar)
</script>
<style lang="scss" scoped>
.reference {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  .name {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 8px;
  }
}
</style>
