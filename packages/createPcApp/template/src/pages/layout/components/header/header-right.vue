<template>
  <div class="infoWrap">
    <!-- <div class="svgWrap">
      <BellOutlined class="symbol" />
    </div> -->
    //#if selectMerchant
    <Store />
    //#endif selectMerchant
    <a-dropdown>
      //#if selectMerchant
      <User :from="2" />
      //#endif selectMerchant
      //#if !selectMerchant
      <User />
      //#endif !selectMerchant
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <!-- <a-menu-item key="info">个人档案</a-menu-item> -->
          <a-menu-item key="logout">退出登录</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>
<script lang="ts"></script>
<script setup lang="ts">
import { defineComponent, ref } from 'vue'

import type { MenuProps } from 'ant-design-vue'
// import { BellOutlined } from '@ant-design/icons-vue'
import User from './user.vue'
//#if selectMerchant
import Store from './store.vue'
//#endif selectMerchant

defineComponent({
  name: 'header-right',
})

const emit = defineEmits(['info', 'logout'])
const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
  if (key === 'info') {
    emit('info')
  } else if (key === 'logout') {
    emit('logout')
  }
}
</script>
<style lang="scss" scoped>
.infoWrap {
  display: flex;
  padding: 0 16px;
  gap: 26px;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-end;

  .svgWrap {
    cursor: pointer;
    .symbol {
      font-size: 16px;
    }
  }
}
.reference {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  .name {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 8px;
  }
}
// .user {
//   &::after {
//     content: '';
//     position: absolute;
//     width: 0;
//     height: 18px;
//     right: -13px;
//     border-right: 1px solid #ebebeb;
//     top: 6px;
//   }
// }
</style>
