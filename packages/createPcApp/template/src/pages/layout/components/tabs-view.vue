<template>
  <!-- <div ref="tagsViewWrap" class="scrollWrap"> -->
  <div class="tagsView">
    <a-tabs
      :tabBarGutter="0"
      class="tab-wrap"
      v-model:activeKey="activeKey"
      hide-add
      type="editable-card"
      @edit="deleteView"
      @change="toTarget"
    >
      <a-tab-pane
        v-for="item in visitedViews"
        :key="item.fullPath"
        :closable="!(item.meta.affix === true || visitedViews.length === 1)"
      >
        <template #tab>
          <a-dropdown :trigger="['contextmenu']">
            <span>
              {{ item.title }}
            </span>
            <template #overlay>
              <a-menu @click="({ key }) => handleMenuClick(key, item)">
                <!-- <a-menu-item key="refresh">刷新当前页</a-menu-item> -->
                <a-menu-item key="closeOther">关闭其他</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-tab-pane>
      <template #rightExtra>
        <a-dropdown :trigger="['hover']">
          <span class="tab-more">
            <more-outlined />
          </span>
          <template #overlay>
            <a-menu @click="({ key }) => handleMenuClick(key)">
              <a-menu-item key="closeOther">关闭其他</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-tabs>
  </div>
</template>
<script setup lang="ts">
import {
  useRoute,
  useRouter,
  onBeforeRouteUpdate,
  onBeforeRouteLeave,
} from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useStore } from 'vuex'
import { ActionsTypes } from '../../../store/modules/tagsView'
import type { TagView } from '../../../store/modules/tagsView'
import { constRoutes } from '@/router'

import { computed, ref, onMounted } from 'vue'
import { MoreOutlined } from '@ant-design/icons-vue'

const $route = useRoute()
const $router = useRouter()
const $store = useStore()

const visitedViews = computed(() => {
  return $store.state.tagsView.visitedViews
})

const activeKey = ref($route.fullPath)

// 初始化页签
const initTags = () => {
  const affixTags = filterAffixTags(constRoutes)
  if (affixTags)
    for (const item of affixTags) {
      $store.dispatch(ActionsTypes.addView, item)
    }
}

// 过滤固定页签
const filterAffixTags = (curRoutes: RouteRecordRaw[]) => {
  let tags: TagView[] = []
  for (const item of curRoutes) {
    if (item.meta?.affix && !item.meta?.hide) {
      const tagPath = $router.resolve({ name: item.name }).path
      const tagFullPath = $router.resolve({ name: item.name }).fullPath
      tags.push({
        fullPath: tagFullPath,
        path: tagPath,
        name: item.name,
        meta: { ...item.meta },
      })
    }
    if (item.children) {
      const childTags = filterAffixTags(item.children)
      if (childTags.length > 0) {
        tags = [...tags, ...childTags]
      }
    }
  }
  return tags
}

// 删除页签
const deleteView = (path: string) => {
  const index = visitedViews.value.findIndex(
    (item: TagView) => item.fullPath === path,
  )
  if (index < 0) {
    return
  }
  const isSelected = visitedViews.value[index].fullPath === $route.fullPath
  $store.dispatch(ActionsTypes.deleteView, visitedViews.value[index])
  if (isSelected) {
    toLastView()
  }
}
const toLastView = () => {
  const len = visitedViews.value.length
  const lastView = visitedViews.value[len - 1]
  if (lastView) {
    toTarget(lastView.fullPath)
  } else {
    $router.push('/')
  }
}
const toTarget = (fullPath: string) => {
  $router.push(fullPath)
}

// 添加页签
const addviews = (tag: TagView) => {
  $store.dispatch(ActionsTypes.addView, tag)
}

// 页签右键menu点击事件
const handleMenuClick = (key, item?) => {
  if (key === 'refresh') {
    console.log('refresh')
  } else if (key === 'closeOther') {
    const { name, fullPath, path, meta } = $route
    const tabView = {
      name: name as string,
      fullPath,
      path,
      meta: { ...meta },
    }
    $store.dispatch(ActionsTypes.deleteOtherViews, tabView)
  }
}

onBeforeRouteUpdate((to, from, next) => {
  // 不能直接使用引用复制，会warning
  const { name, fullPath, path, meta } = to
  addviews({
    name: name as string,
    fullPath,
    path,
    meta: { ...meta },
  })
  activeKey.value = fullPath
  next()
})
onBeforeRouteLeave((to, from, next) => {
  // 不能直接使用引用复制，会warning
  const { name, fullPath, path, meta } = to
  if (fullPath != '/home') {
    addviews({
      name: name as string,
      fullPath,
      path,
      meta: { ...meta },
    })
    activeKey.value = fullPath
  }
  next()
})
onMounted(() => {
  initTags()
  const { name, fullPath, path, meta } = $route
  addviews({
    name: name as string,
    fullPath,
    path,
    meta: { ...meta },
  })
})
</script>
<style lang="scss" scoped>
.tagsView {
  padding-left: 16px;
  display: flex;
  padding-top: 8px;
  width: calc(100% - 138px);
  height: 100%;
  .tab-wrap {
    height: 100%;
  }
  :deep(.ant-tabs-nav-operations) {
    .ant-tabs-nav-more {
      padding: 4px 12px;
    }
  }
  // :deep(.ant-tabs-nav-wrap .ant-tabs-nav-list) {
  //   height: 30px;
  // }
  :deep(.ant-tabs-top > .ant-tabs-nav) {
    margin: 0;
    height: 100%;
    &::before {
      border: none;
    }
  }

  .tab-more {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 100%;
    cursor: pointer;
    flex-shrink: 0;
  }
}
:deep(
    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab
  ) {
  line-height: 20px;
  color: #44515c;
  padding: 8px 8px 12px 8px;
}
:deep(
    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab
  ) {
  border-radius: 6px 6px 6px 0;
  border: none;
  background: none;
  &.ant-tabs-tab-active {
    background: #f5f5f5;
    border-radius: 6px 6px 0px 0px;
    font-size: 14px;
    font-weight: 600;
    .ant-tabs-tab-btn {
      color: #0a1b29;
    }
    .ant-dropdown-trigger {
      color: #0a1b29;
    }
  }
  &:hover {
    background: #f5f5f5;
    border-radius: 6px 6px 0px 0px;
  }
}
</style>
