<template>
  <div :class="$style.wrap">
    <home-outlined :class="$style.home" />
    <div :class="$style.breadcrumb">
      <template v-for="(item, index) in matched" :key="item.name">
        <div
          :class="$style.title"
          @click="handleClick(item, index < matched.length - 1)"
        >
          {{ item.meta?.title }}
        </div>
        <right-outlined
          v-if="index < matched.length - 1"
          :class="$style.symbol"
        />
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, RouteRecordNormalized, useRouter } from 'vue-router'
import { RightOutlined, HomeOutlined } from '@ant-design/icons-vue'

const $route = useRoute()
const $router = useRouter()
const matched = ref<RouteRecordNormalized[]>([])
watch(
  () => $route.matched,
  (values) => {
    matched.value = values
  },
  {
    immediate: true,
  },
)
const handleClick = (item, isJump) => {
  if (!isJump) {
    return
  }
  $router.push(item.path)
}
</script>
<style module>
.wrap {
  display: flex;
  align-items: center;
}
.home {
  font-size: 13px;
}
.breadcrumb {
  display: flex;
  align-items: center;
  margin-left: 2px;
  font-size: 13px;
}
.title {
  cursor: pointer;
}
.symbol {
  margin: 0 8px;
  font-size: 12px;
  color: #999999;
}
</style>
