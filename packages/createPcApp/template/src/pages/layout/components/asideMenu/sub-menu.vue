<template>
  <a-sub-menu :key="menuInfo.name">
    <template #icon>
      <svg-icon :name="menuInfo.meta?.iconName"></svg-icon>
    </template>
    <template #title>{{ menuInfo.meta?.title }}</template>
    <template v-for="item in menuInfo.children" :key="item.name">
      <template v-if="!item.children">
        <template v-if="!item.meta?.hide">
          <a-menu-item :key="item.name">
            <template #icon>
              <svg-icon :name="item.meta?.iconName"></svg-icon>
            </template>
            {{ item.meta?.title }}
          </a-menu-item>
        </template>
      </template>
      <template v-else>
        <sub-menu
          :key="item.name"
          :menu-info="item"
          :selected-keys="selectedKeys"
          :open-keys="openKeys"
        ></sub-menu>
      </template>
    </template>
  </a-sub-menu>
</template>
<script setup lang="ts">
defineProps({
  menuInfo: {
    type: Object,
    default: () => {
      return {}
    },
  },
  openKeys: {
    type: Array,
    default: () => [],
  },
  selectedKeys: {
    type: Array,
    default: () => [],
  },
})
</script>
