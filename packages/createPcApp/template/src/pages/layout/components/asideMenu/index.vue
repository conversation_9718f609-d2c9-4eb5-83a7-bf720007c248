<template>
  <div class="aside">
    <div class="asideTop">
      <div class="siteName">{{ siteName }}</div>
    </div>
    <a-menu
      v-model:openKeys="openKeys"
      v-model:selectedKeys="selectedKeys"
      class="menu"
      theme="dark"
      mode="inline"
      @click="handleMenuItemClick"
    >
      <template v-for="item in routesProxy" :key="item.name">
        <template v-if="!item.children">
          <template v-if="!item.meta?.hide">
            <a-menu-item :key="item.name">
              <template #icon>
                <svg-icon :name="item.meta?.iconName"></svg-icon>
              </template>
              {{ item.meta?.title }}
            </a-menu-item>
          </template>
        </template>
        <template v-else>
          <SubMenu
            v-if="item.meta?.hide !== true"
            :menu-info="item"
            :selected-keys="selectedKeys"
            :open-keys="openKeys"
          ></SubMenu>
        </template>
      </template>
    </a-menu>
    <div class="menuFooter">
      <span @click="toggleCollapsed" class="collapsedWrap">
        <MenuUnfoldOutlined class="collapsedIcon" v-if="curCollapsed" />
        <MenuFoldOutlined class="collapsedIcon" v-else />
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, defineComponent, reactive, ref, toRefs, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useStore } from 'vuex'
import type { MenuProps } from 'ant-design-vue'
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue'
import SubMenu from './sub-menu.vue'

defineComponent({
  name: 'aside-menu',
})
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['collapse'])
const curCollapsed = ref(props.collapsed)
const toOpenKeys = ref<string[]>([])
const toggleCollapsed = () => {
  curCollapsed.value = !curCollapsed.value
  if (!curCollapsed.value) {
    openKeys.value = toOpenKeys.value
  }
  emit('collapse', curCollapsed.value)
}
const router = useRouter()
const route = useRoute()
const store = useStore()
const renderRoutes = computed<RouteRecordRaw[]>(() => {
  return store.state.permission.routes
})
const routesProxy = reactive<RouteRecordRaw[]>(renderRoutes.value)
const openKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const { collapsed } = toRefs(props)
// const siteName = ref(import.meta.env.SYS_NAME)
const siteName = computed(() => {
  return collapsed.value
    ? import.meta.env.SYS_SHORT_NAME
    : import.meta.env.SYS_NAME
})

// 菜单点击
const handleMenuItemClick: MenuProps['onClick'] = ({ key }) => {
  router.push({ name: key as string })
}
// 监听路由变化，展开对应菜单
watch(
  () => route.matched,
  (matched) => {
    const len = matched.length
    const selectItem = matched[len - 1]
    const openMenu = matched.filter((item, index) => {
      return index > 0 && index < len - 1
    })
    // 非展示在菜单的页面，激活其配置的active的菜单
    let activeKey = ''
    if (selectItem.meta?.active) {
      activeKey = selectItem.meta.active
    }
    selectedKeys.value = activeKey ? [activeKey] : [selectItem.name as string]
    toOpenKeys.value = openMenu.map((item) => item.name as string)
    if (!curCollapsed.value) {
      openKeys.value = toOpenKeys.value
    }
  },
  {
    immediate: true,
  },
)
</script>
<style lang="scss" scoped>
.aside {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .asideTop {
    height: 48px;
    padding: 18px 0;
    color: #fff;
    text-align: center;
    background-color: #284155;
    .siteName {
      font-size: 16px;
      line-height: 22px;
    }
    .sysName {
      margin-top: 5px;
      font-size: 12px;
      line-height: 17px;
      opacity: 0.6;
    }
  }
  .menu {
    flex: 1;
  }
  .iconStyle {
    color: inherit;
  }
  .menuFooter {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-shadow: 0px -2px 8px rgb(0 0 0 / 10%);
    flex-shrink: 0;
    &:hover {
      cursor: pointer;
      .collapsed-icon {
        color: #1890ff;
      }
    }
    .collapsedWrap {
      display: inline-flex;
      width: 60px;
      height: 100%;
      align-items: center;
      justify-content: center;
    }
    .collapsedIcon {
      font-size: 20px;
      color: #fff;
    }
  }
}
</style>
