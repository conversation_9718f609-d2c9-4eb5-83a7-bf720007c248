<template>
  <a-layout-content class="main">
    <header :show-tabs="false" class="header">
      <div class="sys-name">{{ sysName }}</div>
      <div class="header-right">
        <User class="user" :from="1" />
        <LogoutOutlined class="logout" @click="handleLogout" />
      </div>
    </header>
    <div class="content">
      <lyy-select-merchant
        :prop="prop"
        :store="$store"
        @selected="handleSelected"
      ></lyy-select-merchant>
    </div>
    <div class="icp-tip">
      Copyright © 2015-2022 广东星云开物科技股份有限公司 粤ICP备16014453号
    </div>
  </a-layout-content>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import User from '../layout/components/header/user.vue'
import { LogoutOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { logout } from '../../request/apis/system'
import Prop from './prop'
import { useStore } from 'vuex'
import { ActionsTypes } from '@/store/modules/user/type'

const prop = ref(Prop)

const $store = useStore()

const $router = useRouter()

const sysName = ref(import.meta.env.SYS_NAME)

const handleSelected = (data: Array<object>) => {
  const [tenant, org] = data
  sessionStorage.setItem('tenant', tenant.tenantId)
  sessionStorage.setItem('org', org.authOrgId)
  sessionStorage.setItem('orgInfo', JSON.stringify(org))
  $store.dispatch(ActionsTypes.setTenantInfo)
  $router.push('/')
}

const handleLogout = async () => {
  const res = await logout()
  if (res.code === '0000000') {
    sessionStorage.clear()
    $router.push('/login')
  }
}
</script>
<style lang="scss" scoped>
.main {
  height: 100vh;
  background: url('../../assets/bg.png') no-repeat;
  padding-bottom: 36px;
  display: flex;
  flex-direction: column;
}
.header {
  height: 48px;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  color: #ffffff;
  .sys-name {
    font-size: 20px;
    font-weight: 600;
  }
  .header-right {
    display: flex;
    align-items: center;
    gap: 24px;
    :deep(.anticon-logout) {
      font-size: 16px;
    }
  }
}
.content {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  flex: 1;
}
.icp-tip {
  display: flex;
  justify-content: center;
  font-size: 12px;
  color: #fff;
  opacity: 0.6;
}
</style>
