<template>
  <a-layout-content v-if="config.length > 0">
    <lyy-pc-render :page-config="config" :jsObject="jsObject" :dataSetList="datasource"></lyy-pc-render>
  </a-layout-content>
</template>
<script lang="ts">
import { defineComponent, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { getPageConfig, getJsObject } from '@/request/apis/app'
export default defineComponent({
  name: 'render-index',
})
</script>
<script lang="ts" setup>
const config = reactive([])
const jsObject = ref([])
const dataSetList = ref([])

const getPageConfigFn = async (id: string) => {
  const res = (await getPageConfig(id)) || []
  config.push(...res)
}

const handlePageConfig = async () => {
  const pathItems = $route.path.split('/')
  const id = pathItems.pop()
  try {
    const res = await getJsObject(id)
    const { js = [], datasource = [] } = res
    jsObject.value = js
    dataSetList.value = datasource
  } catch(err) {
    console.log(err)
  }
  getPageConfigFn(id as string)
}


const $route = useRoute()
handlePageConfig()
</script>
