import request from '../index'
import type { UserInfo } from '@/store/modules/user/type'
//#if selectMerchant
import type { TenantInfo } from '@/store/modules/user/type'
//#endif selectMerchant
import type { DataType } from '@/request/index'
export const getUserInfo = (): Promise<DataType<UserInfo>> => {
  return request({
    url: '/account/info',
    method: 'get',
  })
}

//#if selectMerchant
export const getTenantInfo = (): Promise<DataType<TenantInfo>> => {
  return request({
    url: '/account/tenantInfo',
    method: 'get',
  })
}
//#endif selectMerchant
