import request from '../index'
import type { DataType } from '../index'
// 退出登录
export const logout = (): Promise<DataType<null>> => {
  return request({
    method: 'post',
    url: '/gw/ram-service/permission/account/logout',
    headers: {
      'ram-token': sessionStorage.getItem('token'),
      'ram-system': import.meta.env.SYS_ID,
    },
    data: {
      authSystemId: import.meta.env.SYS_ID,
      clientType: 'pc', // 客户端类型：pc、 mini. h5. app
      token: sessionStorage.getItem('token'),
    },
  })
}
