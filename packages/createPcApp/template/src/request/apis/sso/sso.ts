import request from '../index'
import type { DataType } from '../index'

export const SSO = import.meta.env.SSO_BASE_URL

export interface GetLoginUrlType {
  ssoLogin: boolean
  ssoLoginUrl: string
}

// 获取统一登陆地址
export function getLoginUrlApi(): Promise<DataType<GetLoginUrlType>> {
  return request({
    url: `${SSO}/getLoginUrl`,
    method: 'get',
  }) as any
}

export interface LoginDataType {
  ticket: string
}

// 通过ticket登陆
export function loginApi(params: LoginDataType): Promise<DataType<any>> {
  return request({
    url: `${SSO}/doLoginByTicket`,
    method: 'get',
    params,
  }) as any
}

