import request from '../index'
import type { UserInfo } from '@/store/modules/user/type'
//#if selectMerchant
import type { TenantInfo } from '@/store/modules/user/type'
//#endif selectMerchant
import type { DataType } from '@/request/index'
export const getUserInfo = (): Promise<DataType<UserInfo>> => {
  return request({
    baseURL: '',
    url: '/gw/ram-service/permission/account/info',
    headers: {
      'ram-token': sessionStorage.getItem('token'),
      'ram-system': import.meta.env.SYS_ID,
    },
    method: 'get',
    params: {
      token: sessionStorage.getItem('token'),
    },
  })
}

//#if selectMerchant
export const getTenantInfo = (): Promise<DataType<TenantInfo>> => {
  return request({
    url: '/gw/ram-service/permission/account/tenantInfo',
    headers: {
      'ram-token': sessionStorage.getItem('token'),
      'ram-system': import.meta.env.SYS_ID,
      'ram-org': sessionStorage.getItem('org'),
      'ram-tenant': sessionStorage.getItem('tenant'),
    },
    method: 'get',
  })
}
//#endif selectMerchant
