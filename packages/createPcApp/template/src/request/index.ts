import axios from 'axios'
import { notification, Modal } from 'ant-design-vue'
import router from '@/router'

export interface DataType<T> {
  data: T
  body: T
  code?: number | string
  message?: string
  result?: number
  description?: string
  resultCode: number
}

export const axiosInst = axios.create({
  baseURL: '',
  timeout: 5000,
})

// 不需要校验的白名单
export const whitelist = ['/sso/getLoginUrl']

axiosInst.interceptors.request.use(
  (config: any) => {
    return config
  },
  (error) => {
    // eslint-disable-next-line no-console
    console.log(`request error：${error}`)
    return Promise.reject(error)
  },
)

axiosInst.interceptors.response.use(
  (response) => {
    const res = response.data
    const { description, result, code, message } = res
    // 如果是bolb返回值类型的 直接把整个响应数据返回
    if (
      ['blob'].includes(response.request.responseType) &&
      (typeof code === 'undefined' || typeof result === 'undefined')
    ) {
      return response
    }
    // 如果请求的是 json 文件
    const url = response.config.url as string
    const reg = /\.json/
    if (reg.test(url)) {
      return res
    }
    if (
      [200, 203, 204, '0000000'].includes(code) ||
      [200, 203, 204, 0].includes(result)
    ) {
      return res
    } else if (code === 403 || result === 403) {
      notification.error({
        message: '权限不足',
        description: '你想干什么坏事哈',
      })
      return res
    } else if (code === 401 || result === 401) {
      Modal.confirm({
        title: '登陆失效',
        content: '登陆已经失效，您可以取消停留在此页面，或重新登录!',
        onOk: () => {
          sessionStorage.setItem('token', '')
          router.replace({
            name: 'login',
            query: {
              ...router.currentRoute.value.query,
              routerName: router.currentRoute.value.name as string,
            },
          })
        },
      })
      return Promise.reject(new Error(message || 'Error'))
    } else {
      notification.error({
        message: '请求失败',
        description: message || code || description,
      })
    }

    return res
  },
  (error) => {
    if (error.message.includes('timeout')) {
      return Promise.reject(error)
    }
    const { response } = error
    if (response.status === 401) {
      router.push({
        path: '/login',
      })
    } else {
      notification.error({
        message: '请求失败',
        // eslint-disable-next-line unicorn/consistent-destructuring
        description: error.message,
      })
    }
    return Promise.reject(error)
  },
)

export default axiosInst
