<template>
  <a-config-provider :locale="locale">
    <router-view></router-view>
    <!-- <lyy-watermark
      v-bind="watermarkConfig"
      :content="warterMarkContent"
    ></lyy-watermark> -->
  </a-config-provider>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

const locale = ref(zhCN)

const warterMarkContent = '系统名称'

const watermarkConfig = ref({
  fontSize: 16,
  width: 160,
  height: 12,
})
</script>
<style>
@import './styles/reset.scss';
#app {
  width: 100%;
  height: 100%;
}
</style>
