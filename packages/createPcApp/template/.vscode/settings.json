{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[javascript]": {"editor.formatOnSave": false}, "[javascriptreact]": {"editor.formatOnSave": false}, "[typescript]": {"editor.formatOnSave": false}, "[typescriptreact]": {"editor.formatOnSave": false}, "[vue]": {"editor.formatOnSave": false}, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "files.associations": {"*.env": "env", "*.env.*": "env"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"]}