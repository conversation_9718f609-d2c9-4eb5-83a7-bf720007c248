{"private": true, "name": "admin", "version": "0.0.0", "scripts": {"dev": "leyaoyao-vue -m dev", "sit": "leyaoyao-vue -m sit", "build:dev": "leyaoyao-vue build -m dev", "build:sit": "leyaoyao-vue build -m sit", "build:release": "leyaoyao-vue build -m release", "build:master": "leyaoyao-vue build -m master"}, "devDependencies": {"@leyaoyao/eslint-plugin": "^4.1.2", "@leyaoyao/prettier-config": "^1.0.6", "@leyaoyao/vue-cli": "^2.2.2", "ant-design-vue": "4.0.7", "axios": "^1.1.3", "core-js": "^3.26.0", "eslint": "^8.27.0", "husky": "^8.0.2", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "typescript": "^4.8.4", "vue": "3.2.41", "vue-router": "^4.1.6", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0"}, "dependencies": {"vxe-table": "4.2.8", "xe-utils": "3.5.25"}}