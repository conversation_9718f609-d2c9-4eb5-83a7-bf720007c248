import { isFalse } from '../is-false'
import { decimalPipe } from './decimal-pipe'

/**
 * 把数字转换成货币字符串
 */
export const currencyPipe = (num: number, option: CurrencyPipe) => {
  if (isFalse(num)) return 0
  if (Number.isNaN(num)) return num

  const { fixed, thousands, currency, prefix, suffix } = option ?? {}

  let value = decimalPipe(num, { fixed, thousands })

  if (currency) {
    value = getCurrencyCode(currency) + value
  }

  if (prefix) {
    value = prefix + value
  }

  if (suffix) {
    value = value + suffix
  }

  return value
}

// 世界货币符号 https://coinyep.com/zh/currencies
const getCurrencyCode = (currency: string) => {
  switch (currency) {
    case 'CNY':
      return '¥'

    case 'USD':
      return '$'

    case 'EUR':
      return '€'

    case 'GBP':
      return '£'

    case 'CHF':
      return 'Fr'

    case 'JPY':
      return '¥'

    case 'KRW':
      return '₩'

    case 'HKD':
      return '$'

    default:
      return ''
  }
}
