import { currencyPipe } from './currency-pipe'
import { datePipe } from './date-pipe'
import { decimalPipe } from './decimal-pipe'
import { joinPipe } from './join-pipe'
import { percentPipe } from './percent-pipe'
import { textmapPipe } from './textmap-pipe'

export const datapipe = (value, pipes?: string[] | Pipe[]) => {
  if (!pipes) return value
  // @ts-ignore
  return pipes?.reduce((prev, curr) => {
    return switchPipe(prev, curr)
  }, value)
}

const switchPipe = (value, pipeConfig) => {
  if (typeof pipeConfig === 'string') {
    pipeConfig = { pipe: pipeConfig }
  }

  const { pipe, option } = pipeConfig

  switch (pipe) {
    case 'date':
      return datePipe(value, option as DatePipe)
    case 'currency':
      return currencyPipe(value, option as CurrencyPipe)
    case 'decimal':
      return decimalPipe(value, option as DecimalPipe)
    case 'percent':
      return percentPipe(value, option as PercentPipe)
    case 'textmap':
      return textmapPipe(value, option as TextmapPipe)
    case 'join':
      return joinPipe(value, option as JoinPip<PERSON>)

    default:
      return value
      break
  }
}
