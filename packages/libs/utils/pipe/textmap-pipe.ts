/**
 * 文本映射
 */
export const textmapPipe = (value: Value | Value[], option: TextmapPipe) => {
  const { textMap } = option || {}

  if (!textMap) return value

  let result: Value = ''
  if (Array.isArray(value)) {
    for (const val of value) {
      const one = textMap.find((item) => {
        if (Array.isArray(item.origin) && item.origin.includes(value)) {
          return item
        } else if (item.origin === val) {
          return item
        }
      })
      result += one?.result
    }
  } else {
    const one = textMap.find((item) => {
      if (Array.isArray(item.origin) && item.origin.includes(value)) {
        return item
      } else if (item.origin === value) {
        return item
      }
    })
    result = one?.result ?? value
  }

  return result
}

type Value = number | string | boolean
