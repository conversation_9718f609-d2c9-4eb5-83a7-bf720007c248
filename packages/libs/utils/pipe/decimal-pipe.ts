import { isFalse } from '../is-false'
import _ from 'lodash'

/**
 * 把数字转换成带小数点的字符串（千分位、保留小数点后的位数）
 */
export const decimalPipe = (num: number, option: DecimalPipe) => {
  if (isFalse(num)) return 0
  if (Number.isNaN(Number(num))) return num
  const { fixed = 2, thousands = true } = option ?? {}
  //返回四舍五入的数字
  const value = _.round(Number(num), fixed).toFixed(fixed)
  if (thousands) {
    let [integer, decimal] = (value + '').split('.')
    integer = integer.replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',')
    return decimal ? integer + '.' + decimal : integer
  }
  return value
  // return thousands
  //   ? (value + '').replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',')
  //   : value
}
