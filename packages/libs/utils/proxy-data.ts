import _, { cloneDeep } from 'lodash'
import { PAGE_CONFIG } from '../constants/store-key'
import { getCurrentPageName, useStore } from '../store'
import { getIn } from './get-in'
import { setIn } from './set-in'
import { changeObj } from './change-storage'
import { useRoute } from 'vue-router'
import { treeFind } from './tree-find'
import isEdit from './is-edit'

/**
 * 用于快捷处理数据的类，实时获取平铺后的数据树
 */
class ProxyData {
  // 实时数据，保证数据最新输出
  public getProxyData(origin?: string) {
    // console.log(`getProxyData ===> ${origin}`)
    const store = useStore()
    // 额外的数据处理，并去掉无效数据
    const path = getCurrentPageName()
    let list = {}
    if (isEdit()) {
      treeFind(store.getValue(PAGE_CONFIG), (node) => {
        list[node.entityId] = node
        list[node.compId] = node
        if (node.prop?.aliasName) {
          const key = `${node.prop.aliasName}-${node.compId}`
          list[key] = node
        }
      })
    } else {
      list = this.cacheData[path] || {}
    }
    const data = store.state[path]
    const d1 = {
      ['表单集合']: {},
    }
    if (data) {
      for (const key of Object.keys(data)) {
        if (key == 'formRef' || key == 'origin' || key == 'pageConfig') {
          continue
        }
        if (list[key]) {
          Object.assign(d1['表单集合'], { [key]: data[key] })
        } else {
          d1[key] = data[key]
        }
      }
    }
    // 聚合各数据
    return {
      ...d1,
      ...list,
      localStorage: changeObj(localStorage),
      sessionStorage: changeObj(sessionStorage),
      route: useRoute() || store.getRoute(),
    }
  }
  // 缓存数据镜像
  cacheData = {}
  // 更新镜像数据
  updateProxyData(pageName) {
    if (!this.cacheData[pageName] || isEdit()) {
      this.cacheData[pageName] = {}
    }
    const store = useStore()
    treeFind(store.getValue(PAGE_CONFIG), (node) => {
      this.cacheData[pageName][node.entityId] = node
      this.cacheData[pageName][node.compId] = node
      if (node.prop?.aliasName) {
        const key = `${node.prop.aliasName}-${node.compId}`
        this.cacheData[pageName][key] = node
      }
    })
  }
  /**
   * @description 从数据树中拿到数据
   * @param nodePath 数据路径
   */
  public getValueFromProxyData(nodePath: string) {
    if (!nodePath) {
      return nodePath
    }
    // 此处是为了兼容对象模式下取值静态值使用了字符串数字
    if (_.isFinite(Number(nodePath)) || typeof nodePath === 'boolean') {
      return
    }

    return getIn(
      this.getProxyData('getValueFromProxyData --> proxy-data'),
      nodePath,
    )
  }
  public setProxyData(nodePath: string, value) {
    setIn(this.getProxyData(), nodePath, value)
  }
}

export default new ProxyData()
