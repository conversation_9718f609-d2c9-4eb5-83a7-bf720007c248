class SessionCache {
  set(key: string, value: any) {
    sessionStorage.setItem(key, JSON.stringify(value))
  }

  get(key: string) {
    const value = sessionStorage.getItem(key)
    try {
      if (value) return JSON.parse(value)
    } catch {
      return value
    }
  }

  remove(key: string) {
    sessionStorage.removeItem(key)
  }

  clear() {
    sessionStorage.clear()
  }
}

export const sessionCache = new SessionCache()
