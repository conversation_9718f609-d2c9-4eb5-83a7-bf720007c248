import { camelCase } from 'lodash'
import { IElement } from '../../lyyMobileRender/src/global'
import { treeFind } from './tree-find'
export default function getHashCompId(compId) {
  // const hash = uniqueId('lyy-pc-') + location.hash.replace('#', '---')
  // if (!compId) {
  //   return hash
  // }
  // if (compId && compId.includes('lyy-pc-')) {
  //   return compId
  // }
  // compList.set(hash + compId, hash + compId)
  // return hash + compId
  return compId
}

export function getCompId(compId) {
  // if ((compId && compId.includes('lyy-pc-')) || !compId) {
  //   return compId
  // }
  // const regKey =
  //   location.hash.replace('#', '---').replace(/\?/g, '\\?') + compId
  // const regExp = new RegExp(`${regKey}$`, 'g')
  // for (const [key, value] of compList) {
  //   if (regExp.test(key)) {
  //     return value
  //   }
  // }
  return compId
}

const compType = new Map()
export function getEntityId(comp: IElement) {
  if (comp.entityId) {
    return comp.entityId
  }
  const nameCom = camelCase(comp.compName).replace(/lyy/, '')
  let idx = compType.get(nameCom)
  if (!idx) {
    idx = 1
    compType.set(nameCom, idx)
  } else {
    idx += 1
    compType.set(nameCom, idx)
  }
  return `${nameCom}${idx}`
}
export const clearEntityId = (comp) => {
  // 将对象转换为字符串
  let jsonString = JSON.stringify(comp)

  // 使用正则表达式替换指定键的值
  jsonString = jsonString.replace(/("entityId"\s*:\s*")[^"]*(")/g, '$1$2')

  // 将字符串转换回对象
  const clearedObj = JSON.parse(jsonString)
  return clearedObj
}
export const upDateIdx = (comp) => {
  const nameCom = camelCase(comp.compName).replace(/lyy/, '')
  const curIdx = Number(comp.entityId.replace(nameCom, ''))
  const idx = compType.get(nameCom) || 0
  compType.set(nameCom, Math.max(idx, curIdx))
}
export const addEntityId = (tree) => {
  treeFind(tree, (comp) => {
    if (comp && !comp.entityId) {
      comp.entityId = getEntityId(comp)
    } else if (comp.entityId) {
      upDateIdx(comp)
    }
  })
}
