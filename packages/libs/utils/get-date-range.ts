import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'

export function utcToDateTimeFormat(utcDate, format = DATE_TIME_FORMAT) {
  if (!utcDate) return utcDate
  return dayjs(utcDate).format(format)
}

export function formatTime(obj) {
  const { datetime, start, end, format } = obj
  if (datetime) {
    if (!format) return datetime
    return utcToDateTimeFormat(datetime, format)
  }

  if (!format) return [start, end]
  return [utcToDateTimeFormat(start), utcToDateTimeFormat(end)]
}

// 昨天
export function getYesterday(format?: string) {
  const start = dayjs().subtract(1, 'days').startOf('day')
  const end = dayjs().subtract(1, 'days').endOf('day')
  return formatTime({ start, end, format })
}

// 当前时间
export function getCurrentTime(format = DATE_TIME_FORMAT) {
  const datetime = dayjs()
  return formatTime({ datetime, format })
}

// 今天结束时间 (23:59:59)
export function getTodayEnd(format?: string) {
  const datetime = dayjs().endOf('day')
  return formatTime({ datetime, format })
}

// 近15分钟
export function getLastMinute15(format?: string) {
  const start = dayjs(dayjs().valueOf() - 900_000)
  const end = dayjs()
  return formatTime({ start, end, format })
}
// 近30分钟
export function getLastMinute30(format?: string) {
  const start = dayjs(dayjs().valueOf() - 1_800_000)
  const end = dayjs()
  return formatTime({ start, end, format })
}
// 近1小时
export function getLastHour1(format?: string) {
  const start = dayjs(dayjs().valueOf() - 3_600_000)
  const end = dayjs()
  return formatTime({ start, end, format })
}
// 近2小时
export function getLastHour2(format?: string) {
  const start = dayjs(dayjs().valueOf() - 7_200_000)
  const end = dayjs()
  return formatTime({ start, end, format })
}
// 近6小时
export function getLastHour6(format?: string) {
  const start = dayjs(dayjs().valueOf() - 21_600_000)
  const end = dayjs()
  return formatTime({ start, end, format })
}
// 今天 (00:00:00 ~ 23:59:59)
export function getToday(format?: string) {
  const start = dayjs().startOf('date')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近3天
export function get3DayOfRecently(format?: string) {
  const start = dayjs().subtract(2, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近7天
export function get7DayOfRecently(format?: string) {
  const start = dayjs().subtract(6, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近14天
export function get14DayOfRecently(format?: string) {
  const start = dayjs().subtract(13, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近30天
export function get30DayOfRecently(format?: string) {
  const start = dayjs().subtract(30, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近60天
export function get60DayOfRecently(format?: string) {
  const start = dayjs().subtract(59, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近90天
export function get90DayOfRecently(format?: string) {
  const start = dayjs().subtract(89, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近半年
export function getHalfAYearOfRecently(format?: string) {
  const start = dayjs().subtract(182, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 近一年
export function getYearOfRecently(format?: string) {
  const start = dayjs().subtract(365, 'days').startOf('day')
  const end = getTodayEnd()
  return formatTime({ start, end, format })
}

// 上个月
export function getPrevMonth(format?: string) {
  const start = dayjs().subtract(1, 'months').startOf('month')
  const end = dayjs().subtract(1, 'months').endOf('month')
  return formatTime({ start, end, format })
}

// 本月
export function getNowMonth(format?: string) {
  const start = dayjs().startOf('month')
  const end = dayjs().endOf('month')
  return formatTime({ start, end, format })
}

// 下个月
export function getNextMonth(format?: string) {
  const start = dayjs().subtract(-1, 'months').startOf('month')
  const end = dayjs().subtract(-1, 'months').endOf('month')
  return formatTime({ start, end, format })
}


// 获取当前机器的时区
export function getTimezone() {
  const offsetInMinutes = new Date().getTimezoneOffset();
  const hours = Math.abs(Math.floor(offsetInMinutes / 60));
  const minutes = Math.abs(offsetInMinutes % 60);
  const sign = offsetInMinutes > 0 ? '-' : '+';
  const offsetString = `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  return offsetString

}

// 根据传入的格式格式化时间
export function formatDate(date, formatter) {
  if (date) {
    formatter = formatter ? formatter : 'YYYY-MM-DD HH:mm:ss'
    date = (date ? new Date(date) : new Date)
    const Y = date.getFullYear() + '',
      M = date.getMonth() + 1,
      D = date.getDate(),
      H = date.getHours(),
      m = date.getMinutes(),
      s = date.getSeconds()

    return formatter.replace(/YYYY|yyyy/g, Y)
      .replace(/YY|yy/g, Y.substr(2, 2))
      .replace(/MM|mm/g, (M < 10 ? '0' : '') + M)
      .replace(/DD|dd/g, (D < 10 ? '0' : '') + D)
      .replace(/HH|hh/g, (H < 10 ? '0' : '') + H)
      .replace(/MM|mm/g, (m < 10 ? '0' : '') + m)
      .replace(/SS|ss/g, (s < 10 ? '0' : '') + s)
  }
}

// 将时间根据时区转化为世界标准时间，第一个参数为要转化的时间，第二个参数为要转化的时区偏移量，第三个参数为转化后的格式，
// 没有默认取机器时区偏移量
export function formatTimeToInternational(option) {
  let { utcTime, offset, format } = option
  offset = offset ? offset : getTimezone();
  console.log("🚀 ~ formatTimeToInternational ~ offset:", offset)
  const dateLocal = new Date(utcTime);
  const hours = parseInt(offset.substring(4, 6));
  const minutes = parseInt(offset.substring(7, 9));
  const sign = offset[3] === '+' ? -1 : 1;
  dateLocal.setHours(dateLocal.getHours() + sign * hours);
  dateLocal.setMinutes(dateLocal.getMinutes() + sign * minutes);
  format = format ? format : 'YYYY-MM-DD HH:mm:ss';
  const dateString = formatDate(dateLocal, format);
  return dateString;
};


// 将世界标准时间根据时区转化，第一个参数为世界标准时间，第二个参数为要转化的时区偏移量，第三个参数为转化后的格式，
// 没有默认取机器时区偏移量
export const formatTimeToUTC = (option) => {
  let {utcTime, offset, format} = option
  if (utcTime) {
    offset = offset ? offset : getTimezone()
    const dateUTC = new Date(utcTime);
    const hours = parseInt(offset.substring(4, 6)); // 从偏移量中提取小时部分
    const minutes = parseInt(offset.substring(7, 9)); // 从偏移量中提取分钟部分
    const sign = offset[3] === '+' ? 1 : -1; // 判断偏移量的正负号
    // 根据偏移量调整时间
    dateUTC.setHours(dateUTC.getHours() + sign * hours);
    dateUTC.setMinutes(dateUTC.getMinutes() + sign * minutes);
    // 格式化输出时间
    format = format ? format : 'YYYY-MM-DD HH:mm:ss'
    const dateString = formatDate(dateUTC, format)
    return dateString;
  }
};

// 判断当前所在地区是否有夏令时
export function isDaylightSavingTime() {
  // 获取当前时区
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  // 创建一个日期对象，用于检查夏令时
  const date = new Date();
  const januaryOffset = new Date(Date.UTC(date.getFullYear(), 0, 1)).getTimezoneOffset();
  const julyOffset = new Date(Date.UTC(date.getFullYear(), 6, 1)).getTimezoneOffset();

  // 判断是否使用夏令时
  const isDST = januaryOffset !== julyOffset;

  if (isDST) {
    console.log(`${timeZone} 使用夏令时`);
    return true
  } else {
    console.log(`${timeZone} 不使用夏令时`);
    return false
  }
}