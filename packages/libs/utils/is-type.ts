export default {
  isUndefined(o: any) {
    // 是否 undefined
    return Object.prototype.toString.call(o).slice(8, -1) === 'Undefined'
  },
  isNull(o: any) {
    // 是否为 null
    return Object.prototype.toString.call(o).slice(8, -1) === 'Null'
  },
  isBoolean(o: any) {
    // 是否boolean
    return Object.prototype.toString.call(o).slice(8, -1) === 'Boolean'
  },
  isString(o: any) {
    // 是否字符串
    return Object.prototype.toString.call(o).slice(8, -1) === 'String'
  },
  isNumber(o: any) {
    // 是否数字
    return Object.prototype.toString.call(o).slice(8, -1) === 'Number'
  },
  isBigInt(o: any) {
    // 是否任意大的整数
    return Object.prototype.toString.call(o).slice(8, -1) === 'BigInt'
  },
  isSymbol(o: any) {
    // 是否唯一
    return Object.prototype.toString.call(o).slice(8, -1) === 'Symbol'
  },

  isObject(o: any) {
    // 是否对象
    return Object.prototype.toString.call(o).slice(8, -1) === 'Object'
  },
  isArray(o: any) {
    // 是否数组
    return Object.prototype.toString.call(o).slice(8, -1) === 'Array'
  },
  isFunction(o: any) {
    // 是否函数
    return Object.prototype.toString.call(o).slice(8, -1) === 'Function'
  },
  isDate(o: any) {
    // 是否时间
    return Object.prototype.toString.call(o).slice(8, -1) === 'Date'
  },
  isRegExp(o: any) {
    // 是否为正则
    return Object.prototype.toString.call(o).slice(8, -1) === 'RegExp'
  },
  isFormData(o: any) {
    // 是否FormData
    return Object.prototype.toString.call(o).slice(8, -1) === 'FormData'
  },

  isEmptyObject(o: any) {
    // 是否空对象
    // return JSON.stringify(o) === '{}'
    return this.isObject(o) && Object.keys(o).length === 0
  },
  isEmptyArray(o: any) {
    // 是否空数组
    // return JSON.stringify(o) === '[]'
    return this.isArray(o) && o.length === 0
  },
  isFalse(o: any) {
    // 是否为false
    if (!o || o === 'null' || o === 'undefined' || o === 'false' || o === 'NaN')
      return true
    return false
  },
  isTrue(o: any) {
    // 是否为 true
    return !this.isFalse(o)
  },
}
