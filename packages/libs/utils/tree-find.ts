import { IElement } from '../../lyyMobileRender/src/global'

const findSubChildrens = (prop) => {
  if (!prop) {
    return undefined
  }
  return [
    'tabs',
    'searchFormList',
    'pulldownChildren',
    'operationButtons',
    'extraList',
    'titleComp',
    'buttonList',
    'children',
    'cardList',
    'childList',
  ].reduce((prev, next) => {
    if (Array.isArray(prop[next]) && prop[next].length > 0) {
      prev.push(...prop[next])
    }
    return prev
  }, [])
}

// 此函数主要用于 整树 找单个目标组件
export const treeFind = (tree: IElement[] = [], func) => {
  for (const data of tree) {
    if (func(data)) return data
    // 普通组件
    if (data.childrens) {
      const res = treeFind(data.childrens, func)
      if (res) return res
    }
    // tab容器
    if (data.prop) {
      const target = findSubChildrens(data.prop) || []
      const res = treeFind(target, func)
      if (res) return res
    }
    if (['lyy-excel-table', 'lyy-table'].includes(data.compName)) {
      const children: any[] = []
      const operationButtons = data.prop.columns.filter(
        (item) => item.type === 'operation',
      )
      for (const item of operationButtons) {
        if (item.childrens && item.childrens.length > 0) {
          children.push(...item.childrens)
        }
      }
      treeFind(children, func)
    }
  }
}

/**
 * @description 此方法用于 找同一分支下的 父节点列表
 * @param elements 组件树
 * @param compId 目标组件id
 * @returns 返回树节点数组
 */
export const findParent = (
  elements: IElement[] = [],
  compId: string,
): IElement[] => {
  // 结果栈
  const stack: IElement[] = []
  // 是否暂停搜索
  let going = true
  const walker = (childrens, compId) => {
    for (const node of childrens) {
      if (!going) continue
      // 当前节点加入栈
      stack.push(node)
      const children = findSubChildrens(node.prop)
      if (node.compId === compId) {
        going = false
      } else if (children || node.childrens) {
        node.childrens && walker(node.childrens, compId)
        children && walker(children, compId)
      } else {
        // 如没找到，把曾经加入栈的节点取出
        stack.pop()
      }
    }
  }
  walker(elements, compId)
  return stack
}

export const treeFindPath = (
  tree: IElement[] = [],
  func,
  path: IElement[] = [],
  handleProp = true, // false: 处理只需要查找组件，并需要保持查找到的组件的响应式时
) => {
  if (!tree) return []
  handleProp && handleTree(tree)
  for (const data of tree) {
    data.compId && path.push(data)
    if (func(data)) return path
    if (data.childrens) {
      const findChildren = treeFindPath(data.childrens, func, path, handleProp)
      if (findChildren.length > 0) return findChildren
    }
    data.compId && path.pop()
  }
  return []
}
function handleTree(tree) {
  for (const t of tree) {
    if (t.compName === 'lyy-tabs') {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.tabs)
    }
    if (t.compName === 'lyy-search-pro') {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.searchFormList)
    }
    if (t.compName === 'lyy-directory-tree') {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.operationButtons)
    }
    if (t.compName === 'lyy-pull-down-selector-input') {
      if (!t.childrens) t.childrens = []
      if (!t.prop.pulldownChildren) t.prop.pulldownChildren = []
      t.childrens.push(...t.prop.pulldownChildren)
    }
    if (
      t.compName === 'lyy-card' ||
      t.compName === 'lyy-select-merchant' ||
      t.compName === 'lyy-page-header' ||
      t.compName === 'lyy-form-card'
    ) {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.extraList)
    }
    if (t.compName === 'lyy-grid' || t.compName === 'lyy-input-group') {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.children)
    }
    if (t.compName === 'lyy-form-layout') {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.cardList)
    }
    if (t.compName === 'lyy-form-card') {
      if (!t.childrens) t.childrens = []
      t.childrens.push(...t.prop.childList)
    }
  }
}
