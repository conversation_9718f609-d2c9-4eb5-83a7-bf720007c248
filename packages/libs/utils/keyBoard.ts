export const isTab = (ev) => ev.key === 'Tab'
export const isEnter = (ev) => ev.key === 'Enter'
export const isShift = (ev) => ev.key === 'Shift'
export const isShiftLeft = (ev) => ev.code === 'ShiftLeft'
export const isShiftRight = (ev) => ev.code === 'ShiftRight'
export const isCapsLock = (ev) => ev.key === 'CapsLock'
export const isCtrl = (ev) => ev.key === 'Control'
export const isControlRight = (ev) => ev.code === 'ControlRight'
export const isControlLeft = (ev) => ev.key === 'ControlLeft'
export const isAlt = (ev) => ev.key === 'Alt'
export const isAltRight = (ev) => ev.code === 'AltRight'
export const isAltLeft = (ev) => ev.code === 'AltLeft'
export const isMeta = (ev) => ev.key === 'Meta'
export const isArrowRight = (ev) => ev.key === 'ArrowRight'
export const isArrowLeft = (ev) => ev.key === 'ArrowLeft'
export const isArrowUp = (ev) => ev.key === 'ArrowUp'
export const isArrowDown = (ev) => ev.key === 'ArrowDown'
export const isEscape = (ev) => ev.key === 'Escape'
export const isDelete = (ev) => ev.key === 'Delete'
export const isBackspace = (ev) => ev.key === 'Backspace'
export const composeKey = (ev) => {
  return ev.altKey || ev.ctrlKey || ev.metaKey || ev.shiftKey
}
export const functionKeyBoard = (ev) =>
  [
    'Tab',
    'Enter',
    'Shift',
    'CapsLock',
    'Control',
    'Alt',
    'Meta',
    'ArrowRight',
    'ArrowLeft',
    'ArrowUp',
    'ArrowDown',
    'Escape',
  ].includes(ev.key) || composeKey(ev)
