import { useRequest } from '../hooks/use-ossRequest'
import { useAction } from '../hooks'
import { cloneDeep, isObject, isString } from 'lodash'
import type { UploadFile } from 'ant-design-vue'
import dayjs from 'dayjs'
import { IAction, IElement, IRequestOption } from '../../lyyPcRender/src/global'

const ALIYUN_OSS_KEY = 'aliYunOssUploadTokenResp'
const TENCENT_OSS_KEY = 'tencentCosUploadTokenResp'
type TokenObjectKey = typeof ALIYUN_OSS_KEY | typeof TENCENT_OSS_KEY
type Platform = 'aliyun' | 'tencent'
const tokenObjectKey: Record<Platform, TokenObjectKey> = {
  aliyun: ALIYUN_OSS_KEY,
  tencent: TENCENT_OSS_KEY,
}

export const fileOssUpload = async (
  option: IRequestOption,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
  fileList: any,
  fb: Function,
) => {
  const generateTimestamp = (): string => {
    return dayjs().format('YYYYMMDDHHmmssSSS')
  }

  // 处理上传后的文件
  const updateFileList = (fileList, uploadRes, comp) => {
    // 根据后端的返回值类型进行处理
    if (!fileList) return
    const len = fileList.length
    let isFieldObj = false
    if (Array.isArray(fileList)) {
      let fileListLen = len
      while (fileListLen > 0) {
        // const el = data.pop()
        isFieldObj = isObject(fileList[fileListLen - 1])
        fileList[fileListLen - 1] = transToFile(
          fileList[fileListLen - 1],
          comp,
          uploadRes[fileListLen - 1],
        ) as UploadFile
        fileListLen--
      }
    } else {
      isFieldObj = isObject(fileList)
      fileList[len - 1] = transToFile(
        fileList[len - 1],
        comp,
        uploadRes[len - 1],
      ) as UploadFile
    }
    // console.log('fileList ==============>', fileList)
    // 把文件返回
    return fileList
  }
  // 处理文件
  const transToFile = (el, comp, res) => {
    if (isString(el)) {
      return {
        url: el,
        uid: `${Date.now()}${index ?? ''}`,
        name: extname(el),
        status: res && res.fault ? 'error' : 'done',
      }
    }
    if (isObject(el)) {
      const { name = 'name', url = 'url' } = comp.prop.fieldNames ?? {}
      return {
        url: el[url] ?? el.url,
        uid: `${Date.now()}`,
        name: el[name] ?? el.ossName,
        status: res && res.fault ? 'error' : 'done',
      }
    }
  }
  // 设置文件名
  const extname = (url = '') => {
    const temp = url.split('/')
    const filename = temp.at(-1)
    const filenameWithoutSuffix = filename.split(/#|\?/)[0]
    return filenameWithoutSuffix
  }

  const uploadFileList = (option, fileList, fb) => {
    const { fieldName, data } = currentComp.prop
    const toUploadFiles = fileList?.filter((item) => item.status !== 'done')
    if (!toUploadFiles || toUploadFiles.length === 0) {
      fb && fb([])
      return
    }

    const useFetchArr = []
    for (const file of toUploadFiles) {
      const formData = new FormData()
      const fileKey = generateTimestamp() + file?.name
      file.ossName = fileKey
      if (option.platformKey === 'aliyun') {
        option.url = option.host
        option.method = 'post'
        const host: string = option.cdnHost ?? option.url
        formData.append('key', option.key ?? option.dir + fileKey)
        file.url = [
          host,
          host.endsWith('/') ? '' : '/',
          option.dir,
          fileKey,
        ].join('')
        formData.append('policy', option.policy)
        formData.append('OSSAccessKeyId', option.accessId)
        formData.append('signature', option.signature)
        formData.append('success_action_status', '200')
      } else if (option.platformKey === 'tencent') {
        option.url = option.domain
        option.method = 'post'
        const host: string = option.domain
        formData.append('key', option.key ?? option.dir + fileKey)
        file.url = [
          host,
          host.endsWith('/') ? '' : '/',
          option.dir,
          fileKey,
        ].join('')
        formData.append('policy', option.policy)
        formData.append('q-ak', option.accessKey)
        formData.append('q-sign-algorithm', option.signAlgorithm)
        formData.append('q-key-time', option.keyTime)
        formData.append('q-signature', option.signature)
      }
      const originFile = file instanceof File ? file : file.originFileObj
      formData.append(fieldName || 'file', originFile as any)
      if (data) {
        for (const [key, value] of Object.entries(data)) {
          formData.append(key, value)
        }
      }
      const submitFetch = cloneDeep(option)
      if (!submitFetch.customOption) {
        submitFetch.customOption = {}
      }
      submitFetch.customOption.allowNullResPass = true
      submitFetch.data = formData
      submitFetch.onUploadProgress = (progressEvent) => {
        // 修改进度条
        file.percent = (progressEvent.loaded / progressEvent.total) * 100 || 0
      }
      useFetchArr.push(useRequest(submitFetch))
    }

    return useFetchArr
  }

  const useFetchArr1 = []
  const submitFetch1 = cloneDeep(option)
  useFetchArr1.push(useRequest(submitFetch1))
  Promise.all(useFetchArr1)
    .then((res) => {
      const { prop } = currentComp

      const platformKey: Platform =
        (res[0].platform && typeof res[0].platform === 'string'
          ? res[0].platform.toLocaleLowerCase()
          : prop.ossPlatform) || 'aliyun'

      const tokenKey: TokenObjectKey = tokenObjectKey[platformKey]

      const res1 = {
        ...res[0][tokenKey],
        platformKey: platformKey,
      }

      // res1.url = res1?.host
      // res1.method = 'post'
      const useFetchList = uploadFileList(res1, fileList, fb)
      Promise.all(useFetchList)
        .then((res) => {
          // 上传完成后走的逻辑
          const successfulActions: IAction[] = []
          const failedActions: IAction[] = []
          const finallyActions: IAction[] = []
          const actions = currentComp.actions || []
          if (actions.length > 0) {
            for (const item of actions) {
              const type = item.belong
              if (type === 'error') {
                failedActions.push(item)
              } else if (type === 'finally') {
                finallyActions.push(item)
              } else {
                successfulActions.push(item)
              }
            }
          }
          fb && fb(updateFileList(fileList, res, currentComp))
          useAction(successfulActions, currentComp)
          return
        })
        .catch((error) => {
          fb && fb([])
          console.log('error', error)
          return
        })
        .finally(() => {})
      return
    })
    .catch((error) => {
      console.error('调用oss上传失败，错误信息：', error)
      fb && fb([])
      return
    })
}
