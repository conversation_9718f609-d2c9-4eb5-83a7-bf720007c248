import getHashCompId, { getEntityId, upDateIdx } from './compHash'
import { useStore } from '../store'
import { SHORTCUT_CODE } from '../constants/store-key'
import { LYY_FORM, M_FORM } from '../constants/component-name'
import isEdit from './is-edit'

export const preHandler = (elements: IElement[], formId?: string) => {
  const store = useStore()
  for (const item of elements) {
    item.prop = item.prop ?? {}
    const compId = item.compId
    item.compId = getHashCompId(item.compId)
    if (isEdit() && !item.entityId) {
      item.entityId = getEntityId(item)
    } else if (isEdit() && item.entityId) {
      upDateIdx(item)
    }
    if (item?.prop?.isChangeCompId === false) {
      item.compId = compId
    }
    // 组件增加oldDisabled属性
    // item.prop.oldDisabled =
    //   item?.prop.disabled === undefined ? false : item?.prop.disabled
    // 如果配置了全局的快捷键码就收集起来
    if (item?.prop?.shortcutCode) {
      const shortcutCode = store.getValue(SHORTCUT_CODE) || {}
      store.setValue(SHORTCUT_CODE, {
        [item.prop.shortcutCode]: item.compId,
        ...shortcutCode,
      })
    }
    // table
    if (
      ['lyy-table', 'lyy-table-page'].includes(item.compName) &&
      item.prop.columns
    ) {
      for (const column of item.prop.columns) {
        column.width = column.width ?? 170
        column.resizable = column.resizable ?? true

        column.customFilterDropdown = column.customFilterDropdown ?? false

        // operation
        if (column.type === 'operation') {
          for (const one of column.childrens) {
            one.prop.type = one.prop?.type ?? 'link'
          }
        }
        // 数字居右对齐
        const { pipes = [] } = column
        const hasNumber = pipes?.findIndex((item) => {
          const pipe = typeof item === 'string' ? item : item.pipe
          return ['currency', 'decimal', 'percent'].includes(pipe)
        })
        if (hasNumber > -1 || column.type === 'number') {
          column.align = 'right'
        }
      }
    }
    // 如果是表单组件 存一下formId
    if ([LYY_FORM, M_FORM].includes(item.compName)) {
      Object.assign(item.modelValue, item.prop.form)
      // item.modelValue = item.prop.form
      // formId = item.compId ?? ''
    }
    // 如果有表单id 表单组件绑定一下表单id
    // if (item?.prop?.formId || formId) {
    //   item.prop = item.prop ?? {}
    //   // 表单组件才加 formId
    //   if (item.prop.field || item.prop.fields) {
    //     item.prop.formId = item?.prop?.formId ? item?.prop?.formId : formId
    //   }
    // }
    //递归不同类型的组件
    if (item.childrens && item.childrens.length > 0) {
      preHandler(item.childrens, formId)
    }
    if (item.prop?.pulldownChildren?.length) {
      preHandler(item.prop.pulldownChildren)
    }
    if (item.prop?.tabs && item.prop.tabs.length > 0) {
      preHandler(item.prop.tabs, formId)
    }
    if (item.prop?.operationButtons && item.prop.operationButtons.length > 0) {
      preHandler(item.prop.operationButtons, formId)
    }
    if (item.prop?.extraList && item.prop.extraList.length > 0) {
      preHandler(item.prop.extraList, formId)
    }
    if (item.prop?.titleComp && item.prop.titleComp.length > 0) {
      preHandler(item.prop.extraList, formId)
    }
    if (item.prop?.searchFormList && item.prop.searchFormList.length > 0) {
      preHandler(item.prop.searchFormList, formId)
    }
    if (item.prop?.children && item.prop.children.length > 0) {
      preHandler(item.prop.children, formId)
    }
    if (item.prop?.cardList && item.prop.cardList.length > 0) {
      preHandler(item.prop.cardList, formId)
    }
    if (item.prop?.childList && item.prop.childList.length > 0) {
      preHandler(item.prop.childList, formId)
    }
  }
}
