/**
 * 配置树的遍历
 * @param config 配置树
 * @param fb 查找会调
 * @param mode 查找模式 1表示深度优先 2：表示广度优先
 * **/
export const traverse = (config, fb, mode = 1) => {
  let result = undefined
  let childrens = []
  if (!Array.isArray(config)) {
    return result
  }
  for (let i = 0; i < config.length; i++) {
    const element = config[i]
    if (fb(element)) {
      return element
    }
    if (element?.childrens) {
      if (mode === 1) {
        return traverse(element.childrens, fb, mode)
      } else if (mode === 2) {
        childrens = childrens.concat(element.childrens)
      }
    }
    // 兼容tab页增删改查中的弹窗无法关闭问题
    if (element.compName === 'lyy-tabs') {
      if (mode === 1) {
        return traverse(element.prop.tabs, fb, mode)
      } else if (mode === 2) {
        childrens = childrens.concat(element.prop.tabs)
      }
    }
    if (i == config.length - 1 && childrens.length > 0) {
      return traverse(childrens, fb, mode)
    }
  }
  return result
}
