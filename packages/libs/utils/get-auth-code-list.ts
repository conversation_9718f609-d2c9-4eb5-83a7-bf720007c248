const getCurrentPageName = function () {
  const arr = location.hash.replace('#').split('/')
  return arr.at(-1).split('?')[0]
}

export const getAuthCodeList = () => {
  let authCodeList = []
  try {
    authCodeList =
      JSON.parse(sessionStorage.getItem('service-authCodeList') ?? '{}')[
        getCurrentPageName()
      ]?.buttonAuthCode || []
  } catch {
    authCodeList = []
  }
  return authCodeList
}
