/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useAction } from '../hooks/'
import { getCompId } from '../utils/'

type CompId = string
import getHash from '../hooks/hash'
// 被监听函数
export type WatchFunction = (...args: any[]) => any

// 事件容器
class List {
  [index: CompId]: Function[]
}
// 消息中心
export class Watcher {
  private list = new List()
  private $hasFn(fnList = [], fn) {
    let res = false
    for (const fn_ of fnList) {
      if (fn_ === fn || fn_.name === fn.name) {
        res = true
        break
      }
    }
    return res
  }
  /**
   * @description 监听事件函数
   * @param key 以组件Id作为唯一值
   * @param fn 订阅事件
   */
  public $on(key: CompId, fn: WatchFunction) {
    if (!this.list[key]) {
      this.list[key] = []
    }
    if (!this.$hasFn(this.list[key] as [], fn)) {
      this.list[key].push(fn)
    }
  }
  /**
   * @description 发布事件函数
   * @param key 以组件Id作为唯一值
   * @param args 多个传入参数
   */
  public async $emit(key: CompId, ...args: any[]) {
    // const list = this.list[key] || this.list[`${getHash()}_${key}`]
    const list = this.list[key] || this.list[`${getCompId(key)}`]
    const lastArgs = args[args.length - 1]
    if (list) {
      let once = false
      for (const fn of list) {
        if (lastArgs && lastArgs?.thenActions && !once) {
          const res = await fn(...args.slice(0, -1))
          // 如果执行返回是false，则不会执行then方法
          if (!(typeof res === 'boolean' && !res)) {
            useAction(lastArgs.thenActions, lastArgs.currentComp)
            once = true
          }
        } else {
          const res = await fn(...args)
        }
      }
      list.length === 0
        ? useAction(lastArgs.thenActions, lastArgs.currentComp)
        : ''
    } else {
      useAction(lastArgs.thenActions, lastArgs.currentComp)
    }
  }
  /**
   * @description 注销事件函数
   * @param key 以组件Id作为唯一值
   * @param fn 当时注册的函数
   */
  public $remove(key: CompId, fn?: WatchFunction) {
    if (this.list[key]) {
      if (fn) {
        for (const [idx, oFn] of this.list[key].entries())
          oFn === fn && this.list[key].splice(idx, 1)
      } else {
        this.list[key].length = 0
      }
    }
  }

  /**
   * @description 清空所有的订阅事件函数 【用来暂时解决vue2实例中onBeforeUnmount（vue3钩子）不执行导致事件无法移除】
   */
  public $clear() {
    this.list = {}
  }
}
const globalWatcher = {}
const watcherKey = `Watcher${window.__aliasName__ ?? ''}`
if (!window[watcherKey]) {
  const keyUrl = getHash()
  window[watcherKey] = globalWatcher[keyUrl] = new Watcher()
}
// window.Watcher || Object.assign(window, { Watcher: new Watcher() })
export const WatcherContrl = (router) => {
  router.beforeEach((to) => {
    const keyUrl = to.fullPath.split('?')[0]
    if (!globalWatcher[keyUrl]) {
      globalWatcher[keyUrl] = new Watcher()
    }
    window[watcherKey] = globalWatcher[keyUrl]
  })
}
export default window[watcherKey]
