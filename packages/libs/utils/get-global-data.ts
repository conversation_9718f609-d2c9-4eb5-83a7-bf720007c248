import { SESSION_STORAGE } from '../constants/store-key'
import { getIn } from './get-in'
import { sessionCache } from './session-cache'
export function getGlobalData(key, path) {
  if (key === SESSION_STORAGE) {
    const nowSession = Object.keys(sessionStorage).reduce((pre, key) => {
      Object.assign(pre, { [key]: sessionCache.get(key) })
      return pre
    }, {})
    return getIn(nowSession, path)
  }
}
