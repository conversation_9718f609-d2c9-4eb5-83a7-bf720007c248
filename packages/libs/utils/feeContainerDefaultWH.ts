// 高度是1个单元格，是以rowHeight为30px
export const ReactSets = {
  'lyy-button': [1, 1],
  'lyy-popconfirm': [1, 1],
  'lyy-link': [1, 1],
  'lyy-checkbox': [2, 1],
  'lyy-label-text': [2, 1],
  'lyy-radio': [2, 1],
  'lyy-switch': [2, 1],
  'lyy-image': [2, 5],
  'lyy-qrcode': [2, 7],
  'lyy-image-previewer': [2, 7],
  'lyy-date-picker': [3, 1],
  'lyy-tags-create': [3, 1],
  'lyy-text-input': [3, 1],
  'lyy-auth-code': [4, 1],
  'lyy-time-range': [4, 1],
  'lyy-input-group': [4, 1],
  'lyy-date-range': [4, 1],
  'lyy-transfer': [5, 7],
  'lyy-input-range': [6, 1],
  'lyy-card': [12, 5],
  'lyy-form': [12, 2],
  'lyy-table': [12, 3],
  'lyy-excel-table': [12, 4],
  'lyy-file-uploader': [12, 5],
  'lyy-page-context': [12, 5],
  'lyy-editor': [12, 8],
  'lyy-rich-editor': [12, 10],
  'lyy-container': [12, 10],
  'lyy-map': [12, 12],
  'lyy-select-merchant': [12, 16],
  'lyy-echarts': [12, 11],
}

export const isNotResponsive = {
  'lyy-button': [16, 1],
  'lyy-popconfirm': [16, 1],
  'lyy-link': [16, 1],
  'lyy-checkbox': [34, 1],
  'lyy-label-text': [34, 1],
  'lyy-radio': [34, 1],
  'lyy-switch': [34, 1],
  'lyy-image': [34, 5],
  'lyy-qrcode': [34, 7],
  'lyy-image-previewer': [34, 7],
  'lyy-date-picker': [36, 1],
  'lyy-tags-create': [36, 1],
  'lyy-text-input': [44, 1],
  'lyy-formula': [36, 1],
  'lyy-auth-code': [36, 1],
  'lyy-time-range': [36, 1],
  'lyy-input-group': [36, 1],
  'lyy-date-range': [36, 1],
  'lyy-transfer': [84, 7],
  'lyy-echarts': [90, 12],
  'lyy-input-range': [100, 1],
  'lyy-card': [110, 5],
  'lyy-form': [110, 2],
  'lyy-table': [110, 3],
  'lyy-excel-table': [110, 4],
  'lyy-file-uploader': [110, 5],
  'lyy-page-context': [110, 5],
  'lyy-editor': [110, 8],
  'lyy-rich-editor': [110, 10],
  'lyy-container': [110, 10],
  'lyy-map': [110, 12],
  'lyy-select-merchant': [110, 16],
}

/**
 * 单元格个数计算公式如下
 *  if (!autoSizeFlag) {
 *     h = Math.round((height + margin.value[1]) / (rowHeight.value + margin.value[1]))
 *   } else {
 *     h = Math.ceil((height + margin.value[1]) / (rowHeight.value + margin.value[1]))
 *   }
 *  根据1个单元是30px进行反向推出rowHeight为X的时候h为多少
 */
export const getRealH = (rh, margin, basePx = 30, autoSizeFlag = false) => {
  return !autoSizeFlag
    ? Math.round((basePx + margin) / (rh + margin))
    : Math.ceil((basePx + margin) / (rh + margin))
}
