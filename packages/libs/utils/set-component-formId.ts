import { LYY_FORM, M_FORM } from '../constants/component-name'

/**
 * 设置组件formId
 * 用法: setCompentFormId(pageConfig,formId)
 * 返回: pageConfig
 * */
export const setCompentFormId = (pageConfig: IElement[]) => {
  for (const ele of pageConfig) {
    // if (ele?.prop?.formId || formId) {
    //   ele.prop = ele.prop ?? {}
    //   // 表单组件才加 formId
    //   if (ele.prop.field || ele.prop.fields) {
    //     ele.prop.formId = ele?.prop?.formId ? ele?.prop?.formId : formId
    //   }
    // }
    if ([LYY_FORM, M_FORM].includes(ele.compName)) {
      Object.assign(ele.modelValue, ele.prop.form)
      // ele.modelValue = ele.prop.form
    }
    if (ele.childrens && ele.childrens.length > 0) {
      setCompentFormId(ele.childrens)
    }
    if (ele.prop?.tabs && ele.prop.tabs.length > 0) {
      setCompentFormId(ele.prop.tabs)
    }
    if (ele.prop?.operationButtons && ele.prop.operationButtons.length > 0) {
      setCompentFormId(ele.prop.operationButtons)
    }
    if (ele.prop?.extraList && ele.prop.extraList.length > 0) {
      setCompentFormId(ele.prop.extraList)
    }
    if (ele.prop?.titleComp && ele.prop.titleComp.length > 0) {
      setCompentFormId(ele.prop.extraList)
    }
    if (ele.prop?.searchFormList && ele.prop.searchFormList.length > 0) {
      setCompentFormId(ele.prop.searchFormList)
    }
    if (ele.prop?.pulldownChildren && ele.prop.pulldownChildren.length > 0) {
      setCompentFormId(ele.prop.pulldownChildren)
    }
    if (ele.prop?.children && ele.prop.children.length > 0) {
      setCompentFormId(ele.prop.children)
    }
    if (ele.prop?.cardList && ele.prop.cardList.length > 0) {
      setCompentFormId(ele.prop.cardList)
    }
    if (ele.prop?.childList && ele.prop.childList.length > 0) {
      setCompentFormId(ele.prop.childList)
    }
  }
  // console.log(pageConfig, '配置')
  return pageConfig
}
