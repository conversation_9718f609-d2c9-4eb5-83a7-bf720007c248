import { useRequest } from '../hooks/use-request'
import { useAction } from '../hooks/use-action'
import { cloneDeep, isString, isObject } from 'lodash'
import { useStore } from '../index'
import type { UploadFile } from 'ant-design-vue'
import {
  IAction,
  IElement,
  IRequestOption,
} from '../../lyyMobileRender/src/global'
export const fileUpload = async (
  option: IRequestOption,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
  fileList: any,
  fb: Function,
) => {
  // 处理上传后的文件
  const updateFileList = (fileList, data, comp) => {
    // 根据后端的返回值类型进行处理
    if (!fileList) return
    const len = fileList.length
    let isFieldObj = false
    if (Array.isArray(data)) {
      let fileListLen = len
      while (data.length > 0) {
        const el = data.pop()
        isFieldObj = isObject(el)
        fileList[fileListLen - 1] = transToFile(el, comp) as UploadFile
        fileListLen--
      }
    } else {
      isFieldObj = isObject(data)
      fileList[len - 1] = transToFile(data, comp) as UploadFile
    }
    return fileList.map((item) => {
      const { name = 'name', url = 'url' } = comp.prop.fieldNames || {}
      const res = isFieldObj ? { [name]: item.name, [url]: item.url } : item.url
      return res
    })
  }
  // 处理文件
  const transToFile = (el, comp, index?) => {
    if (isString(el)) {
      return {
        url: el,
        uid: `${Date.now()}${index ?? ''}`,
        name: extname(el),
        status: 'done',
      }
    }
    if (isObject(el)) {
      const { name = 'name', url = 'url' } = comp.prop.fieldNames ?? {}
      return {
        url: el[url],
        uid: `${Date.now()}${index ?? ''}`,
        name: el[name],
        status: 'done',
      }
    }
  }
  // 设置文件名
  const extname = (url = '') => {
    const temp = url.split('/')
    const filename = temp[temp.length - 1]
    const filenameWithoutSuffix = filename.split(/#|\?/)[0]
    return filenameWithoutSuffix
  }
  //开始上传
  const { fieldName, data } = currentComp.prop
  const toUploadFiles = fileList?.filter((item) => item.status !== 'done')
  if (!toUploadFiles || toUploadFiles.length === 0) {
    fb && fb([])
    return
  }
  const useFetchArr = []
  for (const file of toUploadFiles) {
    const formData = new FormData()
    const originFile = file instanceof File ? file : file.originFileObj
    formData.append(fieldName || 'file', originFile as any)
    if (data) {
      for (const [key, value] of Object.entries(data)) {
        formData.append(key, value)
      }
    }
    const submitFetch = cloneDeep(option)
    submitFetch.data = formData
    useFetchArr.push(useRequest(submitFetch))
  }
  Promise.all(useFetchArr)
    .then((res) => {
      // 上传成功
      const success = res.some((item) => {
        return item?.fault ? false : true
      })
      const successfulActions: IAction[] = []
      const failedActions: IAction[] = []
      const finallyActions: IAction[] = []
      if (thenActions)
        for (const item of thenActions) {
          const type = item.belong
          if (type === 'error') {
            failedActions.push(item)
          } else if (type === 'finally') {
            finallyActions.push(item)
          } else {
            successfulActions.push(item)
          }
        }
      if (success) {
        if (option.responseDataKey) {
          const store = useStore()
          store.setValue(option.responseDataKey, res[0])
        }
        useAction(successfulActions, currentComp)
      }
      // 上传成功，并且后端有返回值
      if (success && res.some((item) => !!item)) {
        fb && fb(updateFileList(fileList, cloneDeep(res), currentComp))
        return
      }
    })
    .catch((error) => {
      fb && fb([])
      return
    })
}
