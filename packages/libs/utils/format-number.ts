import { isType } from './index'

// 格式化数字
const formatNumber = (value: any, option: any) => {
  const { force0, forceNull } = option || {}

  // falseTo0 && 0 '' null undefinded false NaN 显示 0
  if (force0 && isType.isFalse(value)) return 0

  // NaN 或 非0的false 或 0强制空 显示空
  if (
    Number.isNaN(value) ||
    (!value && value !== 0) ||
    (forceNull && value == 0)
  ) {
    return ''
  }

  if (!option) {
    // 如果计算小数点溢出，四舍五入保留 2 位小数
    const i = String(value).indexOf('.') + 1 //获取小数点的位置
    const m = String(value).length - i //获取小数点的位数
    if (m > 10) value = numToFixed(value, 2)

    return value
  }

  const { fixed, format, preappend, append } = option
  // 保留小数点位数
  const n = fixed || 0

  // 百分比
  if (format === 'percent') {
    value = numToFixed(value * 100, n) + '%'
  }

  // 千分位
  if (format === 'thousands') {
    if (!(!fixed && fixed !== 0)) {
      value = numToFixed(value * 1, n)
    }
    const valueArr = (value + '').split('.')
    const decimalPoint = valueArr[1] ? '.' + valueArr[1] : ''
    value = valueArr[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',') + decimalPoint
  }

  // 保留小数点 n 位
  if (!format && fixed) {
    value = numToFixed(value, n)
  }

  // 前缀单位
  if (preappend) {
    value = preappend + value
  }

  // 后缀单位
  if (append) {
    value = value + append
  }

  return value
}
export function numToFixed(value, length) {
  const temp = Math.pow(10, length)
  const s = Math.round(value * temp)
  return s / temp
}
export default formatNumber
