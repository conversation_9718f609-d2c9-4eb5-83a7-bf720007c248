class LocalCache {
  set(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value))
  }

  get(key: string) {
    const value = localStorage.getItem(key)
    try {
      if (value) return JSON.parse(value)
    } catch {
      return value
    }
  }

  remove(key: string) {
    localStorage.removeItem(key)
  }

  clear() {
    localStorage.clear()
  }
}

export const localCache = new LocalCache()
