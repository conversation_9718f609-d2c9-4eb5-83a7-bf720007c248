/**
 * 查找目标组件
 * 用法: findComponent(targetId)(pageConfig)
 * 返回: targetComponent or null
 * */
export const findComponent = (targetId: string) => {
  let targetComponent: IElement | null = null
  return function deepReduce(elements: IElement[]): IElement | null {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i]
      // 如果当前行有目标组件 跳出循环
      if (element.compId === targetId) {
        targetComponent = element
        break
      }
      // 如果深层行有当前组件 跳出循环
      if (element.childrens && deepReduce(element.childrens)) {
        break
      }
    }
    // 默认返回 target
    return targetComponent
  }
}
