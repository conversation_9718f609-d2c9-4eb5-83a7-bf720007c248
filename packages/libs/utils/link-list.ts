class ListNode {
  // ListNode类的构造函数，初始化节点的值和下一个节点的引用
  public val: any
  public next: null | ListNode
  public prev: null | ListNode // 增加 prev 属性
  constructor(val) {
    this.val = val
    this.next = null
    this.prev = null // 初始化 prev 属性
  }
}

// 链表类
export class LinkedList {
  // LinkedList类的构造函数，初始化链表的头节点和长度
  private head: null | ListNode
  private length: number
  constructor() {
    this.head = null
    this.length = 0
  }

  // 在链表尾部插入节点
  append(val) {
    // 创建新节点
    const newNode = new ListNode(val)
    // 如果链表不为空，遍历到链表尾部并插入新节点
    if (this.head) {
      let current = this.head
      while (current.next) {
        current = current.next
      }
      current.next = newNode
      newNode.prev = current // 设置新节点的 prev 指针
    } else {
      // 如果链表为空，将新节点作为头节点
      this.head = newNode
    }
    // 更新链表长度
    this.length++
  }

  // 根据值删除节点
  delete(val) {
    // 如果链表为空，直接返回
    if (!this.head) return
    // 如果头节点是要删除的节点，更新头节点和链表长度
    if (this.head.val === val) {
      this.head = this.head.next
      if (this.head) {
        this.head.prev = null // 更新新的头节点的 prev 指针
      }
      this.length--
      return
    }
    // 遍历链表，查找并删除匹配的节点
    let current = this.head
    while (current.next && current.next.val !== val) {
      current = current.next
    }
    // 如果找到匹配的节点，更新引用并减少链表长度
    if (current.next) {
      current.next = current.next.next
      if (current.next) {
        current.next.prev = current // 更新新的 next 节点的 prev 指针
      }
      this.length--
    }
  }

  // 遍历链表并打印节点值
  print() {
    // 遍历链表，将节点值存入数组
    let current = this.head
    const result = []
    while (current) {
      result.push(current.val)
      current = current.next
    }
    // 将节点值以字符串形式输出
    console.log(result.join(' -> '))
  }

  // 从尾部遍历链表并打印节点值
  printReverse() {
    // 遍历链表，将节点值存入数组
    let current = this.head
    if (!current) return
    while (current.next) {
      current = current.next
    }
    const result = []
    while (current) {
      result.push(current.val)
      current = current.prev
    }
    // 将节点值以字符串形式输出
    console.log(result.join(' -> '))
  }
}
