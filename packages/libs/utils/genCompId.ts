// 重新生成 compId
import _ from 'lodash'
import { uuidv4 } from './uuidv4'
export const generateNewCompId = (element) => {
  const compIds = []
  const compIdMap = {}
  iterateObject(element, compIds, compIdMap)
  return compIdMap
}
const iterateObject = (obj, compIds, compIdMap) => {
  for (const key in obj) {
    if (key === 'compId' && !compIds.includes(obj[key])) {
      compIds.push(obj[key])
      compIdMap[obj[key]] = 'c' + uuidv4()
    }
    if (_.isObject(obj[key])) {
      iterateObject(obj[key], compIds, compIdMap)
    }
    if (_.isArray(obj[key])) {
      iterateArray(obj[key], compIds, compIdMap)
    }
  }
}
const iterateArray = (array, compIds, compIdMap) => {
  if (array.length === 0) return
  for (const item of array) {
    if (_.isObject(item)) {
      iterateObject(item, compIds, compIdMap)
    }
    if (_.isArray(item)) {
      iterateArray(item, compIds, compIdMap)
    }
  }
}
