import { evaluate } from 'amis-formula'
import _ from 'lodash'

export function myeval(obj, exp) {
  const keys = Object.keys(obj)
  // 过滤掉不合法的变量名
  const args = keys.filter((item) => {
    // const reg = /^[$A-Z_a-z][\w$]*$/
    const reg = /^(?!null$|undefined$|NAN$)[$A-Z_a-z][\w$]*$/
    return reg.test(item)
  })
  const _with = `
      var { ${args} } = obj;
      // 计算结果
      return eval(exp);
    `
  // 页面初始化时，还没有数据，所以要 try 一下
  try {
    const _eval = new Function('obj', 'exp', _with)
    return _eval(obj, exp)
  } catch (error) {
    console.log('自定义js的错误提示1：', error, exp)
    try {
      return evaluate('${' + exp + '}', obj)
    } catch (error) {
      console.log('自定义js的错误提示：', error)
      return undefined
    }
  }
}

// 公式计算
export function formula(formulaStr: string) {
  const _formulaStr =
    typeof formulaStr === 'string '
      ? formulaStr.replaceAll('prop.form', 'modelValue')
      : formulaStr
  return _formulaStr
    ? (obj: any) => {
        try {
          return evaluate('${' + _formulaStr + '}', obj)
        } catch {
          return myeval(obj, _formulaStr)
        }
      }
    : () => true
}
// 模板计算
export function tpl(templateStr: string) {
  return (obj: any) => evaluate(templateStr, obj)
}
