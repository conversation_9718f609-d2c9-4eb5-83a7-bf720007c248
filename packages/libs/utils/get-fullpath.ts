type Data = {
  children?: Data[]
  [key: string]: string | number | Data[] | undefined
}
// export function getFullPath(data: Data[], id: string | number, field: string) {
//   if (Array.isArray(data)) {
//     for (const i of data) {
//       if (i[field] === id) {
//         return [id]
//       }
//       if (Array.isArray(i.children)) {
//         const res: any = getFullPath(i.children, id, field)
//         if (res.length > 0) {
//           return [i[field], ...res]
//         }
//       }
//     }
//   }
//   return []
// }

export const getFullPath = (
  arr: Data[],
  targetValue: string | number,
  field: string,
) => {
  const traverse = (
    node: Data,
    path: (string | number)[],
  ): (string | number)[] | null => {
    const currentPath = [...path, node[field] as string | number]

    // 如果是叶子节点，检查值
    if (!node.children || node.children.length === 0) {
      if (node[field] === targetValue) {
        return currentPath // 找到匹配，返回路径
      }
      return null // 没有找到
    }
    // 遍历子节点
    for (const child of node.children) {
      const result = traverse(child, currentPath)
      if (result) {
        return result // 找到匹配，返回路径
      }
    }
    return null // 没有找到
  }

  for (const item of arr) {
    const result = traverse(item, [])
    if (result) {
      return result
    }
  }

  return null // 没有找到
}
