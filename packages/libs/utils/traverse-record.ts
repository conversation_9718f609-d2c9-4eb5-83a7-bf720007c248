/**
 * 深度遍历pageConfig并根据回调生成记录
 * 例如：深度遍历pageConfig，并记录所有的compId到targetMap
 */
export const recordTarget = (elements, targetMap, fb) => {
  for (const ele of elements) {
    const { childrens, compName, prop } = ele
    fb && fb(ele, targetMap)
    if (childrens) {
      recordTarget(childrens, targetMap, fb)
    }
    if (compName === 'lyy-tabs') {
      const children = [] as any
      for (const ele of prop.tabs) {
        children.push(...ele.childrens)
      }
      recordTarget(children, targetMap, fb)
    }
    if (compName === 'lyy-search-pro') {
      recordTarget(prop.searchFormList, targetMap, fb)
    }
    if (compName === 'lyy-directory-tree') {
      recordTarget(prop.operationButtons, targetMap, fb)
    }
    if (
      compName === 'lyy-card' ||
      compName === 'lyy-select-merchant' ||
      compName === 'lyy-page-header' ||
      compName === 'lyy-form-card'
    ) {
      recordTarget(prop.extraList, targetMap, fb)
    }
    if (compName === 'lyy-pull-down-selector-input') {
      recordTarget(prop.pulldownChildren, targetMap, fb)
    }
    if (['lyy-grid', 'lyy-input-group'].includes(compName)) {
      recordTarget(prop.children, targetMap, fb)
    }
    if (compName === 'lyy-form-layout') {
      recordTarget(prop.cardList, targetMap, fb)
    }
    if (compName === 'lyy-form-card') {
      recordTarget(prop.childList, targetMap, fb)
    }
  }
}
