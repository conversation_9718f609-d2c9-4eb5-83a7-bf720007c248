// tpl方法会把&、<、>、"、‘、/进行转义，故要转义回去
const entityMap = {
  '&amp;': '&',
  '&lt;': '<',
  '&gt;': '>',
  '&quot;': '“',
  '&#39;': "'",
  '&#x2F;': '/',
}
export const decodeHtmlEntity = (encodedString) => {
  const decodedString = encodedString.replace
    ? encodedString.replaceAll(
        /(&amp;)|(&lt;)|(&gt;)|(&quot;)|(&#39;)|(&#x2F;)/g,
        function (s) {
          return entityMap[s]
        },
      )
    : encodedString
  return decodedString
}

export const copy = (text: string) => {
  const input = document.createElement('input')
  input.setAttribute('value', text)
  // eslint-disable-next-line unicorn/prefer-dom-node-append
  document.body.appendChild(input)
  input.select()
  document.execCommand('copy')
  // eslint-disable-next-line unicorn/prefer-dom-node-remove
  document.body.removeChild(input)
}
