import _ from 'lodash'
import { IElement } from '../../lyyMobileRender/src/global'
import { LYY_FORM } from '../constants/component-name'
import { PAGE_CONFIG } from '../constants/store-key'
import { getCurrentPageName, useStore } from '../store'
import { treeFind, treeFindPath } from './tree-find'
import proxyData from './proxy-data'
import isEdit from './is-edit'

export const findComponent = (compId: string): IElement => {
  const store = useStore()
  const pageName = getCurrentPageName()
  return !isEdit()
    ? proxyData.cacheData[pageName][compId]
    : treeFind(store.getValue(PAGE_CONFIG), (ele) => {
        if (ele.compId === compId || ele.entityId === compId) {
          return ele
        }
      })
}

/**
 *
 * @param compId 当前组件id
 * @returns 离当前组件最近的表单
 */
export const findParentForm = (compId: string): IElement | undefined => {
  const store = useStore()
  // 拿到form的堆栈
  const compStack = treeFindPath(
    _.cloneDeep(store.getValue(PAGE_CONFIG)),
    (node) => node.compId === compId,
  )
  // 找出属于form的组件
  const formArr = compStack.filter((comp) => comp.compName === LYY_FORM)
  return formArr.pop()
}

// 查找离当前最近的指定compName的目标组件
export const findParentComp = (compId: string, compName): IElement => {
  const store = useStore()
  // 拿到form的堆栈
  const compStack = treeFindPath(
    store.getValue(PAGE_CONFIG),
    (node) => node.compId === compId,
    [],
    false,
  )
  // 找出属于指定组件名的组件
  const comps = compStack.filter((comp) => comp.compName === compName)
  return comps.pop()
}
