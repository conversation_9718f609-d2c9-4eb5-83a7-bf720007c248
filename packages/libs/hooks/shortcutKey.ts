import { useStore } from '../store'
import { SHORTCUT_CODE, PAGE_CONFIG } from '../constants/store-key'
import { findComponent } from '../utils'
import { useAction } from './use-action'

export const shortcutKeyHandle = (shortcut) => {
  const store = useStore()
  const shortcutCodes = store.getValue(SHORTCUT_CODE)
  const compId = shortcutCodes[shortcut]
  const comp = findComponent(compId)(store.getValue(PAGE_CONFIG))
  if (comp) {
    const action = comp?.actions || []
    useAction(action, comp)
  }
}
