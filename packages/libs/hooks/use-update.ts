import { useStore } from '../index'
import {
  LYY_EDIT_TABLE,
  LYY_FORM,
  LYY_TABLE,
  LYY_TABLE_COLUMNS_SHOW,
  LYY_EXCEL_TABLE,
  LYY_CARD_LIST,
  LYY_HOC_STATUS_EXCEL_TABLE,
  M_FORM,
  M_LIST,
  M_DETAIL_LIST,
  LYY_CRM_TAG,
  LYY_SELECT_MERCHANT,
  LYY_ITERATOR,
} from '../constants/component-name'
import { DATASOURCE } from '../constants/store-key'
import proxyData from '../utils/proxy-data'
import { findParentForm } from '../utils/find-parent-id'

export const useUpdate = (newValue: any, element: IElement) => {
  const store = useStore()
  const { compId, compName, prop } = element
  const { formId, field, fields } = prop || {}
  // edit-table
  // eslint-disable-next-line unicorn/prefer-switch
  if (compName === LYY_EDIT_TABLE) {
    for (const [key, value] of Object.entries(newValue)) {
      store.setValue(compId, value, key)
      if (key === DATASOURCE) {
        store.setValue(formId, value, field)
      }
    }
    const parentForm = findParentForm(compId)
    if (parentForm && parentForm.prop.isSignChange) {
      store.setValue('formIsChange-' + parentForm.compId, true)
    }
  }

  // table card-list list detail-list
  else if (
    [
      LYY_TABLE,
      LYY_CARD_LIST,
      M_LIST,
      M_DETAIL_LIST,
      LYY_CRM_TAG,
      LYY_SELECT_MERCHANT,
      LYY_ITERATOR,
    ].includes(compName)
  ) {
    for (const [key, value] of Object.entries(newValue)) {
      store.setValue(compId, value, key)
    }
  }
  // excel-table
  else if (
    compName === LYY_EXCEL_TABLE ||
    compName === LYY_HOC_STATUS_EXCEL_TABLE
  ) {
    for (const [key, value] of Object.entries(newValue)) {
      store.setValue(compId, value, key)
    }
    const parentForm = findParentForm(compId)
    if (parentForm && parentForm.prop.isSignChange) {
      store.setValue('formIsChange-' + parentForm.compId, true)
    }
  }
  // form
  else if ([LYY_FORM, M_FORM, LYY_TABLE_COLUMNS_SHOW].includes(compName)) {
    store.setValue(compId, newValue)
  }

  // other
  else {
    element.modelValue = newValue
    const parentForm = findParentForm(compId)

    if (!parentForm) {
      return
    }
    if (parentForm.prop.isSignChange) {
      store.setValue('formIsChange-' + parentForm.compId, true)
    }
    if (fields && fields.length > 0) {
      fields.forEach((field: string, index) => {
        store.setValue(
          parentForm.compId,
          newValue ? newValue[index] : '',
          field,
        )
        proxyData.setProxyData(
          `${parentForm.compId}.modelValue.${field}`,
          newValue ? newValue[index] : '',
        )
      })
    } else if (field) {
      store.setValue(parentForm.compId, newValue, field)
      proxyData.setProxyData(
        `${parentForm.compId}.modelValue.${field}`,
        newValue,
      )
    }
  }
}
