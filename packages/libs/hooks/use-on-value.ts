import _ from 'lodash'
import { evaluate } from 'amis-formula'
/**
 *
 * @param scopeValue 公式能取到的作用域的值
 * @param calcFormula 值的计算公式
 * @returns string | number 返回计算后的值
 */
export function _useOnValue(scopeValue, calcFormula?: string) {
  if (_.isUndefined(calcFormula)) {
    return 0
  }
  const _calcFormula = calcFormula.replace(/prop\.form/g, 'modelValue')
  try {
    if (
      _.isArray(scopeValue) &&
      scopeValue.length > 0 &&
      _.isObject(scopeValue[0])
    ) {
      const dataObj = {}
      for (const [i, obj] of scopeValue.entries()) {
        for (const k of Object.keys(obj)) {
          if (obj[k]) {
            dataObj[k] ? dataObj[k].push(obj[k]) : (dataObj[k] = [obj[k]])
          }
        }
      }
      return evaluate('${' + _calcFormula + '}', dataObj)
    } else {
      const result = evaluate('${' + _calcFormula + '}', scopeValue)
      return result
    }
  } catch (error) {
    console.warn('计算公式错了哦 ' + _calcFormula + '====' + error, scopeValue)
    return null
  }
}
