import { formula } from '../utils/'
import proxyData from '../utils/proxy-data'

export const useShow = (element: IElement, data?: IObject) => {
  let display = true
  const { prop } = element
  //应急处理，处理表格操作列显隐不生效问题
  if (prop?.show && prop?.show?.exp && data) {
    return formula(prop.show.exp)(
      Object.assign(proxyData.getProxyData('useShow --> use-show'), data),
    )
  }
  if (prop?.show && prop?.show?.exp) {
    const { exp } = prop.show
    display = formula(exp)(proxyData.getProxyData('useShow --> use-show'))
  }
  return display
}
