import { ACTION, UPDATE_MODELVALUE } from '../constants/action-type'
import { UPDATE, MOUNTED } from '../constants/event-type'

let emit = (string, any) => {}
export const setEmit = (fn) => {
  emit = fn
}
export const setDataBaseFn = (data) => {
  emit(UPDATE_MODELVALUE, data)
  emit(ACTION, { event: UPDATE })
}
// 清空
export const clear = () => {
  setDataBaseFn('')
}
// 重置
export const reset = (defaultValue) => {
  setDataBaseFn(defaultValue)
}
// 赋值
export const setData = (data) => {
  setDataBaseFn(data)
}
// 重新加载
export const reLoad = () => {
  emit(ACTION, { event: MOUNTED })
}
export const actionsSet = {
  clear,
  reset,
  setData,
  reLoad,
}
