import { getIn, localCache, useStore } from '../index'
import {
  LYY_EDIT_TABLE,
  LYY_FORM,
  LYY_TABLE,
  M_FORM,
  M_LIST,
  M_DETAIL_LIST,
  LYY_TABLE_COLUMNS_SHOW,
  LYY_EXCEL_TABLE,
  LYY_DATA_SHEET,
  LYY_ITERATOR,
  // LYY_FILE_UPLOADER,
} from '../constants/component-name'
import { DATASOURCE } from '../constants/store-key'
import { findParentForm } from '../utils/find-parent-id'
import proxyData from '../utils/proxy-data'
import { nextTick, reactive } from 'vue'
import isEdit from '../utils/is-edit'

const isEditMixin = (fn) => {
  if (isEdit()) {
    nextTick(() => {
      fn()
    })
  } else {
    fn()
  }
}
const getFiledValue = ({ isEdite, field, fields, form_Id, store }) => {
  if (isEdite) {
    // 因为编辑状态下对prop.form和modelValue进行了隔离，取值的时候需要从modelValue取值
    const dataFormSource =
      proxyData.getProxyData('getFiledValue --> use-value')[`${form_Id}`]
        ?.modelValue || {}
    return fields && fields.length > 0
      ? fields.map((field: string) => getIn(dataFormSource, field))
      : getIn(dataFormSource, field)
  } else {
    return fields && fields.length > 0
      ? fields.map((field: string) => store.getValue(form_Id, field))
      : store.getValue(form_Id, field)
  }
}

export const useValue = (element: IElement, isEdite?) => {
  const store = useStore()
  const { compId, compName, prop, modelValue } = element
  const { field, fields, formId } = prop || {}
  if (
    [
      LYY_FORM,
      LYY_TABLE,
      M_FORM,
      M_LIST,
      M_DETAIL_LIST,
      LYY_TABLE_COLUMNS_SHOW,
    ].includes(compName)
  ) {
    if ([LYY_TABLE, M_DETAIL_LIST].includes(compName)) {
      const value = proxyData.getValueFromProxyData(`${compId}.modelValue`)
      if (!value || !value?.selectedRows) {
        const showDataIndexList = localCache.get(`${compId}showDataIndexList`)
        isEditMixin(() => {
          proxyData.setProxyData(
            `${compId}.modelValue`,
            reactive({
              datasource: value?.datasource ? [...value.datasource] : [],
              isShowDetail: showDataIndexList && showDataIndexList.isShowDetail,
              exportColumns: value?.exportColumns
                ? [...value.exportColumns]
                : [],
              selectedRows: value?.selectedRows ? [...value.selectedRows] : [],
              selectedRowKeys: [],
              filters: [],
              sortOptions: [],
            }),
          )
        })
      }
      return proxyData.getValueFromProxyData(`${compId}.modelValue`)
    }
    if (compName === LYY_FORM) {
      return modelValue
    }
    return store.getValue(compId)
  }

  // edit table
  else if ([LYY_EDIT_TABLE, LYY_ITERATOR].includes(compName)) {
    if (formId && field) {
      return { [DATASOURCE]: store.getValue(formId, field) }
    }
    return store.getValue(compId)
  }
  // excel表格
  else if ([LYY_EXCEL_TABLE].includes(compName)) {
    const value = proxyData.getValueFromProxyData(`${compId}.modelValue`)
    let datasource = []
    if (formId && field) {
      datasource = store.getValue(formId, field) || []
    }
    if (!value) {
      isEditMixin(() => {
        proxyData.setProxyData(
          `${compId}.modelValue`,
          reactive({
            datasource: [],
            selectedRows: [],
          }),
        )
      })
    } else if (datasource.length > 0) {
      value.datasource = datasource
    }
    return proxyData.getValueFromProxyData(`${compId}.modelValue`)
  } else if ([LYY_DATA_SHEET].includes(compName)) {
    const value = proxyData.getValueFromProxyData(`${compId}.modelValue`)
    if (!value) {
      isEditMixin(() => {
        proxyData.setProxyData(
          `${compId}.modelValue`,
          reactive({
            datasource: [],
            selectedRows: [],
            currentRow: {},
          }),
        )
      })
    }
    return proxyData.getValueFromProxyData(`${compId}.modelValue`)
  } else if (fields || field) {
    const form_Id = formId ? formId : findParentForm(compId)?.compId
    if (form_Id) {
      const value = getFiledValue({ isEdite, field, fields, form_Id, store })
      return value
    }
  }
  // form item
  else if (formId) {
    return getFiledValue({ isEdite, field, fields, form_Id: formId, store })
  }
  // other
  return modelValue
}
