import { merge, forEach, cloneDeep } from 'lodash'
import httpConfig from '../../lyyPcRender/src/http-config'
import {
  defaultConfig,
  del,
  get,
  head,
  patch,
  post,
  put,
} from '../../lyyPcRender/src/service'
import { getHigherValue, getNewValue } from '../actions/set-new-value'
import { extractVariable, replaceUrl } from '../utils'
import { useStore } from '../index'
const filteData = (data) => {
  if (data) {
    for (const [key, value] of Object.entries(data)) {
      if (key === null || key === '') {
        delete data[key]
      }
    }
  }
  return data
}
export async function useRequest(config: IRequestConfig) {
  // 合并配置
  const systemConfig = httpConfig.getHttpConfig() || {}
  // 先处理系统配置的请求头和单个接口的请求头
  const systemConfigHeaders = getHigherValue(systemConfig?.headerPayloads || [])
  const configHeaders = filteData(getHigherValue(config?.headerPayloads || []))

  const requestConfig = merge({}, defaultConfig, systemConfig, config)
  // // 本地配置
  // if (window.ipcRenderer && localStorage.localConfig) {
  //   try {
  //     requestConfig = merge(
  //       {},
  //       defaultConfig,
  //       systemConfig,
  //       config,
  //       JSON.parse(localStorage.localConfig),
  //     )
  //   } catch (error) {
  //     console.log(error)
  //   }
  // }
  const { method, payloads } = requestConfig
  // 处理参数
  let payload = filteData(cloneDeep(getNewValue(payloads)))
  // 处理：处理参数值的前后空格
  if (!requestConfig?.customOption?.notTrim) {
    forEach(payload, (v, k) => {
      if (typeof v === 'string') {
        payload[k] = v.trim()
      }
    })
  }
  // 如果设置了环境和主域名，并且URL是已${开头
  if (
    systemConfig.env &&
    systemConfig.hosts &&
    /^\s*\${/.test(requestConfig.url)
  ) {
    const host = extractVariable(requestConfig.url)[0]
    requestConfig.url = requestConfig.url.replace(
      /\s*\${[^{}]+}\s*/,
      systemConfig.hosts[host][systemConfig.env],
    )
  }
  const { url, replacedFields } = replaceUrl(requestConfig.url, payload)
  for (const field of replacedFields) delete payload[field]

  // 处理 headers
  const headers = merge({}, systemConfigHeaders, configHeaders)
  payload = payload && Object.keys(payload).length > 0 ? payload : undefined
  Object.assign(requestConfig, { url, payload, headers })

  switch (method!.toUpperCase()) {
    case 'GET': {
      return await get(requestConfig)
    }
    case 'POST': {
      return await post(requestConfig)
    }
    case 'DELETE': {
      return await del(requestConfig)
    }
    case 'PUT': {
      return await put(requestConfig)
    }
    case 'PATCH': {
      return await patch(requestConfig)
    }
    case 'HEAD': {
      return await head(requestConfig)
    }
    default: {
      return await post(requestConfig)
    }
  }
}

export const useDataSetRequest = async (datasetOption) => {
  const res = await useRequest(datasetOption)
  if (datasetOption.responseDataKey) {
    const store = useStore()
    store.setValue(datasetOption.responseDataKey, res)
  }
  return res
}
