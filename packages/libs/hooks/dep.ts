/* eslint-disable @typescript-eslint/no-explicit-any */
type CompId = string
import { type WatchFunction } from '../utils/Watcher'
const watcherKey = `Watcher${window.__aliasName__ || ''}`
export class Action {
  compId: CompId = ''
  type?: string
  params?: any
  pipe?: any
}

/**
 * @description 注册联动模式
 * @param compId 组件唯一id
 * @param watcherFn 订阅函数
 */
export function useDep(compId: string, watcherFn: WatchFunction) {
  window?.Watcher?.$on(compId, watcherFn)
  window[watcherKey]?.$on(compId, watcherFn)
}

/**
 * @description 执行联动模式内的函数
 * @param Action json定义联动动作
 * @param params 需要传递到新组件的值
 */
export async function emitDep(actions?: Action[], params?: any) {
  actions &&
    actions.forEach((action) => {
      let Watcher = window.Watcher
      if (window[watcherKey]) {
        Watcher = window[watcherKey]
      }
      Watcher.$emit(
        action.compId,
        action.type,
        params,
        action.params,
        action.pipe,
      )
    })
}
export async function removeDep(key: CompId, fn?: WatchFunction) {
  let Watcher = window.Watcher
  if (window[watcherKey]) {
    Watcher = window[watcherKey]
  }
  Watcher.$remove(key, fn)
}
