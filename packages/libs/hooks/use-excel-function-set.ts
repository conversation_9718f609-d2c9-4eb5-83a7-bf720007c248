import { _useOnValue } from './index'
/**
 * @description 合计求和
 * @param list array 列数组
 * @param field string 列的字段属性
 * @param exp string 表达式
 * @param expDataSourceKey string 键值
 * @returns sum number 返回值
 * **/
export const sumNum = (
  list: any[],
  field = '',
  exp?: string,
  expDataSourceKey?: string,
  column?: object,
) => {
  let sum = 0
  if (exp) {
    for (const item of list) {
      let tempRes = 0
      try {
        tempRes = expDataSourceKey
          ? _useOnValue(item[expDataSourceKey], exp)
          : _useOnValue(item, exp)
        // tempRes = evaluate('${' + exp + '}', item)
      } catch {
        continue
      }
      sum += tempRes
        ? Number(tempRes)
        : item[field] && column?.editRender
        ? Number(item[field])
        : 0
    }
  } else if (field) {
    for (const item of list) {
      sum += Number(item[field] || '')
    }
  }
  return sum
}
/**
 * @description 设置属性
 * @param target Object 目标对象
 * @param key string 属性key
 * @param descriptor 属性描述
 * ***/
export const setProperty = (target, key, descriptor) => {
  Object.defineProperty(target, key, descriptor)
}
/**
 * @description 去掉千分位
 * @param num string 需要去掉千分位间隔的数字
 * @param pat string 千分位分割符
 * **/
export const clearThousand = (num, pat = ',') => {
  if (isNaN(Number(num)) && typeof num == 'string') {
    return num.replace(pat, '')
  }
  return num
}
/**
 * @description 判断是否是有效行
 * @param fileds array 列字段集
 * @returns function
 * **/
export const isEffectRowHook = (fileds = []) => {
  const exp = fileds.map((v) => `row.${v}`).join('||')
  const exps = exp ? `return !!(${exp})` : 'return false'
  const fn = new Function('row', exps)
  return (row) => {
    return fn(row)
  }
}
/**
 * @description 获取当前行
 * @param xTable Object 表格对象实例
 * **/
export const getCurrentEvent = (xTable) => {
  const $table = xTable.value
  return $table.getCurrentRecord()
}
