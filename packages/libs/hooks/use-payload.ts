import { useStore } from '../store'
import { dataType } from '../utils/'

// 待完善
export function usePayload(payloads: IPayload[] = []) {
  const payload: any = {}
  const store = useStore()

  payloads?.forEach((item) => {
    const { key, value, source, sourceKey, decompose, type } = item

    // 静态值处理
    if (!source && key) {
      payload[key] = value
    }

    // 从某处取值
    if (source) {
      const value = store.getValue(source, sourceKey)

      // val 强制转数组
      if (type === 'array' && key) {
        payload[key] = [value]
      } else {
        // 数据类型
        const valueType: string = dataType(value)
        switch (valueType) {
          case 'Object': {
            // 解构
            if (decompose) {
              Object.assign(payload, value)
              break
            }
            const k = key || sourceKey



            const obj = k
              ? {
                  [k]: value
                }
              : value
            Object.assign(payload, obj)
            break
          }
          default:
            const k = key || sourceKey
            k && (payload[k] = value)
            break
        }
      }
    }
  })

  return payload
}
