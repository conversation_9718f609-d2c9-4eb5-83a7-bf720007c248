import proxyData from '../utils/proxy-data'
import { tpl } from '../utils'
import { myeval } from '../utils/compiler'
import { isString } from 'lodash'

const entityMap = {
  '&amp;': '&',
  '&lt;': '<',
  '&gt;': '>',
  '&quot;': '“',
  '&#39;': "'",
  '&#x2F;': '/',
}
const decodeHtmlEntity = (encodedString) => {
  if (!encodedString) return encodedString
  const decodedString = encodedString.replaceAll(
    /(&amp;)|(&lt;)|(&gt;)|(&quot;)|(&#39;)|(&#x2F;)/g,
    function (s) {
      return entityMap[s]
    },
  )
  return decodedString
}

export const useTpl = (
  prop: string,
  scopeData = {},
  type?: 'object' | 'string',
) => {
  const _prop =
    typeof prop === 'string' ? prop.replaceAll('porp.form', 'modelValue') : prop
  const data = proxyData.getProxyData()
  const payload = {
    ...scopeData,
    ...data,
  }
  if (type && type === 'object') {
    const reg = /{{(.*?)}}/g
    if (!reg.test(_prop)) {
      return _prop
    }
    const target = _prop.replaceAll(reg, function (match, p1) {
      return JSON.stringify(myeval(payload, p1))
    })
    return eval(`(${target})`)
  }
  if (type && type === 'string') {
    const reg = /{{(.*?)}}/g
    const target = _prop.replaceAll(reg, function (match, p1) {
      const result = myeval(payload, p1)
      return isString(result) ? result : JSON.stringify(myeval(payload, p1))
    })
    return target
  }

  return decodeHtmlEntity(tpl(_prop)(payload))
}
