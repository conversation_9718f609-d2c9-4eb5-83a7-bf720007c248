import { toRaw } from 'vue'
import { useRequest } from './index'
import jsonpath from 'jsonpath'
import _ from 'lodash'
import { useStore } from '../index'

function myeval(scopeValue, exp) {
  if (!cacheParameters(scopeValue, exp)) {
    return false
  }
  const args = Object.keys(scopeValue)
  const _with = `
      var { ${args} } = scopeValue;
      // 计算结果
      return eval(exp);
    `
  const _eval = new Function('scopeValue', 'exp', _with)
  try {
    useStore().setValue(exp, _.cloneDeep(scopeValue))
    return _eval(scopeValue, exp)
  } catch {
    return false
  }
}
// 判断参数值是否和之前的值一致 不一致才可走下一步
function cacheParameters(scopeValue, exp) {
  const scopeData = useStore().getValue(exp)
  const reg = /[!&()*+/:?|\-]+/g
  const keys = exp.replace(/\s+/g, '').split(reg)
  try {
    return keys.some((e) => scopeValue[e] != scopeData[e])
  } catch {
    return true
  }
}
async function getData(fetch, rowData?, update?) {
  const data = await useRequest(fetch)
  // const _rowData = toRaw(rowData)
  if (data) {
    if (_.isArray(fetch.result)) {
      for (const res of fetch.result) {
        const val = jsonpath.value(data, res.sourceKey)
        rowData[res.key] = val
      }
      update && update()
      return rowData
    }
    if (_.isString(fetch.result)) {
      return jsonpath.value(data, fetch.result)
    }
    if (_.isUndefined(fetch.result)) {
      return data
    }
  } else {
    return null
  }
}

// 触发计算
export function emitOnFetch(rowData, columns, update) {
  // console.log(toRaw(rowData), columns, '数据哈哈哈')
  for (const column of columns) {
    if (column.onfetch && myeval(toRaw(rowData), column.onfetch.exp)) {
      getData(column.onfetch.fetch, rowData, update)
    }
  }
}
// 组件异步拉代码
export async function emitCompOnFetch(data, onfetch) {
  const { fetch, exp } = onfetch
  if (exp) {
    return myeval(data, exp) ? getData(fetch) : ''
  } else {
    return getData(fetch)
  }
}
