// 快速校验
import { VXETable } from 'vxe-table'

export const validEvent = async (xTable, rows) => {
  const $table = xTable.value
  const errMap = await $table.validate(rows)
  if (errMap) {
    const msgList: string[] = []
    Object.values(errMap).forEach((errList: any) => {
      errList.forEach((params: any) => {
        const { rowIndex, column, rules } = params
        rules.forEach((rule: any) => {
          msgList.push(
            `第 ${rowIndex + 1} 行 ${column.title} 校验错误：${rule.message}`,
          )
        })
      })
    })
    VXETable.modal.message({
      status: 'error',
      slots: {
        default() {
          return [`${msgList.map((msg) => `${msg}\n`)}`]
        },
      },
    })
    return false
  } else {
    return true
  }
}
// 完整的快速校验
export const fullValidEvent = async (xTable, rows = true) => {
  const $table = xTable.value
  const errMap = await $table.fullValidate(rows)
  if (errMap) {
    const msgList: string[] = []
    Object.values(errMap).forEach((errList: any) => {
      errList.forEach((params: any) => {
        const { rowIndex, column, rules } = params
        rules.forEach((rule: any) => {
          msgList.push(
            `第 ${rowIndex + 1} 行 ${column.title} 校验错误：${rule.message}`,
          )
        })
      })
    })
    VXETable.modal.message({
      status: 'error',
      slots: {
        default() {
          return [`${msgList.map((msg) => `${msg}\n`)}`]
        },
      },
    })
    return false
  } else {
    return true
  }
}
// 完整的快速校验
export const clearValidate = async (xTable) => {
  const $table = xTable.value
  const errMap = await $table.clearValidate()
  if (errMap) {
  } else {
    VXETable.modal.message({ status: 'success', content: '校验成功！' })
  }
}
