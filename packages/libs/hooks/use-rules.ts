import proxyData from '../utils/proxy-data'
import { formula } from '../utils'
import { usePayload } from './use-payload'

export const dynamicRules = (prop, dynamicRules = []) => {
  const rules = dynamicRules.filter((item) => !!item.exp)
  return rules?.map((item) => {
    const validator = () => {
      const { exp, payloads, message } = item
      // const payload = usePayload(payloads)
      const payload = prop.rowData
        ? {
            ...prop.rowData,
            ...proxyData.getProxyData('dynamicRules --> use-rules'),
          }
        : proxyData.getProxyData('dynamicRules --> use-rules')
      const res = formula(exp)(payload)
      return res ? Promise.reject(message) : Promise.resolve()
    }
    return {
      validator,
      ...item,
    }
  })
}
export const onRules = (onrules = []) => {
  return onrules.reduce((prev, cur) => {
    const { exp, payloads } = cur
    const payload = usePayload(payloads)
    if (formula(exp)(payload)) {
      prev.push(cur)
    }
    return prev
  }, [])
}
