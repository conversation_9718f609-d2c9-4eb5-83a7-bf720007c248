import {
  broadcast,
  closeModal,
  download,
  linkto,
  openModal,
  request,
  resetValue,
  setValue,
  validate,
  upload,
  print,
  openConfirm,
  depAction,
  htmlToPdf,
  closeDrawer,
  copyToClipboard,
  qtTrackAction,
  requestDataset,
} from '../actions/'
import { setNewValue } from '../actions/set-new-value'
import { customerJs } from '../actions/customer-js'
import {
  BROADCAST,
  CLOSE_MODAL,
  DOWNLOAD,
  LINK_TO,
  OPEN_MODAL,
  RELOAD,
  REQUEST,
  RESET_VALUE,
  SET_VALUE,
  VALIDATE,
  UPLOAD,
  PRINT,
  DEP_ACTION,
  HTML_TO_PDF,
  SET_NEW_VALUE,
  OPEN_DRAWER,
  CLOSE_DRAWER,
  CUSTOMER_JS,
  COPY_TO_ClIPBOARD,
  QT_TRACK,
  RUN_JSOBJECT,
  REQUESTDATASET,
  GlOBAL_DEP_ACTION,
} from '../constants/action-type'
import { formula } from '../utils/'
import { usePayload } from './use-payload'
import proxyData from '../utils/proxy-data'
import { runJsObject } from '../actions/run-jsobject'

export const useAction = (
  actions: IAction[],
  currentComp: IElement,
  ...args
) => {
  actions?.forEach(async (item) => {
    const { action, option, thenActions, condition } = item
    const { exp, payloads } = condition ?? {}
    if (Object.keys(option).length === 0) return
    // 正常流程
    if (exp) {
      let triggered = false
      // const payload = payloads?.reduce((final, payload) => {
      //   // 取静态值 或者 动态取值
      //   return Object.assign(final, {
      //     [payload.key]:
      //       proxyData.getValueFromProxyData(payload.value) || payload.value,
      //   })
      // }, {})
      triggered = formula(exp)(proxyData.getProxyData('useAction -> exp'))
      if (!triggered) return
    }

    switch (action) {
      case OPEN_MODAL: {
        openModal(option)
        break
      }

      case CLOSE_MODAL: {
        closeModal(option)
        break
      }
      case OPEN_DRAWER: {
        openModal(option)
        break
      }

      case CLOSE_DRAWER: {
        closeDrawer(option)
        break
      }
      case 'openConfirm': {
        openConfirm(option, useAction, thenActions, currentComp)
        break
      }

      case LINK_TO: {
        linkto(option, useAction, thenActions, currentComp)
        break
      }
      case QT_TRACK: {
        qtTrackAction(option, useAction, thenActions, currentComp)
        break
      }

      case VALIDATE: {
        validate(option, useAction, thenActions, currentComp)
        break
      }

      case REQUEST: {
        request(option, useAction, thenActions, currentComp, 0)
        break
      }
      case REQUESTDATASET: {
        requestDataset(option, useAction, thenActions, currentComp, 0)
        break
      }
      case SET_VALUE: {
        setValue(undefined, option, currentComp)
        break
      }

      case SET_NEW_VALUE: {
        setNewValue(option, useAction, thenActions, currentComp)
        break
      }

      case RESET_VALUE: {
        resetValue(option)
        break
      }
      case CUSTOMER_JS: {
        customerJs(option, useAction, thenActions, currentComp, args)
        break
      }

      case BROADCAST: {
        broadcast(option)
        break
      }

      case DOWNLOAD: {
        download(option, useAction, thenActions, currentComp)
        break
      }

      case RELOAD: {
        location.reload()
        break
      }

      case UPLOAD: {
        upload(option, useAction, thenActions, currentComp)
        break
      }

      case PRINT: {
        print(option, useAction, thenActions, currentComp)
        break
      }
      case DEP_ACTION: {
        depAction(option, useAction, thenActions, currentComp)
        break
      }
      case HTML_TO_PDF: {
        htmlToPdf(option as IHtmlToPdfOption)
        break
      }
      case COPY_TO_ClIPBOARD: {
        copyToClipboard(option, useAction, thenActions, currentComp)
        break
      }
      case RUN_JSOBJECT: {
        runJsObject(option, useAction, thenActions, currentComp)
        break
      }
      case GlOBAL_DEP_ACTION: {
        depAction(option, useAction, thenActions, currentComp)
        break
      }

      default: {
        break
      }
    }

    const isAsync = [
      VALIDATE,
      REQUEST,
      REQUESTDATASET,
      DOWNLOAD,
      'openConfirm',
      DEP_ACTION,
      UPLOAD,
      SET_NEW_VALUE,
      CUSTOMER_JS,
    ].includes(action)
    if (!isAsync && thenActions) {
      useAction(thenActions, currentComp)
    }
  })
}
