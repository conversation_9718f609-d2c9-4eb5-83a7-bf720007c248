import type { Component } from 'vue'
import type EventType from '../../templateEditor/src/enum/event-type'

interface ModuleBase {
  [key: string]: string | number | Array<object> | Boolean | null | ModuleBase
}

export type Module = ModuleBase & { compName: string }

type PropsDictionary = { name: string; type: string }

type DepAction = {
  [key: string]: string
}

export interface CustomComponentRegisterObject {
  component: Component
  module: Module
  propsDictionary?: Record<string, PropsDictionary>
  emits?: (keyof typeof EventType)[]
  custom?: Boolean
  editAttrCom?: Component
  editActionCom?: Component
  depAction?: DepAction
}

type DefineComponent = (
  config: CustomComponentRegisterObject,
) => CustomComponentRegisterObject

export const defineCustomComponent: DefineComponent = (config) => config
