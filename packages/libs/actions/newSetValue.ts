import proxyData from '../utils/proxy-data
import { getIn } from '../utils/get-in'
import { setIn } from '../utils/set-in'

interface INewSetValue {
  from: {
    target: string
    path: string
  }
  to: {
    target: string
    path: string
  }
}
export const newSetValue = (option: INewSetValue) => {
  const data = getIn(proxyData, `${option.from.target}.${option.from.path}`)
  setIn(proxyData, `${option.to.target}.${option.to.path}`,data)
}
