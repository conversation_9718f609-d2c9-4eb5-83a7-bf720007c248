import { IAction, IElement } from '../../lyyPcRender/src/global'
import proxyData from '../utils/proxy-data'
import { tpl, decodeHtmlEntity, copy } from '../utils'
import { message } from 'ant-design-vue'

declare const document: Document

interface copyToClipboardOption {
  target: string
}

export const copyToClipboard = (
  option: copyToClipboardOption,
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const { target } = option
  const data = proxyData.getProxyData('copyToClipboard --> copy-to-clipboard')
  const value = target ? decodeHtmlEntity(tpl(target)(data)) || '' : target
  copy(value)
  message.success('复制成功')
  thenActions && useAction(thenActions, currentComp)
}
