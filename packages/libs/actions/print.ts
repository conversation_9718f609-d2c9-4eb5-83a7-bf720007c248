import { getIn } from '../utils'
import { useRequest } from '../hooks/'
import { setValue } from './set-value'
import { message } from 'ant-design-vue'
import proxyData from '../utils/proxy-data'
// useAction、thenActions 待完善
export const print = async (
  config: IRequestOption,
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const { customOption } = config
  const { downloadType, fileName: _fileName, jsonPath } = customOption ?? {}

  const response: any = await useRequest(config)

  // 后端返回的是一个 url 地址
  if (typeof response === 'string') {
    handleResponseData(config, currentComp, response)
    return
  }
  if (response.data.size > 100) {
    message.success({ content: '生成成功', key: 'print', duration: 2 })
    handleResponseData(config, currentComp, getObjectURL(response.data))
    return
  } else {
    const reader = new FileReader()
    reader.readAsText(new Blob([response]), 'utf-8')
    reader.addEventListener('load', (res) => {
      const { result } = res.target // 得到字符串
      const data = JSON.parse(result) // 解析成json对象
      const { msgKey = 'description' } = customOption || {}
      message.error({ content: data[msgKey], key: 'print', duration: 2 })
    })
  }
}
const getObjectURL = (data) => {
  let url = null
  const file = new Blob([data], { type: 'application/pdf;chartset=utf-8' })
  if (window.createObjectURL != undefined) {
    // 通用
    url = window.createObjectURL(file)
  } else if (window.webkitURL != undefined) {
    // 兼容谷歌
    try {
      url = window.webkitURL.createObjectURL(file)
    } catch {}
  } else if (window.URL != undefined) {
    // 兼容其他
    try {
      url = window.URL.createObjectURL(file)
    } catch {}
  }
  // 将转化后url赋值
  return url
}
const handleResponseData = (
  option: IResponseSetValue,
  currentComp: IElement,
  data: any,
) => {
  const { sourceKey, targetId, targetKey, targetPath, targets, type, index } =
    option
  const setValueOption = {
    targetId,
    targetKey,
    targetPath,
    targets,
    type,
    index,
  }
  const newValue = sourceKey ? getIn(data, sourceKey) : data
  proxyData.setProxyData(
    `${currentComp.compId}.prop.src`,newValue,
  )
  // setValue(newValue, setValueOption, currentComp)
}
