import { tpl as tplFn, getIn, setIn } from '../utils'
import proxyData from '../utils/proxy-data'
import _ from 'lodash'
import { evaluate } from 'amis-formula'
type nodePath = string

export interface IProxyDataHigher {
  key: string
  value: string | nodePath
}

export type SetNewValueOption = {
  from: {
    dynamic?: {
      nodePath: string // 资源路径
    }
    higher?: IProxyDataHigher[]
    static?: string
  }
  to: {
    type: 'static' | 'dynamic' | 'higher' // 简易设置 高级设置
    dynamic: {
      nodePath: string // 资源路径
    }
  }
  arrayMergeType: 'concact' | 'default'
}
export type getNewValueOption = {
  type: 'static' | 'dynamic' | 'higher' // 简易设置 高级设置
  dynamic?: {
    nodePath: string // 资源路径
  }
  higher?: IProxyDataHigher[]
  static?: string
}

// 静态模板
const staticFn = (tpl: string, toPath: string, arrayMergeType: string) => {
  const data = proxyData.getProxyData('staticFn --> set-new-value')
  // 拿到目标值
  const _tpl = transPropFormPath(tpl)
  const _toPath = transPropFormPath(toPath)
  const fromValue = _tpl ? tplFn(_tpl)(data) ?? '' : _tpl
  const toValue = getIn(data, _toPath)
  if (_.isArray(toValue) && fromValue) {
    if (arrayMergeType !== 'concact') {
      toValue.length = 0
    }
    toValue.push(...fromValue)
    return
  }
  // 设置值
  setIn(data, toPath, fromValue)
}
// 路径赋值
const dynamic = (fromPath: string, toPath: string, arrayMergeType?: string) => {
  try {
    const data = proxyData.getProxyData('dynamic --> set-new-value')
    // 数据元
    const fromValue = getIn(data, fromPath)
    // 目标数据
    const toValue = getIn(data, toPath)
    const newValue = _.cloneDeep(fromValue)
    // 如果是数组 暂时先覆盖
    if (_.isArray(toValue) && newValue) {
      if (arrayMergeType !== 'concact') {
        toValue.length = 0
      }
      toValue.push(...newValue)
      return
    }
    // 都是object 就合并
    if (_.isPlainObject(toValue) && newValue) {
      Object.assign(toValue, newValue)
      return
    }
    // 临时处理表单查询详情时 接口返回null 导致表单值失去响应式问题
    const parentKey = toPath.split('.')[0]
    if (
      _.isPlainObject(toValue) &&
      proxyData.getValueFromProxyData(parentKey)?.compName &&
      proxyData.getValueFromProxyData(parentKey)?.compName == 'lyy-form' &&
      !newValue
    ) {
      Object.assign(toValue, {})
      return
    }
    // 如果是其他普通类型 就直接覆盖
    setIn(data, toPath, fromValue)
  } catch (error) {
    console.log(error, 'dynamic')
  }
}
//====兼容取不到值处理=======
export const filterValue = (value: string) => {
  if (_.isString(value)) {
    return value.includes('.modelValue.') ||
      value.includes('.prop.') ||
      value.includes('sessionStorage.') ||
      value.includes('localStorage.') ||
      value.includes('route.query.') ||
      value.includes('route.params.')
      ? ''
      : value
  }
  return value
}
// 高级模式
const higher = (keyValueList: IProxyDataHigher[], toPath: string) => {
  const newValue = keyValueList?.reduce((final, payload) => {
    // 取静态值 或者 动态取值
    const payloadPath = transPropFormPath(payload.value)
    return Object.assign(final, {
      [payload.key]: _.isUndefined(proxyData.getValueFromProxyData(payloadPath))
        ? filterValue(payload.value)
        : proxyData.getValueFromProxyData(payloadPath),
    })
  }, {})
  const toValue = proxyData.getValueFromProxyData(toPath)
  if (toPath === 'sessionStorage') {
    Object.assign(sessionStorage, newValue)
  } else if (toValue !== undefined && toValue !== null) {
    // 默认是合并
    Object.assign(toValue, newValue)
  }
}

// 高级模式的获取值函数
export const getHigherValue = (keyValueList: IProxyDataHigher[]) => {
  return keyValueList?.reduce((final, payload) => {
    //
    // if (payload.value.includes('.')) {
    //   return Object.assign(final, {
    //     [payload.key]: proxyData.getValueFromProxyData(payload.value),
    //   })
    // }
    // return Object.assign(final, {
    //   [payload.key]: filterValue(evaluate(payload.value, {})),
    // })
    const payloadPath = transPropFormPath(payload.value)

    return Object.assign(final, {
      [payload.key]: _.isUndefined(proxyData.getValueFromProxyData(payloadPath))
        ? filterValue(evaluate(payload.value, {}))
        : proxyData.getValueFromProxyData(payloadPath),
    })
  }, {})
}

export const getNewValue = (option: getNewValueOption) => {
  // 静态模式
  if (option && option.type === 'static' && option.static) {
    return _.isUndefined(proxyData.getValueFromProxyData(option.static))
      ? filterValue(evaluate(option.static, {}))
      : proxyData.getValueFromProxyData(option.static)
    // 路径模式
  } else if (option && option.type === 'dynamic' && option.dynamic) {
    // 兼容旧版本从表单的prop.form中取值
    const nodePath = transPropFormPath(option.dynamic.nodePath)
    return proxyData.getValueFromProxyData(nodePath)
  } else if (option && option.type === 'higher' && option.higher) {
    // 高级模式
    const result = getHigherValue(option.higher)
    return result
  } else {
    return undefined
  }
}

const transPropFormPath = (originPath) => {
  if (typeof originPath === 'string') {
    return originPath.replace('prop.form', 'modelValue')
  }
  return originPath
}

// 设置值
export const setNewValue = (
  option: SetNewValueOption,
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const type = option.to.type
  const toNodePath = transPropFormPath(option.to.dynamic.nodePath)
  const fromNodePath = transPropFormPath(option.from?.dynamic?.nodePath)
  // 静态模板
  if (type === 'static' && option.from.static != undefined) {
    staticFn(option.from.static, toNodePath, option.arrayMergeType)
    // 路径应用
  } else if (type === 'dynamic' && option.from.dynamic) {
    dynamic(fromNodePath, toNodePath, option.arrayMergeType)
    // 高级模式
  } else {
    option.from.higher && higher(option.from.higher, toNodePath)
  }
  thenActions && useAction(thenActions, currentComp)
}
