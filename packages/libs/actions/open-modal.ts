import { useStore } from '../index'
import { PAGE_CONFIG } from '../constants/store-key'
import { treeFind } from '../utils/tree-find'

export const openModal = (option: ITarget) => {
  const store = useStore()
  const pageConfig = store.getValue(PAGE_CONFIG)

  const { targetId } = option
  const targetPageConfig = treeFind(pageConfig, (node) => {
    if (node.compId === targetId || node.entityId === targetId) {
      return true
    }
  })
  if (targetPageConfig) {
    targetPageConfig.modelValue = true
  }
  // pageConfig.some((item: any) => {
  //   if (item.compId === targetId) {
  //     item.modelValue = true
  //     return true
  //   }
  // })
}
