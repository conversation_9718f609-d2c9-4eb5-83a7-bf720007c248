import { getNewValue } from './set-new-value'
import { qt } from '../utils'
import { IAction, IElement, IQtOption } from '../../lyyPcRender/src/global'
export const qtTrackAction = async (
  option: IQtOption,
  useAction: (actions: IAction[], currentComp: IElement) => void,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const { trackerEventCode, eventType = 'EXP', eventParams } = option
  const data = getNewValue(eventParams) || {}
  qt.QTEventTracking({
    trackerEventCode,
    eventType,
    eventParams: data,
  })
  thenActions && useAction(thenActions, currentComp)
}
