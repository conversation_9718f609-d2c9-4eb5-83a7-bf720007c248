import { useStore } from '../index'
import { FORM_REF } from '../constants/store-key'

export const validate = async (
  option: ITarget,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const store = useStore()

  const { targetId, fields } = option
  const formRef = store.getValue(FORM_REF, targetId)
  try {
    await (fields ? formRef.validateFields(fields) : formRef.validate())
    thenActions && useAction(thenActions, currentComp)
  } catch (error) {
    console.log('form validate', error)
    return Promise.reject(error)
    // return error
  }
}
