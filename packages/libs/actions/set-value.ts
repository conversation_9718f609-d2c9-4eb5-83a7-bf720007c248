import _ from 'lodash'

import { findComponent, getCompId, getIn, setIn } from '../utils'
import { usePayload, useUpdate } from '../hooks'

import { useStore } from '../index'
import { PAGE_CONFIG, MODEL_VALUE, PROP } from '../constants/store-key'

export const setValue = (data, option, currentComp) => {
  const { payloads, targets } = option ?? {}
  const newValue = payloads ? usePayload(payloads) : data

  if (targets) {
    setTargetsState(newValue, targets, currentComp)
  } else {
    setTargetState(newValue, option, currentComp)
  }
}

/**
 * 赋值给单个组件
 */
const setTargetState = (data, option, currentComp) => {
  if (option.targetPath) option.targetKey = option.targetPath
  const { type, index, sourceKey, targetId, targetKey } = option ?? {}

  const newValue = getIn(data, sourceKey)

  // 查找目标组件
  const store = useStore()
  const target = targetId
    ? findComponent(getCompId(targetId))(store.getValue(PAGE_CONFIG))
    : currentComp

  // 没有找到相关组件，赋值到 store 的 state 中（自定义数据）
  if (!target) {
    setState(newValue, option, false)
    return
  }

  // 赋值给 modelValue
  if (targetKey === MODEL_VALUE) {
    useUpdate(newValue, target)
  }

  // 赋值给 prop
  else if (targetKey?.slice(0, 5) === PROP) {
    if (type === 'merge') {
      const obj = getIn(target, targetKey)
      if (_.isObject(obj)) Object.assign(obj, newValue)
    } else {
      setIn(target, targetKey, newValue)
    }
  }

  // 赋值到 store 的 state 中
  else if (target.compId) {
    setState(newValue, option)
  }
}

/**
 * 赋值给多个组件
 */
const setTargetsState = (data, targets, currentComp) => {
  for (const item of targets) {
    setTargetState(data, item, currentComp)
  }
}

/**
 * 设置到 Store 的 state 中
 */
const setState = (newValue, option, isComp = true) => {
  const { type, index, targetId, targetKey } = option ?? {}
  const path = toStringPath(targetKey)

  const store = useStore()

  const id = isComp ? getCompId(targetId) : targetId
  if (path) {
    if (type || index) {
      store.setState(id, newValue, { targetKey: path, type, index })
    } else {
      store.setValue(id, newValue, path)
    }
  } else {
    if (type || index) {
      store.setState(id, newValue, { type, index })
    } else {
      store.setValue(id, newValue)
    }
  }
}

/**
 * 路径格式化为如 a.b.c 的形式
 */
const toStringPath = (path: string | string[]) => {
  return Array.isArray(path) ? path.join('.') : path
}
