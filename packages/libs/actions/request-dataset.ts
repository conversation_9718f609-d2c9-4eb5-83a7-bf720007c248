import { request } from './request'
import { useRequest } from '../hooks'
import { defaultConfig } from '../../lyyPcRender/src/service'
import { getIn } from '../utils'
import { setValue } from './set-value'
import httpConfig from '../../lyyPcRender/src/http-config'
import { merge } from 'lodash'
import { notification } from 'ant-design-vue'
import { useStore } from '../index'
import { TEMP_DATA } from '../constants/store-key'
import proxyData from '../utils/proxy-data'
import { feedback } from '../../lyyPcRender/src/service/feedback'
import { IAction } from '../../lyyMobileRender/src/global'
import { getDataSetList } from '../store/jsobject'
export const requestDataset = async (
  option: IRequestDatasetOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
  requestNumber: number,
) => {
  // 未关联数据集id不执行
  if (!option.id) {
    return
  }
  const item = getDataSetList().find((e) => e.name == option.name)
  if (!item) {
    return
  }
  const requestOption = JSON.parse(item.def)
  const data = await useRequest(requestOption)
  const success = data?.fault ? false : true
  // 用户取消了请求，则中止 actions
  if (!success && data?.code === 'ERR_CANCELED') return
  const successfulActions: IAction[] = []
  const failedActions: IAction[] = []
  const finallyActions: IAction[] = []
  if (thenActions)
    for (const item of thenActions) {
      const type = item.belong
      if (type === 'error') {
        failedActions.push(item)
      } else if (type === 'finally') {
        finallyActions.push(item)
      } else {
        successfulActions.push(item)
      }
    }
  if (success) {
    handleResponseData(requestOption, currentComp, data) // 将响应数据 data 赋值
    if (requestOption.responseDataKey) {
      const store = useStore()
      store.setValue(requestOption.responseDataKey, data)
    }
    useAction(successfulActions, currentComp)
    finallyActions.length > 0 && useAction(finallyActions, currentComp)
  }
  if (!success) {
    if (requestOption.customOption.isSuccessStop) {
      if (requestNumber < (requestOption.customOption.roundOrder ?? 5)) {
        setTimeout(() => {
          request(
            requestOption,
            useAction,
            thenActions,
            currentComp,
            requestNumber + 1,
          )
        }, requestOption.customOption.roundTimes ?? 2000)
      } else {
        feedback(requestOption.customOption.failedFeedback, '')
        handleFailAction(option, data)
        finallyActions.length > 0 && useAction(finallyActions, currentComp)
      }
    } else {
      handleFailAction(requestOption, data)
      finallyActions.length > 0 && useAction(finallyActions, currentComp)
    }
  }
}

const handleFailAction = (requestOption: IResponseSetValue, data: any) => {
  const systemConfig = httpConfig.getHttpConfig() || {}
  const requestConfig = merge({}, defaultConfig, systemConfig)
  // const { msgKey } = requestConfig.customOption?.notify ?? {}
  // notification['error']({
  //   message: '操作失败',
  //   description: data[msgKey],
  // })
}

const handleResponseData = (
  option: IResponseSetValue,
  currentComp: IElement,
  data: any,
) => {
  const { sourceKey, targetId, targetKey, targetPath, targets, type, index } =
    option
  const setValueOption = {
    targetId,
    targetKey,
    targetPath,
    targets,
    type,
    index,
  }
  const newValue = sourceKey ? getIn(data, sourceKey) : data
  setTempValue(newValue)
  setValue(newValue, setValueOption, currentComp)
}

// 赋值给临时数据
const setTempValue = (newValue: any) => {
  const store = useStore()
  store.setValue(TEMP_DATA, newValue)
}
