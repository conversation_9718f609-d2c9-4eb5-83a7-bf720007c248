import { broadcastHoc } from './broadcast'
import { requestHoc } from './request'
import { getDataSetList } from '../store/jsobject'
import { Modal, message, notification } from 'ant-design-vue'
import { openModal } from './open-modal'
import { resetValue } from './reset-value'
import { validate } from './validate'
import { linkto as _linkto } from './linkto'
import { depAction as _depAction } from './depAction'
import { emitDep, useAction } from '../hooks'
import { LOADING } from '../constants/store-key'
import { useStore } from '../store'
import { getTimezone, formatDate } from './formatDate'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { closeModal } from './close-modal'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(customParseFormat)

export const linkto = (option) => {
  if (typeof option === 'string') {
    _linkto(
      {
        url: option,
      },
      useAction,
      [],
      {},
    )
  } else if (typeof option === 'object') {
    _linkto(option, useAction, [], null)
  }
}
export const depAction = async (
  targetId: string,
  action: string,
  params: any,
  useAction?,
  thenActions?,
  currentComp?,
) => {
  await emitDep(
    [
      {
        type: action,
        compId: targetId,
        params,
        pipe: {
          currentComp,
          thenActions,
        },
      },
    ],
    params,
  )
}
export const closeLoading = () => {
  const store = useStore()
  store.setValue(LOADING, {
    spinning: false,
    tip: undefined,
  })
}
export const loading = (tip: string, delay: number) => {
  const store = useStore()
  store.setValue(LOADING, {
    spinning: true,
    tip,
  })
  if (delay) {
    setTimeout(() => {
      closeLoading()
    }, delay * 1000)
  }
}
export const formReset = async (targetId: any) => {
  if (typeof targetId === 'string') {
    return await resetValue({
      targetId,
    })
  } else if (typeof targetId === 'object') {
    return await resetValue(targetId)
  }
}
export const formValidate = async (targetId: any) => {
  if (typeof targetId === 'string') {
    return await validate(
      {
        targetId,
      },
      useAction,
      [],
      null,
    )
  } else if (typeof targetId === 'object') {
    return await validate(targetId, useAction, [], null)
  }
}

// excel-table 设置选中行，追加
export const setSelectedRowsConcat = (
  targetId: string,
  data: string | (string | number)[],
) => {
  depAction(targetId, 'setCheckBoxPicked', {
    selectedRowKeys: data,
    isCover: false,
  })
}

export const setSelectedRowsCover = (
  targetId: string,
  data: string | (string | number)[],
) => {
  depAction(targetId, 'setCheckBoxPicked', {
    selectedRowKeys: data,
    isCover: true,
  })
}

// 将世界标准时间根据时区转化，第一个参数为世界标准时间，第二个参数为要转化的时区偏移量，第三个参数为转化后的格式，
// 没有默认取机器时区偏移量
const formatTimeToUTC = (option) => {
  let { utcTime, offset, format } = option
  if (!utcTime) {
    return
  }
  utcTime = utcTime.replaceAll('-', '/')
  if (/^UTC([+-])([01]?\d|2[0-3]):([0-5]\d)$/.test(offset)) {
    const dateUTC = new Date(utcTime)
    const hours = Number.parseInt(offset.slice(4, 6)) // 从偏移量中提取小时部分
    const minutes = Number.parseInt(offset.slice(7, 9)) // 从偏移量中提取分钟部分
    const sign = offset[3] === '+' ? 1 : -1 // 判断偏移量的正负号
    // 根据偏移量调整时间
    dateUTC.setHours(dateUTC.getHours() + sign * hours)
    dateUTC.setMinutes(dateUTC.getMinutes() + sign * minutes)
    // 格式化输出时间
    format = format || 'YYYY-MM-DD HH:mm:ss'
    const dateString = formatDate(dateUTC, format)
    return dateString
  } else {
    // dayjs.extend(utc);
    // dayjs.extend(timezone);
    // 转换为 'America/Los_Angeles' 时区时间
    const guess = offset || dayjs.tz.guess()
    const laTime = dayjs.utc(utcTime, 'YYYY/MM/DD HH:mm:ss').tz(guess)
    const dateString = formatDate(laTime.format('YYYY/MM/DD HH:mm:ss'), format)
    return dateString
  }
}

// 将时间根据时区转化为世界标准时间，第一个参数为要转化的时间，第二个参数为要转化的时区偏移量，第三个参数为转化后的格式，
// 没有默认取机器时区偏移量
export function formatTimeToInternational(option) {
  let { utcTime, offset, format } = option
  utcTime = utcTime.replaceAll('-', '/')
  if (/^UTC([+-])([01]?\d|2[0-3]):([0-5]\d)$/.test(offset)) {
    const dateLocal = new Date(utcTime)
    const hours = Number.parseInt(offset.slice(4, 6))
    const minutes = Number.parseInt(offset.slice(7, 9))
    const sign = offset[3] === '+' ? -1 : 1
    dateLocal.setHours(dateLocal.getHours() + sign * hours)
    dateLocal.setMinutes(dateLocal.getMinutes() + sign * minutes)
    format = format || 'YYYY-MM-DD HH:mm:ss'
    const dateString = formatDate(dateLocal, format)
    return dateString
  } else {
    const guess = offset || dayjs.tz.guess()
    const localTime = dayjs.tz(utcTime, guess)
    // 转换为 UTC 时间
    const utcTimeStr = localTime.utc().format(format)
    return utcTimeStr
  }
}

/**
 * 将设备当前时区时间的转化为当前商户时区时间
 * @param option {time: 时间  timeZone: 时区  format: 格式}
 * @returns
 */
export const exchangeTimeZone = (option) => {
  const guess = dayjs.tz.guess()
  console.log('guess:', guess)
  const { time, timeZone = guess, format = 'YYYY-MM-DD' } = option
  const loaclTime = new Date(time)
  console.log('loaclTime:', loaclTime)
  const exchangedTime = dayjs(loaclTime).tz(timeZone).format(format)
  console.log('exchangedTime:', exchangedTime)
  return exchangedTime
}

export const dataSetRequest = async (
  name,
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
  requestNumber: number,
) => {
  const list = getDataSetList()
  let option = {}
  for (let i = 0; i < list?.length; i++) {
    if (list[i].name === name) {
      option = JSON.parse(list[i].def)
      break
    }
  }
  const result = await requestHoc(
    option,
    useAction,
    thenActions,
    currentComp,
    requestNumber,
  )
  return result
}
export const actionHoc = {
  broadcast: broadcastHoc,
  request: requestHoc,
  dataSetRequest,
  modal: Modal,
  loading,
  closeLoading,
  formatTimeToUTC,
  formatTimeToInternational,
  openExistModal: (entityId: string) => {
    // 打开已有弹框
    if (typeof entityId === 'string') {
      openModal({
        targetId: entityId,
      })
    } else {
      openModal(entityId)
    }
  },
  closeExistModal: (entityId: string) => {
    // 打开已有弹框
    if (typeof entityId === 'string') {
      closeModal({
        targetId: entityId,
      })
    } else {
      closeModal(entityId)
    }
  },
  message,
  notification,
  formReset,
  formValidate,
  linkto,
  depAction,
  setSelectedRowsConcat,
  setSelectedRowsCover,
  exchangeTimeZone,
}
