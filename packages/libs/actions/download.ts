import { getIn, replaceUrl, stringifyUrl } from '../utils'
import { useRequest } from '../hooks/'
import { getNewValue } from '../actions/set-new-value'
import _ from 'lodash'
// useAction、thenActions 待完善
export const download = async (
  config: IDownloadOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const { url = '', customOption, payloads } = config
  const { downloadType, fileName: _fileName, jsonPath } = customOption ?? {}
  let downloadUrl = url ?? ''
  if (payloads) {
    const payload = _.cloneDeep(getNewValue(payloads))
    if (payload.url) {
      downloadUrl = payload.url
    }
  }
  // 如果是完整的下载链接，直接下载
  if (downloadType === 'href' && downloadUrl!.charAt(0) !== '/') {
    aDownload(downloadUrl, _fileName)
    return
  }

  const response: any = await useRequest(config)
  const success = response?.fault ? false : true
  // 如果请求数据失败
  if (!success) return
  // 后端返回的是一个 url 地址
  if (typeof response === 'string') {
    aDownload(response)
    if (thenActions?.length) {
      useAction(thenActions, currentComp)
    }
    return
  }
  // 请求后，下载文件
  let fileName
  if (_fileName) {
    fileName = _fileName + downloadType
  } else if (response?.headers && response.headers['content-disposition']) {
    fileName = decodeURIComponent(
      (response?.headers['content-disposition'] as string).split(
        'filename=',
      )[1],
    )
  } else {
    fileName = '导出文件' + downloadType
  }
  const blobParts = jsonPath ? getIn(response, jsonPath) : response.data
  const options = { type: 'application/octet-stream;charset=utf-8' }

  // 后端返回的是二进制数据
  const blob = new Blob([blobParts], options)
  const elink = document.createElement('a')
  elink.download = fileName
  elink.style.display = 'none'
  elink.href = URL.createObjectURL(blob)
  document.body.append(elink)
  elink.click()
  URL.revokeObjectURL(elink.href)
  elink.remove()
  if (thenActions?.length) {
    useAction(thenActions, currentComp)
  }
}

const aDownload = (href: string, title = '下载文件') => {
  const elink = document.createElement('a')
  elink.href = href
  elink.target = '_blank'
  elink.download = title
  elink.click()
  elink.remove()
  console.log(elink.href)
}
