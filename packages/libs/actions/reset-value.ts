import { cloneDeep, random } from 'lodash'
import { useStore } from '../index'
import { findComponent } from '../utils/find-parent-id'
import { ORIGIN, PAGE_CONFIG, FORM_REF } from '../constants/store-key'
import { treeFind } from '../utils/tree-find'
import proxyData from '../utils/proxy-data'
import {
  LYY_EDIT_TABLE,
  LYY_EXCEL_TABLE,
  LYY_TABLE,
} from '../constants/component-name'
import { reactive } from 'vue'

export const resetValue = (option: ITarget) => {
  const store = useStore()
  const { targetId } = option
  const formRef = store.getValue(FORM_REF, targetId)
  formRef && formRef.clearValidate()
  const originState = store.getValue(ORIGIN, targetId)
  const formComp = findComponent(targetId)
  if (originState) {
    for (const key of Object.keys(formComp.modelValue)) {
      formComp.modelValue[key] = cloneDeep(originState)[key]
    }
  }
  // 兼容modelvalue
  // if (formComp.modelValue) {
  //   if (originState) {
  //     for (const key of Object.keys(formComp.modelValue)) {
  //       formComp.modelValue[key] = cloneDeep(originState)[key]
  //     }
  //   }
  // } else {
  //   formComp.modelValue = {}
  // }
  // const date = []
  // const currentData = proxyData.getProxyData('resetValue -->rest-value')
  // treeFind([formComp], (node) => {
  //   if (node.prop?.field) {
  //     if (
  //       [LYY_EXCEL_TABLE, LYY_EDIT_TABLE, LYY_TABLE].includes(node.compName)
  //     ) {
  //       node.modelValue = reactive({
  //         datasource: formComp.modelValue
  //           ? formComp.modelValue[node.prop.field]
  //           : [],
  //         selectedRows: [],
  //         selectedRowKeys: [],
  //         sortOptions: [],
  //         filters: [],
  //         currentRow: {},
  //       })
  //     } else {
  //       node.modelValue = formComp.modelValue
  //         ? formComp.modelValue[node.prop.field]
  //         : ''
  //     }
  //   }
  //   if (node.compName === 'lyy-pagination') {
  //     proxyData.getProxyData('treeFind --> reset-value')[
  //       node.compId
  //     ].modelValue = [1, node.modelValue[1]]
  //   }
  //   if (node.compName === 'lyy-date-range') {
  //     date.push(proxyData.getProxyData('treeFind --> reset-value')[node.compId])
  //   }
  // })
  // const oldState = cloneDeep(originState)
  // currentData['表单集合'][targetId] &&
  //   Object.keys(currentData['表单集合'][targetId]).forEach((key) => {
  //     currentData['表单集合'][targetId][key] === oldState[key]
  //   })
  // for (const dateItem of date) {
  //   const newDate = []
  //   for (const field of dateItem.prop.fields) {
  //     newDate.push(cloneDeep(originState)[field])
  //   }
  //   dateItem.modelValue = newDate
  // }
}
