import { useAction } from '../hooks'
import { findComponent } from '../utils/find-parent-id'

export const broadcast = (option: ITarget, formId: string) => {
  const { targetId, event } = option
  const target = findComponent(targetId)
  if (!target) return
  const actions = target?.actions?.filter((item) => item.event === event)
  actions && useAction(actions, target)
}
export const broadcastHoc = (targetId, event) => {
  broadcast({
    targetId,
    event,
  })
}
