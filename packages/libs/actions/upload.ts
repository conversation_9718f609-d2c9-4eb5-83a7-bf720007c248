import { useRequest } from '../hooks/use-request'
import { useStore } from '../index'
import { cloneDeep, isString, isObject } from 'lodash'
import type { UploadFile } from 'ant-design-vue'
import { findParentForm } from '../utils/find-parent-id'
import proxyData from '../utils/proxy-data'
// useAction、thenActions 待完善
export const upload = async (
  option: IRequestOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const store = useStore()
  const { compId: formId } = findParentForm(currentComp.compId) ?? {}

  const { field, fieldName, data } = currentComp.prop

  const fileList = formId
    ? store.getValue(formId, field)
    : currentComp.modelValue
  if (fileList?.length === 0) {
    // 删除情况下也需要更新form
    updateForm(fileList, currentComp, true, formId)
    return
  }
  // // 只提交没有上传过的图片
  const toUploadFiles = fileList?.filter((item) => item.status !== 'done')
  if (!toUploadFiles || toUploadFiles.length === 0) {
    return
  }
  const useFetchArr = []
  for (const file of toUploadFiles) {
    const formData = new FormData()
    const originFile = file instanceof File ? file : file.originFileObj
    formData.append(fieldName || 'file', originFile as any)
    if (data) {
      for (const [key, value] of Object.entries(data)) {
        formData.append(key, value)
      }
    }
    const submitFetch = cloneDeep(option)
    submitFetch.data = formData
    useFetchArr.push(useRequest(submitFetch))
  }
  Promise.all(useFetchArr)
    .then((res) => {
      // 上传成功
      const success = res.some((item) => {
        return item?.fault ? false : true
      })
      const successfulActions: IAction[] = []
      const failedActions: IAction[] = []
      const finallyActions: IAction[] = []
      if (thenActions)
        for (const item of thenActions) {
          const type = item.belong
          if (type === 'error') {
            failedActions.push(item)
          } else if (type === 'finally') {
            finallyActions.push(item)
          } else {
            successfulActions.push(item)
          }
        }
      if (success) {
        if (option.responseDataKey) {
          const store = useStore()
          store.setValue(option.responseDataKey, res[0])
        }
        useAction(successfulActions)
      }
      // 上传成功，并且后端有返回值
      if (success && res.some((item) => !!item)) {
        updateFileList(fileList, cloneDeep(res), currentComp, formId)
      } else {
        if (currentComp?.prop?.auto) {
          updateFileList([], cloneDeep(res), currentComp, formId)
        }
      }
    })
    .catch((error) => {})
}
const updateFileList = (fileList, data, comp, formId) => {
  // 根据后端的返回值类型进行处理
  if (!fileList) return
  if (comp.prop.isStopUpdate) return
  const len = fileList.length
  let isFieldObj = false
  if (Array.isArray(data)) {
    let fileListLen = len
    while (data.length > 0) {
      const el = data.pop()
      isFieldObj = isObject(el)
      fileList[fileListLen - 1] = transToFile(el, comp) as UploadFile
      fileListLen--
    }
  } else {
    isFieldObj = isObject(data)
    fileList[len - 1] = transToFile(data, comp) as UploadFile
  }
  updateForm(fileList, comp, isFieldObj, formId)
}
const transToFile = (el, comp, index?) => {
  if (isString(el)) {
    return {
      url: el,
      uid: `${Date.now()}${index ?? ''}`,
      name: extname(el),
      status: 'done',
    }
  }
  if (isObject(el)) {
    const { name = 'name', url = 'url' } = comp.prop.fieldNames ?? {}
    return {
      url: el[url],
      uid: `${Date.now()}${index ?? ''}`,
      name: el[name],
      status: 'done',
    }
  }
}
const extname = (url = '') => {
  const temp = url.split('/')
  const filename = temp.at(-1)
  const filenameWithoutSuffix = filename.split(/#|\?/)[0]
  return filenameWithoutSuffix
}

const updateForm = (fileList, comp, isFieldObj, formId) => {
  const store = useStore()
  const res = fileList.map((item) => {
    const { name = 'name', url = 'url' } = comp.prop.fieldNames || {}
    const res = isFieldObj ? { [name]: item.name, [url]: item.url } : item.url
    return res
  })
  // const { compId: formId } = findParentForm(comp.compId)
  store.setValue(
    formId,
    (comp.prop.maxCount as number) > 1 ? res : res[0],
    comp.prop.field,
  )
  proxyData.setProxyData(
    `${formId}.modelValue.${comp.prop.field}`,
    (comp.prop.maxCount as number) > 1 ? res : res[0],
  )
}
