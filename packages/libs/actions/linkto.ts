import { ILinktoOption } from '../../lyyMobileRender/src/global'
import { useStore } from '../index'
import { replaceUrl, stringifyUrl } from '../utils'
import proxyData from '../utils/proxy-data'
import { filterValue, getNewValue } from '../actions/set-new-value'
import _ from 'lodash'
export const linkto = (
  option: ILinktoOption, // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  // 页面后退或前进
  if (option.go) {
    window.history.go(option.go)
    return
  }
  const store = useStore()
  const router =
    store.getRouter() || window.$router || window.$applicationRouter
  // 页面跳转
  const { url: originUrl, payloads, tab, mode, name, isAddRandom } = option
  const payload = payloads?.reduce((final, payload) => {
    // 取静态值 或者 动态取值
    return Object.assign(final, {
      [payload.key]: _.isUndefined(
        proxyData.getValueFromProxyData(payload.value),
      )
        ? filterValue(payload.value)
        : proxyData.getValueFromProxyData(payload.value),
    })
  }, {})

  let { url, replacedFields } = replaceUrl(originUrl!, payload)
  const reg = /^(https?:\/\/)/g
  if (url && !reg.test(url)) {
    url = getNewValue({
      static: url,
      type: 'static',
    })
  }
  for (const field of replacedFields) delete payload[field]
  if (isAddRandom) {
    payload['_random'] = Date.now()
  }
  // url 不是以 / 开头的 (即，url 有完整域名和路径)
  if (url && url.charAt(0) !== '/') {
    const fullUrl = stringifyUrl(url, payload)
    window.open(fullUrl, tab)
    return
  }

  // 新窗口打开
  if (tab === '_blank') {
    const path = stringifyUrl(url, payload)
    const { href } = router.resolve(path)
    window.open(href, tab)
    return
  }

  // 当前窗口打开 (默认)
  if (!tab || tab === '_self') {
    // 路由可能是react 路由
    const _pathKey = router?.currentRoute ? 'path' : 'pathname'
    if (mode === 'query') {
      router.push({ [_pathKey]: url, query: payload })?.then((res) => {
        thenActions && useAction(thenActions, currentComp)
      })
    } else if (mode === 'params') {
      router.push({ [_pathKey]: url, params: payload })
    } else {
      // console.log(url, '实际路由地址')
      router?.push(url)
    }
  }
}
