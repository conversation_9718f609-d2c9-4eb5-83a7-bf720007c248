import { useStore } from '../index'
import { PAGE_CONFIG } from '../constants/store-key'
import { traverse } from '../utils/'

export const closeModal = (option: ITarget) => {
  const store = useStore()
  const pageConfig = store.getValue(PAGE_CONFIG)
  const { targetId } = option
  const targetPageConfig = traverse(
    pageConfig,
    (item: any) => {
      if (item.compId === targetId) {
        return true
      }
    },
    2,
  )
  if (targetPageConfig) {
    // 上一个action还没有执行完，弹窗已经关闭，存在 弹窗里面的组件更新还未完成就卸载了 的情况，可能会导致数据错误，故做如下处理
    setTimeout(() => {
      targetPageConfig.modelValue = false
    }, 0)
  }
}
