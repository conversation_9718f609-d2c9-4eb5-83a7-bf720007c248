import proxyData from '../utils/proxy-data'
import { apiFns, getAllResource, mergeApiFn } from '../store/jsobject'
import { myeval } from '../utils/compiler'
import { actionHoc } from './actionHoc'

export const customerJs = (
  option,
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp,
  args,
) => {
  const proxyUseAction = (payload) => {
    return useAction([payload], currentComp)
  }
  const data = mergeApiFn(proxyData.getProxyData())
  window.globalData = data
  const allResource = getAllResource()
  const code = option.customJs
    .replace('(useAction,data,context,globalData, _args) => {', '')
    .replace('(useAction,data,context,globalData) => {', '')
    .replace('(useAction,data,context) => {', '') //兼容旧
    .replace('return useAction\n  }', '')
  console.log('新版本')
  myeval(
    {
      useAction: proxyUseAction,
      ...data,
      data,
      globalData: data,
      LYY: actionHoc,
      context: currentComp,
      ...allResource,
      ...apiFns(),
      _args: args,
    },
    code,
  )
  thenActions && useAction(thenActions, currentComp, args)
}
