// 获取当前机器的时区
export const getTimezone = () => {
    const offsetInMinutes = new Date().getTimezoneOffset();
    const hours = Math.abs(Math.floor(offsetInMinutes / 60));
    const minutes = Math.abs(offsetInMinutes % 60);
    const sign = offsetInMinutes > 0 ? '-' : '+';
    const offsetString = `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    return offsetString

}

// 根据传入的格式格式化时间
export const formatDate = (date, formatter) => {
    if (date) {
        formatter = formatter ? formatter : 'YYYY-MM-DD HH:mm:ss'
        date = (date ? new Date(date) : new Date)
        const Y = date.getFullYear() + '',
            M = date.getMonth() + 1,
            D = date.getDate(),
            H = date.getHours(),
            m = date.getMinutes(),
            s = date.getSeconds()

        const formatterString = formatter.replace(/YYYY|yyyy/g, Y)
        .replace(/YY|yy/g, Y.substr(2, 2))
        .replace(/MM/g, (M < 10 ? '0' : '') + M)
        .replace(/DD|dd/g, (D < 10 ? '0' : '') + D)
        .replace(/HH|hh/g, (H < 10 ? '0' : '') + H)
        .replace(/mm/g, (m < 10 ? '0' : '') + m)
        .replace(/SS|ss/g, (s < 10 ? '0' : '') + s)

        return formatterString
    }
}


