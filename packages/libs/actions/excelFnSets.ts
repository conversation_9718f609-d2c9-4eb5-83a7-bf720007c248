import { useStore } from '../index'
import { EXCEL_TABLE } from '../constants/store-key'

export const validate = async (
  option: ExcelTarget,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const store = useStore()

  const { targetId, event = '', dataSource } = option
  const excelTable = store.getValue(EXCEL_TABLE, targetId)
  try {
    await (excelTable[event] && excelTable[event](dataSource))
    thenActions && useAction(thenActions, currentComp)
  } catch (error) {
    console.log('EXCEL_TABLE', error)
    return
  }
}
