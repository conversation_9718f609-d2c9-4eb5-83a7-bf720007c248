import { emitDep } from '../hooks/index'
import { useStore } from '../index'
import { getNewValue } from './set-new-value'

export const depAction = async (
  option: IRequestOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  // @ts-ignore
  const { action, targetId, params, source, sourceKey, payloads } = option
  let data
  if (source) {
    const store = useStore()
    data = store.getValue(source, sourceKey)
  } else {
    data = getNewValue(payloads)
  }
  try {
    await emitDep(
      [
        {
          type: action,
          compId: targetId,
          params,
          pipe: {
            currentComp,
            thenActions,
          },
        },
      ],
      data,
    )
  } catch (error) {
    console.log(error)
  }
}
