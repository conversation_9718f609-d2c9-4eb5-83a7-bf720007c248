import { createVNode } from 'vue'
import { Modal, message, notification } from 'ant-design-vue'
import proxyData from '../utils/proxy-data'
import { tpl } from '../utils/compiler'
// import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

const handleContentFn = (content) => {
  let _content = content
  if (content.includes('\n')) {
    const _p = content.split('\n')
    const contentNode: any = []
    for (let i = 0; i < _p.length; i++) {
      contentNode.push(createVNode('span', null, _p[i]))
      if (i < _p.length - 1) {
        contentNode.push(createVNode('br'))
      }
    }
    _content = createVNode('div', null, contentNode)
  }
  return _content
}
export const openConfirm = (
  option: IOpenConfirmOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
) => {
  const {
    title = '提示信息',
    content = '提示',
    type = 'confirm',
    okText = '确定',
    cancelText = '取消',
    styleType = 'modal',
    placement = 'topRight',
    duration = 3,
    payloads,
    okButtonProps,
  } = option
  let contentMsg = content
  if (payloads && payloads.length > 0) {
    const payload = payloads?.reduce((final, payload) => {
      // 取静态值 或者 动态取值
      return Object.assign(final, {
        [payload.key]:
          proxyData.getValueFromProxyData(payload.value) || payload.value,
      })
    }, {})
    contentMsg = tpl(content)(payload)
  }
  if (styleType == 'modal') {
    Modal[type]({
      title,
      // icon: createVNode(ExclamationCircleOutlined),
      content: handleContentFn(contentMsg),
      onOk() {
        thenActions && useAction(thenActions, currentComp)
      },
      okText,
      cancelText,
      okButtonProps,
    })
  } else if (styleType == 'notification') {
    notification[type]({
      message: title,
      description: handleContentFn(contentMsg),
      placement: placement,
      duration: duration,
    })
    thenActions && useAction(thenActions, currentComp)
  } else if (styleType == 'message') {
    message[type](handleContentFn(contentMsg), [duration])
    thenActions && useAction(thenActions, currentComp)
  }
}
