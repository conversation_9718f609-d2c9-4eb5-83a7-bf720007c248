import { useStore } from '../index'
import { PAGE_CONFIG } from '../constants/store-key'
import { traverse } from '../utils'

export const openDrawer = (option: ITarget) => {
  const store = useStore()
  const pageConfig = store.getValue(PAGE_CONFIG)

  const { targetId } = option
  const targetPageConfig = traverse(
    pageConfig,
    (item: any) => {
      if (item.compId === targetId) {
        return true
      }
    },
    2,
  )
  if (targetPageConfig) {
    targetPageConfig.modelValue = true
  }
}
