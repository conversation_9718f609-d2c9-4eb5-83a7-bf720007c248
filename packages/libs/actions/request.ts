import { useRequest } from '../hooks'
import { defaultConfig } from '../../lyyPcRender/src/service'
import { getIn } from '../utils'
import { setValue } from './set-value'
import httpConfig from '../../lyyPcRender/src/http-config'
import { merge } from 'lodash'
import { notification } from 'ant-design-vue'
import { useStore } from '../index'
import { TEMP_DATA } from '../constants/store-key'
import proxyData from '../utils/proxy-data'
import { feedback } from '../../lyyPcRender/src/service/feedback'
import { IAction } from '../../lyyMobileRender/src/global'
import { getDataSetList } from '../store/jsobject'
export const request = async (
  option: IRequestOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
  requestNumber: number,
) => {
  const data = await useRequest(option)
  const success = data?.fault ? false : true
  // 用户取消了请求，则中止 actions
  if (!success && data?.code === 'ERR_CANCELED') return
  const successfulActions: IAction[] = []
  const failedActions: IAction[] = []
  const finallyActions: IAction[] = []
  if (thenActions)
    for (const item of thenActions) {
      const type = item.belong
      if (type === 'error') {
        failedActions.push(item)
      } else if (type === 'finally') {
        finallyActions.push(item)
      } else {
        successfulActions.push(item)
      }
    }
  if (success) {
    handleResponseData(option, currentComp, data) // 将响应数据 data 赋值
    if (option.responseDataKey) {
      const store = useStore()
      store.setValue(option.responseDataKey, data)
    }
    useAction && useAction(successfulActions, currentComp)
    finallyActions.length > 0 &&
      useAction &&
      useAction(finallyActions, currentComp)
  }
  if (!success) {
    if (option.customOption.isSuccessStop) {
      if (requestNumber < (option.customOption.roundOrder ?? 5)) {
        setTimeout(() => {
          request(
            option,
            useAction,
            thenActions,
            currentComp,
            requestNumber + 1,
          )
        }, option.customOption.roundTimes ?? 2000)
      } else {
        feedback(option.customOption.failedFeedback, '')
        handleFailAction(option, data)
        finallyActions.length > 0 &&
          useAction &&
          useAction(finallyActions, currentComp)
      }
    } else {
      handleFailAction(option, data)
      finallyActions.length > 0 &&
        useAction &&
        useAction(finallyActions, currentComp)
    }
  }
  return data
}

const handleFailAction = (option: IResponseSetValue, data: any) => {
  const systemConfig = httpConfig.getHttpConfig() || {}
  const requestConfig = merge({}, defaultConfig, systemConfig)
  // const { msgKey } = requestConfig.customOption?.notify ?? {}
  // notification['error']({
  //   message: '操作失败',
  //   description: data[msgKey],
  // })
}

const handleResponseData = (
  option: IResponseSetValue,
  currentComp: IElement,
  data: any,
) => {
  const { sourceKey, targetId, targetKey, targetPath, targets, type, index } =
    option
  const setValueOption = {
    targetId,
    targetKey,
    targetPath,
    targets,
    type,
    index,
  }
  const newValue = sourceKey ? getIn(data, sourceKey) : data
  setTempValue(newValue)
  setValue(newValue, setValueOption, currentComp)
}

// 赋值给临时数据
const setTempValue = (newValue: any) => {
  const store = useStore()
  store.setValue(TEMP_DATA, newValue)
}
// 提供简单配置选项
export const requestHoc = async (
  option: IRequestOption,
  // eslint-disable-next-line @typescript-eslint/ban-types
  useAction: Function,
  thenActions: IAction[] | undefined,
  currentComp: IElement,
  requestNumber: number,
) => {
  if (option.data) {
    const keys = Object.keys(option.data)
    const higher: any = []
    for (const key of keys) {
      higher.push({
        key,
        value: option.data[key],
      })
    }
    option.payloads = {
      type: 'higher',
      dynamic: {
        nodePath: '',
      },
      higher,
    }
  }
  const result = await request(
    option,
    // eslint-disable-next-line @typescript-eslint/ban-types
    useAction,
    thenActions,
    currentComp,
    requestNumber,
  )
  return result
}
