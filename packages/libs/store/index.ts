import { reactive } from 'vue'
import { getIn, setIn, localCache, sessionCache } from '../index'
import {
  LOADING,
  LOCAL_STORAGE,
  PAGE_CONFIG,
  ROUTE,
  ROUTE_PARAMS,
  ROUTE_QUERY,
  SESSION_STORAGE,
  TEMP_DATA,
} from '../constants/store-key'
import _ from 'lodash'
import proxyData from '../utils/proxy-data'

const state = {}
// 页面名称
export const getCurrentPageName = function () {
  const arr = location.hash.replace('#').split('/')
  return arr[arr.length - 1]
}
export function useStore() {
  /**
   * 获取数据
   * 1. 取路由 query参数 和 params参数
   * 2. 取 localStorage数据 和 sessionStorage数据
   * 3. 取 state数据（浅层值、深层值）
   */
  const getValue = (target?: string, path?: string | string[]) => {
    if (!target) return

    if (target === ROUTE_QUERY) {
      const route = state[ROUTE]
      return path ? getIn(route.query, path) : route.query
    }

    if (target === ROUTE_PARAMS) {
      const route = state[ROUTE]
      return path ? getIn(route.params, path) : route.params
    }

    if (target === LOCAL_STORAGE) {
      if (path?.includes('.')) {
        const index = path.indexOf('.')
        const field = path.slice(0, index)
        const targetKey = path.slice(index + 1)
        const data = sessionCache.get(field as string)
        return getIn(data, targetKey)
      } else {
        return localCache.get(path as string)
      }
    }

    if (target === SESSION_STORAGE) {
      if (path?.includes('.')) {
        const index = path.indexOf('.')
        const field = path.slice(0, index)
        const targetKey = path.slice(index + 1)
        const data = sessionCache.get(field as string)
        return getIn(data, targetKey)
      } else {
        return sessionCache.get(path as string)
      }
    }
    if (target === TEMP_DATA || target === LOADING) {
      return path ? getIn(state[target], path) : state[target]
    }

    const pageName = getCurrentPageName()
    if (!state[pageName]) return
    return path ? getIn(state[pageName][target], path) : state[pageName][target]
  }

  /**
   * 设置 state 值（对象新增合并和数组插入）
   * Q：为什么不直接改造 setValue ？
   * A：很多地方有用到 setValue，直接改造传参不一致会造成影响，
   * 后续其他地方 setValue 替换 为 setState，则可以废弃 setValue
   */
  const setState = (targetId, newValue, option) => {
    if (!targetId) return

    const { targetKey, type = 'replace', index } = option
    if (type !== 'replace' || index !== undefined || index !== null) {
      const pageName = getCurrentPageName()
      if (!state[pageName]) state[pageName] = reactive({})

      const path = targetKey ? [targetId, targetKey] : targetId
      switch (type) {
        case 'replace':
          setValue(targetId, newValue, targetKey)
          break

        case 'merge':
          const targetObject = getIn(state[pageName], path)
          Object.assign(targetObject, newValue)
          break

        case 'splice':
          const targetArray = getIn(state[pageName], path)
          if (Array.isArray(targetArray)) {
            let start = targetArray.length
            if (index === 'start') {
              start = 0
            } else if (typeof index === 'number') {
              start = index
            }
            targetArray.splice(start, 0, newValue)
          }
          break

        default:
          setValue(targetId, newValue, targetKey)
          break
      }
    } else {
      setValue(targetId, newValue, targetKey)
    }
  }

  /**
   * 设置数据
   * 1. 设置 localStorage数据 和 sessionStorage数据
   * 2. 设置 state数据（浅层值、深层值）
   */
  const setValue = (target?: string, newValue?: any, targetKey?: string) => {
    if (!target) return
    if (target === LOCAL_STORAGE) {
      localCache.set(targetKey ?? target, newValue)
      return
    }
    if (target === SESSION_STORAGE) {
      sessionCache.set(targetKey ?? target, newValue)
      return
    }
    if (target === TEMP_DATA) {
      state[TEMP_DATA] = newValue
      return
    }
    if (target === LOADING) {
      if (!state[target]) state[target] = reactive({})
      Object.assign(state[target], newValue)
      return
    }
    const pageName = getCurrentPageName()
    if (!state[pageName]) state[pageName] = reactive({})
    // 临时处理 把旧数据保留
    let oldCompId = null
    if (target.includes('lyy-pc-')) {
      oldCompId = target.split(location.hash.replace('#', '---'))[1]
    }
    if (targetKey) {
      if (target && !state[pageName][target])
        state[pageName][target] = reactive({})
      if (oldCompId && !state[pageName][oldCompId])
        state[pageName][oldCompId] = {}
      setIn(state[pageName][target], targetKey, newValue)
      oldCompId && setIn(state[pageName][oldCompId], targetKey, newValue)
      transProxyData(oldCompId || target, targetKey, newValue)
    } else {
      Object.assign(state[pageName], { [target]: newValue })
      if (target == PAGE_CONFIG) {
        proxyData.updateProxyData(pageName)
      }
      oldCompId && Object.assign(state[pageName], { [oldCompId]: newValue })
      transProxyData(oldCompId || target, null, newValue)
    }
  }

  /**
   * 删除数据
   */
  const deleteValue = (target: string, path?: string) => {
    if (target === LOCAL_STORAGE) {
      localCache.remove(path as string)
      return
    }
    if (target === SESSION_STORAGE) {
      sessionCache.remove(path as string)
      return
    }

    const pageName = getCurrentPageName()
    if (!state[pageName] || (state[pageName] && !state[pageName][target]))
      return
    delete state[pageName][target][path as string]
  }
  const clearPageStore = (page: string) => {
    const pageName = page || getCurrentPageName()
    if (!state[pageName]) return
    delete state[pageName]
  }
  /**
   * 清空 state
   */
  const clear = (exclude: string[]) => {
    const pageName = getCurrentPageName()
    if (!state[pageName]) return
    const pageState = state[pageName]
    for (const key in pageState) {
      !exclude.includes(key) && delete pageState[key]
    }
  }

  // 设置 route、router
  const setRoute = (route: IObject) => {
    state.route = route
  }

  // 设置 router
  const setRouter = (router: IObject) => {
    state.router = router
  }

  const setStore = (store: IObject) => {
    state.store = store
  }

  // 获取 route、router
  const getRoute = () => {
    return state.route
  }

  // 获取 router
  const getRouter = () => {
    return state.router
  }
  // 获取 router
  const getStore = () => {
    return state.store
  }

  return {
    state,
    getValue,
    setValue,
    deleteValue,
    clear,

    setRoute,
    getRoute,
    setRouter,
    getRouter,

    setStore,
    getStore,

    // 新增的功能
    setState,
    clearPageStore,
    getCurrentPageName,
  }
}

// 把store数据同步到数据树
function transProxyData(compId, key, value) {
  if (
    key === 'datasource' ||
    key === 'filters' ||
    key === 'currentRow' ||
    key === 'currentItem' ||
    key === 'currentIndex' ||
    key === 'selectedRows' ||
    key === 'sortOptions' ||
    key === 'isShowDetail' ||
    key === 'exportColumns' ||
    key === 'value' ||
    key === 'selectedRowKeys' ||
    key === 'summaryData' ||
    key === 'selected'
  ) {
    const pageName = getCurrentPageName()
    const target = proxyData.cacheData[pageName][compId]
    if (target) {
      if (!target.modelValue) {
        target.modelValue = reactive({})
      }
      Object.assign(target.modelValue, {
        [key]: value,
      })
    }
  }
}
