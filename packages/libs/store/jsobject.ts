import { myeval } from '../utils/compiler'
import { ref } from 'vue'

import proxyData from '../utils/proxy-data'
import { actionHoc } from '../actions'

export type IJsObject = {
  pageConfigureInfoId: string
  pageComponentId: string
  name: string
  def: string
}

// 总资源 包含：数据集和逻辑集
export const allResource = {}

export const JsObjectList = ref<IJsObject[]>([])
export const dataSetList = []
export const apiFns = () => {
  const fns = getDataSetList() || []
  const runs = {}
  for (const fn of fns) {
    runs[fn['name']] = {
      run: async () => {
        const args = Object.keys(actionHoc).filter((item) => {
          const reg = /^[$A-Z_a-z][\w$]*$/
          return reg.test(item)
        })
        const _with = `
            var { ${args} } = obj;
            // 计算结果
            return eval(exp);
          `
        // 页面初始化时，还没有数据，所以要 try 一下
        try {
          const _eval = new Function('obj', 'exp', _with)
          return _eval(actionHoc, `dataSetRequest('${fn['name']}')`)
        } catch (error) {
          console.log(error)
          return undefined
        }
      },
    }
  }
  return runs
}
export const mergeApiFn = (data) => {
  const apis = apiFns()
  for (const key of Object.keys(apis)) {
    if (data[key]) {
      Object.assign(data[key], apis[key])
    } else {
      data[key] = apis[key]
    }
  }
  return data
}
export const getAllResource = () => {
  const data = mergeApiFn(proxyData.getProxyData())
  for (const jsobject of JsObjectList.value) {
    allResource[jsobject.name] = myeval(
      {
        data,
        globalData: data,
        LYY: actionHoc,
        ...allResource,
        ...data,
        ...apiFns(),
      },
      `${jsobject.def.replace('export default', 'const module = ')}` +
        ';module;',
    )
  }
  allResource.globalData = proxyData.getProxyData()
  return allResource
}

// 设置数据集列表
export const setDataSetList = (arr: IJsObject[]) => {
  dataSetList.length = 0
  dataSetList.push(...arr)
}
// 获取数据集列表
export const getDataSetList = () => {
  return dataSetList
}
// 获取jsobject列表
export const getObjectList = () => {
  return JsObjectList
}

// 设置jsobject列表
export const setObject = (jsArr: IJsObject[]) => {
  JsObjectList.value.length = 0
  Array.isArray(jsArr) && JsObjectList.value.push(...jsArr)
}

// 获取指定数据集配置
export const getDatasetOption = (dataset, datasetList) => {
  const list = datasetList
  const { def } = list?.find((item) => item.name === dataset) ?? {}
  if (!def) {
    return undefined
  }
  return typeof def === 'string' ? JSON.parse(def) : def
}
