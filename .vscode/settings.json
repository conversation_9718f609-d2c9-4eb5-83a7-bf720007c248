{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "typescript.tsdk": "node_modules/typescript/lib", "[javascript]": {"editor.formatOnSave": false}, "[javascriptreact]": {"editor.formatOnSave": false}, "[typescript]": {"editor.formatOnSave": false, "editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescriptreact]": {"editor.formatOnSave": false}, "[vue]": {"editor.formatOnSave": false, "editor.defaultFormatter": "Vue.volar"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {"*.env": "env", "*.env.*": "env"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "[less]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "i18n-ally.localesPaths": ["packages/lyyPcRender/src/locales", "packages/lyyPcRender/src/locales/lang", "packages/lyyPcRender/src/components/lyy-rich-editor/langs"]}